using Avanade.Teams.Auth;
using Microsoft.Identity.Web;

namespace Avanade.Geranium.Attane.Api;

/// <summary>
/// Represents the <b>startup</b> class.
/// </summary>
public class Startup
{
    private static void CheckNotNull<T>(T? value, string name)
    {
        if (value == null)
        {
            throw new ArgumentNullException(name);
        }
    }

    /// <summary>
    /// The configure services method called by the runtime; use this method to add services to the container.
    /// </summary>
    /// <param name="services">The <see cref="IServiceCollection"/>.</param>
    public void ConfigureServices(IServiceCollection services)
    {
        CheckNotNull(services, nameof(services));

        services
            .AddSettings<GeraniumAttaneSettings>()
            // Add type-to-type mapping services using reflection.
            .AddMappers<GeraniumAttaneSettings>();

        var config = services.GetConfig<GeraniumAttaneSettings>() ?? throw new Exception("No configuration");

        // Add the core services.
        services
            .AddBeef(config)
            .AddSwagger()
            // CORS設定を追加
            .AddCors(options =>
            {
                options.AddDefaultPolicy(builder =>
                    builder
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .AllowAnyOrigin()
                );
            })
            // TeamsAuthサービス追加
            .AddTeamsAuthentication(config.Configuration!)
            // Application Insights追加
            .AddApplicationInsights(config)
            .AddGeraniumDependencies(config.Configuration!)
            // WebAPI保護用のMicrosoft identity platform設定を追加 - Authentication Builderを返すため、チェーン出来ないことに注意
            .AddMicrosoftIdentityWebApiAuthentication(config.Configuration!);
    }

    /// <summary>
    /// The configure method called by the runtime; use this method to configure the HTTP request pipeline.
    /// </summary>
    /// <param name="app">The <see cref="IApplicationBuilder"/>.</param>
    /// <param name="env">The environment.</param>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        // Handle any unhandled exceptions.:e :
        app.UseWebApiExceptionHandler();

        if (IsDevelopmentEnvironment(env))
        {
            // Add Swagger as a JSON endpoint and to serve the swagger-ui to the pipeline.
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Avanade.Geranium.Attane"));
            // Local環境でAPIを実行する際に必要
            app.UseCors();
        }

        app.UseHttpsRedirection();
        // Add execution context set up to the pipeline.
        app.UseExecutionContext();
#if RefData
        app.UseReferenceDataOrchestrator();
#endif
        // Add health checks.
        app.UseHealthChecks("/health");

        // Use controllers.
        app.UseRouting();
        app.UseAuthentication();
        app.UseAuthorization();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
    }

    private static bool IsDevelopmentEnvironment(IWebHostEnvironment env)
    {
        return env.IsDevelopment() || env.IsEnvironment("Local");
    }
}

file static class Extension
{
    public static T? GetConfig<T>(this IServiceCollection services)
        where T : class
    {
        return services.BuildServiceProvider().GetRequiredService<T>();
    }
}