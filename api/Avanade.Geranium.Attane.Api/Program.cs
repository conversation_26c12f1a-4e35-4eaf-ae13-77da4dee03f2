namespace Avanade.Geranium.Attane.Api;

/// <summary>
/// The <b>Web API</b> host/program.
/// </summary>
public static class Program
{
    /// <summary>
    /// Main startup.
    /// </summary>
    /// <param name="args">The startup arguments.</param>
    public static void Main(string[] args) =>
        CreateWebHostBuilder(args).Build().Run();

    private static IHostBuilder CreateWebHostBuilder(string[] args) =>
        Host
            .CreateDefaultBuilder()
            .ConfigureWebHostDefaults(webBuilder => webBuilder.UseStartup<Startup>())
            .ConfigureAppConfiguration(config =>
            {
                config
                    .AddEnvironmentVariables("Geranium.Attane")
                    .AddCommandLine(args);
            });
}