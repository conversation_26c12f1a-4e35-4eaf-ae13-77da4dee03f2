/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Api.Controllers
{
    /// <summary>
    /// Provides the <see cref="SearchRequest"/> Web API functionality.
    /// </summary>
    [Authorize]
    [Route("users")]
    [Produces(System.Net.Mime.MediaTypeNames.Application.Json)]
    public partial class SearchRequestController : ControllerBase
    {
        private readonly WebApi _webApi;
        private readonly ISearchRequestManager _manager;
        private readonly Microsoft.Extensions.Logging.ILogger<SearchRequestController> _logger;
        private readonly Caller.ICallerProvider _callerProvider;
        private readonly Token.ITokenIssuer _tokenIssuer;
        private readonly Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration> _configuration;
        private readonly Avanade.Teams.Auth.TokenControllerHelper _tokenControllerHelper;

        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestController"/> class.
        /// </summary>
        /// <param name="webApi">The <see cref="WebApi"/>.</param>
        /// <param name="manager">The <see cref="ISearchRequestManager"/>.</param>
        /// <param name="logger">The <see cref="Microsoft.Extensions.Logging.ILogger{SearchRequestController}"/>.</param>
        /// <param name="callerProvider">The <see cref="Caller.ICallerProvider"/>.</param>
        /// <param name="tokenIssuer">The <see cref="Token.ITokenIssuer"/>.</param>
        /// <param name="configuration">The <see cref="Microsoft.Extensions.Options.IOptions{Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration}"/>.</param>
        /// <param name="tokenControllerHelper">The <see cref="Avanade.Teams.Auth.TokenControllerHelper"/>.</param>
        public SearchRequestController(WebApi webApi, ISearchRequestManager manager, Microsoft.Extensions.Logging.ILogger<SearchRequestController> logger, Caller.ICallerProvider callerProvider, Token.ITokenIssuer tokenIssuer, Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration> configuration, Avanade.Teams.Auth.TokenControllerHelper tokenControllerHelper)
        {
            _webApi = webApi ?? throw new ArgumentNullException(nameof(webApi));
            _manager = manager ?? throw new ArgumentNullException(nameof(manager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _callerProvider = callerProvider ?? throw new ArgumentNullException(nameof(callerProvider));
            _tokenIssuer = tokenIssuer ?? throw new ArgumentNullException(nameof(tokenIssuer));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _tokenControllerHelper = tokenControllerHelper ?? throw new ArgumentNullException(nameof(tokenControllerHelper));
            SearchRequestControllerCtor();
        }

        partial void SearchRequestControllerCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 与えられたUserIdに対応する検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="SearchRequestResult"/> where found.</returns>
        [HttpGet("{userId}/search/result")]
        [ProducesResponseType(typeof(Common.Entities.SearchRequestResult), (int)HttpStatusCode.OK)]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> Get(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.GetAsync<SearchRequestResult?>(Request, p => 
                    _logger.TraceResultAsync(_manager.GetAsync(userId), nameof(Get)), alternateStatusCode: HttpStatusCode.NoContent);
            }, nameof(Get), $"userId: { userId }");

        /// <summary>
        /// 与えられたUserIdに対応する検索結果について、既知の結果を除いたものを取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The 検索結果要求.</param>
        /// <returns>A resultant <see cref="SearchRequestResult"/>.</returns>
        [HttpPost("{userId}/search/result")]
        [ProducesResponseType(typeof(Common.Entities.SearchRequestResult), (int)HttpStatusCode.OK)]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> GetWith(string userId, [FromBody] SearchResultRequest? request) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.PostAsync<SearchRequestResult?>(Request, p => 
                    _logger.TraceResultAsync(_manager.GetWithAsync(userId, request), nameof(GetWith)), alternateStatusCode: HttpStatusCode.NoContent, operationType: CoreEx.OperationType.Unspecified);
            }, nameof(GetWith), $"userId: { userId }, request: { request }");

        /// <summary>
        /// 与えられたUserIdに対応する検索要求を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The created <see cref="SearchRequest"/>.</returns>
        [HttpPost("{userId}/search")]
        [AcceptsBody(typeof(Common.Entities.SearchRequest))]
        [ProducesResponseType(typeof(Common.Entities.SearchRequest), (int)HttpStatusCode.Accepted)]
        public Task<IActionResult> Register(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.PostAsync<SearchRequest, SearchRequest>(Request, p => 
                    _logger.TraceResultAsync(_manager.RegisterAsync(p.Value!, userId, IssueTokensAsync()), nameof(Register)), statusCode: HttpStatusCode.Accepted);
            }, nameof(Register), $", userId: { userId }");

        /// <summary>
        /// 与えられたUserIdに対応する検索をキャンセルします.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="string"/>.</returns>
        [HttpPost("{userId}/search/cancellation")]
        [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> Cancell(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.PostAsync<string?>(Request, p => 
                    _logger.TraceResultAsync(_manager.CancellAsync(userId), nameof(Cancell)), alternateStatusCode: HttpStatusCode.NoContent, operationType: CoreEx.OperationType.Unspecified);
            }, nameof(Cancell), $"userId: { userId }");

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="System.Collections.Generic.Dictionary{string, string}"/> where found.</returns>
        [HttpGet("{userId}/search/context")]
        [ProducesResponseType(typeof(System.Collections.Generic.Dictionary<string, string>), (int)HttpStatusCode.OK)]
        [ProducesResponseType((int)HttpStatusCode.NotFound)]
        public Task<IActionResult> GetContext(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.GetAsync<System.Collections.Generic.Dictionary<string, string>?>(Request, p => 
                    _logger.TraceResultAsync(_manager.GetContextAsync(userId), nameof(GetContext)));
            }, nameof(GetContext), $"userId: { userId }");

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を更新します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The updated <see cref="System.Collections.Generic.Dictionary{string, string}"/>.</returns>
        [HttpPut("{userId}/search/context")]
        [AcceptsBody(typeof(Dictionary<string, string>))]
        [ProducesResponseType(typeof(System.Collections.Generic.Dictionary<string, string>), (int)HttpStatusCode.OK)]
        public Task<IActionResult> UpdateContext(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.PutAsync<Dictionary<string, string>, System.Collections.Generic.Dictionary<string, string>>(Request, p => _manager.UpdateContextAsync(p.Value!, userId));
            }, nameof(UpdateContext), $", userId: { userId }");

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        [HttpDelete("{userId}/search/context")]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> DeleteContext(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.DeleteAsync(Request, p => _manager.DeleteContextAsync(userId));
            }, nameof(DeleteContext), $"userId: { userId }");

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The selected <see cref="string"/> where found.</returns>
        [HttpGet("{userId}/search/context/{fieldName}")]
        [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
        [ProducesResponseType((int)HttpStatusCode.NotFound)]
        public Task<IActionResult> GetContextField(string userId, string fieldName) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.GetAsync<string?>(Request, p => 
                    _logger.TraceResultAsync(_manager.GetContextFieldAsync(userId, fieldName), nameof(GetContextField)));
            }, nameof(GetContextField), $"userId: { userId }, fieldName: { fieldName }");

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The updated <see cref="string"/>.</returns>
        [HttpPut("{userId}/search/context/{fieldName}")]
        [AcceptsBody(typeof(string))]
        [ProducesResponseType(typeof(string), (int)HttpStatusCode.OK)]
        public Task<IActionResult> UpdateContextField(string userId, string fieldName) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.PutAsync<string, string?>(Request, p => _manager.UpdateContextFieldAsync(p.Value!, userId, fieldName));
            }, nameof(UpdateContextField), $", userId: { userId }, fieldName: { fieldName }");

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        [HttpDelete("{userId}/search/context/{fieldName}")]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> DeleteContextField(string userId, string fieldName) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.DeleteAsync(Request, p => _manager.DeleteContextFieldAsync(userId, fieldName));
            }, nameof(DeleteContextField), $"userId: { userId }, fieldName: { fieldName }");
    }
}

#pragma warning restore
#nullable restore
