/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Api.Controllers
{
    /// <summary>
    /// Provides the <see cref="Bookmark"/> Web API functionality.
    /// </summary>
    [Authorize]
    [Route("users")]
    [Produces(System.Net.Mime.MediaTypeNames.Application.Json)]
    public partial class BookmarkController : ControllerBase
    {
        private readonly WebApi _webApi;
        private readonly IBookmarkManager _manager;
        private readonly Microsoft.Extensions.Logging.ILogger<BookmarkController> _logger;
        private readonly Caller.ICallerProvider _callerProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="BookmarkController"/> class.
        /// </summary>
        /// <param name="webApi">The <see cref="WebApi"/>.</param>
        /// <param name="manager">The <see cref="IBookmarkManager"/>.</param>
        /// <param name="logger">The <see cref="Microsoft.Extensions.Logging.ILogger{BookmarkController}"/>.</param>
        /// <param name="callerProvider">The <see cref="Caller.ICallerProvider"/>.</param>
        public BookmarkController(WebApi webApi, IBookmarkManager manager, Microsoft.Extensions.Logging.ILogger<BookmarkController> logger, Caller.ICallerProvider callerProvider)
        {
            _webApi = webApi ?? throw new ArgumentNullException(nameof(webApi));
            _manager = manager ?? throw new ArgumentNullException(nameof(manager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _callerProvider = callerProvider ?? throw new ArgumentNullException(nameof(callerProvider));
            BookmarkControllerCtor();
        }

        partial void BookmarkControllerCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 与えられたUserIdに対応する<see cref="Bookmark"/>の配列を取得します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <returns>The selected <see cref="Bookmark[]"/> where found.</returns>
        [HttpGet("{userId}/bookmarks")]
        [ProducesResponseType(typeof(Common.Entities.Bookmark[]), (int)HttpStatusCode.OK)]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> GetBookmarks(string userId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.GetAsync<Bookmark[]?>(Request, p => 
                    _logger.TraceResultAsync(_manager.GetBookmarksAsync(userId), nameof(GetBookmarks)), alternateStatusCode: HttpStatusCode.NoContent);
            }, nameof(GetBookmarks), $"userId: { userId }");

        /// <summary>
        /// <see cref="Bookmark"/>を登録し、既に存在している場合は内容を更新します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <returns>The created or updated <see cref="Bookmark"/>.</returns>
        [HttpPut("{userId}/bookmarks/{articleId}")]
        [AcceptsBody(typeof(Common.Entities.Bookmark))]
        [ProducesResponseType(typeof(Common.Entities.Bookmark), (int)HttpStatusCode.Created)]
        public Task<IActionResult> PutBookmark(string userId, string articleId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.PutAsync<Bookmark, Bookmark>(Request, p => _manager.PutBookmarkAsync(p.Value!, userId, articleId), statusCode: HttpStatusCode.Created);
            }, nameof(PutBookmark), $", userId: { userId }, articleId: { articleId }");

        /// <summary>
        /// 与えられたキーに対応する<see cref="Bookmark"/>を削除します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        [HttpDelete("{userId}/bookmarks/{articleId}")]
        [ProducesResponseType((int)HttpStatusCode.NoContent)]
        public Task<IActionResult> DeleteBookmark(string userId, string articleId) =>
            _logger.LogBlockAsync(() =>
            {
                _callerProvider.ValidateUserId(this, _logger, userId);
                return _webApi.DeleteAsync(Request, p => _manager.DeleteBookmarkAsync(userId, articleId));
            }, nameof(DeleteBookmark), $"userId: { userId }, articleId: { articleId }");
    }
}

#pragma warning restore
#nullable restore
