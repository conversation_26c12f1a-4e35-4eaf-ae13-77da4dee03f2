﻿using Avanade.Geranium.Attane.Api.Token;

namespace Avanade.Geranium.Attane.Api.Controllers
{
    partial class SearchRequestController
    {
        private Task<Dictionary<string, string>> IssueTokensAsync() =>
            TokenHelper.IssueTokensAsync(
                _callerProvider.GetRequestToken(HttpContext),
                _configuration.Value.TokenKeys,
                _tokenIssuer.IssueTokenAsync
            );
    }
}
