﻿using Avanade.Teams.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Api.Controllers.Auth
{
    /// <summary>
    /// トークン生成を行うController
    /// </summary>
    [ApiController]
    [Route("auth/[controller]")]
    [Authorize]
    public class TokenController : ControllerBase
    {
        private readonly ILogger<TokenController> _logger;
        private readonly TokenControllerHelper _tokenControllerHelper;

        /// <summary>
        /// loggerとtokenControllerHelperを指定して <see cref="TokenController"/> の新しいインスタンスを生成します。
        /// </summary>
        /// // <param name="logger">logger</param>
        /// <param name="tokenControllerHelper">tolkenControllerHelper</param>
        public TokenController(ILogger<TokenController> logger, TokenControllerHelper tokenControllerHelper)
        {
            _logger = logger;
            _tokenControllerHelper = tokenControllerHelper;
        }

        /// <summary>
        /// 与えられたキーに対応するコンテナに対するトークンを取得します。
        /// </summary>
        /// <param name="key">キー</param>
        /// <param name="requireRefreshToken">リフレッシュトークンを必要とするかどうか</param>
        /// <returns>与えられたキーに対応するコンテナに対するトークン</returns>
        [HttpPost("issue/{key}")]
        [Produces("application/json")]
        [Authorize]
        public async Task<IActionResult> IssueAsync(string key, bool requireRefreshToken = false)
        {
            System.Diagnostics.Stopwatch sw = new();
            sw.Start();

            try
            {
                return await _tokenControllerHelper.IssueAsync(this, key, requireRefreshToken);
            }
            finally
            {
                sw.Stop();
                _logger.LogTrace("IssueAsync({key}, {requireRefreshToken}), Duration: {elapsed}, {elapsedMilliseconds}ms", key, requireRefreshToken, sw.Elapsed, sw.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// 与えられたキーに対応するコンテナに対するリフレッシュトークンを取得します。
        /// </summary>
        /// <param name="key">キー</param>
        /// <returns>与えられたキーに対応するコンテナに対するリフレッシュトークン</returns>
        // トークンを更新するエンドポイント
        [HttpPost("refresh/{key}")]
        [Produces("application/json")]
        [Authorize]
        public async Task<IActionResult> RefreshAsync(string key)
        {
            var sw = new System.Diagnostics.Stopwatch();
            sw.Start();

            try
            {
                return await _tokenControllerHelper.RefreshAsync(this, key);
            }
            finally
            {
                sw.Stop();
                _logger.LogTrace("RefreshAsync({key}), Duration: {elapsed}, {elapsedMilliseconds}ms", key, sw.Elapsed, sw.ElapsedMilliseconds);
            }
        }
    }
}
