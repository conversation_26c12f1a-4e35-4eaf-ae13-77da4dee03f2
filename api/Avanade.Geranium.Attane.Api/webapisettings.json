{
  "UseUserSecrets": false, // Turns on: https://docs.microsoft.com/en-us/aspnet/core/security/app-secrets (recommend setting via environment variable Geranium.Search_UseUserSecrets)
  "KeyVaultName": null, // Turns on: https://docs.microsoft.com/en-us/aspnet/core/security/key-vault-configuration (recommend setting via environment variable Geranium.Search_KeyVaultName)
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  },
  "AllowedHosts": "*",
  "IncludeExceptionInResult": true,
  "BeefDefaultPageSize": 25,
  "BeefCaching": {
    "Policies": [
      {
        "Name": "30min sliding with 2hr max (default)",
        "IsDefault": true,
        "Policy": "Beef.Caching.Policy.SlidingCachePolicy, Beef.Core",
        "Properties": [
          {
            "Name": "Duration",
            "Value": "00:30:00"
          },
          {
            "Name": "MaxDuration",
            "Value": "02:00:00"
          },
          {
            "Name": "RandomizerOffset",
            "Value": "05:00:00"
          }
        ]
      }
    ]
  }
}