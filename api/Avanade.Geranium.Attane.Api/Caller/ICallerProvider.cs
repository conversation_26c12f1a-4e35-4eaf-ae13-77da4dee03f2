﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Api.Caller
{
    /// <summary>
    /// Controllerに対する呼び出し元の情報を取得します。
    /// </summary>
    public interface ICallerProvider
    {
        /// <summary>
        /// 呼び出し元の情報を取得します。
        /// </summary>
        /// <param name="controller">対象の <see cref="ControllerBase" />を指定します。</param>
        /// <returns>呼び出し元の情報</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない</exception>
        string GetCaller(ControllerBase controller);

        /// <summary>
        /// 呼び出し元のUPNを取得します。
        /// </summary>
        /// <param name="context">現在のコンテキストを表す <see cref="HttpContext"/>を指定します。</param>
        /// <returns>呼び出し元のUPN</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない、もしくはBearerとなっていない</exception>
        string GetCaller(HttpContext context);

        /// <summary>
        /// 呼び出し元のIDを取得します。
        /// </summary>
        /// <param name="controller">対象の <see cref="ControllerBase" />を指定します。</param>
        /// <returns>呼び出し元のID</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない</exception>
        string GetCallerId(ControllerBase controller);

        /// <summary>
        /// 呼び出し元のoidを取得します。
        /// </summary>
        /// <param name="context">現在のコンテキストを表す <see cref="HttpContext"/>を指定します。</param>
        /// <returns>呼び出し元のoid</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない、もしくはBearerとなっていない</exception>
        string GetCallerId(HttpContext context);

        /// <summary>
        /// 呼び出し時のトークンを取得します。
        /// </summary>
        /// <param name="context">現在のコンテキストを表す <see cref="HttpContext"/>を指定します。</param>
        /// <returns>呼び出し時のトークン</returns>
        string GetRequestToken(HttpContext context);
    }
}
