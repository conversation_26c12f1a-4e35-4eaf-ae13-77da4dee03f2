﻿using Avanade.Geranium.Attane.Api.Token;
using Avanade.Teams.Auth;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.Net.Http.Headers;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Api.Caller
{
    /// <summary>
    /// Bearerから呼び出し元の情報を取得します。
    /// </summary>
    public class BearerCallerProvider : ICallerProvider
    {
        /// <summary>
        /// 呼び出し元のUPNを取得します。
        /// </summary>
        /// <param name="controller">対象の <see cref="ControllerBase"/>を指定します。</param>
        /// <returns>呼び出し元のUPN</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない、もしくはBearerとなっていない</exception>
        public string GetCaller(ControllerBase controller)
            => GetCaller(controller.Request.HttpContext);

        /// <summary>
        /// 呼び出し元のUPNを取得します。
        /// </summary>
        /// <param name="context">現在のコンテキストを表す <see cref="HttpContext"/>を指定します。</param>
        /// <returns>呼び出し元のUPN</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない、もしくはBearerとなっていない</exception>
        public string GetCaller(HttpContext context)
            => GetFieldValue<string>(context, "upn");

        /// <summary>
        /// 呼び出し元のoidを取得します。
        /// </summary>
        /// <param name="controller">対象の <see cref="ControllerBase"/>を指定します。</param>
        /// <returns>呼び出し元のoid</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない、もしくはBearerとなっていない</exception>
        public string GetCallerId(ControllerBase controller) => GetCallerId(controller.Request.HttpContext);

        /// <summary>
        /// 呼び出し元のoidを取得します。
        /// </summary>
        /// <param name="context">現在のコンテキストを表す <see cref="HttpContext"/>を指定します。</param>
        /// <returns>呼び出し元のoid</returns>
        /// <exception cref="UnauthorizedAccessException">認証情報が与えられていない、もしくはBearerとなっていない</exception>
        public string GetCallerId(HttpContext context)
            => GetFieldValue<string>(context, "oid");

        /// <summary>
        /// Bearer Token内のフィールドを取得します。
        /// </summary>
        /// <typeparam name="T">フィールドの型</typeparam>
        /// <param name="context">現在のコンテキストを表す <see cref="HttpContext"/>を指定します。</param>
        /// <param name="fieldName">Bearer token内のフィールド名</param>
        /// <returns>Bearer token内のフィールド値</returns>
        private static T GetFieldValue<T>(HttpContext context, string fieldName)
        {
            var authorization = context.Request.Headers[HeaderNames.Authorization].FirstOrDefault()
                ?? throw new UnauthorizedAccessException();
            return GetFieldValue<T>(authorization, fieldName);
        }

        /// <summary>
        /// Bearer Token内のフィールドを取得します。
        /// </summary>
        /// <typeparam name="T">フィールドの型</typeparam>
        /// <param name="bearer">Bearer tokenを表す文字列</param>
        /// <param name="fieldName">Bearer token内のフィールド名</param>
        /// <returns>Bearer token内のフィールド値</returns>
        private static T GetFieldValue<T>(string bearer, string fieldName)
        {
            var items = bearer.Split(' ');
            if (items[0] != "Bearer")
            {
                throw new UnauthorizedAccessException();
            }

            var jwt = new JsonWebToken(items[1]);
            return jwt.GetPayloadValue<T>(fieldName);
        }

        /// <summary>
        /// 呼び出し時のトークンを取得します。
        /// </summary>
        /// <param name="context">現在のコンテキストを表す <see cref="HttpContext"/>を指定します。</param>
        /// <returns>呼び出し時のトークン</returns>
        public string GetRequestToken(HttpContext context)
            => TokenHelper.GetToken(context.Request.Headers.Authorization);
    }
}
