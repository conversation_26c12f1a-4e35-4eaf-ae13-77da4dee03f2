﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>true</ImplicitUsings>
    <RestoreAdditionalProjectSources>
      https://api.nuget.org/v3/index.json;
      https://pkgs.dev.azure.com/MEC-O365DevOps-dev/Geranium/_packaging/Avanade.Geranium/nuget/v3/index.json;
    </RestoreAdditionalProjectSources>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
    <LangVersion>11.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>Avanade.Geranium.Attane.Api.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>
  <ItemGroup>
    <Content Remove="webapisettings.json" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="webapisettings.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Avanade.Teams.Auth" Version="6.0.2" />
    <PackageReference Include="CoreEx.Azure" Version="2.10.1" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.23.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.1" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.7.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="8.0.0" />
    <PackageReference Include="Azure.Search.Documents" Version="11.7.0-beta.1" />
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
    <PackageReference Include="Azure.Identity" Version="1.13.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Avanade.Geranium.Attane.Business\Avanade.Geranium.Attane.Business.csproj" />
    <ProjectReference Include="..\Avanade.Geranium.Attane.Common\Avanade.Geranium.Attane.Common.csproj" />
  </ItemGroup>
</Project>
