global using CoreEx;
global using CoreEx.Entities;
global using CoreEx.Events;
global using CoreEx.Http;
global using CoreEx.RefData;
global using CoreEx.Validation;
global using CoreEx.WebApis;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.Caching.Memory;
global using Microsoft.Extensions.Logging;
global using Microsoft.OpenApi.Models;
global using System.Net;
global using System.Reflection;
global using Avanade.Geranium.Attane.Business;
global using Avanade.Geranium.Attane.Business.Data;
global using Avanade.Geranium.Attane.Business.Entities;
global using Avanade.Geranium.Attane.Business.Validation;
global using RefDataNamespace = Avanade.Geranium.Attane.Business.Entities;