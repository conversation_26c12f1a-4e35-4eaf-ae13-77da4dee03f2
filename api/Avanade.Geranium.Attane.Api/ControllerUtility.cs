using Avanade.Geranium.Attane.Api.Caller;
using Avanade.Geranium.Attane.Common.Constants;

namespace Avanade.Geranium.Attane.Api
{
    /// <summary>
    /// コントローラでの共通機能
    /// </summary>
    public static class ControllerUtility
    {
        #region ILogger 拡張メソッド
        /// <summary>
        /// リストの各要素に対してTraceログを出力します。
        /// </summary>
        /// <typeparam name="TData">対象の要素の型</typeparam>
        /// <param name="logger">Logger</param>
        /// <param name="collection">出力するリスト</param>
        /// <param name="operationName">ログに出力する操作名</param>
        public static void TraceResult<TData>(this ILogger logger, ICollection<TData>? collection, string operationName)
        {
            // ログ対象でなければ内容の確認も不要
            if (!logger.IsEnabled(LogLevel.Trace))
            {
                return;
            }

            if (collection?.Any() ?? false)
            {
                foreach (var item in collection)
                {
                    logger.LogTrace("Return:{operationName} {item}", operationName, item);
                }
            }
            else
            {
                logger.LogTrace("Return:{operationName} (0件)", operationName);
            }
        }

        /// <summary>
        /// 戻り値に対してTraceログを出力します。
        /// </summary>
        /// <typeparam name="T">戻り値の型</typeparam>
        /// <param name="logger">Logger</param>
        /// <param name="result">戻り値</param>
        /// <param name="operationName">ログに出力する操作名</param>
        public static T TraceResult<T>(this ILogger logger, T result, string operationName)
        {
            // ログ対象でなければ内容の確認も不要
            if (!logger.IsEnabled(LogLevel.Trace))
            {
                return result;
            }

            if (result != null)
            {
                logger.LogTrace("Return:{operationName} {result}", operationName, result);
            }
            else
            {
                logger.LogTrace(TraceResultMessage.ResourceNotFound, operationName);
            }
            return result;
        }

        /// <summary>
        /// 戻り値に対してTraceログを出力します。
        /// </summary>
        /// <typeparam name="T">戻り値の型</typeparam>
        /// <param name="logger">Logger</param>
        /// <param name="task">戻り値を返すタスク</param>
        /// <param name="operationName">ログに出力する操作名</param>
        public static async Task<T> TraceResultAsync<T>(this ILogger logger, Task<T> task, string operationName) =>
            TraceResult(logger, await task, operationName);

        /// <summary>
        /// 実行ユーザーエラーのログを出力します。
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="callerId">処理を呼び出したユーザーID</param>
        /// <param name="userId">処理対象のユーザーID</param>
        public static void LogAuthError(this ILogger logger, string callerId, string userId) =>
            logger.LogError("BizError:Auth callerId({callerId})とuserId({userId})が一致しません", callerId, userId);

        /// <summary>
        /// メソッドを実行し、実行ログを出力します。
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="func">実行するメソッド</param>
        /// <param name="operationName">ログに出力する操作名</param>
        /// <param name="param">ログに出力する呼出パラメータ</param>
        /// <returns><paramref name="func"/>の戻り値</returns>
        public static async Task<IActionResult> LogBlockAsync(this ILogger logger, Func<Task<IActionResult>> func, string operationName, string param)
        {
            logger.LogInformation($"Arg:{operationName} {param}");
            var result = await func();
            logger.LogTrace($"ExecEnd:{operationName} 処理完了");
            return result;
        }
        #endregion

        #region ICallerProvider 拡張メソッド
        /// <summary>
        /// 処理対象のユーザーが呼び出したユーザーと等しいことを確認します。
        /// </summary>
        /// <param name="callerProvider">The <see cref="ICallerProvider"/> which provides caller information</param>
        /// <param name="controller">呼び出されたコントローラー</param>
        /// <param name="logger">ログ出力用の<see cref="ILogger"/></param>
        /// <param name="userId">処理対象のユーザーID</param>
        public static void ValidateUserId(this ICallerProvider callerProvider, ControllerBase controller, ILogger logger, string userId)
        {
            var callerId = callerProvider.GetCallerId(controller);
            if (callerId != userId)
            {
                logger.LogAuthError(callerId, userId);
                throw new AuthorizationException();
            }
        }
        #endregion
    }
}
