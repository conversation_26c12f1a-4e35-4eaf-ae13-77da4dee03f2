﻿using Avanade.Teams.Auth;
using Azure.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Avanade.Teams.Auth.TokenControllerHelper;

namespace Avanade.Geranium.Attane.Api.Token
{
    /// <summary>
    /// トークン取得のヘルパー
    /// </summary>
    public static class TokenHelper
    {
        /// <summary>
        /// AuthorizationヘッダーからBearerTokenを取得します。
        /// </summary>
        /// <param name="authorizationHeader">HTTPリクエストのAuthorizationヘッダー</param>
        /// <exception cref="UnauthorizedException"></exception>
        public static string GetToken(IList<string> authorizationHeader)
        {
            if (authorizationHeader.Count == 0)
            {
                throw new UnauthorizedException();
            }
            var auth = authorizationHeader[0];
            // bearer のあとにトークンがあるはず
            var tokens = auth.Split(' ', 3);
            if (tokens.Length < 2)
            {
                throw new UnauthorizedException();
            }

            return tokens[1];
        }

        /// <summary>
        /// On-Behalf-Ofトークンを生成します。
        /// </summary>
        /// <param name="token">生成元のトークン</param>
        /// <param name="tokenKeys">生成するOBOトークンのキーの配列</param>
        /// <param name="issueTokenAsync">生成元のトークンとキーを渡すとOBOトークンを生成する処理</param>
        /// <returns><paramref name="tokenKeys"/>で指定したキーと、そのキーに対応するOBOトークンを持った<see cref="Dictionary{TKey, TValue}"/></returns>
        /// <exception cref="Exception"></exception>
        public static async Task<Dictionary<string, string>> IssueTokensAsync(string token, IEnumerable<string>? tokenKeys, Func<string, string, Task<(string? accessToken, string? refreshToken)>> issueTokenAsync)
        {
            var oboTokens = new Dictionary<string, string>();
            if (tokenKeys == null)
            {
                return oboTokens;
            }

            foreach (var key in tokenKeys)
            {
                var oboToken = (await issueTokenAsync(token, key)).accessToken
                    ?? throw new UnauthorizedException();
                oboTokens.Add(key, oboToken);
            }
            return oboTokens;
        }
    }
}
