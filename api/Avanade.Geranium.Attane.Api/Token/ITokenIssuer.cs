﻿using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Api.Token
{
    /// <summary>
    /// トークンを発行します。
    /// </summary>
    public interface ITokenIssuer
    {
        /// <summary>
        /// トークンを発行します。
        /// </summary>
        /// <param name="token">元トークン</param>
        /// <param name="key">発行するトークンのキー</param>
        /// <returns>発行されたBearerトークン</returns>
        Task<(string? accessToken, string? refreshToken)> IssueTokenAsync(string token, string key);
    }
}
