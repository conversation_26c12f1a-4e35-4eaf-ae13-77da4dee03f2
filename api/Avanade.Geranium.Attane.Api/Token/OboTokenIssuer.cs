﻿using Avanade.Teams.Auth;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Api.Token
{
    /// <summary>
    /// On-Behalf-Ofトークンを発行します。
    /// </summary>
    public class OboTokenIssuer : ITokenIssuer
    {
        private readonly TokenControllerHelper _tokenControllerHelper;

        /// <summary>
        /// TokenControllerIssuerを指定して<see cref="OboTokenIssuer"/>の新しいインスタンスを生成します。
        /// </summary>
        /// <param name="tokenControllerHelper">TokenControllerHelper</param>
        public OboTokenIssuer(TokenControllerHelper tokenControllerHelper)
        {
            _tokenControllerHelper = tokenControllerHelper;
        }

        /// <summary>
        /// Bearerトークンを発行します。
        /// </summary>
        /// <param name="token">元トークン</param>
        /// <param name="key">発行するトークンのキー</param>
        /// <returns>発行されたBearerトークン</returns>
        public Task<(string? accessToken, string? refreshToken)> IssueTokenAsync(string token, string key)
            => _tokenControllerHelper.IssueTokenAsync(token, key);
    }
}
