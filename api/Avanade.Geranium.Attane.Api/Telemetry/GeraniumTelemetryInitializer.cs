using Avanade.Geranium.Attane.Api.Caller;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using System;

namespace Avanade.Geranium.Attane.Api.Telemetry
{
    /// <summary>
    /// アプリケーションでテレメトリに必要な内容を追加します。
    /// </summary>
    public class GeraniumTelemetryInitializer : ITelemetryInitializer
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICallerProvider _callerProvider;
        /// <summary>
        /// <see cref="GeraniumTelemetryInitializer"/> クラスの新しいインスタンスを作成します
        /// </summary>
        /// <param name="httpContextAccessor"><see cref="IHttpContextAccessor"/>.</param>
        /// <param name="callerProvider"><see cref="ICallerProvider"/>.</param>
        public GeraniumTelemetryInitializer(IHttpContextAccessor httpContextAccessor, ICallerProvider callerProvider)
        {
            _httpContextAccessor = httpContextAccessor;
            _callerProvider = callerProvider;
        }

        /// <summary>
        /// テレメトリを初期化します。
        /// </summary>
        /// <param name="telemetry"></param>
        public void Initialize(ITelemetry telemetry)
        {
            // 全てのテレメトリから削除する
            RemoveClientLocationIp(telemetry);

            // 対象のテレメトリを識別するだけでなく、ISupportPropertiesが実装されていることを確認するために
            // 個別のテレメトリにダウンキャストする
            switch (telemetry)
            {
                case RequestTelemetry requestTelemetry:
                    AddOidTo(requestTelemetry);
                    break;
                case ExceptionTelemetry exceptionTelemetry:
                    AddOidTo(exceptionTelemetry);
                    break;
                case TraceTelemetry traceTelemetry:
                    AddOidTo(traceTelemetry);
                    break;
                case DependencyTelemetry dependencyTelemetry:
                    AddOidTo(dependencyTelemetry);
                    break;
            }
        }

        /// <summary>
        /// テレメトリから位置情報のIPアドレスを削除します。
        /// </summary>
        /// <param name="telemetry">対象のテレメトリ</param>
        private static void RemoveClientLocationIp(ITelemetry telemetry) =>
            telemetry.Context.Location.Ip = "0.0.0.0";

        /// <summary>
        /// テレメトリにoidを追加します。
        /// </summary>
        /// <param name="telemetry">対象のテレメトリ</param>
        private void AddOidTo(ISupportProperties telemetry) =>
            AddContextValueTo(telemetry, "Oid", context => _callerProvider.GetCallerId(context));

        /// <summary>
        /// テレメトリにHttpContextから取得する値を追加します。
        /// </summary>
        /// <param name="telemetry">対象のテレメトリ</param>
        /// <param name="name">テレメトリのプロパティ名</param>
        /// <param name="extractor"><see cref="HttpContext"/>から設定値を取得する関数。nullを返したときは出力しない</param>
        private void AddContextValueTo(ISupportProperties telemetry, string name, Func<HttpContext, string?> extractor)
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null)
            {
                return;
            }

            try
            {
                var value = extractor(context);
                if (value == null)
                {
                    return;
                }

                telemetry.Properties[name] = value;
            }
            catch
            {
                // 例外が発生したら単に追加しない
            }
        }
    }
}
