using FluentAssertions;
using FluentAssertions.Primitives;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;

namespace Avanade.Geranium.Attane.Test
{
    public static class TestHelper
    {
        /// <summary>
        /// 与えられた構成値を持つ <see cref="IConfiguration"/> のインスタンスを生成します。
        /// </summary>
        /// <param name="initialData">構成値</param>
        /// <returns>The <see cref="IConfiguration"/>.</returns>
        public static IConfigurationRoot BuildConfiguration(IEnumerable<KeyValuePair<string, string?>> initialData)
        {
            if (initialData is null)
            {
                throw new ArgumentNullException(nameof(initialData));
            }

            ConfigurationBuilder? builder = new ConfigurationBuilder();
            builder.AddInMemoryCollection(initialData);
            var configuration = builder.Build();
            return configuration;
        }

        /// <summary>
        /// Loggerに出力された内容を確認します。
        /// </summary>
        /// <typeparam name="T">Loggerの型パラメータ</typeparam>
        /// <param name="loggerMock">Loggerのモック</param>
        /// <param name="logLevel">出力されたログレベル</param>
        /// <param name="expected">出力された文字列</param>
        /// <param name="times">出力回数</param>
        /// <returns><paramref name="loggerMock"/>自体</returns>
        public static Mock<ILogger<T>> VerifyLogger<T>(this Mock<ILogger<T>> loggerMock, LogLevel logLevel, string expected, Func<Times> times)
            where T : class
            => VerifyLogger(loggerMock, logLevel, s => s == expected, times);

        /// <summary>
        /// Loggerに出力された内容を確認します。
        /// </summary>
        /// <typeparam name="T">Loggerの型パラメータ</typeparam>
        /// <param name="loggerMock">Loggerのモック</param>
        /// <param name="logLevel">出力されたログレベル</param>
        /// <param name="test">出力された文字列が満たすべき式</param>
        /// <param name="times">出力回数</param>
        /// <returns><paramref name="loggerMock"/>自体</returns>
        public static Mock<ILogger<T>> VerifyLogger<T>(this Mock<ILogger<T>> loggerMock, LogLevel logLevel, Func<string?, bool> test, Func<Times> times)
            where T : class
        {
            loggerMock.Verify(o =>
                o.Log<It.IsAnyType>(
                    logLevel,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((obj, t) => test(obj.ToString())),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()
                ), times);
            return loggerMock;
        }

        [CustomAssertion]
        public static void BeInRange(this DateTimeAssertions assertion, DateTime before, DateTime after, string because = "", params object[] becauseArgs)
        {
            assertion.BeOnOrAfter(before, because, becauseArgs);
            assertion.BeOnOrBefore(after, because, becauseArgs);
        }

        [CustomAssertion]
        public static void BeInRange(this NullableDateTimeAssertions assertion, DateTime before, DateTime after, string because = "", params object[] becauseArgs)
        {
            assertion.NotBeNull(because, becauseArgs);
            assertion.BeOnOrAfter(before, because, becauseArgs);
            assertion.BeOnOrBefore(after, because, becauseArgs);
        }
    }
}
