using System;
using System.Linq;
using System.Text.Json;
using Avanade.Geranium.Attane.Shared;
using FluentAssertions;
using Xunit;

namespace Avanade.Geranium.Attane.Test.Shared
{
    public class ConditionTreeConverterTest
    {
        #region Serialization
        [Fact]
        public void 単一の文字列は文字列としてシリアライズされる()
        {
            var simple = new SearchConditionTree("simple");
            var result = JsonSerializer.Serialize(simple);
            result.Should().Be(@"""simple""");
        }

        [Fact]
        public void And演算はAndオペレーターと複数のOperandのJSONオブジェクトとしてシリアライズされる()
        {
            var tree = new SearchConditionTree(
                SearchConditionOperator.And,
                new[] { new SearchConditionTree("a"), new SearchConditionTree("b") }
            );
            var result = JsonSerializer.Serialize(tree);
            result.Should().Be(@"{""operator"":""And"",""operand"":[""a"",""b""]}");
        }

        [Fact]
        public void Or演算はOrオペレーターと複数のOperandのJSONオブジェクトとしてシリアライズされる()
        {
            var tree = new SearchConditionTree(
                SearchConditionOperator.Or,
                new[] { new SearchConditionTree("a"), new SearchConditionTree("b"), new SearchConditionTree("c") }
            );
            var result = JsonSerializer.Serialize(tree);
            result.Should().Be(@"{""operator"":""Or"",""operand"":[""a"",""b"",""c""]}");
        }

        [Fact]
        public void オペランドはオペレーターをもった検索条件木を許容する()
        {
            var tree = new SearchConditionTree(
                SearchConditionOperator.And,
                new[] {
                    new SearchConditionTree("a"),
                    new SearchConditionTree(
                        SearchConditionOperator.Or,
                        new [] {new SearchConditionTree("b"), new SearchConditionTree("c") }
                    )
                }
            );
            var result = JsonSerializer.Serialize(tree);
            result.Should().Be(@"{""operator"":""And"",""operand"":[""a"",{""operator"":""Or"",""operand"":[""b"",""c""]}]}");
        }
        #endregion

        #region deserialization
        [Fact]
        public void 単一の文字列をパースできる()
        {
            var simple = @"""simple""";
            var result = JsonSerializer.Deserialize<SearchConditionTree>(simple);
            var expect = new SearchConditionTree("simple");
            result.Should().NotBeNull().And.Match<SearchConditionTree>(res => (res.Equal(expect)));
        }

        [Fact]
        public void And演算を表すJSONオブジェクトをパースできる()
        {
            var input = @"{""operator"":""And"",""operand"":[""a"",""b""]}";
            var result = JsonSerializer.Deserialize<SearchConditionTree>(input);
            var expect = new SearchConditionTree(
                SearchConditionOperator.And,
                new[] { new SearchConditionTree("a"), new SearchConditionTree("b") }
            );
            result.Should().NotBeNull().And.Match<SearchConditionTree>(res => res.Equal(expect));
        }

        [Fact]
        public void Or演算を表すJSONオブジェクトをパースできる()
        {
            var input = @"{""operator"":""Or"",""operand"":[""a"",""b"",""c""]}";
            var result = JsonSerializer.Deserialize<SearchConditionTree>(input);
            var expect = new SearchConditionTree(
                SearchConditionOperator.Or,
                new[] { new SearchConditionTree("a"), new SearchConditionTree("b"), new SearchConditionTree("c") }
            );
            result.Should().NotBeNull().And.Match<SearchConditionTree>(res => res.Equal(expect));
        }

        [Fact]
        public void 検索条件木をオペランドに含むJSONをパースできる()
        {
            var input = @"{""operator"":""And"",""operand"":[""a"",{""operator"":""Or"",""operand"":[""b"",""c""]}]}";
            var result = JsonSerializer.Deserialize<SearchConditionTree>(input);
            var expect = new SearchConditionTree(
                SearchConditionOperator.And,
                new[] {
                    new SearchConditionTree("a"),
                    new SearchConditionTree(
                        SearchConditionOperator.Or,
                        new [] {new SearchConditionTree("b"), new SearchConditionTree("c") }
                    )
                }
            );
            result.Should().NotBeNull().And.Match<SearchConditionTree>(res => res.Equal(expect));
        }
        #endregion
    }

}

public static class ConditionTreeExtension
{
    public static bool Equal(this SearchConditionTree tree, SearchConditionTree other)
    {
        return ((tree.Item == null && other.Item == null) || tree.Item == other.Item) &&
            tree.Operator == other.Operator &&
            (
                (tree.Operand == null && other.Operand == null) ||
                (tree.Operand!.Count == other.Operand!.Count &&
                tree.Operand!.Zip(other.Operand!, (a, b) => a.Equal(b)).All(a => a)));
    }
}