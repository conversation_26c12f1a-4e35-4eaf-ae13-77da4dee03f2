using Avanade.Geranium.Attane.Business.Validation;
using Avanade.Geranium.Attane.Business.Entities;
using FluentAssertions;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Avanade.Geranium.Attane.Test.Validators
{
    public class BookmarkValidatorTest
    {
        private readonly BookmarkValidator _validator = new BookmarkValidator();
        private readonly Bookmark _bookmark = BuildBookmark();
        // 最小値未満で最大の値
        private readonly DateTime LessThanMinimum = new DateTime(1601, 1, 1).AddTicks(-1);

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task 正常系()
        {
            var result = await _validator.ValidateAsync(_bookmark);
            result.HasErrors.Should().BeFalse();
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task UserIdが指定されてないなければエラーとなる()
        {
            _bookmark.UserId = "";
            var result = await _validator.ValidateAsync(_bookmark);
            result.HasError(f => f.UserId).Should().BeTrue();
            result.Messages!.Count.Should().Be(1);
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task Kindが指定されてないなければエラーとなる()
        {
            _bookmark.Kind = "";
            var result = await _validator.ValidateAsync(_bookmark);
            result.HasError(f => f.Kind).Should().BeTrue();
            result.Messages!.Count.Should().Be(1);
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task DisplayDateが最小値未満ならエラーとなる()
        {
            _bookmark.DisplayDate = LessThanMinimum;
            var result = await _validator.ValidateAsync(_bookmark);
            result.HasError(f => f.DisplayDate).Should().BeTrue();
            result.Messages!.Count.Should().Be(1);
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task ReposCreatedDateが最小値未満ならエラーとなる()
        {
            _bookmark.ReposCreatedDate = LessThanMinimum;
            var result = await _validator.ValidateAsync(_bookmark);
            result.HasError(f => f.ReposCreatedDate).Should().BeTrue();
            result.Messages!.Count.Should().Be(1);
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task ReposUpdatedDateが最小値未満ならエラーとなる()
        {
            _bookmark.ReposUpdatedDate = LessThanMinimum;
            var result = await _validator.ValidateAsync(_bookmark);
            result.HasError(f => f.ReposUpdatedDate).Should().BeTrue();
            result.Messages!.Count.Should().Be(1);
        }

        private static Bookmark BuildBookmark()
        {
            return new Bookmark
            {
                UserId = "Test-UserId-0001",
                Kind = "SPO",
                Properties = "Test-Properties",
                Id = "Test-Id-0001",
                Note = "Category-Name0001",
                DisplayDate = DateTime.Now,
                ReposCreatedDate = DateTime.Now,
                ReposUpdatedDate = DateTime.Now,
            };
        }
    }
}
