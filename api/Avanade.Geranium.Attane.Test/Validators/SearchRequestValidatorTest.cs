using Avanade.Geranium.Attane.Business.Validation;
using Avanade.Geranium.Attane.Business.Entities;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Avanade.Geranium.Attane.Test.Validators
{
    public class SearchRequestValidatorTest
    {
        private readonly SearchRequestValidator _validator = new SearchRequestValidator();

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task ConditionがNullだとエラーとなる()
        {
            // arrange
            var searchRequest = new SearchRequest
            {
                Condition = null,
            };

            // act
            var result = await _validator.ValidateAsync(searchRequest);

            // assert
            result.HasErrors.Should().BeTrue();
            result.HasError(x => x.Condition).Should().BeTrue();
            result.Messages![0].Text.Should().BeEquivalentTo("Condition is required.");
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task Conditionが空文字列だとエラーとなる()
        {
            // arrange
            var searchRequest = new SearchRequest
            {
                Condition = "",
            };

            // act
            var result = await _validator.ValidateAsync(searchRequest);

            // assert
            result.HasErrors.Should().BeTrue();
            result.HasError(x => x.Condition).Should().BeTrue();
            result.Messages![0].Text.Should().BeEquivalentTo("Condition is required.");
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task Conditionが1001文字だとエラーとなる()
        {
            // arrange
            var searchRequest = new SearchRequest
            {
                Condition = new string('a', 1001),
            };

            // act
            var result = await _validator.ValidateAsync(searchRequest);

            // assert
            result.HasErrors.Should().BeTrue();
            result.HasError(x => x.Condition).Should().BeTrue();
            result.Messages![0].Text.Should().BeEquivalentTo("CoreEx.Validation.MaxLengthFormat");
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task Conditionが101ワードだとエラーとなる()
        {
            // arrange
            var searchRequest = new SearchRequest
            {
                Condition = string.Join(' ', Enumerable.Range(1, 101).Select(a => "a").ToArray()),
            };

            // act
            var result = await _validator.ValidateAsync(searchRequest);

            // assert
            result.HasErrors.Should().BeTrue();
            result.HasError(x => x.Condition).Should().BeTrue();
            result.Messages![0].Text.Should().BeEquivalentTo("キーワードの数は100以下でなければなりません");
        }

        [Fact, Trait("Method", "ValidateAsync")]
        public async Task Conditionが100ワードだと成功する()
        {
            // arrange
            var searchRequest = new SearchRequest
            {
                Condition = string.Join(' ', Enumerable.Range(1, 100).Select(a => "a").ToArray()),
            };

            // act
            var result = await _validator.ValidateAsync(searchRequest);

            // assert
            result.HasErrors.Should().BeFalse();
        }
    }
}
