using Avanade.Geranium.Attane.Business;
using Avanade.Geranium.Attane.Common.Entities;
using FluentAssertions;
using System.Text.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;
using System.Text.Unicode;

namespace Avanade.Geranium.Attane.Test.Business
{
    public class SearchConditionParserTest
    {

        #region Tokenize
        [Fact, Trait("Method", "Tokenize")]
        public void 空文字列を渡されたら空配列を返す()
        {
            // arrange
            var condition = "";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().BeEmpty();
        }

        [Fact, Trait("Method", "Tokenize")]
        public void 単一のキーワードを渡されたらそのキーワードを返す()
        {
            // arrange
            var condition = "keyword";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .<PERSON><PERSON>Contain("keyword");
        }

        [Fact, Trait("Method", "Tokenize")]
        public void ふたつの異なるキーワードを渡されたらそれらのキーワードを返す()
        {
            // arrange
            var condition = "keyword1 keyword2";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().HaveCount(2)
                .And.Contain("keyword1", "keyword2");
        }

#if ExtractDuplicate
        [Fact, Trait("Method", "Tokenize")]
        public void ふたつの等しいキーワードを渡されたらそれらのキーワードを返す()
        {
            // arrange
            var condition = "keyword Ｋｅｙｗｏｒｄ";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .And.Contain(k => k == "keyword" || k == "Ｋｅｙｗｏｒｄ");
        }


        [Fact, Trait("Method", "Tokenize")]
        public void 一方に含まれるキーワードは結果に含まれない()
        {
            // arrange
            var condition = "keyword2 Ｋｅｙｗｏｒｄ";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .And.Contain("keyword2");
        }

        [Fact, Trait("Method", "Tokenize")]
        public void かなが含まれるキーワードも一方に含まれるキーワードは結果に含まれない()
        {
            // arrange
            var condition = "キーワード ｷｰﾜｰﾄﾞ2";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .And.Contain("ｷｰﾜｰﾄﾞ2");
        }
#endif

        [Fact, Trait("Method", "Tokenize")]
        public void 十語くらいで分割する()
        {
            // arrange
            var condition = "When I came to India I saw lot of indian foods to buy.";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
#if ExtractDuplicate
            result.Should().HaveCount(9)
                .And.Contain(new[] { "When", "came", "to", "saw", "lot", "of", "indian", "foods", "buy." });
#else
            result.Should().HaveCount(13)
                .And.Contain(new[] { "When", "I", "came", "to", "India", "I", "saw", "lot", "of", "indian", "foods", "to", "buy." });
#endif
        }

        [Fact, Trait("Method", "Tokenize")]
        public void 括弧はスペースがなくても分割する()
        {
            // arrange
            var condition = "f(3 + 4)";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().HaveCount(6)
                .And.Contain(new[] { "f", "(", "3", "+", "4", ")" });
        }

        [Fact, Trait("Method", "Tokenize")]
        public void クォートした部分は分割しない()
        {
            // arrange
            var condition = "A \"B C\" D";

            // act
            var result = SearchConditionParser.Tokenize(condition);

            // assert
            result.Should().HaveCount(3)
                .And.Contain(new[] { "A", "\"B C\"", "D" });
        }
        #endregion

        #region ParseExpression
        [Fact, Trait("Method", "ParseExpression")]
        public void 単一のキーワードの場合単純に返す()
        {
            // arrange
            var searchWords = new[] { "a" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"""a""");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void キーワードの順接の場合単純にANDで結合する()
        {
            // arrange
            var searchWords = new[] { "a", "b", "c" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"{""operator"":""And"",""operand"":[""a"",""b"",""c""]}");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void クォートしたトークンのクォートは除去される()
        {
            // arrange
            var searchWords = new[] { "\"AND\"", "\"OR\"", "\"(\"", "\")\"" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"{""operator"":""And"",""operand"":[""AND"",""OR"",""("","")""]}");

        }


        [Fact, Trait("Method", "ParseExpression")]
        public void 小文字のandとorはキーワードとして解釈しない()
        {
            // arrange
            var searchWords = new[] { "and", "or" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"{""operator"":""And"",""operand"":[""and"",""or""]}");

        }

        // 上流でこのデータは作成しないはずなので今のところ存在しないが、単体テストは実施しておく
        [Fact, Trait("Method", "ParseExpression")]
        public void 先頭または末尾のみのクォートは除去しない()
        {
            // arrange
            var searchWords = new[] { "\"a", "b\"", "\"" };
            var serializeOptions = new JsonSerializerOptions
            {
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            // act
            var result = JsonSerializer.Serialize(
                SearchConditionParser.ParseExpression(searchWords), serializeOptions);

            // assert
            result.Should().Be(@"{""operator"":""And"",""operand"":[""\""a"",""b\"""",""\""""]}");

        }

        [Fact, Trait("Method", "ParseExpression")]
        public void ORと順接が含まれる場合に優先順位どおりに結合する()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "b", "c", "OR", "d" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"{""operator"":""And"",""operand"":[{""operator"":""Or"",""operand"":[""a"",""b""]},{""operator"":""Or"",""operand"":[""c"",""d""]}]}");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void ANDとORが含まれる場合に優先順位どおりに結合する()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "b", "AND", "c", "OR", "d" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"{""operator"":""Or"",""operand"":[""a"",{""operator"":""And"",""operand"":[""b"",""c""]},""d""]}");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 不要な括弧が含まれる場合に優先順位どおりに結合する()
        {
            // arrange
            var searchWords = new[] { "a", "(", "b", "c", ")", "d" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"{""operator"":""And"",""operand"":[""a"",{""operator"":""And"",""operand"":[""b"",""c""]},""d""]}");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 必要な括弧が含まれる場合に優先順位どおりに結合する()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "(", "b", "c", ")", "OR", "d" };

            // act
            var result = JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            result.Should().Be(@"{""operator"":""Or"",""operand"":[""a"",{""operator"":""And"",""operand"":[""b"",""c""]},""d""]}");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 閉じ括弧がない場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "(", "b", "c", ")", "OR", "(" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("開き括弧に対応する閉じ括弧がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 開き括弧がない場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "(", "b", "c", ")", "OR", ")" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("閉じ括弧に対応する開き括弧がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 先頭にORがある場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "OR", "(", "b", "c", ")", "OR", "d" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ORの左辺がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 末尾にORがある場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "(", "b", "c", ")", "OR" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ORの右辺がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void ORが連続する場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "OR", "(", "b", "c", ")" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ORの左辺がありません");
        }


        [Fact, Trait("Method", "ParseExpression")]
        public void 先頭にANDがある場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "AND", "(", "b", "c", ")", "OR", "d" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ANDの左辺がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 末尾にAMDがある場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "(", "b", "c", ")", "AND" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ANDの右辺がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void ANDが連続する場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "AND", "AND", "(", "b", "c", ")" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ANDの左辺がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void ANDとORが並んでいる場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "AND", "OR", "(", "b", "c", ")" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ORの左辺がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void ORとANDが並んでいる場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "OR", "AND", "(", "b", "c", ")" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("ANDの左辺がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 括弧の中が空の場合に構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "AND", "(", ")", "AND", "b" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("括弧の中が空です");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 閉じ括弧が余ると構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "b", ")", "c" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("閉じ括弧に対応する開き括弧がありません");
        }

        [Fact, Trait("Method", "ParseExpression")]
        public void 閉じ括弧だけが余ると構文エラーとなる()
        {
            // arrange
            var searchWords = new[] { "a", "b", ")" };

            // act
            var act = () => JsonSerializer.Serialize(SearchConditionParser.ParseExpression(searchWords));

            // assert
            act.Should().ThrowExactly<SyntaxErrorException>().WithMessage("閉じ括弧に対応する開き括弧がありません");
        }
        #endregion
    }
}

