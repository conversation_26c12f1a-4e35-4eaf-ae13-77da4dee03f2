using Avanade.Geranium.Attane.Business;
using Avanade.Geranium.Attane.Business.Configuration;
using Avanade.Geranium.Attane.Business.DataSvc;
using Avanade.Geranium.Attane.Business.Entities;
using Avanade.Geranium.Attane.Business.Validation;
using Avanade.Geranium.Attane.Shared;
using CoreEx;
using CoreEx.Entities;
using CoreEx.Validation;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using static System.Collections.Specialized.BitVector32;

namespace Avanade.Geranium.Attane.Test.Business
{
    public class SearchRequestManagerTest
    {
        private readonly Mock<ISearchRequestDataSvc> _dataSvcMock = new Mock<ISearchRequestDataSvc>();
        private readonly Mock<IOptions<SearchConfiguration>> _configMock = new Mock<IOptions<SearchConfiguration>>();
        private readonly SearchRequestManager _manager;
        private readonly Mock<IServiceProvider> _serviceProviderMock = new Mock<IServiceProvider>();

        public SearchRequestManagerTest()
        {
            _manager = new SearchRequestManager(_dataSvcMock.Object, _configMock.Object, new IdentifierGenerator(), new CoreEx.Events.NullEventPublisher());
            _serviceProviderMock.Setup(x => x.GetService(typeof(IValidator<SearchRequest>))).Returns(new SearchRequestValidator());
            _serviceProviderMock.Setup(x => x.GetService(typeof(SearchRequestValidator))).Returns(new SearchRequestValidator());
            if (!ExecutionContext.HasCurrent)
            {
                ExecutionContext.SetCurrent(new ExecutionContext());
            }
            ExecutionContext.Current.ServiceProvider = _serviceProviderMock.Object;
        }

        private void SetupMocks(SearchState state, int processCount = 10, int resultCount = 2, int hasNextCount = 2, bool useContext = false)
        {
            var reqId = new Guid("11111111-1111-1111-1111-111111111111");

            _dataSvcMock
                .Setup(m => m.GetRequestAsync("ExistingUser"))
                .Returns(Task.FromResult(new InternalSearchRequest
                {
                    UserId = "ExistingUser",
                    ReqId = reqId,
                    State = state,
                    ProcessCount = processCount,
                    Condition = "abc def",
                    ConditionKeywords = SearchConditionParser.ParseExpression(new[] { "abc", "def" }),
                    Tokens = new Dictionary<string, string> { { "key1", "token1" }, { "key2", "token2" } },
                    Context = useContext ? new Dictionary<string, string> { { "key1", "context1" }, { "key2", "context2" } } : null,
                }
                ));

            if (resultCount > 0)
            {
                _dataSvcMock
                    .Setup(m => m.GetResultsAsync("ExistingUser"))
                    .Returns(Task.FromResult(Enumerable.Range(1, resultCount).Select(i => new SearchProcessRequestResult
                    {
                        UserId = "ExistingUser",
                        ReqId = reqId,
                        Pid = $"11111111-1111-1111-2222-{i:D12}",
                        DataSource = new DataSource
                        {
                            Kind = DataSourceKind.SPO,
                            Properties = new Dictionary<string, string> { { "key1", $"source{i}-1" }, { "key2", $"source{i}-2" } },
                        },
                        Ids = new string[] { $"result{i}-1", $"result{i}-2" },
                        HasNext = i <= hasNextCount,
                    })));
            }
        }

        #region GetOnImplementationAsync
        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task 見つからない場合はnullを返す()
        {
            // arrange
            const string userId = "NotExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 2,
                resultCount: 2
            );

            // act
            var result = await _manager.GetOnImplementationAsync(userId);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task 取得した検索結果が返される()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 5,
                useContext: true
            );

            // act
            var result = await _manager.GetOnImplementationAsync(userId);

            Func<SearchProcessRequestResult, int, bool> assertItem = (r, i) =>
                r.Pid == $"11111111-1111-1111-2222-{i:D12}" &&
                r.DataSource!.Properties != null &&
                r.DataSource.Properties.Count == 2 &&
                r.DataSource.Properties["key1"] == $"source{i}-1" &&
                r.DataSource.Properties["key2"] == $"source{i}-2";

            // assert
            result.Should().NotBeNull();
            result!.ReqId.Should().Be(new Guid("11111111-1111-1111-1111-111111111111"));
            result.Condition.Should().Be("abc def");
            result.UserId.Should().Be("ExistingUser");
            result.ConditionKeywords?.Operand.Should().Contain(i => i.Item == "abc").And.Contain(i => i.Item == "def");
            result.Results.Should().NotBeNull();
            result.Results.Should()
                .OnlyContain(r =>
                    r.UserId == userId &&
                    r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                    r.DataSource != null &&
                    r.DataSource.Kind == DataSourceKind.SPO
                )
                .And.Satisfy(
                    r => assertItem(r, 1),
                    r => assertItem(r, 2),
                    r => assertItem(r, 3),
                    r => assertItem(r, 4),
                    r => assertItem(r, 5)
                );
            result.Context.Should().NotBeNull();
            result.Context!["key1"].Should().Be("context1");
            result.Context["key2"].Should().Be("context2");
        }

        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task SearchStateがInProgressでResultがProcessCount件ある場合はStateがCompletedになる検索結果が1件もない場合はResultが空配列になる()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 0
            );

            // act
            var result = await _manager.GetOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull();
            result!.Results.Should().NotBeNull();
            result.Results.Should().BeEmpty();
        }

        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task SearchStateがInProgressでResultがProcessCount件ある場合はStateがCompletedになる()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 7,
                hasNextCount: 2
            );

            // act
            var result = await _manager.GetOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull();
            result!.State.Should().Be(SearchState.Completed);
        }

        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task SearchStateがInProgressでResultがProcessCount件ない場合はStateがInProgressになる()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 5,
                hasNextCount: 1
            );

            // act
            var result = await _manager.GetOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull();
            result!.State.Should().Be(SearchState.InProgress);
        }

        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task SearchStateがErrorでResultがProcessCount件ある場合はStateがErrorになる()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.Error,
                processCount: 5,
                resultCount: 8,
                hasNextCount: 3
            );

            // act
            var result = await _manager.GetOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull();
            result!.State.Should().Be(SearchState.Error);
        }
        #endregion

        #region GetWithOnImplementationAsync
        [Fact, Trait("Method", "GetWithOnImplementationAsync")]
        public async Task Withで見つからない場合はnullを返す()
        {
            // arrange
            const string userId = "NotExistingUser";
            var request = new SearchResultRequest
            {
                ReqId = new Guid("11111111-1111-1111-1111-111111111111"),
                KnownPids = new[] { "pid1", "pid2" },
            };
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 2,
                resultCount: 2
            );

            // act
            var result = await _manager.GetWithOnImplementationAsync(userId, request);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetWithOnImplementationAsync")]
        public async Task Withで取得したReqIdが要求と異なる場合は検索結果が返される()
        {
            // arrange
            const string userId = "ExistingUser";
            var request = new SearchResultRequest
            {
                ReqId = new Guid("*************-1111-1111-111111111111"),
                KnownPids = new[] { "11111111-1111-1111-2222-000000000001", "11111111-1111-1111-2222-000000000002" },
            };

            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 5
            );

            // act
            var result = await _manager.GetWithOnImplementationAsync(userId, request);

            Func<SearchProcessRequestResult, int, bool> assertItem = (r, i) =>
                r.Pid == $"11111111-1111-1111-2222-{i:D12}" &&
                r.DataSource!.Properties != null &&
                r.DataSource.Properties.Count == 2 &&
                r.DataSource.Properties["key1"] == $"source{i}-1" &&
                r.DataSource.Properties["key2"] == $"source{i}-2";

            // assert
            result.Should().NotBeNull();
            result!.ReqId.Should().Be(new Guid("11111111-1111-1111-1111-111111111111"));
            result.Condition.Should().Be("abc def");
            result.UserId.Should().Be("ExistingUser");
            result.ConditionKeywords?.Operand.Should().Contain(i => i.Item == "abc").And.Contain(i => i.Item == "def");
            result.Results.Should().NotBeNull();
            result.Results.Should()
                .OnlyContain(r =>
                    r.UserId == userId &&
                    r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                    r.DataSource != null &&
                    r.DataSource.Kind == DataSourceKind.SPO
                )
                .And.Satisfy(
                    r => assertItem(r, 1),
                    r => assertItem(r, 2),
                    r => assertItem(r, 3),
                    r => assertItem(r, 4),
                    r => assertItem(r, 5)
                );
        }

        [Fact, Trait("Method", "GetWithOnImplementationAsync")]
        public async Task Withで取得したReqIdが要求と等しい場合は指定したPidを除く検索結果が返される()
        {
            // arrange
            const string userId = "ExistingUser";
            var request = new SearchResultRequest
            {
                ReqId = new Guid("11111111-1111-1111-1111-111111111111"),
                KnownPids = new[] { "11111111-1111-1111-2222-000000000001", "11111111-1111-1111-2222-000000000002" },
            };

            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 5
            );

            // act
            var result = await _manager.GetWithOnImplementationAsync(userId, request);

            Func<SearchProcessRequestResult, int, bool> assertItem = (r, i) =>
                r.Pid == $"11111111-1111-1111-2222-{i:D12}" &&
                r.DataSource!.Properties != null &&
                r.DataSource.Properties.Count == 2 &&
                r.DataSource.Properties["key1"] == $"source{i}-1" &&
                r.DataSource.Properties["key2"] == $"source{i}-2";

            // assert
            result.Should().NotBeNull();
            result!.ReqId.Should().Be(new Guid("11111111-1111-1111-1111-111111111111"));
            result.Condition.Should().Be("abc def");
            result.UserId.Should().Be("ExistingUser");
            result.ConditionKeywords?.Operand.Should().Contain(i => i.Item == "abc").And.Contain(i => i.Item == "def");
            result.Results.Should().NotBeNull();
            result.Results.Should()
                .OnlyContain(r =>
                    r.UserId == userId &&
                    r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                    r.DataSource != null &&
                    r.DataSource.Kind == DataSourceKind.SPO
                )
                .And.Satisfy(
                    r => assertItem(r, 3),
                    r => assertItem(r, 4),
                    r => assertItem(r, 5)
                );
        }

        [Fact, Trait("Method", "GetWithOnImplementationAsync")]
        public async Task WithでSearchStateがInProgressでResultがProcessCount件ある場合はStateがCompletedになる検索結果が1件もない場合はResultが空配列になる()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 0
            );
            var request = new SearchResultRequest
            {
                ReqId = new Guid("11111111-1111-1111-1111-111111111111"),
                KnownPids = new[] { "11111111-1111-1111-2222-000000000001", "11111111-1111-1111-2222-000000000002" },
            };

            // act
            var result = await _manager.GetWithOnImplementationAsync(userId, request);

            // assert
            result.Should().NotBeNull();
            result!.Results.Should().NotBeNull();
            result.Results.Should().BeEmpty();
        }

        [Fact, Trait("Method", "GetWithOnImplementationAsync")]
        public async Task WithでSearchStateがInProgressでResultがProcessCount件ある場合はStateがCompletedになる()
        {
            // arrange
            const string userId = "ExistingUser";
            var request = new SearchResultRequest
            {
                ReqId = new Guid("11111111-1111-1111-1111-111111111111"),
                KnownPids = new[] { "11111111-1111-1111-2222-000000000001", "11111111-1111-1111-2222-000000000002" },
            };
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 7,
                hasNextCount: 2
            );

            // act
            var result = await _manager.GetWithOnImplementationAsync(userId, request);

            // assert
            result.Should().NotBeNull();
            result!.State.Should().Be(SearchState.Completed);
        }

        [Fact, Trait("Method", "GetWithOnImplementationAsync")]
        public async Task WithでSearchStateがInProgressでResultがProcessCount件ない場合はStateがInProgressになる()
        {
            // arrange
            const string userId = "ExistingUser";
            var request = new SearchResultRequest
            {
                ReqId = new Guid("11111111-1111-1111-1111-111111111111"),
                KnownPids = new[] { "11111111-1111-1111-2222-000000000001", "11111111-1111-1111-2222-000000000002" },
            };
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 5,
                resultCount: 5,
                hasNextCount: 1
            );

            // act
            var result = await _manager.GetWithOnImplementationAsync(userId, request);

            // assert
            result.Should().NotBeNull();
            result!.State.Should().Be(SearchState.InProgress);
        }

        [Fact, Trait("Method", "GetWithOnImplementationAsync")]
        public async Task WithでSearchStateがErrorでResultがProcessCount件ある場合はStateがErrorになる()
        {
            // arrange
            const string userId = "ExistingUser";
            var request = new SearchResultRequest
            {
                ReqId = new Guid("11111111-1111-1111-1111-111111111111"),
                KnownPids = new[] { "11111111-1111-1111-2222-000000000001", "11111111-1111-1111-2222-000000000002" },
            };
            SetupMocks(
                state: SearchState.Error,
                processCount: 5,
                resultCount: 8,
                hasNextCount: 3
            );

            // act
            var result = await _manager.GetWithOnImplementationAsync(userId, request);

            // assert
            result.Should().NotBeNull();
            result!.State.Should().Be(SearchState.Error);
        }
        #endregion

        #region RegisterOnImplementationAsync
        [Fact, Trait("Method", "RegisterOnImplementationAsync")]
        public async Task 検索要求が正しく登録される()
        {
            // arrange
            const string userId = "ExistingUser";
            var searchRequest = new SearchRequest
            {
                UserId = userId,
                ReqId = new Guid("11111111-**************-111111111111"),
                Condition = "abc def ghi",
                ParsedCondition = SearchConditionParser.Parse("abc def ghi"),
            };
            var tokens = Task.FromResult(new Dictionary<string, string> {
                { "Spo", "spoToken" },
                { "Graph", "graphToken" }
            });
            _configMock.Setup(c => c.Value).Returns(new SearchConfiguration
            {
                DataSources = new[]
                {
                    new DataSources {
                        Kind = DataSourceKind.SPO,
                        Properties = new [] { new Dictionary<string, string> { { "key1", "source1c-1" }, { "key2", "source1c-2" } } },
                    },
                    new DataSources {
                        Kind = DataSourceKind.SPO,
                        Properties = new [] { new Dictionary<string, string> { { "key1", "source2c-1" }, { "key2", "source2c-2" } } },
                    }
                }
            });

            // act
            var result = await _manager.RegisterOnImplementationAsync(searchRequest, userId, tokens);

            // assert
            result.Should().Be(searchRequest);
            _dataSvcMock.Verify(d => d.DeleteRequestAsync("ExistingUser"), Times.Once);
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-**************-111111111111") &&
                r.Condition == "abc def ghi" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.ConditionKeywords.Operand[2].Item == "ghi" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["Spo"] == "spoToken" &&
                r.Tokens["Graph"] == "graphToken"
            )), Times.Once);
            _dataSvcMock.Verify(d => d.CreateProcessAsync("ExistingUser", It.Is<SearchProcessRequest>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-**************-111111111111") &&
                r.DataSources != null &&
                r.DataSources.Kind == DataSourceKind.SPO &&
                r.DataSources.Properties != null &&
                r.DataSources.Properties.Length == 1 &&
                r.DataSources.Properties[0] != null &&
                r.DataSources.Properties[0].Count == 2 &&
                r.DataSources.Properties[0]["key1"] == "source1c-1" &&
                r.DataSources.Properties[0]["key2"] == "source1c-2"
            )), Times.Once);
            _dataSvcMock.Verify(d => d.CreateProcessAsync("ExistingUser", It.Is<SearchProcessRequest>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-**************-111111111111") &&
                r.DataSources != null &&
                r.DataSources.Kind == DataSourceKind.SPO &&
                r.DataSources.Properties != null &&
                r.DataSources.Properties.Length == 1 &&
                r.DataSources.Properties[0] != null &&
                r.DataSources.Properties[0].Count == 2 &&
                r.DataSources.Properties[0]["key1"] == "source2c-1" &&
                r.DataSources.Properties[0]["key2"] == "source2c-2"
            )), Times.Once);
        }

        [Fact, Trait("Method", "RegisterOnImplementationAsync")]
        public async Task 検索要求に子が複数あるときは子の件数を合計する()
        {
            // arrange
            const string userId = "ExistingUser";
            var searchRequest = new SearchRequest
            {
                UserId = userId,
                ReqId = new Guid("11111111-**************-111111111111"),
                Condition = "abc def ghi",
            };
            var tokens = Task.FromResult(new Dictionary<string, string> {
                { "Spo", "spoToken" },
                { "Graph", "graphToken" }
            });
            _configMock.Setup(c => c.Value).Returns(new SearchConfiguration
            {
                DataSources = new[]
                {
                    new DataSources {
                        Kind = DataSourceKind.SPO,
                        Properties = new [] {
                            new Dictionary<string, string> { { "key1", "source1c-1" }, { "key2", "source1c-2" } },
                            new Dictionary<string, string> { { "key1", "source2c-1" }, { "key2", "source2c-2" } },
                            new Dictionary<string, string> { { "key1", "source3c-1" }, { "key2", "source3c-2" } },
                        },
                    },
                    new DataSources {
                        Kind = DataSourceKind.SPO,
                        Properties = new [] {
                            new Dictionary<string, string> { { "key1", "source4c-1" }, { "key2", "source4c-2" } },
                            new Dictionary<string, string> { { "key1", "source5c-1" }, { "key2", "source5c-2" } },
                        },
                    }
                }
            });

            // act
            var result = await _manager.RegisterOnImplementationAsync(searchRequest, userId, tokens);

            // assert
            result.Should().Be(searchRequest);
            _dataSvcMock.Verify(d => d.DeleteRequestAsync("ExistingUser"), Times.Once);
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.ProcessCount == 5
            )), Times.Once);
        }
        #endregion

        #region CancellOnImplementationAsync
        [Fact, Trait("Method", "CancellOnImplementationAsync")]
        public async Task 検索要求が正しくキャンセルされる()
        {
            // arrange
            SetupMocks(SearchState.InProgress, useContext: true);
            const string userId = "ExistingUser";

            // act
            var result = await _manager.CancellOnImplementationAsync(userId);

            // assert
            result.Should().Be("Cancelled");
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync(
                userId,
                It.Is<InternalSearchRequest>(r => r != null &&
                r.State == SearchState.Cancelled &&
                r.UserId == userId &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.ProcessCount == 10 &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key1"] == "context1" &&
                r.Context["key2"] == "context2")
            ), Times.Once);
        }

        [Fact, Trait("Method", "CancellOnImplementationAsync")]
        public async Task 検索要求がキャンセルされているときはエラーにならない()
        {
            // arrange
            SetupMocks(SearchState.Cancelled, useContext: true);
            const string userId = "ExistingUser";

            // act
            var result = await _manager.CancellOnImplementationAsync(userId);

            // assert
            result.Should().Be("Cancelled");
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync(
                userId,
                It.Is<InternalSearchRequest>(r => r != null &&
                r.State == SearchState.Cancelled &&
                r.UserId == userId &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.ProcessCount == 10 &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key1"] == "context1" &&
                r.Context["key2"] == "context2")
            ), Times.Once);
        }

        [Fact, Trait("Method", "CancellOnImplementationAsync")]
        public async Task 検索要求が見つからない場合はNotFoundExceptionを投げる()
        {
            // arrange
            SetupMocks(SearchState.InProgress, useContext: true);
            const string userId = "NotExistingUser";

            // act
            var action = () => _manager.CancellOnImplementationAsync(userId);

            // assert
            await action.Should().ThrowAsync<NotFoundException>();
        }
        #endregion

        #region GetContextOnImplementationAsync
        [Fact, Trait("Method", "GetContextOnImplementationAsync")]
        public async Task 検索要求が見つからない場合はnullを返す()
        {
            // arrange
            const string userId = "NotExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var result = await _manager.GetContextOnImplementationAsync(userId);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextOnImplementationAsync")]
        public async Task 検索要求が見つかってもContextが存在しない場合はnullを返す()
        {
            // arrange
            const string userId = "NotExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: false
            );

            // act
            var result = await _manager.GetContextOnImplementationAsync(userId);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextOnImplementationAsync")]
        public async Task 取得したコンテキストが返される()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var result = await _manager.GetContextOnImplementationAsync(userId);

            result.Should().NotBeNull();
            result!["key1"].Should().Be("context1");
            result["key2"].Should().Be("context2");
        }
        #endregion

        #region UpdateContextOnImplementationAsync
        [Fact, Trait("Method", "UpdateContextOnImplementationAsync")]
        public async Task UpdateContextで検索要求が見つからない場合はNotFoundExceptionを投げる()
        {
            // arrange
            const string userId = "NotExistingUser";
            var context = new Dictionary<string, string> { { "key1", "context1" }, { "key2", "context2" } };
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var action = () => _manager.UpdateContextOnImplementationAsync(context, userId);

            // assert
            await action.Should().ThrowAsync<NotFoundException>();
        }

        [Fact, Trait("Method", "UpdateContextOnImplementationAsync")]
        public async Task UpdateContextで検索要求が見つかってもContextが存在しない場合はContextが登録される()
        {
            // arrange
            const string userId = "ExistingUser";
            var context = new Dictionary<string, string> { { "key1", "context1" }, { "key2", "context2" } };
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: false
            );

            // act
            await _manager.UpdateContextOnImplementationAsync(context, userId);

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key1"] == "context1" &&
                r.Context["key2"] == "context2"
            )), Times.Once);
        }

        [Fact, Trait("Method", "UpdateContextOnImplementationAsync")]
        public async Task 渡したコンテキストで更新される()
        {
            // arrange
            const string userId = "ExistingUser";
            var context = new Dictionary<string, string> { { "key2", "newContext1" }, { "key3", "newContext2" } };
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            await _manager.UpdateContextOnImplementationAsync(context, userId);

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key2"] == "newContext1" &&
                r.Context["key3"] == "newContext2"
            )), Times.Once);
        }
        #endregion

        #region DeleteContextOnImplementationAsync
        [Fact, Trait("Method", "DeleteContextOnImplementationAsync")]
        public async Task DeleteContextで検索要求が見つからない場合は何もしない()
        {
            // arrange
            const string userId = "NotExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var action = () => _manager.DeleteContextOnImplementationAsync(userId);

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextOnImplementationAsync")]
        public async Task DeleteContextで検索要求が見つかってもContextが存在しない場合は何もしない()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: false
            );

            // act
            var action = () => _manager.DeleteContextOnImplementationAsync(userId);

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextOnImplementationAsync")]
        public async Task 既存のコンテキストが削除される()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            await _manager.DeleteContextOnImplementationAsync(userId);

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context == null
            )), Times.Once);
        }
        #endregion

        #region GetContextFieldOnImplementationAsync
        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得で検索要求が見つからない場合はnullを返す()
        {
            // arrange
            const string userId = "NotExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "someField");

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得で検索要求が見つかってもContextが存在しない場合はnullを返す()
        {
            // arrange
            const string userId = "NotExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: false
            );

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "someField");

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得で指定したキーが存在しない場合はnullを返す()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "someField");

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得で取得したフィールドが返される()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "key1");

            result.Should().Be("context1");
        }
        #endregion

        #region UpdateContextFieldOnImplementationAsync
        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldで検索要求が見つからない場合はNotFoundExceptionを投げる()
        {
            // arrange
            const string userId = "NotExistingUser";

            // act
            var action = () => _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "someField");

            // assert
            await action.Should().ThrowAsync<NotFoundException>();
        }

        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldで検索要求が見つかってもContextが存在しない場合はそのフィールドだけ持つContextが登録される()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: false
            );

            // act
            await _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "someField");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 1 &&
                r.Context["someField"] == "someValue"
            )), Times.Once);
        }

        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldで渡したフィールドが存在しない場合は追加される()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            await _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "someField");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 3 &&
                r.Context["key1"] == "context1" &&
                r.Context["key2"] == "context2" &&
                r.Context["someField"] == "someValue"
            )), Times.Once);
        }
        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldで渡したフィールドが存在する場合は更新される()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            await _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "key1");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key1"] == "someValue" &&
                r.Context["key2"] == "context2"
            )), Times.Once);
        }
        #endregion

        #region DeleteContextFieldOnImplementationAsync
        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldで検索要求が見つからない場合は何もしない()
        {
            // arrange
            const string userId = "NotExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var action = () => _manager.DeleteContextFieldOnImplementationAsync(userId, "someField");

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldで検索要求が見つかってもContextが存在しない場合は何もしない()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: false
            );

            // act
            var action = () => _manager.DeleteContextFieldOnImplementationAsync(userId, "someField");

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldでContextが存在してもキーが一致しない場合は何もしない()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            var action = () => _manager.DeleteContextFieldOnImplementationAsync(userId, "someField");

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldでContextが存在してキーが一致する場合はその要素を削除する()
        {
            // arrange
            const string userId = "ExistingUser";
            SetupMocks(
                state: SearchState.InProgress,
                processCount: 0,
                resultCount: 0,
                useContext: true
            );

            // act
            await _manager.DeleteContextFieldOnImplementationAsync(userId, "key1");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateRequestAsync("ExistingUser", It.Is<InternalSearchRequest?>(r =>
                r != null &&
                r.UserId == "ExistingUser" &&
                r.ReqId == new Guid("11111111-1111-1111-1111-111111111111") &&
                r.Condition == "abc def" &&
                r.ConditionKeywords != null &&
                r.ConditionKeywords.Operator == SearchConditionOperator.And &&
                r.ConditionKeywords.Operand![0].Item == "abc" &&
                r.ConditionKeywords.Operand[1].Item == "def" &&
                r.State == SearchState.InProgress &&
                r.Tokens != null &&
                r.Tokens.Count == 2 &&
                r.Tokens["key1"] == "token1" &&
                r.Tokens["key2"] == "token2" &&
                r.Context != null &&
                r.Context.Count == 1 &&
                r.Context["key2"] == "context2"
            )), Times.Once);
        }
        #endregion

        #region Tokenize
        [Fact, Trait("Method", "Tokenize")]
        public void 空文字列を渡されたら空配列を返す()
        {
            // arrange
            const string condition = "";

            // act
            var result = SearchRequestManager.Tokenize(condition);

            // assert
            result.Should().BeEmpty();
        }

        [Fact, Trait("Method", "Tokenize")]
        public void 単一のキーワードを渡されたらそのキーワードを返す()
        {
            // arrange
            const string condition = "keyword";

            // act
            var result = SearchRequestManager.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .And.Contain("keyword");
        }

        [Fact, Trait("Method", "Tokenize")]
        public void ふたつの異なるキーワードを渡されたらそれらのキーワードを返す()
        {
            // arrange
            const string condition = "keyword1 keyword2";

            // act
            var result = SearchRequestManager.Tokenize(condition);

            // assert
            result.Should().HaveCount(2)
                .And.Contain("keyword1", "keyword2");
        }

        [Fact, Trait("Method", "Tokenize")]
        public void ふたつの等しいキーワードを渡されたらそれらのキーワードを返す()
        {
            // arrange
            const string condition = "keyword Ｋｅｙｗｏｒｄ";

            // act
            var result = SearchRequestManager.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .And.Contain(k => k == "keyword" || k == "Ｋｅｙｗｏｒｄ");
        }

        [Fact, Trait("Method", "Tokenize")]
        public void 一方に含まれるキーワードは結果に含まれない()
        {
            // arrange
            const string condition = "keyword2 Ｋｅｙｗｏｒｄ";

            // act
            var result = SearchRequestManager.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .And.Contain("keyword2");
        }

        [Fact, Trait("Method", "Tokenize")]
        public void かなが含まれるキーワードも一方に含まれるキーワードは結果に含まれない()
        {
            // arrange
            const string condition = "キーワード ｷｰﾜｰﾄﾞ2";

            // act
            var result = SearchRequestManager.Tokenize(condition);

            // assert
            result.Should().HaveCount(1)
                .And.Contain("ｷｰﾜｰﾄﾞ2");
        }

        [Fact, Trait("Method", "Tokenize")]
        public void 十語くらいで分割する()
        {
            // arrange
            const string condition = "When I came to India I saw lot of indian foods to buy.";

            // act
            var result = SearchRequestManager.Tokenize(condition);

            // assert
            result.Should().HaveCount(9)
                .And.Contain("When", "came", "to", "saw", "lot", "of", "indian", "foods", "buy");
        }
        #endregion
    }
}
