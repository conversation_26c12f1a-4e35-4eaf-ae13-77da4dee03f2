using Avanade.Geranium.Attane.Business.Data;
using Avanade.Geranium.Attane.Business.Configuration;
using Avanade.Geranium.Attane.Test.Helpers;
using Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Avanade.Geranium.Attane.Shared;
using Avanade.Geranium.Attane.Business;
using CoreEx.Json;

namespace Avanade.Geranium.Attane.Test.Business.Data
{
    public class SearchRequestDataTest
    {
        private const string TableName = "search";
        private const string QueueName = "search-process";
        private readonly InMemoryTableStorageClient _tableStorageClient = new InMemoryTableStorageClient();
        private readonly InMemoryQueueStorageClient _queueStorageClient = new InMemoryQueueStorageClient();
        private readonly ILogger<SearchRequestData> _logger;
        private readonly SearchRequestData _dac;
        public SearchRequestDataTest(ITestOutputHelper testOutputHelper)
        {
            var loggerFactory = new LoggerFactory();
            loggerFactory.AddProvider(new XunitLoggerProvider(testOutputHelper));
            _logger = loggerFactory.CreateLogger<SearchRequestData>();
            var configuration = Options.Create(new SearchConfiguration());

            _dac = new SearchRequestData(_tableStorageClient, _queueStorageClient, _logger, configuration, new CoreEx.Events.NullEventPublisher());

            _tableStorageClient.CreateTable(TableName);
            _queueStorageClient.CreateQueue(QueueName);

            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "ExistingUser",
                RowKey = "request",
                UserId = "ExistingUser",
                ReqId = new Guid("{11111111-1111-1111-1111-111111111111}"),
                Condition = "aaa bbb",
                ConditionKeywords = JsonSerializer.Default.Serialize(SearchConditionParser.ParseExpression(new[] { "aaa", "bbb" })),
                ProcessCount = 1,
                State = "InProgress",
                Tokens = @"{""key1"":""token1"",""key2"":""token2""}",
            });
            // illegal data - should be ignored
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "ExistingUser",
                RowKey = "a",
                ReqId = new Guid("{*************-1111-1111-111111111111}"),
                Pid = "*************-1111-1111-000000000001",
                Kind = "SPO",
                Properties = @"{""key1"":""value1-1"",""key2"":""value1-2""}",
                Ids = @"[""result1-1"",""result1-2""]",
            });
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "ExistingUser",
                RowKey = "result-{11111111-1111-1111-1111-111111111111}-{11111111-1111-1111-1111-000000000001}",
                ReqId = new Guid("{11111111-1111-1111-1111-111111111111}"),
                Pid = "11111111-1111-1111-1111-000000000001",
                Kind = "SPO",
                Properties = @"{""key1"":""value1-1"",""key2"":""value1-2""}",
                Ids = @"[""result1-1"",""result1-2""]",
            });
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "ExistingUser",
                RowKey = "result-{11111111-1111-1111-1111-111111111111}-{11111111-1111-1111-1111-000000000002}",
                ReqId = new Guid("{11111111-1111-1111-1111-111111111111}"),
                Pid = "11111111-1111-1111-1111-000000000002",
                Kind = "SPO",
                Properties = @"{""key1"":""value2-1"",""key2"":""value2-2""}",
                Ids = @"[""result2-1"",""result2-2""]",
            });

            // another user
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "AnotherUser",
                RowKey = "request",
                UserId = "AnotherUser",
                ReqId = new Guid("{*************-1111-1111-111111111111}"),
                Condition = "aaa bbb",
                ConditionKeywords = JsonSerializer.Default.Serialize(SearchConditionParser.ParseExpression(new[] { "aaa", "bbb" })),
                ProcessCount = 1,
                State = "InProgress",
                Tokens = @"{""key1"":""token1"",""key2"":""token2""}",
            });
            // illegal data - should be ignored
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "AnotherUser",
                RowKey = "a",
                ReqId = new Guid("{*************-1111-1111-111111111111}"),
                Pid = "*************-1111-1111-000000000001",
                Kind = "SPO",
                Properties = @"{""key1"":""value1-1"",""key2"":""value1-2""}",
                Ids = @"[""result1-1"",""result1-2""]",
            });
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "AnotherUser",
                RowKey = "result-{*************-1111-1111-111111111111}-{*************-1111-1111-000000000001}",
                ReqId = new Guid("{*************-1111-1111-111111111111}"),
                Pid = "*************-1111-1111-000000000001",
                Kind = "SPO",
                Properties = @"{""key1"":""value1-1"",""key2"":""value1-2""}",
                Ids = @"[""result1-1"",""result1-2""]",
            });
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "AnotherUser",
                RowKey = "result-{*************-1111-1111-111111111111}-{*************-1111-1111-000000000002}",
                ReqId = new Guid("{*************-1111-1111-111111111111}"),
                Pid = "*************-1111-1111-000000000002",
                Kind = "SPO",
                Properties = @"{""key1"":""value2-1"",""key2"":""value2-2""}",
                Ids = @"[""result2-1"",""result2-2""]",
            });

            // user with old format
            _tableStorageClient.Add(TableName, new SearchTableEntity
            {
                PartitionKey = "OldUser",
                RowKey = "request",
                UserId = "OldUser",
                ReqId = new Guid("{*************-3333-3333-************}"),
                Condition = "aaa bbb",
                ConditionKeywords = JsonSerializer.Default.Serialize(new[] { "aaa", "bbb" }),
                ProcessCount = 1,
                State = "InProgress",
                Tokens = @"{""key1"":""token1"",""key2"":""token2""}",
            });
        }

        #region GetRequestOnImplementationAsync
        [Fact, Trait("Method", "GetRequestOnImplementationAsync")]
        public async Task UserIdに対応するデータがなければnullを返す()
        {
            // arrange
            const string userId = "notExistingUser";

            // act
            var result = await _dac.GetRequestOnImplementationAsync(userId);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetRequestOnImplementationAsync")]
        public async Task UserIdに対応するデータを返す()
        {
            // arrange
            const string userId = "ExistingUser";

            // act
            var result = await _dac.GetRequestOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull();
            result!.UserId.Should().Be("ExistingUser");
            result.ReqId.Should().Be(new Guid("11111111-1111-1111-1111-111111111111"));
            result.Condition.Should().Be("aaa bbb");
            result.ConditionKeywords!.Operator.Should().Be(SearchConditionOperator.And);
            result.ConditionKeywords!.Operand![0].Item.Should().Be("aaa");
            result.ConditionKeywords!.Operand![1].Item.Should().Be("bbb");
            result.ProcessCount.Should().Be(1);
            result.State.Should().Be(SearchState.InProgress);
            result.Tokens.Should()
                .HaveCount(2)
                .And.Contain("key1", "token1")
                .And.Contain("key2", "token2");
        }

        [Fact, Trait("Method", "GetRequestOnImplementationAsync")]
        public async Task 旧形式のデータも正しく変換して返す()
        {
            // arrange
            const string userId = "OldUser";

            // act
            var result = await _dac.GetRequestOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull();
            result!.UserId.Should().Be("OldUser");
            result.ReqId.Should().Be(new Guid("*************-3333-3333-************"));
            result.Condition.Should().Be("aaa bbb");
            result.ConditionKeywords!.Operator.Should().Be(SearchConditionOperator.And);
            result.ConditionKeywords!.Operand![0].Item.Should().Be("aaa");
            result.ConditionKeywords!.Operand![1].Item.Should().Be("bbb");
            result.ProcessCount.Should().Be(1);
            result.State.Should().Be(SearchState.InProgress);
            result.Tokens.Should()
                .HaveCount(2)
                .And.Contain("key1", "token1")
                .And.Contain("key2", "token2");
        }
        #endregion

        #region CreateOrUpdateRequestOnImplementationAsync
        [Fact, Trait("Method", "CreateOrUpdateRequestOnImplementationAsync")]
        public async Task 与えられたuserIdに対するSearchRequestがなければSearchRequestを登録する()
        {
            // arrange
            const string userId = "NotExistingUser";
            var searchRequest = new Attane.Business.Entities.InternalSearchRequest
            {
                UserId = userId,
                ReqId = new Guid("11111112-1111-1111-1111-111111111111"),
                Condition = "new condition",
                ConditionKeywords = SearchConditionParser.ParseExpression(new[] { "new", "condition" }),
                ProcessCount = 3,
                State = SearchState.InProgress,
                Tokens = new Dictionary<string, string> { { "key1", "newToken1" }, { "key2", "newToken2" } },
                Context = new Dictionary<string, string> { { "key1", "newContext1" }, { "key2", "newContext2" } },
            };

            // act
            await _dac.CreateOrUpdateRequestOnImplementationAsync(userId, searchRequest);

            // assert
            var entity = await _tableStorageClient.GetByKey<SearchTableEntity>(TableName, "NotExistingUser", "request");
            entity.Should().NotBeNull();
            entity!.UserId.Should().Be("NotExistingUser");
            entity.ReqId.Should().Be(new Guid("11111112-1111-1111-1111-111111111111"));
            entity.Condition.Should().Be("new condition");
            entity.ConditionKeywords.Should().Be(@"{""operator"":""And"",""operand"":[""new"",""condition""]}");
            entity.ProcessCount.Should().Be(3);
            entity.State.Should().Be("InProgress");
            entity.Tokens.Should().Be(@"{""key1"":""newToken1"",""key2"":""newToken2""}");
            entity.Context.Should().Be(@"{""key1"":""newContext1"",""key2"":""newContext2""}");
        }

        [Fact, Trait("Method", "CreateOrUpdateRequestOnImplementationAsync")]
        public async Task 与えられたuserIdに対するSearchRequestがあればSearchRequestを更新する()
        {
            // arrange
            const string userId = "ExistingUser";
            var searchRequest = new Attane.Business.Entities.InternalSearchRequest
            {
                UserId = userId,
                ReqId = new Guid("11111112-1111-1111-1111-111111111111"),
                Condition = "new condition",
                ConditionKeywords = SearchConditionParser.ParseExpression(new string[] { "new", "condition" }),
                ProcessCount = 3,
                State = SearchState.InProgress,
                Tokens = new Dictionary<string, string> { { "key1", "newToken1" }, { "key2", "newToken2" } },
                Context = new Dictionary<string, string> { { "key1", "newContext1" }, { "key2", "newContext2" } },
            };

            // act
            await _dac.CreateOrUpdateRequestOnImplementationAsync(userId, searchRequest);

            // assert
            var entity = await _tableStorageClient.GetByKey<SearchTableEntity>(TableName, "ExistingUser", "request");
            entity.Should().NotBeNull();
            entity!.UserId.Should().Be("ExistingUser");
            entity.ReqId.Should().Be(new Guid("11111112-1111-1111-1111-111111111111"));
            entity.Condition.Should().Be("new condition");
            entity.ConditionKeywords.Should().Be(@"{""operator"":""And"",""operand"":[""new"",""condition""]}");
            entity.ProcessCount.Should().Be(3);
            entity.State.Should().Be("InProgress");
            entity.Tokens.Should().Be(@"{""key1"":""newToken1"",""key2"":""newToken2""}");
            entity.Context.Should().Be(@"{""key1"":""newContext1"",""key2"":""newContext2""}");
        }
        #endregion

        #region DeleteRequestOnImplementationAsync
        [Fact, Trait("Method", "DeleteRequestOnImplementationAsync")]
        public async Task 与えられたuserIdに対する検索要求および結果を削除する()
        {
            // arrange
            const string userId = "ExistingUser";
            var RowKey = "request";
            // PartitionKeyはUserId
            var filter = $"PartitionKey eq '{userId}' and RowKey eq '{RowKey}'";
            // userIdが持つ検索要求と結果が消えるはず
            await _dac.DeleteRequestOnImplementationAsync(userId);

            // assert
            // 検索要求を取りに行く
            (await _tableStorageClient.GetByKey<SearchTableEntity>(TableName, "ExistingUser", "request")).Should().BeNull();
            // 検索結果をとりにく
            (await _tableStorageClient.Get<SearchTableEntity>(TableName, filter))
            .Count().Should().Be(1);
        }

        [Fact, Trait("Method", "DeleteRequestOnImplementationAsync")]
        public async Task 存在しないuserIdに対して何もしない()
        {
            // arrange
            const string userId = "NotExistingUser";
            var filterUserId = "ExistingUser";
            var filter = $"PartitionKey eq '{filterUserId}'";
            // act
            await _dac.DeleteRequestOnImplementationAsync(userId);

            // assert
            (await _tableStorageClient.Get<SearchTableEntity>(TableName, filter))
            .Count().Should().Be(4);
        }
        #endregion

        #region GetResultsOnImplementationAsync
        [Fact, Trait("Method", "GetResultsOnImplementationAsync")]
        public async Task 指定したユーザーの検索結果を返す()
        {
            // arrange
            const string userId = "ExistingUser";

            // act
            var result = await _dac.GetResultsOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull()
                .And.Satisfy(
                    t =>
                        t.ReqId == new Guid("{11111111-1111-1111-1111-111111111111}") &&
                        t.Pid == "11111111-1111-1111-1111-000000000001" &&
                        t.DataSource != null &&
                        t.DataSource.Kind == DataSourceKind.SPO &&
                        t.DataSource.Properties != null &&
                        t.DataSource.Properties.Count == 2 &&
                        t.DataSource.Properties["key1"] == "value1-1" &&
                        t.DataSource.Properties["key2"] == "value1-2" &&
                        t.Ids != null &&
                        t.Ids.Length == 2 &&
                        t.Ids[0] == "result1-1" &&
                        t.Ids[1] == "result1-2",
                    t =>
                        t.ReqId == new Guid("{11111111-1111-1111-1111-111111111111}") &&
                        t.Pid == "11111111-1111-1111-1111-000000000002" &&
                        t.DataSource != null &&
                        t.DataSource.Kind == DataSourceKind.SPO &&
                        t.DataSource.Properties != null &&
                        t.DataSource.Properties.Count == 2 &&
                        t.DataSource.Properties["key1"] == "value2-1" &&
                        t.DataSource.Properties["key2"] == "value2-2" &&
                        t.Ids != null &&
                        t.Ids.Length == 2 &&
                        t.Ids[0] == "result2-1" &&
                        t.Ids[1] == "result2-2"
                );
        }

        [Fact, Trait("Method", "GetResultsOnImplementationAsync")]
        public async Task ユーザーの検索結果が存在しない場合は空配列を返す()
        {
            // arrange
            const string userId = "NotExistingUser";

            // act
            var result = await _dac.GetResultsOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull().And.BeEmpty();
        }
        #endregion
    }
}
