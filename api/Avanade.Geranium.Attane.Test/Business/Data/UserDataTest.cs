using Avanade.Geranium.Attane.Business.Data;
using Avanade.Geranium.Attane.Business.Entities;
using Avanade.Geranium.Attane.Test.Helpers;
using Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;

namespace Avanade.Geranium.Attane.Test.Business.Data
{
    public class UserDataTest
    {
        private const string TableName = "users";
        private readonly InMemoryTableStorageClient _tableStorageClient = new InMemoryTableStorageClient();
        private readonly ILogger<UserData> _logger;
        private readonly UserData _dac;
        public UserDataTest(ITestOutputHelper testOutputHelper)
        {
            var loggerFactory = new LoggerFactory();
            loggerFactory.AddProvider(new XunitLoggerProvider(testOutputHelper));
            _logger = loggerFactory.CreateLogger<UserData>();

            _dac = new UserData(_tableStorageClient, _logger);

            _tableStorageClient.CreateTable(TableName);

            _tableStorageClient.Add(TableName, new UserTableEntity
            {
                PartitionKey = "ExistingUser",
                RowKey = "user",
                UserId = "ExistingUser",
                Context = @"{""key1"":""token1"",""key2"":""token2""}",
            });
            // illegal data - should be ignored
            _tableStorageClient.Add(TableName, new UserTableEntity
            {
                PartitionKey = "ExistingUser",
                RowKey = "a",
                Context = @"{""key1"":""value1-1"",""key2"":""value1-2""}",
            });

            // another user
            _tableStorageClient.Add(TableName, new UserTableEntity
            {
                PartitionKey = "AnotherUser",
                RowKey = "user",
                UserId = "AnotherUser",
                Context = @"{""key1"":""token1"",""key2"":""token2""}",
            });
            // illegal data - should be ignored
            _tableStorageClient.Add(TableName, new UserTableEntity
            {
                PartitionKey = "AnotherUser",
                RowKey = "a",
                Context = @"{""key1"":""value1-1"",""key2"":""value1-2""}",
            });
        }

        #region GetOnImplementationAsync
        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task UserIdに対応するデータがなければnullを返す()
        {
            // arrange
            var userId = "notExistingUser";

            // act
            var result = await _dac.GetOnImplementationAsync(userId);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetOnImplementationAsync")]
        public async Task UserIdに対応するデータを返す()
        {
            // arrange
            var userId = "ExistingUser";

            // act
            var result = await _dac.GetOnImplementationAsync(userId);

            // assert
            result.Should().NotBeNull();
            result!.UserId.Should().Be("ExistingUser");
            result.Context.Should()
                .HaveCount(2)
                .And.Contain("key1", "token1")
                .And.Contain("key2", "token2");
        }
        #endregion

        #region CreateOrUpdateOnImplementationAsync
        [Fact, Trait("Method", "CreateOrUpdateOnImplementationAsync")]
        public async Task 与えられたuserIdに対するUserがなければUserを登録する()
        {
            // arrange
            var userId = "NotExistingUser";
            var User = new User
            {
                UserId = userId,
                Context = new Dictionary<string, string> { { "key1", "newContext1" }, { "key2", "newContext2" } },
            };

            // act
            await _dac.CreateOrUpdateOnImplementationAsync(userId, User);

            // assert
            var entity = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "NotExistingUser", "user");
            entity.Should().NotBeNull();
            entity!.UserId.Should().Be("NotExistingUser");
            entity.Context.Should().Be(@"{""key1"":""newContext1"",""key2"":""newContext2""}");
        }

        [Fact, Trait("Method", "CreateOrUpdateOnImplementationAsync")]
        public async Task 与えられたuserIdに対するUserがあればUserを更新する()
        {
            // arrange
            var userId = "ExistingUser";
            var User = new User
            {
                UserId = userId,
                Context = new Dictionary<string, string> { { "key1", "newContext1" }, { "key2", "newContext2" } },
            };

            // act
            await _dac.CreateOrUpdateOnImplementationAsync(userId, User);

            // assert
            var entity = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "ExistingUser", "user");
            entity.Should().NotBeNull();
            entity!.UserId.Should().Be("ExistingUser");
            entity.Context.Should().Be(@"{""key1"":""newContext1"",""key2"":""newContext2""}");
        }
        #endregion

        #region DeleteOnImplementationAsync
        [Fact, Trait("Method", "DeleteOnImplementationAsync")]
        public async Task 与えられたuserIdに対する検索要求および結果を削除する()
        {
            // arrange
            var userId = "ExistingUser";
            var filterUserId = "ExistingUser";
            var filter = $"PartitionKey eq '{filterUserId}'";
            // act
            await _dac.DeleteOnImplementationAsync(userId);

            // assert
            (await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "ExistingUser", "user")).Should().BeNull();

            (await _tableStorageClient.Get<UserTableEntity>(TableName, filter))
            .Count().Should().Be(1);
        }

        [Fact, Trait("Method", "DeleteOnImplementationAsync")]
        public async Task 存在しないuserIdに対して何もしない()
        {
            // arrange
            var userId = "NotExistingUser";
            var filterUserId = "ExistingUser";
            var filter = $"PartitionKey eq '{filterUserId}'";
            // act
            await _dac.DeleteOnImplementationAsync(userId);

            // assert
            (await _tableStorageClient.Get<UserTableEntity>(TableName, filter))
                .Count().Should().Be(2);
        }
        #endregion
    }
}
