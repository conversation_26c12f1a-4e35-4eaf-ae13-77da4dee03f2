using Avanade.Geranium.Attane.Business;
using Avanade.Geranium.Attane.Business.DataSvc;
using Avanade.Geranium.Attane.Business.Configuration;
using Avanade.Geranium.Attane.Business.Entities;
using CoreEx;
using CoreEx.Entities;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace Avanade.Geranium.Attane.Test.Business
{
    public class UserManagerTest
    {
        private readonly Mock<IUserDataSvc> _dataSvcMock = new Mock<IUserDataSvc>();
        private readonly UserManager _manager;

        public UserManagerTest()
        {
            _manager = new UserManager(_dataSvcMock.Object);
        }

        private void SetupMocks(bool useContext = false)
        {
            _dataSvcMock
                .Setup(m => m.GetAsync("ExistingUser"))
                .Returns(Task.FromResult<User?>(new User
                {
                    UserId = "ExistingUser",
                    Context = useContext ? new Dictionary<string, string> { { "key1", "context1" }, { "key2", "context2" } } : null,
                }
                ));
        }

        #region GetContextOnImplementationAsync
        [Fact, Trait("Method", "GetContextOnImplementationAsync")]
        public async Task プロファイルが見つからない場合はnullを返す()
        {
            // arrange
            var userId = "NotExistingUser";
            SetupMocks(useContext: true);

            // act
            var result = await _manager.GetContextOnImplementationAsync(userId);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextOnImplementationAsync")]
        public async Task プロファイルが見つかってもContextが存在しない場合はnullを返す()
        {
            // arrange
            var userId = "NotExistingUser";
            SetupMocks(useContext: false);

            // act
            var result = await _manager.GetContextOnImplementationAsync(userId);

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextOnImplementationAsync")]
        public async Task 取得したコンテキストが返される()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            var result = await _manager.GetContextOnImplementationAsync(userId);

            result.Should().NotBeNull();
            result!["key1"].Should().Be("context1");
            result["key2"].Should().Be("context2");
        }
        #endregion

        #region UpdateContextOnImplementationAsync
        [Fact, Trait("Method", "UpdateContextOnImplementationAsync")]
        public async Task UpdateContextでプロファイルが見つからない場合はContextが登録される()
        {
            // arrange
            var userId = "NotExistingUser";
            var context = new Dictionary<string, string> { { "key1", "context1" }, { "key2", "context2" } };
            SetupMocks(useContext: true);


            // act
            await _manager.UpdateContextOnImplementationAsync(context, userId);

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("NotExistingUser", It.Is<User>(r =>
                r.UserId == "NotExistingUser" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key1"] == "context1" &&
                r.Context["key2"] == "context2"
            )), Times.Once);
        }

        [Fact, Trait("Method", "UpdateContextOnImplementationAsync")]
        public async Task UpdateContextでプロファイルが見つかってもContextが存在しない場合はContextが登録される()
        {
            // arrange
            var userId = "ExistingUser";
            var context = new Dictionary<string, string> { { "key1", "context1" }, { "key2", "context2" } };
            SetupMocks(useContext: false);

            // act
            await _manager.UpdateContextOnImplementationAsync(context, userId);

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("ExistingUser", It.Is<User>(r =>
                r.UserId == "ExistingUser" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key1"] == "context1" &&
                r.Context["key2"] == "context2"
            )), Times.Once);
        }

        [Fact, Trait("Method", "UpdateContextOnImplementationAsync")]
        public async Task 渡したコンテキストで更新される()
        {
            // arrange
            var userId = "ExistingUser";
            var context = new Dictionary<string, string> { { "key2", "newContext1" }, { "key3", "newContext2" } };
            SetupMocks(useContext: true);

            // act
            await _manager.UpdateContextOnImplementationAsync(context, userId);

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("ExistingUser", It.Is<User>(r =>
                r.UserId == "ExistingUser" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key2"] == "newContext1" &&
                r.Context["key3"] == "newContext2"
            )), Times.Once);
        }
        #endregion

        #region DeleteContextOnImplementationAsync
        [Fact, Trait("Method", "DeleteContextOnImplementationAsync")]
        public async Task DeleteContextでプロファイルが見つからない場合は何もしない()
        {
            // arrange
            var userId = "NotExistingUser";
            SetupMocks(useContext: true);


            // act
            var action = () => _manager.DeleteContextOnImplementationAsync(userId);

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextOnImplementationAsync")]
        public async Task DeleteContextでプロファイルが見つかってもContextが存在しない場合は何もしない()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: false);

            // act
            var action = () => _manager.DeleteContextOnImplementationAsync(userId);

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextOnImplementationAsync")]
        public async Task 既存のコンテキストが削除される()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            await _manager.DeleteContextOnImplementationAsync(userId);

            // assert
            _dataSvcMock.Verify(d => d.DeleteAsync("ExistingUser"), Times.Once);
        }
        #endregion

        #region GetContextFieldOnImplementationAsync
        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得でプロファイルが見つからない場合はnullを返す()
        {
            // arrange
            var userId = "NotExistingUser";
            SetupMocks(useContext: true);

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "someField");

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得でプロファイルが見つかってもContextが存在しない場合はnullを返す()
        {
            // arrange
            var userId = "NotExistingUser";
            SetupMocks(useContext: false);

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "someField");

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得で指定したキーが存在しない場合はnullを返す()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "someField");

            // assert
            result.Should().BeNull();
        }

        [Fact, Trait("Method", "GetContextFieldOnImplementationAsync")]
        public async Task Field取得で取得したフィールドが返される()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            var result = await _manager.GetContextFieldOnImplementationAsync(userId, "key1");

            result.Should().Be("context1");
        }
        #endregion

        #region UpdateContextFieldOnImplementationAsync
        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldでプロファイルが見つからない場合は新規作成される()
        {
            // arrange
            var userId = "NotExistingUser";

            // act
            await _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "someField");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("NotExistingUser", It.Is<User>(r =>
                r.UserId == "NotExistingUser" &&
                r.Context != null &&
                r.Context.Count == 1 &&
                r.Context["someField"] == "someValue"
            )), Times.Once);
        }

        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldでプロファイルが見つかってもContextが存在しない場合はそのフィールドだけ持つContextが登録される()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: false);

            // act
            await _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "someField");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("ExistingUser", It.Is<User>(r =>
                r.UserId == "ExistingUser" &&
                r.Context != null &&
                r.Context.Count == 1 &&
                r.Context["someField"] == "someValue"
            )), Times.Once);
        }

        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldで渡したフィールドが存在しない場合は追加される()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            await _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "someField");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("ExistingUser", It.Is<User>(r =>
                r.UserId == "ExistingUser" &&
                r.Context != null &&
                r.Context.Count == 3 &&
                r.Context["key1"] == "context1" &&
                r.Context["key2"] == "context2" &&
                r.Context["someField"] == "someValue"
            )), Times.Once);
        }
        [Fact, Trait("Method", "UpdateContextFieldOnImplementationAsync")]
        public async Task UpdateContextFieldで渡したフィールドが存在する場合は更新される()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            await _manager.UpdateContextFieldOnImplementationAsync("someValue", userId, "key1");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("ExistingUser", It.Is<User>(r =>
                r.UserId == "ExistingUser" &&
                r.Context != null &&
                r.Context.Count == 2 &&
                r.Context["key1"] == "someValue" &&
                r.Context["key2"] == "context2"
            )), Times.Once);
        }
        #endregion

        #region DeleteContextFieldOnImplementationAsync
        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldでプロファイルが見つからない場合は何もしない()
        {
            // arrange
            var userId = "NotExistingUser";
            SetupMocks(useContext: true);


            // act
            var action = () => _manager.DeleteContextFieldOnImplementationAsync(userId, "someField");

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldでプロファイルが見つかってもContextが存在しない場合は何もしない()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: false);

            // act
            var action = () => _manager.DeleteContextFieldOnImplementationAsync(userId, "someField");

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldでContextが存在してもキーが一致しない場合は何もしない()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            var action = () => _manager.DeleteContextFieldOnImplementationAsync(userId, "someField");

            // assert
            await action.Should().NotThrowAsync();
        }

        [Fact, Trait("Method", "DeleteContextFieldOnImplementationAsync")]
        public async Task DeleteContextFieldでContextが存在してキーが一致する場合はその要素を削除する()
        {
            // arrange
            var userId = "ExistingUser";
            SetupMocks(useContext: true);

            // act
            await _manager.DeleteContextFieldOnImplementationAsync(userId, "key1");

            // assert
            _dataSvcMock.Verify(d => d.CreateOrUpdateAsync("ExistingUser", It.Is<User>(r =>
                r.UserId == "ExistingUser" &&
                r.Context != null &&
                r.Context.Count == 1 &&
                r.Context["key2"] == "context2"
            )), Times.Once);
        }
        #endregion
    }
}
