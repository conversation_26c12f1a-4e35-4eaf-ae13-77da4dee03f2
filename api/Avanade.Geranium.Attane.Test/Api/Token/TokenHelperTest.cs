using Avanade.Geranium.Attane.Api.Token;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;
using static Avanade.Teams.Auth.TokenControllerHelper;

namespace Avanade.Geranium.Attane.Test.Api.Token
{
    public class TokenHelperTest
    {
        #region GetToken
        [Fact, Trait("Method", "GetToken")]
        public void AuthorizationHeaderがなければUnauthorizedExceptionを投げる()
        {
            // arrange
            var authorizationHeader = Array.Empty<string>();

            // act
            var exception = Record.Exception(() => TokenHelper.GetToken(authorizationHeader));

            // assert
            Assert.IsType<UnauthorizedException>(exception);
        }

        [Fact, Trait("Method", "GetToken")]
        public void AuthorizationHeaderがBearerの書式どおりでなければUnauthorizedExceptionを投げる()
        {
            // arrange
            var authorizationHeader = new string[] { "Non-bearer-string" };

            // act
            var exception = Record.Exception(() => TokenHelper.GetToken(authorizationHeader));

            // assert
            Assert.IsType<UnauthorizedException>(exception);
        }

        [Fact, Trait("Method", "GetToken")]
        public void AuthorizationHeaderがBearerの書式どおりでならばトークンを返す()
        {
            // arrange
            var authorizationHeader = new string[] { "bearer bearer-token-string" };

            // act
            var result = TokenHelper.GetToken(authorizationHeader);

            // assert
            Assert.Equal("bearer-token-string", result);
        }
        #endregion

        #region IssueTokensAsync
        [Fact, Trait("Method", "IssueTokensAsync")]
        public async Task TokenKeysがnullのときは空のDictionaryを返す()
        {
            // arrange
            const string token = "token";
            IEnumerable<string>? tokenKeys = null;

            // act
            var result = await TokenHelper.IssueTokensAsync(token, tokenKeys, this.IssueTokenAsync);

            // assert
            Assert.Empty(result);
        }

        [Fact, Trait("Method", "IssueTokensAsync")]
        public async Task TokenKeysがemptyのときは空のDictionaryを返す()
        {
            // arrange
            const string token = "token";
            IEnumerable<string>? tokenKeys = Array.Empty<string>();

            // act
            var result = await TokenHelper.IssueTokensAsync(token, tokenKeys, this.IssueTokenAsync);

            // assert
            Assert.Empty(result);
        }

        [Fact, Trait("Method", "IssueTokensAsync")]
        public async Task IssueTokenAsyncがトークンを生成できない場合はUnauthorizedExceptionを投げる()
        {
            // arrange
            const string token = "token";
            IEnumerable<string>? tokenKeys = new string[] { "key1" };

            static Task<(string? accessToken, string? refreshToken)> IssueTokenAsync(string token, string key) => Task.FromResult<(string?, string?)>((null, "aaa"));

            // act
            var exception = await Record.ExceptionAsync(() => TokenHelper.IssueTokensAsync(token, tokenKeys, IssueTokenAsync));

            // assert
            Assert.IsType<UnauthorizedException>(exception);
        }

        [Fact, Trait("Method", "IssueTokensAsync")]
        public async Task キーに対応するトークンを返す()
        {
            // arrange
            const string token = "token";
            IEnumerable<string>? tokenKeys = new string[] { "key1", "key2" };

            // act
            var result = await TokenHelper.IssueTokensAsync(token, tokenKeys, this.IssueTokenAsync);

            // assert
            Assert.Equal(2, result.Count);
            Assert.Equal("token-key1-accessToken", result["key1"]);
            Assert.Equal("token-key2-accessToken", result["key2"]);
        }

        private Task<(string? accessToken, string? refreshToken)> IssueTokenAsync(string token, string key) =>
            Task.FromResult<(string?, string?)>(($"{token}-{key}-accessToken", $"{token}-{key}-refreshToken"));

        #endregion
    }
}
