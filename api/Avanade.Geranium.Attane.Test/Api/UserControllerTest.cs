// TODO: MockをMockでテストしているためリファイメントタスクとしてテスト方法を検討する必要あり

// using Avanade.Geranium.Attane.Api;
// using Avanade.Geranium.Attane.Api.Controllers;
// using Avanade.Geranium.Attane.Business.Data;
// using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
// using Avanade.Geranium.Attane.Test.Helpers.Api;
// using Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients;
// using FluentAssertions;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Logging;
// using Moq;
// using System.Net;
// using System.Net.Http;
// using System.Text;
// using System.Threading.Tasks;
// using Xunit;

// namespace Avanade.Geranium.Attane.Test.Api
// {
//     public class UserControllerTest
//     {
//         private const string TableName = "users";

//         private readonly CustomWebApplicationFactory<Startup> _factory = new CustomWebApplicationFactory<Startup>()
//         {

//         };
//         private readonly InMemoryTableStorageClient _tableStorageClient = new InMemoryTableStorageClient();
//         private readonly Mock<ILogger<UserController>> _loggerMock = new Mock<ILogger<UserController>>();

//         public UserControllerTest()
//         {
//             _loggerMock.Setup(o => o.IsEnabled(LogLevel.Trace)).Returns(true);
//             ApiTestHelper.AddConfiguration(_factory, services => services
//                 .AddSingleton<ITableStorageClient, ITableStorageClient>(provider => _tableStorageClient)
//                 .SetSingletonMock(_loggerMock)
//             );
//         }

//         #region Get /users/oid/context
//         [Fact, Trait("Method", "Get /users/oid/context")]
//         public async Task Bearerを指定せずGetContextを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             var client = ApiTestHelper.InitializeAuthTest(_factory);

//             // act
//             var response = await client.GetAsync("users/ExistingUser/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
//         }

//         [Fact, Trait("Method", "Get /users/oid/context")]
//         public async Task 他人のデータをGetContextしたときはForbiddenを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("otherUserId");

//             // act
//             var response = await client.GetAsync("users/otherUserId/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
//         }

//         [Fact, Trait("Method", "Get /users/oid/context")]
//         public async Task データが一度も作成されていないときにGetContextしたときはNotFoundを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId");

//             // act
//             var response = await client.GetAsync("users/testUserId/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NotFound);
//             var content = await response.Content.ReadAsStringAsync();
//             content.Should().BeEmpty();
//         }

//         [Fact, Trait("Method", "Get /users/oid/context")]
//         public async Task 作成されているコンテキストを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId", useContext: true);

//             // act
//             var response = await client.GetAsync("users/testUserId/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.OK);
//             var content = await response.Content.ReadAsStringAsync();
//             var contentShouldBe =
//                 @"{""key1"":""context1"",""key2"":""context2""}";

//             content.Should().Be(contentShouldBe);
//         }

//         #endregion

//         #region Put /users/oid/context
//         [Fact, Trait("Method", "Put /users/oid/context")]
//         public async Task Bearerを指定せずPutContextを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             var client = ApiTestHelper.InitializeAuthTest(_factory);
//             var rawContent = @"{ ""key1"":""newContext1"", ""key2"":""newContext2"" }";
//             var content = new StringContent(rawContent, Encoding.UTF8, "application/json");

//             // act
//             var response = await client.PutAsync("users/ExistingUser/context", content);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
//         }

//         [Fact, Trait("Method", "Put /users/oid/context")]
//         public async Task 他人のデータをPutContextしたときはForbiddenを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             var rawContent = @"{ ""key1"":""newContext1"", ""key2"":""newContext2"" }";
//             var content = new StringContent(rawContent, Encoding.UTF8, "application/json");
//             AddTestData("otherUserId");

//             // act
//             var response = await client.PutAsync("users/otherUserId/context", content);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
//         }

//         [Fact, Trait("Method", "Put /users/oid/context")]
//         public async Task データが一度も作成されておらずにPutContextしたときはデータを作成する()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             var rawContent = @"{ ""key1"":""newContext1"", ""key2"":""newContext2"" }";
//             var requestContent = new StringContent(rawContent, Encoding.UTF8, "application/json");
//             AddTestData("otherUserId");

//             // act
//             var response = await client.PutAsync("users/testUserId/context", requestContent);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.OK);
//             var content = await response.Content.ReadAsStringAsync();
//             var contentShouldBe =
//                 @"{""key1"":""newContext1"",""key2"":""newContext2""}";

//             content.Should().Be(contentShouldBe);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""key1"":""newContext1"",""key2"":""newContext2""}");
//         }

//         [Fact, Trait("Method", "Put /users/oid/context")]
//         public async Task PutContextしたとき作成されているデータを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             var rawContent = @"{ ""key1"":""newContext1"", ""key2"":""newContext2"" }";
//             var requestContent = new StringContent(rawContent, Encoding.UTF8, "application/json");
//             AddTestData("testUserId");

//             // act
//             var response = await client.PutAsync("users/testUserId/context", requestContent);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.OK);
//             var content = await response.Content.ReadAsStringAsync();
//             var contentShouldBe =
//                 @"{""key1"":""newContext1"",""key2"":""newContext2""}";

//             content.Should().Be(contentShouldBe);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""key1"":""newContext1"",""key2"":""newContext2""}");
//         }

//         #endregion

//         #region Delete /users/oid/context
//         [Fact, Trait("Method", "Delete /users/oid/context")]
//         public async Task Bearerを指定せずDeleteContextを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             var client = ApiTestHelper.InitializeAuthTest(_factory);

//             // act
//             var response = await client.DeleteAsync("users/ExistingUser/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context")]
//         public async Task 他人のデータをDeleteContextしたときはForbiddenを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("otherUserId");

//             // act
//             var response = await client.DeleteAsync("users/otherUserId/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context")]
//         public async Task データが一度も作成されておらずにDeleteContextしたときは何もしない()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("otherUserId");

//             // act
//             var response = await client.DeleteAsync("users/testUserId/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NoContent);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context")]
//         public async Task データがあるがコンテキストがないときにDeleteContextしたときは何もしない()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId");

//             // act
//             var response = await client.DeleteAsync("users/testUserId/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NoContent);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context")]
//         public async Task コンテキストがあるときにDeleteContextしたときは削除する()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId", useContext: true);

//             // act
//             var response = await client.DeleteAsync("users/testUserId/context");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NoContent);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().BeNull();
//         }
//         #endregion

//         #region Get /users/oid/context/field
//         [Fact, Trait("Method", "Get /users/oid/context/field")]
//         public async Task Bearerを指定せずGetContextFieldを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             var client = ApiTestHelper.InitializeAuthTest(_factory);

//             // act
//             var response = await client.GetAsync("users/ExistingUser/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
//         }

//         [Fact, Trait("Method", "Get /users/oid/context/field")]
//         public async Task 他人のデータをGetContextFieldしたときはForbiddenを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("otherUserId");

//             // act
//             var response = await client.GetAsync("users/otherUserId/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
//         }

//         [Fact, Trait("Method", "Get /users/oid/context/field")]
//         public async Task 既存のキーと一致しないキーを指定してGetContextFieldしたときはNotFoundを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId");

//             // act
//             var response = await client.GetAsync("users/testUserId/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NotFound);
//             var content = await response.Content.ReadAsStringAsync();
//             content.Should().BeEmpty();
//         }

//         [Fact, Trait("Method", "Get /users/oid/context/field")]
//         public async Task 作成されているコンテキストのフィールド値を返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId", useContext: true);

//             // act
//             var response = await client.GetAsync("users/testUserId/context/key1");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.OK);
//             var content = await response.Content.ReadAsStringAsync();

//             content.Should().Be(@"""context1""");
//         }

//         #endregion

//         #region Put /users/oid/context/field
//         [Fact, Trait("Method", "Put /users/oid/context/field")]
//         public async Task Bearerを指定せずPutContextFieldを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             var client = ApiTestHelper.InitializeAuthTest(_factory);
//             var rawContent = @"""context1""";
//             var content = new StringContent(rawContent, Encoding.UTF8, "application/json");

//             // act
//             var response = await client.PutAsync("users/ExistingUser/context/some", content);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
//         }

//         [Fact, Trait("Method", "Put /users/oid/context/field")]
//         public async Task 他人のデータをPutContextFieldしたときはForbiddenを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             var rawContent = @"""context1""";
//             var content = new StringContent(rawContent, Encoding.UTF8, "application/json");
//             AddTestData("otherUserId");

//             // act
//             var response = await client.PutAsync("users/otherUserId/context/some", content);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
//         }

//         [Fact, Trait("Method", "Put /users/oid/context/field")]
//         public async Task データが一度も作成されておらずにPutContextFieldしたときは新規のコンテキストを作成する()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             var rawContent = @"""context1""";
//             var requestContent = new StringContent(rawContent, Encoding.UTF8, "application/json");
//             AddTestData("otherUserId");

//             // act
//             var response = await client.PutAsync("users/testUserId/context/some", requestContent);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.OK);
//             var content = await response.Content.ReadAsStringAsync();
//             var contentShouldBe =
//                 @"""context1""";

//             content.Should().Be(contentShouldBe);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""some"":""context1""}");
//         }

//         [Fact, Trait("Method", "Put /users/oid/context/field")]
//         public async Task PutContextFieldしたとき既存のコンテキストがなければ新規のコンテキストを作成する()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             var rawContent = @"""context1""";
//             var requestContent = new StringContent(rawContent, Encoding.UTF8, "application/json");
//             AddTestData("testUserId");

//             // act
//             var response = await client.PutAsync("users/testUserId/context/key1", requestContent);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.OK);
//             var content = await response.Content.ReadAsStringAsync();
//             var contentShouldBe =
//                 @"""context1""";

//             content.Should().Be(contentShouldBe);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""key1"":""context1""}");
//         }


//         [Fact, Trait("Method", "Put /users/oid/context/field")]
//         public async Task PutContextFieldしたとき既存のコンテキストとキーが一致しなければ新規のキーを作成する()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             var rawContent = @"""context3""";
//             var requestContent = new StringContent(rawContent, Encoding.UTF8, "application/json");
//             AddTestData("testUserId", useContext: true);

//             // act
//             var response = await client.PutAsync("users/testUserId/context/key3", requestContent);

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.OK);
//             var content = await response.Content.ReadAsStringAsync();
//             var contentShouldBe =
//                 @"""context3""";

//             content.Should().Be(contentShouldBe);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""key1"":""context1"",""key2"":""context2"",""key3"":""context3""}");
//         }
//         #endregion

//         #region Delete /users/oid/context/field
//         [Fact, Trait("Method", "Delete /users/oid/context/field")]
//         public async Task Bearerを指定せずDeleteContextFieldを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             var client = ApiTestHelper.InitializeAuthTest(_factory);

//             // act
//             var response = await client.DeleteAsync("users/ExistingUser/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context/field")]
//         public async Task 他人のデータをDeleteContextFieldしたときはForbiddenを返す()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("otherUserId");

//             // act
//             var response = await client.DeleteAsync("users/otherUserId/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.Forbidden);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context/field")]
//         public async Task データが一度も作成されておらずにDeleteContextFieldしたときは何もしない()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("otherUserId");

//             // act
//             var response = await client.DeleteAsync("users/testUserId/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NoContent);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context/field")]
//         public async Task データがあるがコンテキストがないときにDeleteContextFieldしたときは何もしない()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId");

//             // act
//             var response = await client.DeleteAsync("users/testUserId/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NoContent);
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context/field")]
//         public async Task コンテキストがあるときに合致しないキーをDeleteContextFieldしたときは削除する()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId", useContext: true);

//             // act
//             var response = await client.DeleteAsync("users/testUserId/context/some");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NoContent);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""key1"":""context1"",""key2"":""context2""}");
//         }

//         [Fact, Trait("Method", "Delete /users/oid/context/field")]
//         public async Task コンテキストがあるときにDeleteContextFieldしたときは削除する()
//         {
//             // arrange
//             var (client, _, _) = ApiTestHelper.InitializeTest(_factory);
//             AddTestData("testUserId", useContext: true);

//             // act
//             var response = await client.DeleteAsync("users/testUserId/context/key1");

//             // assert
//             response.StatusCode.Should().Be(HttpStatusCode.NoContent);

//             var storedSearch = await _tableStorageClient.GetByKey<UserTableEntity>(TableName, "testUserId", "user");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""key2"":""context2""}");
//         }
//         #endregion

//         #region Test Data Preparation
//         private void AddTestData(string userId, bool useContext = false)
//         {
//             _tableStorageClient.CreateTable(TableName);
//             _tableStorageClient.AddRange(TableName, new[]
//             {
//                 new UserTableEntity
//                 {
//                     PartitionKey = userId,
//                     RowKey = "user",
//                     UserId = userId,
//                     Context = useContext ? @"{""key1"":""context1"",""key2"":""context2""}" : null,
//                 }
//             });
//         }

//         #endregion
//     }
// }
