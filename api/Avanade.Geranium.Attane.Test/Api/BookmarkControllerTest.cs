// TODO: MockをMockでテストしているためリファイメントタスクとしてテスト方法を検討する必要あり

// using Avanade.Geranium.Attane.Api;
// using Avanade.Geranium.Attane.Business;
// using Avanade.Geranium.Attane.Common.Entities;
// using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
// using Avanade.Geranium.Attane.Test.Helpers.Api;
// using Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients;
// using Azure;
// using FluentAssertions;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Logging;
// using Moq;
// using System;
// using System.Dynamic;
// using System.Linq;
// using System.Net.Http;
// using System.Text.Json;
// using System.Threading.Tasks;
// using UnitTestEx;
// using UnitTestEx.Xunit.Internal;
// using Xunit;
// using Xunit.Abstractions;
// using BookmarkEntity = Avanade.Geranium.Attane.Business.Data.BookmarkData.BookmarkEntity;


// namespace Avanade.Geranium.Attane.Test.Api
// {
//     public class BookmarkControllerTest : ApiUnitTestBase
//     {
//         public readonly Mock<ILogger<BookmarkManager>> LoggerMock = new();

//         internal readonly InMemoryTableStorageClient TableStorageClient = new InMemoryTableStorageClient();

//         protected override void Initialize(ApiTester<Startup> test)
//         {
//             base.Initialize(test);

//             LoggerMock.Setup(o => o.IsEnabled(LogLevel.Trace)).Returns(true);

//             test.ConfigureServices(services => services
//                 .AddSingleton<ITableStorageClient, ITableStorageClient>(_ => TableStorageClient)
//                 .AddSingleton<ILogger<BookmarkManager>, ILogger<BookmarkManager>>(_ => LoggerMock.Object)
//             );
//         }

//         private const string TableName = "bookmarks";

//         public BookmarkControllerTest(ITestOutputHelper output) : base(output)
//         {
//             TestSetUp.Environment = "test";
//         }

//         #region 追加APIのテスト
//         [Fact, Trait("Method", "Put")]
//         public async Task Bearerを指定せず追加APIを呼び出し認証に失敗したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/bookmarks/abc", "{}", "application/json"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Put")]
//         public async Task 必須項目であるIdが空だった時ValidationErrorを返す()
//         {
//             // arrange
//             // Idが空のデータ
//             Bookmark putDataWithEmptyId = new Bookmark()
//             {
//                 UserId = "userId1",
//                 Title = "title1",
//                 Id = "",
//                 Kind = "SPO",
//                 Properties = "{\"listUrl\":\"abc\"}",
//                 Note = "category1",
//                 DisplayDate = new DateTime(2022, 1, 1),
//                 ReposCreatedDate = new DateTime(2022, 3, 1),
//                 ReposUpdatedDate = new DateTime(2022, 4, 1),
//                 TimeStamp = new DateTime(2022, 5, 1),
//             };
//             var json = JsonSerializer.Serialize(putDataWithEmptyId);

//             using var test = CreateTest();
//             PrepareData(DataMode.UserAndBookmark);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/bookmarks/abc", json, "application/json"))
//                 // assert
//                 .AssertBadRequest()
//                 .AssertErrors("Identifier is required.");
//         }

//         [Fact, Trait("Method", "Put")]
//         public async Task 追加APIが成功するとレコードを追加してユーザーのReposUpdatedDateを更新する()
//         {
//             // arrange
//             Bookmark putData = new Bookmark()
//             {
//                 UserId = "testUserId",
//                 Title = "title1",
//                 Id = "abc",
//                 Kind = "SPO",
//                 Properties = "{}",
//                 Note = "category1",
//                 DisplayDate = new DateTime(2022, 1, 1),
//                 ReposCreatedDate = new DateTime(2022, 3, 1),
//                 ReposUpdatedDate = new DateTime(2022, 4, 1),
//                 TimeStamp = new DateTime(2022, 5, 1),
//             };
//             var json = JsonSerializer.Serialize(putData);

//             using var test = CreateTest();
//             PrepareData(DataMode.UserAndBookmark);


//             var before = DateTime.UtcNow;
//             // act
//             var response = await test.Http()
//                 .RunAsync(HttpMethod.Put, "users/testUserId/bookmarks/abc", json, "application/json");
//             var after = DateTime.UtcNow;

//             // assert
//             response
//                 .AssertCreated()
//                 .AssertJson("""
//                     {
//                         "userId": "testUserId",
//                         "kind": "SPO",
//                         "properties": "{}",
//                         "id": "abc",
//                         "title": "title1",
//                         "note": "category1",
//                         "displayDate": "2022-01-01T00:00:00Z",
//                         "reposCreatedDate": "2022-03-01T00:00:00Z",
//                         "reposUpdatedDate": "2022-04-01T00:00:00Z",
//                         "timeStamp": "2022-05-01T00:00:00Z"
//                     }
//                 """);

//             var created = await TableStorageClient.GetByKey<BookmarkEntity>(TableName, "testUserId", "spo_abc");

//             created.Should().NotBeNull();
//             created!.UserId.Should().Be("testUserId");
//             created.Title.Should().Be("title1");
//             created.Id.Should().Be("abc");
//             created.Kind.Should().Be("SPO");
//             created.Properties.Should().Be("{}");
//             created.Note.Should().Be("category1");
//             created.DisplayDate.Should().Be(new DateTime(2022, 1, 1));
//             created.ReposCreatedDate.Should().Be(new DateTime(2022, 3, 1));
//             created.ReposUpdatedDate.Should().Be(new DateTime(2022, 4, 1));

//             var user = await TableStorageClient.GetByKey<BookmarkEntity>(TableName, "testUserId", "user");

//             user?.ReposUpdatedDate.Should().BeInRange(before, after);
//         }

//         [Fact, Trait("Method", "Put")]
//         public async Task 追加APIをユーザーが初めて成功するとユーザーのレコードも追加する()
//         {
//             // arrange
//             Bookmark putData = new Bookmark()
//             {
//                 UserId = "testUserId",
//                 Title = "title1",
//                 Id = "abc",
//                 Kind = "SPO",
//                 Properties = "{}",
//                 Note = "category1",
//                 DisplayDate = new DateTime(2022, 1, 1),
//                 ReposCreatedDate = new DateTime(2022, 3, 1),
//                 ReposUpdatedDate = new DateTime(2022, 4, 1),
//                 TimeStamp = new DateTime(2022, 5, 1),
//             };
//             var json = JsonSerializer.Serialize(putData);

//             using var test = CreateTest();
//             PrepareData(DataMode.None);

//             var countBefore = (await Task.Run(() =>
//             TableStorageClient.Get<BookmarkEntity>(TableName))).Count();
//             var before = DateTime.UtcNow;
//             // act
//             var response = await test.Http()
//                 .RunAsync(HttpMethod.Put, "users/testUserId/bookmarks/abc", json, "application/json");
//             var after = DateTime.UtcNow;
//             var countAfter = (await Task.Run(() =>
//             TableStorageClient.Get<BookmarkEntity>(TableName))).Count();

//             response
//                 .AssertCreated()
//                 .AssertJson("""
//                     {
//                         "userId": "testUserId",
//                         "kind": "SPO",
//                         "properties": "{}",
//                         "id": "abc",
//                         "title": "title1",
//                         "note": "category1",
//                         "displayDate": "2022-01-01T00:00:00Z",
//                         "reposCreatedDate": "2022-03-01T00:00:00Z",
//                         "reposUpdatedDate": "2022-04-01T00:00:00Z",
//                         "timeStamp": "2022-05-01T00:00:00Z"
//                     }
//                 """);

//             var created = await TableStorageClient.GetByKey<BookmarkEntity>(TableName, "testUserId", "spo_abc");

//             created.Should().NotBeNull();
//             created!.UserId.Should().Be("testUserId");
//             created.Title.Should().Be("title1");
//             created.Id.Should().Be("abc");
//             created.Kind.Should().Be("SPO");
//             created.Properties.Should().Be("{}");
//             created.Note.Should().Be("category1");
//             created.DisplayDate.Should().Be(new DateTime(2022, 1, 1));
//             created.ReposCreatedDate.Should().Be(new DateTime(2022, 3, 1));
//             created.ReposUpdatedDate.Should().Be(new DateTime(2022, 4, 1));

//             var user = await TableStorageClient.GetByKey<BookmarkEntity>(TableName, "testUserId", "user");

//             user?.ReposUpdatedDate.Should().BeInRange(before, after);
//             // 2 records(bookmark and user) should be added.
//             countAfter.Should().Be(countBefore + 2);
//         }
//         [Fact, Trait("Method", "Put")]
//         public async Task 追加APIに記事IDパスが不足していると405を返す()
//         {
//             // arrange
//             Bookmark putData = new Bookmark()
//             {
//                 UserId = "user1",
//                 Title = "title1",
//                 Id = "id1",
//                 Kind = "SPO",
//                 Properties = "{}",
//                 Note = "category1",
//                 DisplayDate = new DateTime(2022, 1, 1),
//                 ReposCreatedDate = new DateTime(2022, 3, 1),
//                 ReposUpdatedDate = new DateTime(2022, 4, 1),
//                 TimeStamp = new DateTime(2022, 5, 1),
//             };
//             var json = JsonSerializer.Serialize(putData);
//             using var test = CreateTest();
//             PrepareData(DataMode.None);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/bookmarks", json, "application/json"))

//                 // assert
//                 .AssertMethodNotAllowed();
//         }

//         [Fact, Trait("Method", "Put")]
//         public async Task 追加APIに自分と異なるUserIdを渡すとForbiddenを返す()
//         {
//             Bookmark putData = new Bookmark()
//             {
//                 UserId = "user1",
//                 Title = "title1",
//                 Id = "id1",
//                 Kind = "SPO",
//                 Properties = "{}",
//                 Note = "category1",
//                 DisplayDate = new DateTime(2022, 1, 1),
//                 ReposCreatedDate = new DateTime(2022, 3, 1),
//                 ReposUpdatedDate = new DateTime(2022, 4, 1),
//                 TimeStamp = new DateTime(2022, 5, 1),
//             };
//             var json = JsonSerializer.Serialize(putData);
//             using var test = CreateTest();
//             PrepareData(DataMode.None);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/otherUserId/bookmarks/abc", json, "application/json"))
//                 // assert
//                 .AssertForbidden();
//         }
//         #endregion

//         #region 削除APIのテスト
//         [Fact, Trait("Method", "Delete")]
//         public async Task Bearerを指定せず削除APIを呼び出し認証に失敗したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/bookmarks/abc"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Delete")]
//         public async Task 削除APIが成功すると204_NoContentを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.UserAndBookmark);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/bookmarks/id1"))
//                 // assert
//                 .AssertNoContent();

//             var deleted = await TableStorageClient.GetByKey<BookmarkEntity>(TableName, "testUserId", "spo_id1");
//             deleted.Should().BeNull();
//         }

//         [Fact, Trait("Method", "Delete")]
//         public async Task 削除対象のリソースが存在しない時も204_NoContentを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.UserAndBookmark);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/bookmarks/abc"))
//                 // assert
//                 .AssertNoContent();

//             LoggerMock.VerifyLogger(LogLevel.Trace, "Return:DeleteBookmarkAsync 対象となるリソースが存在しません", Times.Once);
//         }

//         [Fact, Trait("Method", "Delete")]
//         public async Task 削除APIに記事IDパスが不足していると405を返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.UserAndBookmark);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/bookmarks"))
//                 // assert
//                 .AssertMethodNotAllowed();
//         }

//         [Fact, Trait("Method", "Delete")]
//         public async Task 削除APIに自分と異なるUserIdを渡すとForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.UserAndBookmark);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/otherUserId/bookmarks/abc"))
//                 // assert
//                 .AssertForbidden();
//         }
//         #endregion

//         #region 取得APIのテスト
//         [Fact, Trait("Method", "Get")]
//         public async Task Bearerを指定せず取得APIを呼び出し認証に失敗したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/bookmarks"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Get")]
//         public async Task 取得APIにUserIdを渡すとお気に入り一覧が取得できる()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.UserAndBookmark);

//             var content = (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/bookmarks"))
//                 // assert
//                 .AssertOK()
//                 .GetContent();

//             var deserialized = JsonSerializer.Deserialize<ExpandoObject[]>(content!);

//             // null時は失敗させる
//             deserialized.Should().NotBeNull();

//             deserialized!.Length.Should().Be(2);
//             dynamic item0 = deserialized[0];
//             ((string)item0.userId.GetString()).Should().Be("testUserId");
//             ((string)item0.title.GetString()).Should().Be("title1");
//             ((string)item0.id.GetString()).Should().Be("id1");
//             ((string)item0.kind.GetString()).Should().Be("SPO");
//             ((string)item0.properties.GetString()).Should().Be("{\"listUrl\":\"abc\"}");
//             ((string)item0.note.GetString()).Should().Be("category1");
//             ((string)item0.displayDate.GetString()).Should().Be("2022-01-01T00:00:00Z");
//             ((string)item0.reposCreatedDate.GetString()).Should().Be("2022-03-01T00:00:00Z");
//             ((string)item0.reposUpdatedDate.GetString()).Should().Be("2022-04-01T00:00:00Z");
//             ((string)item0.timeStamp.GetString()).Should().Be("2022-05-01T00:00:00Z");

//             dynamic item1 = deserialized[1];
//             ((string)item1.userId.GetString()).Should().Be("testUserId");
//             ((string)item1.title.GetString()).Should().Be("title2");
//             ((string)item1.id.GetString()).Should().Be("id2");
//             ((string)item1.kind.GetString()).Should().Be("SPO");
//             ((string)item1.properties.GetString()).Should().Be("{\"listUrl\":\"def\"}");
//             ((string)item1.note.GetString()).Should().Be("category2");
//             ((string)item1.displayDate.GetString()).Should().Be("2022-01-02T00:00:00Z");
//             ((string)item1.reposCreatedDate.GetString()).Should().Be("2022-03-02T00:00:00Z");
//             ((string)item1.reposUpdatedDate.GetString()).Should().Be("2022-04-02T00:00:00Z");
//             ((string)item1.timeStamp.GetString()).Should().Be("2022-05-02T00:00:00Z");
//         }

//         [Fact, Trait("Method", "Get")]
//         public async Task 取得APIでデータ0件時は空配列が返却される()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.UserOnly);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/bookmarks"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson("[]");
//         }

//         [Fact, Trait("Method", "Get")]
//         public async Task データが一度も作成されていない時は204_NoContentが返却される()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.None);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/bookmarks"))
//                 // assert
//                 .AssertNoContent()
//                 .Assert("");
//         }

//         [Fact, Trait("Method", "Get")]
//         public async Task 取得APIに自分と異なるUserIdを渡すとForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.None);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/otherUserId/bookmarks"))
//                 // assert
//                 .AssertForbidden();
//         }
//         #endregion

//         #region Test Data Preparation
//         private enum DataMode
//         {
//             None,
//             UserOnly,
//             UserAndBookmark
//         }

//         private void PrepareData(DataMode dataMode)
//         {
//             this.TableStorageClient.CreateTable(TableName);
//             switch (dataMode)
//             {
//                 case DataMode.UserOnly:
//                 case DataMode.UserAndBookmark:
//                     TableStorageClient.AddRange(TableName, new[]
//                     {
//                         new BookmarkEntity
//                         {
//                             PartitionKey = "testUserId",
//                             RowKey = "user",
//                             UserId = "testUserId",
//                             Title = "",
//                             Id = "",
//                             Kind = "SPO",
//                             Properties = "",
//                             Note = "",
//                             DisplayDate = new DateTime(2022, 1, 1),
//                             ReposCreatedDate = new DateTime(2022, 3, 1),
//                             ReposUpdatedDate = new DateTime(2022, 4, 1),
//                             Timestamp = new DateTime(2022, 5, 1),
//                             ETag = new ETag("123"),
//                         },
//                     });
//                     break;
//             }
//             switch (dataMode)
//             {
//                 case DataMode.UserAndBookmark:
//                     TableStorageClient.AddRange(TableName, new[]
//                     {
//                         new BookmarkEntity
//                         {
//                             PartitionKey = "testUserId",
//                             RowKey = "spo_id1",
//                             UserId = "testUserId",
//                             Title = "title1",
//                             Id = "id1",
//                             Kind = "SPO",
//                             Properties = "{\"listUrl\":\"abc\"}",
//                             Note = "category1",
//                             DisplayDate = new DateTime(2022, 1, 1),
//                             ReposCreatedDate = new DateTime(2022, 3, 1),
//                             ReposUpdatedDate = new DateTime(2022, 4, 1),
//                             Timestamp = new DateTime(2022, 5, 1),
//                             ETag = new ETag("234"),
//                         },
//                         new BookmarkEntity
//                         {
//                             PartitionKey = "testUserId",
//                             RowKey = "spo_id2",
//                             UserId = "testUserId",
//                             Title = "title2",
//                             Id = "id2",
//                             Kind = "SPO",
//                             Properties = "{\"listUrl\":\"def\"}",
//                             Note = "category2",
//                             DisplayDate = new DateTime(2022, 1, 2),
//                             ReposCreatedDate = new DateTime(2022, 3, 2),
//                             ReposUpdatedDate = new DateTime(2022, 4, 2),
//                             Timestamp = new DateTime(2022, 5, 2),
//                             ETag = new ETag("345"),
//                         },
//                     });
//                     break;
//             }
//             TableStorageClient.AddRange(TableName, new[]
//             {
//                 new BookmarkEntity
//                 {
//                     PartitionKey = "anotherUserId",
//                     RowKey = "user",
//                     UserId = "anotherUserId",
//                     Title = "",
//                     Id = "",
//                     Kind = "SPO",
//                     Properties = "",
//                     Note = "",
//                     DisplayDate = new DateTime(2022, 1, 1),
//                     ReposCreatedDate = new DateTime(2022, 3, 1),
//                     ReposUpdatedDate = new DateTime(2022, 4, 1),
//                     Timestamp = new DateTime(2022, 5, 1),
//                     ETag = new ETag("456"),
//                 },
//                 new BookmarkEntity
//                 {
//                     PartitionKey = "anotherUserId",
//                     RowKey = "spo_id3",
//                     UserId = "anotherUserId",
//                     Title = "title3",
//                     Id = "id3",
//                     Kind = "SPO",
//                     Properties = "{\"listUrl\":\"ghi\"}",
//                     DisplayDate = new DateTime(2022, 1, 1),
//                     ReposCreatedDate = new DateTime(2022, 3, 1),
//                     ReposUpdatedDate = new DateTime(2022, 4, 1),
//                     Timestamp = new DateTime(2022, 5, 1),
//                     ETag = new ETag("567"),
//                 },
//             });
//         }

//         #endregion
//     }
// }
