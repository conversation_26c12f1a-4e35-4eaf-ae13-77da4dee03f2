// TODO: MockをMockでテストしているためリファイメントタスクとしてテスト方法を検討する必要あり

// using Avanade.Geranium.Attane.Api;
// using Avanade.Geranium.Attane.Api.Controllers;
// using Avanade.Geranium.Attane.Business;
// using Avanade.Geranium.Attane.Business.Configuration;
// using Avanade.Geranium.Attane.Business.Data;
// using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
// using Avanade.Geranium.Attane.Shared;
// using Avanade.Geranium.Attane.Test.Helpers.Api;
// using Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients;
// using CoreEx.Events;
// using CoreEx.Json;
// using FluentAssertions;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using Moq;
// using System;
// using System.Linq;
// using System.Net;
// using System.Net.Http;
// using System.Text;
// using System.Threading;
// using System.Threading.Tasks;
// using UnitTestEx;
// using UnitTestEx.Xunit.Internal;
// using Xunit;
// using Xunit.Abstractions;

// namespace Avanade.Geranium.Attane.Test.Api
// {

//     public class SearchRequestControllerTest : ApiUnitTestBase
//     {
//         public readonly Mock<ILogger<SearchRequestController>> LoggerMock = new();
//         public readonly Mock<IEventPublisher> EventPublisherMock = new();
//         internal readonly InMemoryTableStorageClient TableStorageClient = new InMemoryTableStorageClient();
//         internal readonly InMemoryQueueStorageClient QueueStorageClient = new InMemoryQueueStorageClient();

//         protected override void Initialize(ApiTester<Startup> test)
//         {
//             base.Initialize(test);
//             LoggerMock.Setup(o => o.IsEnabled(LogLevel.Trace)).Returns(true);

//             test.ConfigureServices(services => services
//                 .AddSingleton<ITableStorageClient, ITableStorageClient>(_ => TableStorageClient)
//                 .AddSingleton<IEventPublisher, IEventPublisher>(_ => EventPublisherMock.Object)
//                 .AddSingleton<ILogger<SearchRequestController>, ILogger<SearchRequestController>>(_ => LoggerMock.Object)
//             );
//         }

//         private const string TableName = "search";
//         private const string QueueName = "search-process";

//         public SearchRequestControllerTest(ITestOutputHelper output) : base(output)
//         {
//             TestSetUp.Environment = "test";
//         }

//         #region Get /users/oid/search/result
//         [Fact, Trait("Method", "Get /users/oid/search/result")]
//         public async Task Bearerを指定せずGetを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/ExistingUser/search/result"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/result")]
//         public async Task 他人のデータをGetしたときはForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/otherUserId/search/result"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/result")]
//         public async Task データが一度も作成されていないときはNoContentを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.NotRegistered);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/search/result"))
//                 // assert
//                 .AssertNoContent()
//                 .Assert<string>("");
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/result")]
//         public async Task 作成されているデータを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/search/result"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson("""
//                 {
//                     "conditionKeywords": {"operator":"And","operand":["aaa","bbb"]},
//                     "state": "InProgress",
//                     "results": [
//                         {
//                             "pid": "11111111-1111-1111-2222-000000000001",
//                             "dataSource": {"kind": "SPO", "properties": {"key1": "value1-1", "key2": "value1-2"}},
//                             "ids": ["result1-1", "result1-2"],
//                             "reqId": "11111111-1111-1111-1111-111111111111"
//                         },
//                         {
//                             "pid": "11111111-1111-1111-2222-000000000002",
//                             "dataSource": {"kind": "SPO", "properties": {"key1": "value2-1", "key2": "value2-2"}},
//                             "ids": ["result2-1", "result2-2"],
//                             "reqId": "11111111-1111-1111-1111-111111111111"
//                         },
//                         {
//                             "pid": "11111111-1111-1111-2222-000000000003",
//                             "dataSource": {"kind": "SPO", "properties": {"key1": "value3-1", "key2": "value3-2"}},
//                             "ids": ["result3-1", "result3-2"],
//                             "reqId": "11111111-1111-1111-1111-111111111111"
//                         },
//                         {
//                             "pid": "11111111-1111-1111-2222-000000000004",
//                             "dataSource": {"kind": "SPO", "properties": {"key1": "value4-1", "key2": "value4-2"}},
//                             "ids": ["result4-1", "result4-2"],
//                             "reqId": "11111111-1111-1111-1111-111111111111"
//                         }
//                     ],
//                     "reqId": "11111111-1111-1111-1111-111111111111",
//                     "condition": "aaa bbb",
//                     "userId": "testUserId"
//                 }
//                 """);
//         }

//         #endregion

//         #region Post /users/oid/search/result
//         [Fact, Trait("Method", "Post users/oid/search/result")]
//         public async Task Bearerを指定せずPost_resultを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""reqId"": ""11111111-1111-1111-1111-111111111111"", ""knownPids"": [""11111111-1111-1111-1111-111111111111"", ""11111111-1111-1111-1111-111111111111""] }";
//             var content = new StringContent(rawContent, Encoding.UTF8, "application/json");

//             using var test = CreateTest(useAuth: true);
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search/result", content))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Post users/oid/search/result")]
//         public async Task 他人のデータをPost_resultしたときはForbiddenを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""reqId"": ""11111111-1111-1111-1111-111111111111"", ""knownPids"": [""11111111-1111-1111-1111-111111111111"", ""11111111-1111-1111-1111-111111111111""] }";
//             var content = new StringContent(rawContent, Encoding.UTF8, "application/json");

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/otherUserId/search/result", content))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Post users/oid/search/result")]
//         public async Task データが一度も作成されておらずにPost_resultしたときはNoContentを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""reqId"": ""11111111-1111-1111-1111-111111111111"", ""knownPids"": [""11111111-1111-1111-1111-111111111111"", ""11111111-1111-1111-1111-111111111111""] }";
//             var content = new StringContent(rawContent, Encoding.UTF8, "application/json");

//             using var test = CreateTest();
//             PrepareData(DataMode.NotRegistered);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search/result", content))
//                 // assert
//                 .AssertNoContent()
//                 .Assert<string>("");
//         }

//         [Fact, Trait("Method", "Post users/oid/search/result")]
//         public async Task Post_resultしたときに作成されているデータを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""reqId"": ""11111111-1111-1111-1111-111111111111"", ""knownPids"": [""11111111-1111-1111-2222-000000000002"", ""11111111-1111-1111-2222-000000000004""] }";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search/result", rawContent, "application/json"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson("""
//                     {
//                         "conditionKeywords": {"operator":"And","operand":["aaa","bbb"]},
//                         "state": "InProgress",
//                         "results": [
//                             {
//                                 "pid": "11111111-1111-1111-2222-000000000001",
//                                 "dataSource": {"kind": "SPO", "properties": {"key1": "value1-1", "key2": "value1-2"}},
//                                 "ids": ["result1-1", "result1-2"],
//                                 "reqId": "11111111-1111-1111-1111-111111111111"
//                             },
//                             {
//                                 "pid": "11111111-1111-1111-2222-000000000003",
//                                 "dataSource": {"kind": "SPO", "properties": {"key1": "value3-1", "key2": "value3-2"}},
//                                 "ids": ["result3-1", "result3-2"],
//                                 "reqId": "11111111-1111-1111-1111-111111111111"
//                             }
//                         ],
//                         "reqId": "11111111-1111-1111-1111-111111111111",
//                         "condition": "aaa bbb",
//                         "userId": "testUserId"
//                     }
//                 """);
//         }

//         #endregion

//         #region Post /users/oid/search
//         [Fact, Trait("Method", "Post /users/oid/search")]
//         public async Task Bearerを指定せずPostを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             const string rawContent = "";

//             using var test = CreateTest(useAuth: true);
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search", rawContent, "application/json"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Post /users/oid/search")]
//         public async Task 他人のデータをPostしたときはForbiddenを返す()
//         {
//             // arrange
//             const string rawContent = "";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/otherUserId/search", rawContent, "application/json"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Post /users/oid/search")]
//         public async Task 要求とデータのuserIdが一致していないデータをPostしたときも要求のuserIdが使われる()
//         {
//             // arrange
//             const string rawContent = "{" +
//                 @"""userId"": ""notExistingUserId"", " +
//                 @"""condition"": ""xxx yyy""" +
//                 "}";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search", rawContent, "application/json"))
//                 // assert
//                 .AssertAccepted();

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Condition.Should().Be("xxx yyy");

//             (await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "notExistingUserId", "search")).Should().BeNull();
//         }

//         [Fact, Trait("Method", "Post /users/oid/search")]
//         public async Task 検索条件が構文エラーとなったときは400を返す()
//         {
//             // arrange
//             var rawContent = @"{" +
//                 @"""condition"":""xxx yyy OR""" +
//                 @"}";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search", rawContent, "application/json"))
//                 // assert
//                 .AssertBadRequest()
//                 .AssertJson("{\"value.condition\":[\"ORの右辺がありません\"]}");
//         }

//         [Fact, Trait("Method", "Post /users/oid/search")]
//         public async Task 要求を受け取って情報を登録する()
//         {
//             // arrange
//             const string rawContent = """{"condition":"xxx yyy"}""";
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);
//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search", rawContent, "application/json"))
//                 // assert
//                 .AssertAccepted();
//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");
//             storedSearch.Should().NotBeNull();
//             storedSearch!.Condition.Should().Be("xxx yyy");
//             storedSearch.Should().NotBeNull();
//             storedSearch!.ReqId.Should().NotBe(new Guid("11111111-1111-1111-1111-111111111111"));
//             storedSearch.Condition.Should().Be("xxx yyy");
//             storedSearch.State.Should().Be("InProgress");
//             storedSearch.ConditionKeywords.Should().Be(@"{""operator"":""And"",""operand"":[""xxx"",""yyy""]}");
//             // 非同期処理の書き方を変更した
//             var storedSearchResults = (await Task.Run(() => TableStorageClient.Get<SearchTableEntity>(TableName)))
//                 .Where(t => t.PartitionKey == "testUserId")
//                 .RowKeyStartsWith("result")
//                 .Count();
//             // var storedSearchResults = (await TableStorageClient.Get<SearchTableEntity>(TableName))
//             //     .Where(t => t.PartitionKey == "testUserId")
//             //     .RowKeyStartsWith("result")
//             //     .Count();
//             storedSearchResults.Should().Be(0);
//             // プログラムではPublishValueを呼んでいるが、拡張メソッド内でPublishの呼出となる
//             // 全体では回数だけ確認する
//             EventPublisherMock.Verify(
//                 p => p.Publish(It.IsAny<EventData<SearchQueueEntry>>()),
//                 Times.Exactly(3)
//             );
//             EventPublisherMock.Verify(p => p.SendAsync(CancellationToken.None), Times.Once);
//             // 2個だけ具体的な内容まで確認する
//             EventPublisherMock.Verify(p => p.Publish(
//                 It.Is<EventData<SearchQueueEntry>>(d =>
//                     d.Subject == QueueName &&
//                     d.Value.UserId == "testUserId" &&
//                     d.Value.ReqId == storedSearch.ReqId &&
//                     d.Value.Kind == DataSourceKind.SPO &&
//                     d.Value.Properties != null &&
//                     d.Value.Properties.Length == 1 &&
//                     d.Value.Properties[0]["site"] == "https://ydxxq.sharepoint.com/sites/mitene-dev/" &&
//                     d.Value.Properties[0]["list"] == "710ddfb8-142c-4dd9-b9b0-54aa24e9da7b" &&
//                     d.Value.Properties[0]["listUrl"] == "https://ydxxq.sharepoint.com/sites/mitene-dev/Lists/List/DispForm.aspx" &&
//                     d.Value.Properties[0]["listName"] == "会社からのお知らせ" &&
//                     d.Value.Properties[0]["category"] == "category1" &&
//                     d.Value.Properties[0]["filterByPresentPeriod"] == "true"
//                 )
//             ));
//             EventPublisherMock.Verify(p => p.Publish(
//                 It.Is<EventData<SearchQueueEntry>>(d =>
//                     d.Subject == QueueName &&
//                     d.Value.UserId == "testUserId" &&
//                     d.Value.ReqId == storedSearch.ReqId &&
//                     d.Value.Kind == DataSourceKind.SPO &&
//                     d.Value.Properties != null &&
//                     d.Value.Properties.Length == 1 &&
//                     d.Value.Properties[0]["site"] == "https://ydxxq.sharepoint.com/sites/mitene-dev/" &&
//                     d.Value.Properties[0]["list"] == "d2a49bb4-161e-48ce-8a69-2c071ef4da7b" &&
//                     d.Value.Properties[0]["listUrl"] == "https://ydxxq.sharepoint.com/sites/mitene-dev/Lists/List1/DispForm.aspx" &&
//                     d.Value.Properties[0]["listName"] == "テスト" &&
//                     d.Value.Properties[0]["category"] == "category1" &&
//                     d.Value.Properties[0]["filterByPresentPeriod"] == "true"
//                 )
//             ));
//         }
//         #endregion

//         #region Post /users/oid/search/cancellation
//         [Fact, Trait("Method", "Post /users/oid/search/cancellation")]
//         public async Task Bearerを指定せずcancellationを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search/cancellation"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Post /users/oid/search/cancellation")]
//         public async Task 他人の検索要求をキャンセルしようとしたときはForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/otherUserId/search/cancellation"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Post /users/oid/search/cancellation")]
//         public async Task 検索要求が一度も作成されていないときにキャンセルしようとされたときはNotFoundを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             TableStorageClient.CreateTable(TableName);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search/cancellation"))
//                 // assert
//                 .AssertNotFound();
//         }

//         [Fact, Trait("Method", "Post /users/oid/search/cancellation")]
//         public async Task すでにキャンセルされている要求を再度キャンセルしてもエラーが起きない()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.Cancelled);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search/cancellation"))
//                 // assert
//                 .AssertOK();

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.State.Should().Be("Cancelled");
//         }

//         [Fact, Trait("Method", "Post /users/oid/search/cancellation")]
//         public async Task 要求を受け取って検索要求をキャンセルする()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Post, "users/testUserId/search/cancellation"))
//                 // assert
//                 .AssertOK();

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.State.Should().Be("Cancelled");
//         }
//         #endregion

//         #region Get /users/oid/search/context
//         [Fact, Trait("Method", "Get /users/oid/search/context")]
//         public async Task Bearerを指定せずGetContextを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/ExistingUser/search/context"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/context")]
//         public async Task 他人のデータをGetContextしたときはForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/otherUserId/search/context"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/context")]
//         public async Task データが一度も作成されていないときにGetContextしたときはNotFoundを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.NotRegistered);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/search/context"))
//                 // assert
//                 .AssertNotFound()
//                 .Assert("");
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/context")]
//         public async Task 作成されているコンテキストを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/search/context"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson("""{"key1": "context1", "key2": "context2"}""");
//         }
//         #endregion

//         #region Put /users/oid/search/context
//         [Fact, Trait("Method", "Put /users/oid/search/context")]
//         public async Task Bearerを指定せずPutContextを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""key1"": ""newContext1"", ""key2"": ""newContext2"" }";

//             using var test = CreateTest(useAuth: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/ExistingUser/search/context", rawContent, "application/json"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context")]
//         public async Task 他人のデータをPutContextしたときはForbiddenを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""key1"": ""newContext1"", ""key2"": ""newContext2"" }";

//             using var test = CreateTest();

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/otherUserId/search/context", rawContent, "application/json"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context")]
//         public async Task データが一度も作成されておらずにPutContextしたときはNotFoundを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""key1"": ""newContext1"", ""key2"": ""newContext2"" }";

//             using var test = CreateTest();
//             PrepareData(DataMode.NotRegistered);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/search/context", rawContent, "application/json"))
//                 // assert
//                 .AssertNotFound();
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context")]
//         public async Task PutContextしたとき作成されているデータを返す()
//         {
//             // arrange
//             const string rawContent = @"{ ""key1"": ""newContext1"", ""key2"": ""newContext2"" }";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/search/context", rawContent, "application/json"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson("""{"key1": "newContext1", "key2": "newContext2"}""");

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be(@"{""key1"":""newContext1"",""key2"":""newContext2""}");
//         }
//         #endregion

//         #region Delete /users/oid/search/context
//         [Fact, Trait("Method", "Delete /users/oid/search/context")]
//         public async Task Bearerを指定せずDeleteContextを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/ExistingUser/search/context"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context")]
//         public async Task 他人のデータをDeleteContextしたときはForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/otherUserId/search/context"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context")]
//         public async Task データが一度も作成されておらずにDeleteContextしたときは何もしない()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.NotRegistered);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/search/context"))
//                 // assert
//                 .AssertNoContent();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context")]
//         public async Task データがあるがコンテキストがないときにDeleteContextしたときは何もしない()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/search/context"))
//                 // assert
//                 .AssertNoContent();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context")]
//         public async Task コンテキストがあるときにDeleteContextしたときは削除する()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/search/context"))
//                 // assert
//                 .AssertNoContent();

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().BeNull();
//         }
//         #endregion

//         #region Get /users/oid/search/context/field
//         [Fact, Trait("Method", "Get /users/oid/search/context/field")]
//         public async Task Bearerを指定せずGetContextFieldを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/ExistingUser/search/context/some"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/context/field")]
//         public async Task 他人のデータをGetContextFieldしたときはForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/otherUserId/search/context/some"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/context/field")]
//         public async Task 既存のキーと一致しないキーを指定してGetContextFieldしたときはNotFoundを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/search/context/some"))
//                 // assert
//                 .AssertNotFound()
//                 .Assert("");
//         }

//         [Fact, Trait("Method", "Get /users/oid/search/context/field")]
//         public async Task 作成されているコンテキストのフィールド値を返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Get, "users/testUserId/search/context/key1"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson(@"""context1""");
//         }
//         #endregion

//         #region Put /users/oid/search/context/field
//         [Fact, Trait("Method", "Put /users/oid/search/context/field")]
//         public async Task Bearerを指定せずPutContextFieldを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             const string rawContent = @"""context1""";

//             using var test = CreateTest(useAuth: true);
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/ExistingUser/search/context/some", rawContent, "application/json"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context/field")]
//         public async Task 他人のデータをPutContextFieldしたときはForbiddenを返す()
//         {
//             // arrange
//             const string rawContent = @"""context1""";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/otherUserId/search/context/some", rawContent, "application/json"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context/field")]
//         public async Task データが一度も作成されておらずにPutContextFieldしたときはNotFoundを返す()
//         {
//             // arrange
//             const string rawContent = @"""context1""";

//             using var test = CreateTest();
//             PrepareData(DataMode.NotRegistered);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/search/context/some", rawContent, "application/json"))
//                 // assert
//                 .AssertNotFound();
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context/field")]
//         public async Task PutContextFieldしたとき既存のコンテキストがなければ新規のコンテキストを作成する()
//         {
//             // arrange
//             const string rawContent = @"""context1""";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/search/context/key1", rawContent, "application/json"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson(@"""context1""");

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be("""{"key1":"context1"}""");
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context/field")]
//         public async Task PutContextFieldしたとき既存のコンテキストとキーが一致すれば値を更新する()
//         {
//             // arrange
//             const string rawContent = @"""context3""";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/search/context/key1", rawContent, "application/json"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson(@"""context3""");

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be("""{"key1":"context3","key2":"context2"}""");
//         }

//         [Fact, Trait("Method", "Put /users/oid/search/context/field")]
//         public async Task PutContextFieldしたとき既存のコンテキストとキーが一致しなければ新規のキーを作成する()
//         {
//             // arrange
//             const string rawContent = @"""context3""";

//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Put, "users/testUserId/search/context/key3", rawContent, "application/json"))
//                 // assert
//                 .AssertOK()
//                 .AssertJson(@"""context3""");

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be("""{"key1":"context1","key2":"context2","key3":"context3"}""");
//         }
//         #endregion

//         #region Delete /users/oid/search/context/field
//         [Fact, Trait("Method", "Delete /users/oid/search/context/field")]
//         public async Task Bearerを指定せずDeleteContextFieldを呼び出したらUnauthorizedを返す()
//         {
//             // arrange
//             using var test = CreateTest(useAuth: true);
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/ExistingUser/search/context/some"))
//                 // assert
//                 .AssertUnauthorized();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context/field")]
//         public async Task 他人のデータをDeleteContextFieldしたときはForbiddenを返す()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/otherUserId/search/context/some"))
//                 // assert
//                 .AssertForbidden();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context/field")]
//         public async Task データが一度も作成されておらずにDeleteContextFieldしたときは何もしない()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.NotRegistered);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/search/context/some"))
//                 // assert
//                 .AssertNoContent();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context/field")]
//         public async Task データがあるがコンテキストがないときにDeleteContextFieldしたときは何もしない()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/search/context/some"))
//                 // assert
//                 .AssertNoContent();
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context/field")]
//         public async Task コンテキストがあるときに合致しないキーをDeleteContextFieldしたときは何もしない()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/search/context/some"))
//                 // assert
//                 .AssertNoContent();

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be("""{"key1":"context1","key2":"context2"}""");
//         }

//         [Fact, Trait("Method", "Delete /users/oid/search/context/field")]
//         public async Task コンテキストがあるときにDeleteContextFieldしたときは削除する()
//         {
//             // arrange
//             using var test = CreateTest();
//             PrepareData(DataMode.InProgress, useContext: true);

//             (await test.Http()
//                 // act
//                 .RunAsync(HttpMethod.Delete, "users/testUserId/search/context/key1"))
//                 // assert
//                 .AssertNoContent();

//             var storedSearch = await TableStorageClient.GetByKey<SearchTableEntity>(TableName, "testUserId", "request");

//             storedSearch.Should().NotBeNull();
//             storedSearch.Should().NotBeNull();
//             storedSearch!.Context.Should().Be("""{"key2":"context2"}""");
//         }
//         #endregion

//         #region Test Data Preparation

//         private enum DataMode
//         {
//             NotRegistered,
//             InProgress,
//             Completed,
//             Error,
//             Cancelled
//         }

//         private void PrepareData(DataMode dataMode, bool useContext = false)
//         {
//             TableStorageClient.CreateTable(TableName);

//             AddTestData(
//                 "otherUserId",
//                 new Guid("*************-1111-1111-111111111111"),
//                 SearchState.InProgress,
//                 3,
//                 3
//             );

//             if (dataMode == DataMode.NotRegistered)
//             {
//                 return;
//             }
//             AddTestData(
//                 "testUserId",
//                 new Guid("11111111-1111-1111-1111-111111111111"),
//                 dataMode switch
//                 {
//                     DataMode.Error => SearchState.Error,
//                     _ => SearchState.InProgress
//                 },
//                 5,
//                 dataMode switch
//                 {
//                     DataMode.InProgress => 4,
//                     _ => 5
//                 },
//                 useContext
//             );
//         }

//         private void AddTestData(string userId, Guid reqId, SearchState state, int processCount, int resultCount, bool useContext = false)
//         {
//             TableStorageClient.CreateTable(TableName);
//             QueueStorageClient.CreateQueue(QueueName);
//             TableStorageClient.AddRange(TableName, new[]
//             {
//                 new SearchTableEntity
//                 {
//                     PartitionKey = userId,
//                     RowKey = "request",
//                     UserId = userId,
//                     ReqId = reqId,
//                     Condition = "aaa bbb",
//                     ConditionKeywords = JsonSerializer.Default.Serialize(SearchConditionParser.ParseExpression(new[] {"aaa", "bbb"})),
//                     ProcessCount = processCount,
//                     State = state.ToString(),
//                     Tokens = """{"key1": "token1", "key2": "token2"}""",
//                     Context = useContext ? """{"key1":"context1","key2":"context2"}""" : null,
//                 }
//             });

//             if (resultCount == 0)
//             {
//                 return;
//             }

//             TableStorageClient.AddRange(TableName, Enumerable.Range(1, resultCount).Select(i =>
//                 new SearchTableEntity
//                 {
//                     PartitionKey = userId,
//                     RowKey = $"result-{{{reqId}}}-{{11111111-1111-1111-2222-{i:000000000000}}}",
//                     ReqId = reqId,
//                     Pid = $"11111111-1111-1111-2222-{i:000000000000}",
//                     Kind = "SPO",
//                     Properties = $$"""{"key1":"value{{i}}-1","key2":"value{{i}}-2"}""",
//                     Ids = $"""["result{i}-1","result{i}-2"]""",
//                 }));
//         }
//         #endregion
//     }
// }
