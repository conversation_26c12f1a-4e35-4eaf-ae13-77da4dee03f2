<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>11.0</LangVersion>
    <IsPackable>false</IsPackable>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;CA1707</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="Schema\**\*" />
    <EmbeddedResource Include="Migrations\**\*" />
    <EmbeddedResource Include="Data\**\*" />
    <EmbeddedResource Include="Cosmos\**\*" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" Version="8.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.14" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="ReportGenerator" Version="5.4.5" />
    <PackageReference Include="UnitTestEx.Xunit" Version="2.2.3" />
    <PackageReference Include="xunit.extensibility.core" Version="2.9.3" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Avanade.Geranium.Attane.Api\Avanade.Geranium.Attane.Api.csproj" />
    <ProjectReference Include="..\Avanade.Geranium.Attane.Business\Avanade.Geranium.Attane.Business.csproj" />
    <ProjectReference Include="..\Avanade.Geranium.Attane.Common\Avanade.Geranium.Attane.Common.csproj" />
    <ProjectReference Include="..\Avanade.Geranium.Attane.Infrastructure\Avanade.Geranium.Attane.Infrastructure.csproj" />
    <ProjectReference Include="..\Avanade.Geranium.Attane.Shared\Avanade.Geranium.Attane.Shared.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <None Remove="Shared\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Shared\" />
  </ItemGroup>
</Project>
