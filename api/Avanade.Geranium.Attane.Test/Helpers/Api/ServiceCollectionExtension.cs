using Microsoft.Extensions.DependencyInjection;
using Moq;
using System.Linq;

namespace Avanade.Geranium.Attane.Test.Helpers.Api
{
    public static class ServiceCollectionExtension
    {
        public static IServiceCollection SetSingletonMock<TService>(this IServiceCollection services, Mock<TService> mock)
            where TService : class
        {
            services.RemoveServiceIfExists<TService>();
            services.AddSingleton<TService, TService>(provider => mock.Object);
            return services;
        }

        /// <summary>
        /// サービスが登録されていたら削除します。
        /// </summary>
        /// <typeparam name="TService">サービスの型</typeparam>
        /// <param name="services">対象のサービスコレクション</param>
        private static void RemoveServiceIfExists<TService>(this IServiceCollection services)
            where TService : class
        {
            foreach (var descriptor in services.Where(x => x.ServiceType == typeof(TService)).ToArray())
            {
                services.Remove(descriptor);
            }
        }
    }
}
