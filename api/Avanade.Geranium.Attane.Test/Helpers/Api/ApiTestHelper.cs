using System.Net;
using UnitTestEx.Assertors;
using Avanade.Geranium.Attane.Api;
using Avanade.Geranium.Attane.Api.Caller;
using Avanade.Geranium.Attane.Api.Token;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using System;
using System.Linq.Expressions;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using CoreEx.Json;

namespace Avanade.Geranium.Attane.Test.Helpers.Api
{
    public static class ApiTestHelper
    {
        #region Extension Methods
        /// <summary>
        /// Asserts that the <see cref="HttpResponseMessageAssertorBase.Response"/> is a <see cref="HttpStatusCode.MethodNotAllowed"/>.
        /// </summary>
        /// <returns>The <see cref="HttpResponseMessageAssertorBase{TSelf}"/> instance to support fluent-style method-chaining.</returns>
        public static TSelf AssertMethodNotAllowed<TSelf>(
            this HttpResponseMessageAssertorBase<TSelf> assertor) where TSelf : HttpResponseMessageAssertorBase<TSelf> =>
            assertor.Assert(HttpStatusCode.MethodNotAllowed);
        #endregion

        /// <summary>
        /// 起動時のファクトリにDI構成を追加します。
        /// </summary>
        /// <param name="factory">起動時のファクトリ</param>
        /// <param name="configuration">追加する構成</param>
        public static void AddConfiguration(CustomWebApplicationFactory<Startup> factory, Func<IServiceCollection, IServiceCollection> configuration)
        {
            if (factory.customConfiguration == null)
            {
                factory.customConfiguration = configuration;
            }
            else
            {
                var currentConfiguration = factory.customConfiguration;
                factory.customConfiguration = services => configuration(currentConfiguration(services));
            }
        }

        /// <summary>
        /// テストを初期化します。
        /// </summary>
        /// <returns>テスト用クライアント</returns>
        public static HttpClient InitializeAuthTest(CustomWebApplicationFactory<Startup> factory) =>
            factory.CreateClient(new WebApplicationFactoryClientOptions
            {
                AllowAutoRedirect = false,
            });

        /// <summary>
        /// テストを初期化します。
        /// </summary>
        /// <returns>テスト用クライアント, ICallerProviderのモック, モックに対して呼び出される操作の組</returns>
        public static (
            HttpClient client,
            Mock<ICallerProvider> callerProviderMock,
            Expression<Func<ICallerProvider, string>> callerExpression
        ) InitializeTest(CustomWebApplicationFactory<Startup> factory)
        {
            var (callerProviderMock, callerExpression) = MockCallerProvider();
            var tokenIssuerMock = MockTokenIssuer();

            AddConfiguration(factory, services => services.SetSingletonMock(callerProviderMock));
            AddConfiguration(factory, services => services.SetSingletonMock(tokenIssuerMock));
            AddConfiguration(factory, services => services.AddSingleton<IPolicyEvaluator, CustomPolicyEvaluator>());

            var client = factory.CreateClient(new WebApplicationFactoryClientOptions
            {
                AllowAutoRedirect = false,
            });

            return (client, callerProviderMock, callerExpression);
        }

        private static (Mock<ICallerProvider> callerProviderMock, Expression<Func<ICallerProvider, string>> callerExpression) MockCallerProvider(string userName = "testUser", string userID = "testUserId", string token = "bearerToken")
        {
            var callerProviderMock = new Mock<ICallerProvider>();
            Expression<Func<ICallerProvider, string>> callerExpression = p => p.GetCaller(It.IsAny<ControllerBase>());
            callerProviderMock.Setup(callerExpression).Returns(userName);

            Expression<Func<ICallerProvider, string>> callerIdExpression = p => p.GetCallerId(It.IsAny<ControllerBase>());
            callerProviderMock.Setup(callerIdExpression).Returns(userID);

            Expression<Func<ICallerProvider, string>> tokenExpression = p => p.GetRequestToken(It.IsAny<HttpContext>());
            callerProviderMock.Setup(tokenExpression).Returns(token);

            return (callerProviderMock, callerExpression);
        }

        private static Mock<ITokenIssuer> MockTokenIssuer()
        {
            var tokenIssuerMock = new Mock<ITokenIssuer>();
            Expression<Func<ITokenIssuer, Task<(string?, string?)>>> issueSpoTokenExpression = p => p.IssueTokenAsync("bearerToken", "Spo");
            tokenIssuerMock.Setup(issueSpoTokenExpression).Returns(Task.FromResult<(string?, string?)>(("spoToken", "spoRefreshToken")));

            return tokenIssuerMock;
        }

        public static StringContent CreateJsonContent<T>(T data, string mediaType = "application/json")
        {
            var json = JsonSerializer.Default.Serialize(data);
            var requestContent = new StringContent(json, Encoding.UTF8, mediaType);
            return requestContent;
        }

        public static StringContent CreatePatch(string json, string mediaType = "application/merge-patch+json")
        {
            var requestContent = new StringContent(json, Encoding.UTF8, mediaType);

            // null時はエラー
            if (requestContent?.Headers?.ContentType == null)
            {
                throw new Exception("requestContent null exception");
            }

            // HACK: 現在のBeefはContent-TypeのCharsetを指定できない
            requestContent.Headers.ContentType.CharSet = null;
            return requestContent;
        }
    }
}
