using Avanade.Geranium.Attane.Api;
using Avanade.Geranium.Attane.Api.Caller;
using Avanade.Geranium.Attane.Api.Token;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using System;
using System.Linq.Expressions;
using System.Threading.Tasks;
using UnitTestEx;
using UnitTestEx.Xunit;
using UnitTestEx.Xunit.Internal;
using Xunit.Abstractions;

namespace Avanade.Geranium.Attane.Test.Helpers.Api
{
    /// <summary>
    /// テストクラスの基底型
    /// </summary>
    public class ApiUnitTestBase : UnitTestBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UnitTestBase"/> class.
        /// </summary>
        /// <param name="output">The <see cref="ITestOutputHelper"/>.</param>
        public ApiUnitTestBase(ITestOutputHelper output) : base(output) { }

        public bool UseAuth { get; private set; }
        public string AuthUserName { get; private set; } = "";
        public string AuthUserId { get; private set; } = "";
        public string AuthUserToken { get; private set; } = "";

        public Mock<ICallerProvider> CallerProviderMock { get; } = new();
        public Mock<ITokenIssuer> TokenIssuerMock { get; } = new();

        public Expression<Func<ICallerProvider, string>>? CallerExpression { get; private set; } = null;
        public Expression<Func<ICallerProvider, string>>? CallerIdExpression { get; private set; } = null;
        public Expression<Func<ICallerProvider, string>>? TokenExpression { get; private set; } = null;

        /// <summary>
        /// 初期化を行うポイント
        /// </summary>
        /// <param name="test">テスト</param>
        protected virtual void Initialize(ApiTester<Startup> test)
        {
            TestSetUp.Environment = "test";

            if (!UseAuth)
            {
                // 認証周りのモックを行う
                CallerExpression = p => p.GetCaller(It.IsAny<ControllerBase>());
                CallerProviderMock.Setup(CallerExpression).Returns(AuthUserName);

                CallerIdExpression = p => p.GetCallerId(It.IsAny<ControllerBase>());
                CallerProviderMock.Setup(CallerIdExpression).Returns(AuthUserId);

                TokenExpression = p => p.GetRequestToken(It.IsAny<HttpContext>());
                CallerProviderMock.Setup(TokenExpression).Returns(AuthUserToken);

                TokenIssuerMock.Setup(
                    p => p.IssueTokenAsync("bearerToken", "Spo")
                ).Returns(
                    Task.FromResult<(string?, string?)>(("spoToken", "spoRefreshToken"))
                );

                test.ConfigureServices(services =>
                {
                    services.AddSingleton<ICallerProvider, ICallerProvider>(_ => CallerProviderMock.Object);
                    services.AddSingleton<ITokenIssuer, ITokenIssuer>(_ => TokenIssuerMock.Object);
                    services.AddSingleton<IPolicyEvaluator, CustomPolicyEvaluator>();
                });
            }
        }

        /// <summary>
        /// テストを作成します。
        /// </summary>
        /// <param name="useAuth">認可を行うか。既定値は <see langword="false"/>。</param>
        /// <param name="authUserName">認可をスキップする場合に返されるユーザー名。既定値は "testUser"。</param>
        /// <param name="authUserId">認可をスキップする場合に返されるユーザーID。既定値は "testUserId"。</param>
        /// <param name="authUserToken">認可をスキップする場合に返されるトークン。既定値は "bearerToken"。</param>
        /// <returns>作成したテスト</returns>
        public ApiTester<Startup> CreateTest(
            bool useAuth = false,
            string authUserName = "testUser",
            string authUserId = "testUserId",
            string authUserToken = "bearerToken"
        )
        {
            UseAuth = useAuth;
            AuthUserName = authUserName;
            AuthUserId = authUserId;
            AuthUserToken = authUserToken;

            var test = CreateApiTester<Startup>();
            Initialize(test);

            return test;
        }
    }

}
