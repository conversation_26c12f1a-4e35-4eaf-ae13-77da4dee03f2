﻿// TODO: MockをMockでテストしているためリファイメントタスクとしてテスト方法を検討する必要あり

// using FluentAssertions;
// using Azure.Data.Tables;
// using System.Linq;
// using System.Threading.Tasks;
// using Xunit;
// using Azure;
// using System;

// namespace Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients
// {
//     public class InMemoryTableStorageClientTest
//     {
//         private const string TableName = "table";

//         [Fact, Trait("Method", "ExecuteBatchAsync")]
//         public async Task ExecuteBatchAsyncで複数の操作を行える()
//         {
//             var client = new InMemoryTableStorageClient();
//             InitilalizeData(client);

//             var operations = new[]
//             {
//                 new Tuple<TableTransactionActionType, ITableEntity>(TableTransactionActionType.Add, new TestEntity { PartitionKey = "a", RowKey = "3000", Prop = "abc" }),
//                 new Tuple<TableTransactionActionType, ITableEntity>(TableTransactionActionType.UpsertReplace, new TestEntity { PartitionKey = "a", RowKey = "1000", Prop = "def", ETag = new ETag("E1000") }),
//                 new Tuple<TableTransactionActionType, ITableEntity>(TableTransactionActionType.Delete, new TestEntity { PartitionKey = "a", RowKey = "1100", ETag = new ETag("E0900") })
//             };

//             var result = await client.ExecuteBatchAsync<TestEntity>(TableName, operations);

//             result.Value.Count.Should().Be(3);

//             var inserted = await client.GetByKey<TestEntity>(TableName, "a", "3000");
//             inserted?.Prop.Should().Be("abc");

//             var updated = await client.GetByKey<TestEntity>(TableName, "a", "1000");
//             updated?.Prop.Should().Be("def");

//             var deleted = await client.GetByKey<TestEntity>(TableName, "a", "1100");
//             deleted.Should().BeNull();
//         }

//         [Fact, Trait("Method", "ExecuteBatchAsync(IEnumerable)")]
//         public async Task ExecuteBatchAsync_IEnumerableで複数の操作を行える()
//         {
//             var client = new InMemoryTableStorageClient();
//             InitilalizeData(client);

//             var result = await client.ExecuteBatchAsync<TestEntity>(TableName, new[]
//             {
//                 new Tuple<TableTransactionActionType, ITableEntity>(TableTransactionActionType.Add, new TestEntity { PartitionKey = "a", RowKey = "3000", Prop = "abc" }),
//                 new Tuple<TableTransactionActionType, ITableEntity>(TableTransactionActionType.UpsertReplace, new TestEntity { PartitionKey = "a", RowKey = "1000", Prop = "def", ETag = new ETag("E1000") }),
//                 new Tuple<TableTransactionActionType, ITableEntity>(TableTransactionActionType.Delete, new TestEntity { PartitionKey = "a", RowKey = "1100", ETag = new ETag("E0900") })
//             });

//             result.Value.Count.Should().Be(3);

//             var updated = await client.GetByKey<TestEntity>(TableName, "a", "1000");
//             updated?.Prop.Should().Be("def");

//             var deleted = await client.GetByKey<TestEntity>(TableName, "a", "1100");
//             deleted.Should().BeNull();
//         }

//         [Fact, Trait("Method", "Get")]
//         public async Task GetでIQueryableが取得できソートされて最大件数でフィルタされる()
//         {
//             var client = new InMemoryTableStorageClient();
//             InitilalizeData(client);

//             var result = (await client.Get<TestEntity>(TableName)).Where(e => e.RowKey.CompareTo("0100") > 0).ToArray();

//             result.Length.Should().Be(1000);
//             result[0].RowKey.Should().Be("0101");
//         }

//         [Fact, Trait("Method", "GetByKey")]
//         public async Task GetByKeyでキー指定で取得できる()
//         {
//             var client = new InMemoryTableStorageClient();
//             InitilalizeData(client);

//             (await client.GetByKey<TestEntity>(TableName, "a", "3000")).Should().BeNull();

//             (await client.GetByKey<TestEntity>(TableName, "a", "0500"))?.Prop.Should().Be("Prop1500");
//         }

//         private static void InitilalizeData(InMemoryTableStorageClient client)
//         {
//             client.CreateTable(TableName);
//             client.AddRange<TestEntity>(TableName, Enumerable.Range(1, 2000).Select(i => new TestEntity
//             {
//                 PartitionKey = "a",
//                 RowKey = $"{2000 - i:0000}",
//                 Prop = $"Prop{i:0000}",
//                 ETag = new ETag("E{i:0000}"),
//             }));
//         }
//         public class TestEntity : ITableEntity
//         {
//             public string? PartitionKey { get; set; }
//             public string? RowKey { get; set; }
//             public DateTimeOffset? Timestamp { get; set; }
//             public ETag ETag { get; set; }
//             public string? Prop { get; set; }
//         }
//     }
// }
