#define UseETagForDelete

using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
using Azure;
using Azure.Core;
using Azure.Data.Tables;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients
{
    /// <summary>
    /// In-memory mock for ICloudTableStorage (extracted from Azure Table Storage). Enables fast unit testing without needing to run a CloudStorageAccount client.
    /// You can inject this mock into ReliableCloudTableRepository etc.
    /// NOTE: This isn't intended to test Table Storage serialization,
    ///       so be aware that your object might be stored successfully here but still fail to serialize in Azure.
    /// Further improvements: error handling eg if PartitionKey or RowKey are not set on an Entity.
    /// Originally from https://gist.github.com/timiles/4078750#file-inmemorytablestorage-cs
    /// </summary>
    public partial class InMemoryTableStorageClient : ITableStorageClient
    {
        private readonly TableServiceClient? _serviceClient;

        public InMemoryTableStorageClient(TableServiceClient serviceClient)
        {
            _serviceClient = serviceClient;
        }

        public InMemoryTableStorageClient()
        {
        }

        private readonly Impl impl = new Impl();

        public Task<Response<IReadOnlyList<Response>>> ExecuteBatchAsync<TEntity>(string tableName, TableTransactionActionType operation, ITableEntity entity)
            where TEntity : class, ITableEntity
        {
            var tableClient = _serviceClient?.GetTableClient(tableName);
            List<TableTransactionAction> batch = new List<TableTransactionAction>
            {
                new TableTransactionAction(operation, entity)
            };
            if (tableClient == null)
            {
                throw new InvalidOperationException("TableClient is null.");
            }
            return tableClient.SubmitTransactionAsync(batch);
        }

        public Task<Response<IReadOnlyList<Response>>> ExecuteBatchAsync<TEntity>(string tableName, IEnumerable<Tuple<TableTransactionActionType, ITableEntity>> operations)
            where TEntity : class, ITableEntity
        {
            var tableClient = _serviceClient?.GetTableClient(tableName);
            List<TableTransactionAction> batch = new List<TableTransactionAction>();
            foreach (var operation in operations)
            {
                batch.Add(new TableTransactionAction(operation.Item1, operation.Item2));
            };
            if (tableClient == null)
            {
                throw new InvalidOperationException("TableClient is null.");
            }
            return tableClient.SubmitTransactionAsync(batch);
        }
        public Task<Pageable<TEntity>> Get<TEntity>(string tableName, string? filter = "")
            where TEntity : class, ITableEntity, new()
        {
            return Task.Run(() =>
            {
                var entities = impl.GetList<TEntity>(tableName, filter);
                Page<TEntity> page1 = Page<TEntity>.FromValues(
                entities.ToList(),
                continuationToken: null,
                new MockResponse()
                );
                return Pageable<TEntity>.FromPages(new[] { page1 });
            });
        }

        private class TableStorageQueryProvider<TEntity> : QueryProvider
            where TEntity : ITableEntity
        {
            public override object Execute(Expression expression)
            {
                var result = Expression.Lambda(expression).Compile().DynamicInvoke();
                if (result is IEnumerable<TEntity> enumerable)
                {
                    return enumerable.OrderBy(e => e.PartitionKey).ThenBy(e => e.RowKey).Take(1000).ToArray();
                }

#pragma warning disable CS8603 // Null 参照戻り値である可能性があります。
                return result;
#pragma warning restore CS8603 // Null 参照戻り値である可能性があります。
            }

            public override string GetQueryText(Expression expression)
            {
                return "";
            }
        }

        public Task Delete<TEntity>(string tableName, TEntity entity)
            where TEntity : class, ITableEntity
        {
            return Task.FromResult<TEntity?>(impl.Delete(tableName, entity) ? entity : null);
        }

        public Task<TEntity?> ExecuteTableOperation<TEntity>(string tableName, TableOperation<TEntity> operation)
            where TEntity : class, ITableEntity
        {
            var entity = operation.Entity;
            switch (operation.ActionType)
            {
                case TableTransactionActionType.Add:
                    impl.Add(tableName, entity);
                    break;
                case TableTransactionActionType.Delete:
                    impl.Delete(tableName, entity);
                    break;
                case TableTransactionActionType.UpdateMerge:
                    impl.Update(tableName, entity);
                    break;
                case TableTransactionActionType.UpdateReplace:
                    impl.Update(tableName, entity);
                    break;
                case TableTransactionActionType.UpsertReplace:
                    impl.Upsert(tableName, entity, true);
                    break;
                case TableTransactionActionType.UpsertMerge:
                    impl.Upsert(tableName, entity, false);
                    break;
            }
            return Task.FromResult<TEntity?>(entity as TEntity);
        }

        public Task<TEntity?> GetByKey<TEntity>(string tableName, string partitionKey, string rowKey)
            where TEntity : class, ITableEntity
        {
            return Task.FromResult<TEntity?>(impl.Get<TEntity>(tableName, partitionKey, rowKey));
        }

        public void Add<TEntity>(string tableName, TEntity entity)
            where TEntity : class, ITableEntity
        {
            impl.Add(tableName, entity);
        }

        public void AddRange<TEntity>(string tableName, IEnumerable<TEntity> entities)
            where TEntity : class, ITableEntity
        {
            foreach (var entity in entities)
            {
                impl.Add(tableName, entity);
            }
        }

        public bool CreateTable(string tableName) => impl.CreateTable(tableName);

        protected partial class Impl
        {
            #region original
            private readonly Dictionary<string, Dictionary<string, Dictionary<string, ITableEntity>>> _tables =
                new Dictionary<string, Dictionary<string, Dictionary<string, ITableEntity>>>();

            /// <summary>
            /// Creates a new table if it does not already exist.
            ///
            /// </summary>
            /// <param name="tableName">The name of the table to be created.</param>
            /// <returns>
            /// A flag indicating whether or not the table has actually been created.
            /// </returns>
            public bool CreateTable(string tableName)
            {
                if (TableExists(tableName))
                    return false;

                _tables.Add(tableName, new Dictionary<string, Dictionary<string, ITableEntity>>());
                return true;
            }

            /// <summary>
            /// Deletes a table if it exists.
            ///
            /// </summary>
            /// <param name="tableName">The name of the table to be deleted.</param>
            /// <returns>
            /// A flag indicating whether or not the table has actually been deleted.
            /// </returns>
            public bool DeleteTable(string tableName)
            {
                return _tables.Remove(tableName);
            }

            /// <summary>
            /// Checks whether the specified table exists.
            ///
            /// </summary>
            /// <param name="tableName">The name of the table to be checked for existence.</param>
            /// <returns>
            /// A flag indicating whether or not the table already exists.
            /// </returns>
            public bool TableExists(string tableName)
            {
                return _tables.ContainsKey(tableName);
            }

            /// <summary>
            /// Gets a collection of entities from the specified table by their optional partition key and row key.
            ///
            /// </summary>
            /// <typeparam name="T">The type of the entity stored in the table.</typeparam><param name="tableName">The name of the source table to be queried.</param><param name="partitionKey">The optional table partition key that will enforce the server-side filtering.</param><param name="rowKey">The optional table row key that will enforce the server-side filtering.</param>
            /// <returns>
            /// The LINQ-queryable collection of entities retrieved from the specified table.
            /// </returns>
            public T? Get<T>(string tableName, string? partitionKey = null, string? rowKey = null) where T : ITableEntity
            {
                var table = _tables[tableName]
                    ?? throw new Exception("table null exception");
                return table
                    .Where(x => partitionKey == null || x.Key == partitionKey)
                    .SelectMany(x => x.Value)
                    .Where(x => rowKey == null || x.Key == rowKey).Select(x => x.Value)
                    .Cast<T>()
                    .FirstOrDefault();
            }

            /// <summary>
            /// Gets a collection of entities from the specified table by their optional partition key and row key.
            ///
            /// </summary>
            /// <typeparam name="T">The type of the entity stored in the table.</typeparam><param name="tableName">The name of the source table to be queried.</param><param name="partitionKey">The optional table partition key that will enforce the server-side filtering.</param><param name="rowKey">The optional table row key that will enforce the server-side filtering.</param>
            /// <returns>
            /// The LINQ-queryable collection of entities retrieved from the specified table.
            /// </returns>
            public IEnumerable<T> GetList<T>(string tableName, string? filter = null, string? rowKey = null)
                where T : ITableEntity, new()
            {
                var table = _tables[tableName];
                string? partitionKey = null;
                string? rowKeyPrefix = null;
                string? nextPrefix = null;

                if (!string.IsNullOrEmpty(filter))
                {
                    // テンプレートリテラルからそれぞれの値を抽出する
                    var partitionKeyMatch = MyRegex().Match(filter);
                    if (partitionKeyMatch.Success)
                    {
                        partitionKey = partitionKeyMatch.Groups[1].Value;
                    }

                    var rowKeyPrefixMatch = MyRegex1().Match(filter);
                    if (rowKeyPrefixMatch.Success)
                    {
                        rowKeyPrefix = rowKeyPrefixMatch.Groups[1].Value;
                    }

                    var nextPrefixMatch = MyRegex2().Match(filter);
                    if (nextPrefixMatch.Success)
                    {
                        nextPrefix = nextPrefixMatch.Groups[1].Value;
                    }
                }
                // 抽出された値を元にフィルタリング
                return table
                    .Where(x => partitionKey == null || x.Key == partitionKey)
                    .SelectMany(x => x.Value)
                    .Where(x =>
                        (rowKeyPrefix == null || string.Compare(x.Key, rowKeyPrefix) >= 0) &&
                        (nextPrefix == null || string.Compare(x.Key, nextPrefix) < 0) &&
                        (rowKey == null || x.Key == rowKey))
                    .Select(x => x.Value)
                    .Cast<T>()
                    .ToList();
            }

            /// <summary>
            /// Adds the specified entity into the table.
            ///
            /// </summary>
            /// <typeparam name="T">The type of the table entity.</typeparam>
            /// <param name="tableName">The name of the table into which the specified entity will be added.</param>
            /// <param name="entity">The instance of the entity to be added into the table.</param>
            /// <returns>
            /// True if the entity has been successfully added, otherwise False.
            /// </returns>
            public bool Add<T>(string tableName, T entity)
                where T : class, ITableEntity
            {
                GetTableEntityKeys(entity, out var partitionKey, out var rowKey);
                var table = _tables[tableName];
                if (!table.ContainsKey(partitionKey))
                {
                    table.Add(partitionKey, new Dictionary<string, ITableEntity>());
                }
                if (table[partitionKey].ContainsKey(rowKey))
                {
                    return false;
                }
                table[partitionKey].Add(rowKey, entity);
                return true;
            }

            /// <summary>
            /// Adds the entities returned by the specified provider into the table.
            ///
            /// </summary>
            /// <typeparam name="T">The type of the table entity.</typeparam><param name="tableName">The name of the table into which the entities will be added.</param><param name="provider">The provider supplying the entities to be added into the table.</param><param name="batchSize">The number of entities to be batched together and sent in a single request. For optimal batch size, refer to the Windows Azure Table Storage API documentation.</param><param name="concurrent">The flag indicating whether or not batches will be handled concurrently, which may in turn greatly improve performance.</param>
            /// <returns>
            /// The object which enables the consumer to track the progress of the operation, and be notified when an error occurs or when the operation finishes.
            /// </returns>
            public IObservable<T> Add<T>(string tableName, IObservable<T> provider, int batchSize, bool concurrent)
            {
                throw new NotImplementedException();
            }

            /// <summary>
            /// Updates the specified entity stored in the table.
            ///
            /// </summary>
            /// <typeparam name="T">The type of the table entity.</typeparam>
            /// <param name="tableName">The name of the table containing the entity to be updated.</param>
            /// <param name="entity">The instance of the entity to be updated.</param>
            /// <returns>
            /// True if the entity has been successfully updated, otherwise False.
            /// </returns>
            public bool Update<T>(string tableName, T entity)
                where T : class, ITableEntity
            {
                GetTableEntityKeys(entity, out var partitionKey, out var rowKey);
                var table = _tables[tableName];
                if (!table.ContainsKey(partitionKey))
                {
                    return false;
                }
                if (!table[partitionKey].ContainsKey(rowKey))
                {
                    return false;
                }
                if (entity.ETag.ToString() != "*" && entity.ETag != table[partitionKey][rowKey].ETag)
                {
                    throw new Exception("An error occurred while processing this request.");
                }
                table[partitionKey][rowKey] = entity;
                return true;
            }

            /// <summary>
            /// Updates the entities returned by the specified provider in the table.
            ///
            /// </summary>
            /// <typeparam name="T">The type of the table entity.</typeparam><param name="tableName">The name of the table in which the entities will be updated.</param><param name="provider">The provider supplying the entities to be updated.</param><param name="batchSize">The number of entities to be batched together and sent in a single request. For optimal batch size, refer to the Windows Azure Table Storage API documentation.</param><param name="concurrent">The flag indicating whether or not batches will be handled concurrently, which may in turn greatly improve performance.</param>
            /// <returns>
            /// The object which enables the consumer to track the progress of the operation, and be notified when an error occurs or when the operation finishes.
            /// </returns>

            /// <summary>
            /// Updates the specified entity if it already exists, or inserts the entity if the specified entity doesn't exist in the target table.
            ///             The Upsert action will save an extra roundtrip to the storage system when an application wants to insert or update an entity without needing to know if the
            ///             entity already exists or not. Upsert can help increase performance and decrease latencies in the described scenario.
            ///
            /// </summary>
            ///
            /// <remarks>
            /// The <paramref name="replace"/> flag tells the Windows Azure Table storage infrastructure how it should treat the existing entities.
            ///
            /// <para>
            /// If <paramref name="replace"/> is set to True, Upsert will insert the entity if the entity does not exist, or if the entity exists,
            ///             replace the existing one. This means that once the operation successfully completes, the table will contain the specified entity with its properties,
            ///             replacing the previous entity and all of its existing properties if it had previously existed.
            /// </para>
            ///
            /// <para>
            /// If <paramref name="replace"/> is set to False, Upsert will insert the entity if the entity does not exist or, if the entity exists,
            ///             merges the specified entity's properties with the already existing ones. Once the operation successfully completes, the table will contain the specified
            ///             entity with updated properties provided in the request.
            /// </para>
            ///
            /// </remarks>
            /// <typeparam name="T">The type of the table entity.</typeparam><param name="tableName">The name of the table containing the entity.</param><param name="entity">The instance of the entity to be either updated or inserted.</param><param name="replace">A flag indicating whether the operation will replace the existing entity if it already exists.</param>
            /// <returns>
            /// True if the entity has been successfully updated or inserted, otherwise False.
            /// </returns>
            public bool Upsert<T>(string tableName, T entity, bool replace)
                where T : class, ITableEntity
            {
                GetTableEntityKeys(entity, out var partitionKey, out var rowKey);
                var table = _tables[tableName];
                if (!table.ContainsKey(partitionKey))
                {
                    if (!replace)
                    {
                        throw new NotImplementedException("Merge is not implemented");
                    }
                    table.Add(partitionKey, new Dictionary<string, ITableEntity>());
                }
                if (table[partitionKey].ContainsKey(rowKey))
                {
#if UseETagForUpdate
                    if (entity.ETag == "*" || entity.ETag == table[partitionKey][rowKey].ETag)
                    {
                        table[partitionKey][rowKey] = entity;
                    }
                    else
                    {
                        throw new Exception("An error occurred while processing this request.");
                    }
#else
                    table[partitionKey][rowKey] = entity;
#endif
                }
                else
                {
                    table[partitionKey].Add(rowKey, entity);
                }
                return true;
            }

            /// <summary>
            /// Deletes the specified entity stored in the table.
            ///
            /// </summary>
            /// <typeparam name="T">The type of the table entity.</typeparam>
            /// <param name="tableName">The name of the table containing the entity to be deleted.</param>
            /// <param name="entity">The instance of the entity to be deleted.</param>
            /// <returns>
            /// True if the entity has been successfully deleted, otherwise False.
            /// </returns>
            public bool Delete<T>(string tableName, T entity)
                where T : class, ITableEntity
            {
                GetTableEntityKeys(entity, out var partitionKey, out var rowKey);
                var table = _tables[tableName];
                if (!table.ContainsKey(partitionKey))
                {
                    return false;
                }
#if UseETagForDelete
                if (table[partitionKey].ContainsKey(rowKey) && (entity.ETag.ToString() == "*" || entity.ETag == table[partitionKey][rowKey].ETag))
#else
                if (table[partitionKey].ContainsKey(rowKey))
#endif
                {
                    table[partitionKey].Remove(rowKey);
                    return true;
                }
                // Azure actually throws an exception if the entity doesn't exist
                throw new Exception("An error occurred while processing this request.");
            }

            private static void GetTableEntityKeys<T>(T entity, out string partitionKey, out string rowKey)
                where T : class
            {
                if (entity is ITableEntity tableEntity)
                {
                    partitionKey = tableEntity.PartitionKey;
                    rowKey = tableEntity.RowKey;
                    return;
                }

                throw new NotImplementedException("Can't get entity keys from type " + typeof(T).Name);
            }

            [GeneratedRegex(@"PartitionKey eq '([^']*)'")]
            private static partial Regex MyRegex();
            [GeneratedRegex(@"RowKey ge '([^']*)'")]
            private static partial Regex MyRegex1();
            [GeneratedRegex(@"RowKey lt '([^']*)'")]
            private static partial Regex MyRegex2();
            #endregion
        }

        /// <summary>
        /// Azure.Pageを生成するためのMockレスポンス
        /// </summary>
        protected sealed class MockResponse : Response
        {
            public override int Status => throw new NotImplementedException();

            public override string ReasonPhrase => throw new NotImplementedException();

            public override Stream? ContentStream
            {
                get => throw new NotImplementedException();
                set => throw new NotImplementedException();
            }
            public override string ClientRequestId
            {
                get => throw new NotImplementedException();
                set => throw new NotImplementedException();
            }

            public override void Dispose() =>
                throw new NotImplementedException();
            protected override bool ContainsHeader(string name) =>
                throw new NotImplementedException();
            protected override IEnumerable<HttpHeader> EnumerateHeaders() =>
                throw new NotImplementedException();
            protected override bool TryGetHeader(
                string name,
                [NotNullWhen(true)] out string? value) =>
                throw new NotImplementedException();
            protected override bool TryGetHeaderValues(
                string name,
                [NotNullWhen(true)] out IEnumerable<string>? values) =>
                throw new NotImplementedException();
        }


    }
}
