﻿using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
using CoreEx.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Test.Helpers.Infrastructure.Data.Clients
{
    internal class InMemoryQueueStorageClient : IQueueStorageClient
    {
        public readonly Dictionary<string, Queue<string>> Queues = new Dictionary<string, Queue<string>>();

        public Task EnqueueAsync<TEntity>(string queueName, TEntity entity)
        {
            Queues[queueName]!.Enqueue(JsonSerializer.Default.Serialize(entity));
            return Task.CompletedTask;
        }

        internal void CreateQueue(string queueName)
        {
            if (!Queues.ContainsKey(queueName))
            {
                Queues.Add(queueName, new Queue<string>());
            }
        }
    }
}
