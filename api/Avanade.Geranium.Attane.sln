Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33213.308
MinimumVisualStudioVersion = 15.0.26124.0
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avanade.Geranium.Attane.Api", "Avanade.Geranium.Attane.Api\Avanade.Geranium.Attane.Api.csproj", "{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avanade.Geranium.Attane.Business", "Avanade.Geranium.Attane.Business\Avanade.Geranium.Attane.Business.csproj", "{F98FFBA3-32CD-4127-B464-A9D18A545D2E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avanade.Geranium.Attane.Common", "Avanade.Geranium.Attane.Common\Avanade.Geranium.Attane.Common.csproj", "{81860842-8A59-4A45-8C15-84FA45606BD3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Testing", "Testing", "{53EEA045-4195-4A3F-A7C1-13A517B88080}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tools", "Tools", "{A4E363CD-D0BE-4B43-9C80-18EA6CD2FAA6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avanade.Geranium.Attane.CodeGen", "Avanade.Geranium.Attane.CodeGen\Avanade.Geranium.Attane.CodeGen.csproj", "{A65032E5-F3FC-4963-B135-E462190D0ABC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avanade.Geranium.Attane.Shared", "Avanade.Geranium.Attane.Shared\Avanade.Geranium.Attane.Shared.csproj", "{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avanade.Geranium.Attane.Infrastructure", "Avanade.Geranium.Attane.Infrastructure\Avanade.Geranium.Attane.Infrastructure.csproj", "{11E68172-FED8-4E0F-832F-01593A3C84E0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Avanade.Geranium.Attane.Test", "Avanade.Geranium.Attane.Test\Avanade.Geranium.Attane.Test.csproj", "{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Debug|x64.Build.0 = Debug|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Debug|x86.Build.0 = Debug|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Release|Any CPU.Build.0 = Release|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Release|x64.ActiveCfg = Release|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Release|x64.Build.0 = Release|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Release|x86.ActiveCfg = Release|Any CPU
		{FBC7842F-B35C-4813-B3F3-EB4E34583EEC}.Release|x86.Build.0 = Release|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Debug|x64.Build.0 = Debug|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Debug|x86.Build.0 = Debug|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Release|x64.ActiveCfg = Release|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Release|x64.Build.0 = Release|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Release|x86.ActiveCfg = Release|Any CPU
		{F98FFBA3-32CD-4127-B464-A9D18A545D2E}.Release|x86.Build.0 = Release|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Debug|x64.Build.0 = Debug|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Debug|x86.Build.0 = Debug|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Release|Any CPU.Build.0 = Release|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Release|x64.ActiveCfg = Release|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Release|x64.Build.0 = Release|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Release|x86.ActiveCfg = Release|Any CPU
		{81860842-8A59-4A45-8C15-84FA45606BD3}.Release|x86.Build.0 = Release|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Debug|x64.Build.0 = Debug|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Debug|x86.Build.0 = Debug|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Release|Any CPU.Build.0 = Release|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Release|x64.ActiveCfg = Release|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Release|x64.Build.0 = Release|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Release|x86.ActiveCfg = Release|Any CPU
		{A65032E5-F3FC-4963-B135-E462190D0ABC}.Release|x86.Build.0 = Release|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Debug|x64.Build.0 = Debug|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Debug|x86.Build.0 = Debug|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Release|x64.ActiveCfg = Release|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Release|x64.Build.0 = Release|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Release|x86.ActiveCfg = Release|Any CPU
		{BAE259D5-F61F-4D8A-B2CC-DED7B982E0EB}.Release|x86.Build.0 = Release|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Debug|x64.Build.0 = Debug|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Debug|x86.Build.0 = Debug|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Release|x64.ActiveCfg = Release|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Release|x64.Build.0 = Release|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Release|x86.ActiveCfg = Release|Any CPU
		{11E68172-FED8-4E0F-832F-01593A3C84E0}.Release|x86.Build.0 = Release|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Debug|x64.Build.0 = Debug|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Debug|x86.Build.0 = Debug|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Release|x64.ActiveCfg = Release|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Release|x64.Build.0 = Release|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Release|x86.ActiveCfg = Release|Any CPU
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A65032E5-F3FC-4963-B135-E462190D0ABC} = {A4E363CD-D0BE-4B43-9C80-18EA6CD2FAA6}
		{CDFE45A6-6D8E-418A-89B9-72C4CA9787BC} = {53EEA045-4195-4A3F-A7C1-13A517B88080}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {44AE4366-B3C4-4390-A283-8C2F5D600AC5}
	EndGlobalSection
EndGlobal
