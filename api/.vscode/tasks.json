{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Avanade.Geranium.Attane.Api/Avanade.Geranium.Attane.Api.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Avanade.Geranium.Attane.Api/Avanade.Geranium.Attane.Api.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/Avanade.Geranium.Attane.Api/Avanade.Geranium.Attane.Api.csproj"], "problemMatcher": "$msCompile"}]}