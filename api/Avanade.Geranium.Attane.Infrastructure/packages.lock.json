{"version": 1, "dependencies": {".NETStandard,Version=v2.1": {"Azure.Core": {"type": "Direct", "requested": "[1.45.0, )", "resolved": "1.45.0", "contentHash": "a4ZZ8SHrYqDYqan0vUZrCoN2V09sSXWqsy5PwgyRDZWF/ZjYPUNqBcOOV01Mm7Rcd4Psf08GUBPOhGz+7onmPQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.2.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Data.Tables": {"type": "Direct", "requested": "[12.10.0, )", "resolved": "12.10.0", "contentHash": "fW6873rvhpAYUGXzu7JmbfSkYfppvfkKkiZqifSyOVJ0SsAWBMqHWL9VbUPsApP3DWTwakpU35nfpKTPqvFVww==", "dependencies": {"Azure.Core": "1.44.1", "System.Text.Json": "6.0.10"}}, "Azure.Storage.Queues": {"type": "Direct", "requested": "[12.22.0, )", "resolved": "12.22.0", "contentHash": "HPQgOlfH+rJ4CL4V8ePFnsT/KKnvLU35ytxC3fsTTqOazhQ0593C0aPVu258DRN8bQCbx4OpNpjtiO9czDy3VQ==", "dependencies": {"Azure.Storage.Common": "12.23.0", "System.Memory.Data": "6.0.1", "System.Text.Json": "6.0.11"}}, "CoreEx": {"type": "Direct", "requested": "[2.10.1, )", "resolved": "2.10.1", "contentHash": "THAh81GNe7HKRTwLLFmcTRkBpfPk5WeLwgFnRjH9WChtxJkvfchYUKSLHTVscek+A3j7CbLMu7v4Q+m0NOEzuw==", "dependencies": {"CloudNative.CloudEvents.SystemTextJson": "2.6.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.16", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0", "Swashbuckle.AspNetCore": "6.5.0", "System.Collections.Immutable": "6.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Memory.Data": "6.0.0", "System.Text.Json": "6.0.7", "YamlDotNet": "13.1.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": {"type": "Direct", "requested": "[9.0.3, )", "resolved": "9.0.3", "contentHash": "3sNYjO9jUQQVdF5dPoVc5rCV3YKIoUDBZPr9/SuxyxIGH15Wv3/8MvJxeGByufwcM5nhziY2qpbDN50MCMvH0w=="}, "Azure.Storage.Common": {"type": "Transitive", "resolved": "12.23.0", "contentHash": "X/pe1LS3lC6s6MSL7A6FzRfnB6P72rNBt5oSuyan6Q4Jxr+KiN9Ufwqo32YLHOVfPcB8ESZZ4rBDketn+J37Rw==", "dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}}, "CloudNative.CloudEvents": {"type": "Transitive", "resolved": "2.6.0", "contentHash": "VIcb3tQZcluVEqO6yhnFIpMaPYq5Ime1JbqxLeGPZ+CqeVDOZTBGAtOGQPgfadv2tZFU7+jZqcX0Oplrs7oR7w==", "dependencies": {"System.Memory": "4.5.5"}}, "CloudNative.CloudEvents.SystemTextJson": {"type": "Transitive", "resolved": "2.6.0", "contentHash": "pDrmaQqFYhVOHsg5LpAsLbiL5jd67RWilN0854gM5RbUkAOVWSZTT3m2F8HnVsjq22YfZrwuOkDaLBYFOjiFZQ==", "dependencies": {"CloudNative.CloudEvents": "2.6.0", "System.Text.Json": "5.0.2"}}, "Microsoft.AspNetCore.Authentication.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Core": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authorization": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Authorization.Policy": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Http": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.ApiExplorer": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "UEmXDfLhy9OaTH4t2iXFnPYf1UBpwCM4tdeBoQyGn/pvKEA/TTvxO4K1dC6kF8x5l/IbtLI+ud0rcPttjdyYUA==", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0"}}, "Microsoft.AspNetCore.Mvc.Core": {"type": "Transitive", "resolved": "2.2.5", "contentHash": "/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.1"}}, "Microsoft.AspNetCore.Mvc.DataAnnotations": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "JHcJKYjf8ASL++LVMGYZTa3NYVWXKBwDmP1sBIVWD21wDI58/b+NkUiGrcKxmG4VAYrHnEy6XkLbXxh0gVmBRw==", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.1.0", "Microsoft.Extensions.Localization": "2.1.0", "System.ComponentModel.Annotations": "4.5.0"}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}}, "Microsoft.AspNetCore.Routing": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Routing.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.StaticFiles": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "puGNg55r79arhgictNnlOnvk0ODR0MgLwWt2slzgoCiy9uCb5UaDi8rGyoYDRSlNXrXPSVen25Hk/s/gmH2Pow==", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.0", "Microsoft.AspNetCore.Http.Extensions": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.WebEncoders": "2.1.0"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg=="}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "17h8b5mXa87XYKrrVqdgZ38JefSUqLChUQpXgSnpzsM0nDOhE40FTeNWOJ/YmySGV6tG6T8+hjz6vxbknHJr6A==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}}, "Microsoft.DotNet.PlatformAbstractions": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}}, "Microsoft.Extensions.ApiDescription.Server": {"type": "Transitive", "resolved": "6.0.5", "contentHash": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw=="}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "b3ErKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg=="}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"type": "Transitive", "resolved": "6.0.16", "contentHash": "aj1b5amhKQReVWEDsoOW6b1c7/1C+pJHHsI9EEb671uvGsXpiWNEz++1rtw8G8xoeriJmsJEVKaWKnwpG7xPVA==", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "6.0.16", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.3", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "Zh8BkC4aneMITeGzflkDZKDkgDC+YhsvjAaeDLWEmgzNoAGPLbH+tbvn9Boo94V0cryfR/5396goB5KAElKzfw==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.1.0"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Localization": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "EssCj6ZGhZL6ojL8IKLxJEIEgz8RQQHO1ZUgW6uIUoyzcwCMTEnN9mQWDXcXAitx0tyNz8xKNudTJ1RCc3/lkA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Localization.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}}, "Microsoft.Extensions.Localization.Abstractions": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "i5LgUcc0OB4KMujLmjdpDsha72xLa7CQwpG3zEEa9SRC5k8qrSyx97flmiikq61sVWzPrHXjYaj3jaZC6z+TBw=="}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "6.0.3", "contentHash": "SUpStcdjeBbdKjPKe53hVVLkFjylX0yIXY8K+xWa47+o1d+REDyOMZjHZa+chsQI1K9qZeiHWk9jos0TFU7vGg==", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.WebEncoders": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "YwzwLadahZiEbqbDoAlKhAq/szBL05ZmIIlrfHjLsF9M3zppmWRKAOGjFalmwONxbZMl3OHUoAiPKShtieV0KA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "1.0.1", "contentHash": "2G6OjjJzwBfNOO8myRV/nFrbTw5iA+DEm0N+qUqhrOmaVtn4pC77h38I1jsXGw5VH55+dPfQsqHD0We9sCl9FQ=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.0.1", "contentHash": "rkn+fKobF/cbWfnnfBOQHKVKIOpxMZBvlSHkqDWgBpwGDcLRduvs3D9OLGeV6GWGvVwNlVi2CBbTjuPmtHvyNw=="}, "Microsoft.OpenApi": {"type": "Transitive", "resolved": "1.2.3", "contentHash": "Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw=="}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "U82mHQSKaIk+lpSVCbWYKNavmNH1i5xrExDEquU1i6I5pV6UMOqRnJRSlKO3cMPfcpp0RgDY+8jUXHdQ4IfXvw==", "dependencies": {"Microsoft.CSharp": "4.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}}, "Polly": {"type": "Transitive", "resolved": "7.2.3", "contentHash": "DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ=="}, "Polly.Extensions.Http": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "dependencies": {"Polly": "7.1.0"}}, "runtime.native.System": {"type": "Transitive", "resolved": "4.0.0", "contentHash": "QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "Swashbuckle.AspNetCore": {"type": "Transitive", "resolved": "6.5.0", "contentHash": "FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger": {"type": "Transitive", "resolved": "6.5.0", "contentHash": "XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.OpenApi": "1.2.3"}}, "Swashbuckle.AspNetCore.SwaggerGen": {"type": "Transitive", "resolved": "6.5.0", "contentHash": "Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "2.1.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "2.1.0", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "System.Text.Json": "4.6.0"}}, "Swashbuckle.AspNetCore.SwaggerUI": {"type": "Transitive", "resolved": "6.5.0", "contentHash": "OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "dependencies": {"Microsoft.AspNetCore.Routing": "2.1.0", "Microsoft.AspNetCore.StaticFiles": "2.1.0", "Microsoft.Extensions.FileProviders.Embedded": "2.1.0", "System.Text.Json": "4.6.0"}}, "System.AppContext": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "dependencies": {"System.Runtime": "4.1.0"}}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg=="}, "System.ClientModel": {"type": "Transitive", "resolved": "1.2.1", "contentHash": "s9+M5El+DXdCRRLzxak8uGBKWT8H/eIssGpFtpaMKdJULrQbBDPH/zFbVyHX+NDczhS5EvjHFbBH9/L+0UhmcA==", "dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "6.0.10"}}, "System.Collections": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.ComponentModel.Annotations": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg=="}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.Tools": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Dynamic.Runtime": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Globalization": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.IO": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem.Primitives": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "dependencies": {"System.Runtime": "4.1.0"}}, "System.IO.Hashing": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}}, "System.Linq": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Linq.Expressions": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Memory": {"type": "Transitive", "resolved": "4.5.5", "contentHash": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}}, "System.Memory.Data": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "yliDgLh9S9Mcy5hBIdZmX6yphYIW3NH+3HN1kV1m7V1e0s7LNTw/tHNjJP4U9nSMEgl3w1TzYv/KA1Tg9NYy6w==", "dependencies": {"System.Memory": "4.5.4", "System.Text.Json": "6.0.11"}}, "System.Numerics.Vectors": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ=="}, "System.ObjectModel": {"type": "Transitive", "resolved": "4.0.12", "contentHash": "tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}}, "System.Reflection": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.ILGeneration": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.Lightweight": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Extensions": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.TypeExtensions": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "v6c/4Yaa9uWsq+JMhnOFewrYkgdNHNG2eMKuNqRn8P733rNXeRCGvV5FkkjBXn2dbVkPXOsO0xjsEeM1q2zC0g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Runtime.Handles": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Runtime.InteropServices": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}}, "System.Runtime.InteropServices.RuntimeInformation": {"type": "Transitive", "resolved": "4.0.0", "contentHash": "hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Runtime.Serialization.Primitives": {"type": "Transitive", "resolved": "4.1.1", "contentHash": "HZ6Du5QrTG8MNJbf4e4qMO3JRAkIboGT5Fk804uZtg3Gq516S7hAqTm2UZKUHa7/6HUGdVy3AqMQKbns06G/cg==", "dependencies": {"System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "U3gGeMlDZXxCEiY4DwVLSacg+DFWCvoiX+JThA/rvw37Sqrku7sEFeVBBBMBnfB6FeZHsyDx85HlKL19x0HtZA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Text.Encoding.Extensions": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "E5M5AE2OUTlCrf4omZvzzziUJO9CofBl+lXHaN5IKePPJvHqYFYYpaDPgCpR4VwaFbEebfnjOxxEBtPtsqAxpQ==", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json": {"type": "Transitive", "resolved": "6.0.11", "contentHash": "xqC1HIbJMBFhrpYs76oYP+NAskNVjc6v73HqLal7ECRDPIp4oRU5pPavkD//vNactCn9DA2aaald/I5N+uZ5/g==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.1", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Text.RegularExpressions": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "i88YCXpRTjCnoSQZtdlHkAOx4KNNik4hMy83n0+Ftlb7jvV6ZiZWMpnEZHhjBp6hQVh8gWd/iKNPzlPF7iyA2g==", "dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Threading": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ==", "dependencies": {"System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}}, "System.Xml.ReaderWriter": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "ZIiLPsf67YZ9zgr31vzrFaYQqxRPX9cVHjtPSnmx4eN6lbS/yEyYNr2vs1doGDEscF0tjCZFsk9yUg1sC9e8tg==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Extensions": "4.0.0"}}, "System.Xml.XDocument": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}}, "YamlDotNet": {"type": "Transitive", "resolved": "13.1.0", "contentHash": "S4tzJjofMDx8PKtbnwkaMznbPD1aUffVKW+ClhfOGAYFIPuY9sSrArcFQOcRkbwRP1kYIaMbXk4Vsnfk3dE3dQ=="}}}}