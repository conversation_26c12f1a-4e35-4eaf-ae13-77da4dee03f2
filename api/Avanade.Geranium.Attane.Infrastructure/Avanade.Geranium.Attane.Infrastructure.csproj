<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Nullable>enable</Nullable>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
  </PropertyGroup>
  <PropertyGroup>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="packages.lock.json" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="CoreEx" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.lock.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Core" Version="1.45.0" />
    <PackageReference Include="Azure.Data.Tables" Version="12.10.0" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.22.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" Version="9.0.3" />
    <PackageReference Include="CoreEx" Version="2.10.1" />
  </ItemGroup>
</Project>
