using Azure;
using Azure.Data.Tables;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Infrastructure.Data.Clients
{
    /// <summary>
    /// Azure Table Storage を扱うクライアントのインターフェイス
    /// </summary>
    public interface ITableStorageClient
    {
        /// <summary>
        /// テーブルからキーを用いた取得を行います。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="filter">フィルタ</param>
        /// <returns>取得結果</returns>
        Task<TEntity?> GetByKey<TEntity>(string tableName, string partitionKey, string rowKey)
            where TEntity : class, ITableEntity;

        /// <summary>
        /// テーブルからの取得を行います。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <returns>取得結果</returns>
        Task<Pageable<TEntity>> Get<TEntity>(string tableName, string? filter = "")
           where TEntity : class, ITableEntity, new();


        /// <summary>
        /// テーブルに対する操作を行います。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="tableOperation">操作</param>
        /// <returns>操作を行った結果</returns>
        Task<TEntity?> ExecuteTableOperation<TEntity>(string tableName, TableOperation<TEntity> operation)
            where TEntity : class, ITableEntity;
        /// <summary>
        /// テーブルからエンティティを削除します。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="entity">削除したいエンティティ</param>
        /// <returns>操作を行った結果</returns>
        Task Delete<T>(string tableName, T entity)
            where T : class, ITableEntity;

        /// <summary>
        /// バッチ処理を行います。
        /// </summary>
        /// <param name="tableName">テーブル名</param>
        /// <param name="operations">行いたい操作</param>
        /// <returns>操作を行った結果</returns>
        Task<Response<IReadOnlyList<Response>>> ExecuteBatchAsync<TEntity>(string tableName, TableTransactionActionType operation, ITableEntity entity)
            where TEntity : class, ITableEntity;

        /// <summary>
        /// バッチ処理を行います。
        /// </summary>
        /// <param name="tableName">テーブル名</param>
        /// <param name="operations">行いたい操作</param>
        /// <returns>操作を行った結果</returns>
        Task<Response<IReadOnlyList<Response>>> ExecuteBatchAsync<TEntity>(string tableName, IEnumerable<Tuple<TableTransactionActionType, ITableEntity>> operations)
            where TEntity : class, ITableEntity;
    }
}
