using Azure;
using Azure.Data.Tables;
using Azure.Data.Tables.Models;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Infrastructure.Data.Clients
{
    /// <summary>
    /// Azure Table Storage を扱うクライアント
    /// </summary>
    public class TableStorageClient : ITableStorageClient
    {
        private readonly TableServiceClient _serviceClient;
        private readonly ILogger<TableStorageClient> _logger;

        public TableStorageClient(TableServiceClient serviceClient, ILogger<TableStorageClient> logger)
        {
            _serviceClient = serviceClient;
            _logger = logger;
        }

        /// <summary>
        /// テーブルからキーを用いた取得を行います。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="filter">フィルタ</param>
        /// <returns>取得結果</returns>
        public Task<TEntity?> GetByKey<TEntity>(string tableName, string partitionKey, string rowKey)
            where TEntity : class, ITableEntity
            => this.Retrieve<TEntity>(tableName, partitionKey, rowKey);

        /// <summary>
        /// テーブルからのキーを用いた取得を行います。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="partitionKey"></param>
        /// <param name="rowKey"></param>
        /// <returns>取得結果</returns>
        private async Task<TEntity?> Retrieve<TEntity>(string tableName, string partitionKey, string rowKey)
            where TEntity : class, ITableEntity
        {
            // nullではなくエラーを返す仕様になったのでハンドリングを追加
            try
            {
                var tableClient = _serviceClient.GetTableClient(tableName);
                var tableResult = await tableClient.GetEntityAsync<TEntity>(partitionKey, rowKey).ConfigureAwait(false);
                return tableResult.Value;
            }
            catch (RequestFailedException ex) when (ex.Status == 404)
            {
                _logger.LogTrace(ex.ToString());
                return null;
            }
        }

        /// <summary>
        /// テーブルからの取得を行います。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <returns>取得結果</returns>
        public async Task<Pageable<TEntity>> Get<TEntity>(string tableName, string? filter = "")
            where TEntity : class, ITableEntity, new()
        {
            // 特定のテーブルに対するTableClientを取得
            var tableClient = _serviceClient.GetTableClient(tableName);
            // テーブルが存在しない場合は作成
            await tableClient.CreateIfNotExistsAsync().ConfigureAwait(false);
            // Null or 空文字ではないことを確認
            if (!string.IsNullOrWhiteSpace(filter))
            {
                // 指定されたフィルターを使用してクエリを実行
                return tableClient.Query<TEntity>(filter);
            }
            else
            {
                // フィルターなしでクエリを実行
                return tableClient.Query<TEntity>();
            }
        }
        /// <summary>
        /// テーブルに対する操作を行います。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="tableOperation">操作</param>
        /// <returns>操作を行った結果</returns>
        public async Task<TEntity?> ExecuteTableOperation<TEntity>(string tableName, TableOperation<TEntity> operation)
          where TEntity : class, ITableEntity
        {
            var tableClient = _serviceClient.GetTableClient(tableName);
            Response response;

            switch (operation.ActionType)
            {
                case TableTransactionActionType.Add:
                    response = await tableClient.AddEntityAsync<TEntity>((TEntity)operation.Entity);
                    break;
                case TableTransactionActionType.Delete:
                    await Delete<TEntity>(tableName, (TEntity)operation.Entity);
                    break;
                case TableTransactionActionType.UpdateMerge:
                    response = await tableClient.UpdateEntityAsync<TEntity>((TEntity)operation.Entity, operation.ETag).ConfigureAwait(false);
                    break;
                case TableTransactionActionType.UpdateReplace:
                    response = await tableClient.UpdateEntityAsync<TEntity>((TEntity)operation.Entity, operation.ETag).ConfigureAwait(false);
                    break;
                case TableTransactionActionType.UpsertMerge:
                    response = await tableClient.UpsertEntityAsync<TEntity>((TEntity)operation.Entity, operation.Mode, operation.CancellationToken).ConfigureAwait(false);
                    break;
                case TableTransactionActionType.UpsertReplace:
                    response = await tableClient.UpsertEntityAsync<TEntity>((TEntity)operation.Entity, operation.Mode, operation.CancellationToken).ConfigureAwait(false);
                    break;
            }
            return (TEntity)operation.Entity;
        }

        /// <summary>
        /// テーブルからエンティティを削除します。
        /// </summary>
        /// <typeparam name="TEntity">結果のエンティティの型</typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="entity">削除したいエンティティ</param>
        /// <returns>操作を行った結果</returns>

        public async Task Delete<T>(string tableName, T entity)
            where T : class, ITableEntity
        {
            var tableClient = _serviceClient.GetTableClient(tableName);
            await tableClient.DeleteEntityAsync(entity.PartitionKey, entity.RowKey).ConfigureAwait(false);
        }

        /// <summary>
        /// バッチ処理を行います。
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <param name="tableName">テーブル名</param>
        /// <param name="operation">行いたい操作</param>
        /// <param name="entity"></param>
        /// <returns>操作を行った結果</returns>
        public Task<Response<IReadOnlyList<Response>>> ExecuteBatchAsync<TEntity>(string tableName, TableTransactionActionType operation, ITableEntity entity)
            where TEntity : class, ITableEntity
        {
            var tableClient = _serviceClient.GetTableClient(tableName);
            List<TableTransactionAction> batch = new List<TableTransactionAction>
            {
                new TableTransactionAction(operation, entity)
            };

            return tableClient.SubmitTransactionAsync(batch);

        }

        /// <summary>
        /// バッチ処理を行います。
        /// </summary>
        /// <param name="tableName">テーブル名</param>
        /// <param name="operations">行いたい操作</param>
        /// <returns>操作を行った結果</returns>

        public Task<Response<IReadOnlyList<Response>>> ExecuteBatchAsync<TEntity>(string tableName, IEnumerable<Tuple<TableTransactionActionType, ITableEntity>> operations)
            where TEntity : class, ITableEntity
        {

            var tableClient = _serviceClient.GetTableClient(tableName);

            List<TableTransactionAction> batch = new List<TableTransactionAction>();
            foreach (Tuple<TableTransactionActionType, ITableEntity> operation in operations)
            {
                batch.Add(new TableTransactionAction(operation.Item1, operation.Item2));
            }
            //TODO: 更新処理後のEntityの返却方法とあるべき姿の検討
            return tableClient.SubmitTransactionAsync(batch);
        }

        public async static Task<HealthCheckResult> HealthCheck(TableServiceClient serviceClient, params string[] tableNames)
        {
            foreach (var tableName in tableNames)
            {
                try
                {
                    /*
                     * HACK: Azure Storage AccountのAccounte Tableから指定されたテーブル名のアクセス権限を取得するAPIが正常呼出できるかを確認する
                     * だけなのでAccount Tableに対して実行する処理は何でも構わない。
                     * おそらくSelect処理などは検索条件が必要なため、最も単純に呼び出せる指定したテーブルへのアクセス権限を取得していると思われる。
                     */
                    var tableClient = serviceClient.GetTableClient(tableName);
                    await tableClient.GetAccessPoliciesAsync().ConfigureAwait(false);
                }
                catch (RequestFailedException ex) when (ex.Status == 404)
                {
                    return HealthCheckResult.Unhealthy($"Table with name {tableName} does not exist.");
                }
                catch (Exception ex)
                {
                    return HealthCheckResult.Unhealthy(exception: ex);
                }
            }
            return HealthCheckResult.Healthy();
        }
    }
}
