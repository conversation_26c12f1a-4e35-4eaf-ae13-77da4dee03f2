using Azure.Storage.Queues;
using CoreEx.Json;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System;
using System.Text;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Infrastructure.Data.Clients
{
    /// <summary>
    /// Azure Queue Storage を扱うクライアント
    /// </summary>
    public class QueueStorageClient : IQueueStorageClient
    {
        private readonly string _connectionString;

        /// <summary>
        /// 接続文字列を指定して<see cref="QueueStorageClient"/> クラスの新しいインスタンスを生成します。
        /// </summary>
        /// <param name="connectionString">接続文字列</param>
        public QueueStorageClient(string connectionString)
        {
            if (connectionString == null)
            {
                throw new ArgumentNullException(nameof(connectionString));
            }
            if (connectionString.Length == 0)
            {
                throw new ArgumentException($"{nameof(connectionString)} is empty");
            }

            _connectionString = connectionString;
        }

        /// <summary>
        /// キューにエンティティを追加します。
        /// </summary>
        /// <typeparam name="TEntity">エンティティの型</typeparam>
        /// <param name="queueName">キュー名</param>
        /// <param name="entity">エンティティ</param>
        /// <returns>タスク</returns>
        public async Task EnqueueAsync<TEntity>(string queueName, TEntity entity)
        {
            var client = await GetClient(queueName).ConfigureAwait(false);
            var json = JsonSerializer.Default.Serialize(entity);
            var base64String = Convert.ToBase64String(Encoding.UTF8.GetBytes(json));
            await client.SendMessageAsync(base64String).ConfigureAwait(false);
        }

        /// <summary>
        /// クライアントを取得します。
        /// </summary>
        /// <param name="queueName">キュー名</param>
        /// <returns>クライアント</returns>
        private async Task<QueueClient> GetClient(string queueName)
        {
            var client = new QueueClient(_connectionString, queueName);
            await client.CreateIfNotExistsAsync().ConfigureAwait(false);
            return client;
        }

        public async Task<HealthCheckResult> HealthCheck(params string[] queueNames)
        {
            foreach (var queueName in queueNames)
            {
                try
                {
                    var client = await GetClient(queueName).ConfigureAwait(false);
                    var exist = await client.ExistsAsync().ConfigureAwait(false);
                    if (exist == null)
                    {
                        return HealthCheckResult.Unhealthy($"Getting queue with name {queueName} failed.");
                    }
                    if (!exist.Value)
                    {
                        return HealthCheckResult.Unhealthy($"Queue with name {queueName} does not exist.");
                    }
                }
                catch (Exception ex)
                {
                    return HealthCheckResult.Unhealthy(exception: ex);
                }
            }
            return HealthCheckResult.Healthy();
        }
    }
}
