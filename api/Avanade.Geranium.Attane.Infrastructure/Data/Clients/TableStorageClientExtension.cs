using Azure.Data.Tables;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Avanade.Geranium.Attane.Infrastructure.Data.Clients
{
    /// <summary>
    /// TableStorageクエリ用の拡張メソッド
    /// </summary>
    public static class TableStorageClientExtension
    {
        /// <summary>
        /// <see cref="TableEntity.RowKey"/> が指定したプレフィックスで開始している範囲にフィルタします。
        /// </summary>
        /// <typeparam name="T">対象のエンティティ型</typeparam>
        /// <param name="query">フィルタ対象の<see cref="IEnumerable{T}"/></param>
        /// <param name="prefix">フィルタする範囲</param>
        /// <returns><paramref name="query"/> を <paramref name="prefix"/>でフィルタした結果</returns>
        public static IEnumerable<T> RowKeyStartsWith<T>(this IEnumerable<T> query, string prefix)
            where T : ITableEntity
        {
            if (prefix == null)
            {
                throw new ArgumentNullException(nameof(prefix));
            }

            if (prefix.Length == 0)
            {
                return query;
            }

            // prefixの最後の文字を次の文字にすることでprefixの範囲を示す
            // 厳密には文字が7FFF/FFFFの場合を考慮する必要があるが、実用上ないので考慮しない
            var nextPrefix = $"{prefix[0..^1]}{(char)(prefix[^1] + 1)}";
            return query.RowKeyInRange(prefix, nextPrefix);
        }

        /// <summary>
        /// <see cref="TableEntity.RowKey"/> が指定した開始・終了に含まれる範囲にフィルタします。
        /// </summary>
        /// <typeparam name="T">対象のエンティティ型</typeparam>
        /// <param name="query">フィルタ対象の<see cref="IEnumerable{T}"/></param>
        /// <param name="start">開始値。この値は範囲に含まれる。</param>
        /// <param name="end">終了値。この値は範囲に含まれない。</param>
        /// <returns><paramref name="query"/> を <paramref name="start"/> ～ <paramref name="end"/> でフィルタした結果</returns>
        public static IEnumerable<T> RowKeyInRange<T>(this IEnumerable<T> query, string start, string end)
            where T : ITableEntity
        {
            if (start == null)
            {
                throw new ArgumentNullException(nameof(start));
            }

            if (end == null)
            {
                throw new ArgumentNullException(nameof(end));
            }

            return query.Where(entity =>
                entity.RowKey.CompareTo(start) >= 0 &&
                entity.RowKey.CompareTo(end) < 0
            );
        }

        /// <summary>
        /// 1度の取得処理のレコード上限
        /// </summary>
        private const int MaxRowsPerPagination = 1000;

        /// <summary>
        /// Table Storageの制約を意識せずに、クエリの結果を全件取得します。
        /// </summary>
        /// <typeparam name="T">取得するデータの型</typeparam>
        /// <param name="query">対象のクエリ</param>
        /// <returns>取得結果</returns>
        public static IEnumerable<T> GetAll<T>(this IQueryable<T> query)
            where T : ITableEntity
        {
            var list = new List<T>();
            var result = query.ToArray();
            list.AddRange(result);

            while (result?.Length == MaxRowsPerPagination)
            {
                var lastPartitionKey = result[^1].PartitionKey;
                var lastRowKey = result[^1].RowKey;
                result = query.Where(FilterAfter<T>(lastPartitionKey, lastRowKey)).ToArray();
                list.AddRange(result);
            }
            return list;
        }

        /// <summary>
        /// Table Storageの制約を意識せずに、クエリの結果を最大件数まで取得します。
        /// </summary>
        /// <typeparam name="T">取得するデータの型</typeparam>
        /// <param name="query">対象のクエリ</param>
        /// <param name="maxRows">結果を取得する最大件数</param>
        /// <returns>取得結果</returns>
        public static IEnumerable<T> GetAll<T>(this IQueryable<T> query, int maxRows)
            where T : ITableEntity
        {
            var list = new List<T>();
            var result = query.ToArray();
            list.AddRange(result);

            while (list.Count < maxRows && result?.Length == MaxRowsPerPagination)
            {
                var lastPartitionKey = result[^1].PartitionKey;
                var lastRowKey = result[^1].RowKey;
                result = query.Where(FilterAfter<T>(lastPartitionKey, lastRowKey)).ToArray();
                list.AddRange(result);
            }
            return (list.Count > maxRows)
                ? list.Take(maxRows)
                : list;
        }

        private static Expression<Func<T, bool>> FilterAfter<T>(string lastPartitionKey, string lastRowKey)
            where T : ITableEntity =>
            entity =>
                entity.PartitionKey.CompareTo(lastPartitionKey) > 0 ||
                (entity.PartitionKey == lastPartitionKey && entity.RowKey.CompareTo(lastRowKey) > 0);
    }
}
