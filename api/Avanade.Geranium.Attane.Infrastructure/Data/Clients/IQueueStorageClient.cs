using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Infrastructure.Data.Clients
{
    /// <summary>
    /// Azure Queue Storage を扱うクライアント
    /// </summary>
    public interface IQueueStorageClient
    {
        /// <summary>
        /// キューにエンティティを追加します。
        /// </summary>
        /// <typeparam name="TEntity">エンティティの型</typeparam>
        /// <param name="queueName">キュー名</param>
        /// <param name="entity">エンティティ</param>
        /// <returns>タスク</returns>
        Task EnqueueAsync<TEntity>(string queueName, TEntity entity);
    }
}
