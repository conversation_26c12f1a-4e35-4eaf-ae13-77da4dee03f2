using Azure;
using Azure.Data.Tables;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Infrastructure.Data.Clients
{

    //
    // 概要:
    //     Represents a single table operation.
    public sealed class TableOperation<ITableEntity>
        where ITableEntity : class
    {

        //
        // 概要:
        //     Gets the entity that is being operated upon.
        public ITableEntity Entity { get; set; }

        //
        // 概要:
        //     Gets the type of operation.
        public TableTransactionActionType ActionType { get; set; }

        internal bool IsTableEntity { get; set; }

        internal bool IsPrimaryOnlyRetrieve { get; set; }

        internal string? RetrievePartitionKey { get; set; }

        internal string? RetrieveRowKey { get; set; }

        internal string PartitionKey
        {
            get
            {
                return PartitionKey;
            }
        }

        internal string RowKey
        {
            get
            {
                return RowKey;
            }
        }
        internal ETag ETag
        {
            get
            {
                _ = ActionType;
                return ETag;
            }
        }

        //
        // 概要:
        //     List of columns to project with for the retrieve operation.
        internal List<string>? SelectColumns { get; set; }

        public TableUpdateMode Mode { get; internal set; }

        public CancellationToken CancellationToken { get; internal set; }
        //
        // Entityがnull以外になるようにするためコンストラクタを追加
        //
        public TableOperation(ITableEntity entity)
        {
            Entity = entity;
        }
        //
        // UserData.cs、BookmarkData.csでTableOperationが必要なため追加
        //
        public TableOperation() : this(null!)
        {
        }
    }
}