using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Avanade.Geranium.Attane.Shared
{
    [JsonConverter(typeof(SearchConditionTree.Converter))]
    public class SearchConditionTree
    {
        [JsonPropertyName("operator")]
        public SearchConditionOperator Operator { get; private init; }
        [JsonPropertyName("operand")]
        public List<SearchConditionTree>? Operand { get; private init; }
        public string? Item { get; private init; }

        public SearchConditionTree() { }

        public SearchConditionTree(SearchConditionOperator searchRequestOperator, IEnumerable<SearchConditionTree> operand)
        {
            this.Operator = searchRequestOperator;
            this.Operand = new List<SearchConditionTree>(operand);
        }

        public SearchConditionTree(string item)
        {
            this.Operator = SearchConditionOperator.Item;
            this.Item = item;
        }

        public class Converter : JsonConverter<SearchConditionTree>
        {
            public override SearchConditionTree? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            {
                if (reader.TokenType == JsonTokenType.String)
                {
                    // 単一の値の場合、SearchConditionTreeに値を渡すことでItemの要素を作成する
                    return new SearchConditionTree(reader.GetString()!);
                }
                else if (reader.TokenType == JsonTokenType.StartObject)
                {
                    SearchConditionOperator? operatorValue = null;
                    List<SearchConditionTree> operandValue = new();

                    // オブジェクトの場合、中身を取得する
                    while (reader.Read())
                    {
                        if (reader.TokenType == JsonTokenType.EndObject)
                        {
                            break;
                        }
                        if (reader.TokenType == JsonTokenType.PropertyName)
                        {
                            var prop = reader.GetString();
                            reader.Read();

                            switch (prop)
                            {
                                case "operator":
                                    if (Enum.TryParse<SearchConditionOperator>(reader.GetString(), out var value))
                                    {
                                        operatorValue = value;
                                    }
                                    else
                                    {
                                        throw new System.Runtime.Serialization.SerializationException("operatorの値がSearchConditionOperatorに含まれていません");
                                    }
                                    break;

                                case "operand":
                                    while (reader.Read())
                                    {
                                        if (reader.TokenType == JsonTokenType.EndArray)
                                        {
                                            break;
                                        }
                                        else if (reader.TokenType == JsonTokenType.String || reader.TokenType == JsonTokenType.StartObject)
                                        {
                                            var operand = Read(ref reader, typeToConvert, options);
                                            if (operand != null)
                                            {
                                                operandValue.Add(operand);
                                            }
                                        }
                                    }
                                    break;
                            }
                        }
                    }

                    // operator, operandともに必須
                    if (
                        operatorValue.HasValue &&
                        operandValue.Any()
                    )
                    {
                        return new SearchConditionTree(operatorValue.Value, operandValue);
                    }
                }
                throw new System.Runtime.Serialization.SerializationException("書式が間違っています");
            }

            public override void Write(Utf8JsonWriter writer, SearchConditionTree value, JsonSerializerOptions options)
            {
                switch (value?.Operator)
                {
                    case SearchConditionOperator.Item:
                        writer.WriteStringValue(value.Item);
                        break;
                    case null:
                        break;
                    default:
                        writer.WriteStartObject();

                        writer.WritePropertyName("operator");
                        writer.WriteStringValue(Enum.GetName(typeof(SearchConditionOperator), value.Operator));

                        writer.WritePropertyName("operand");
                        writer.WriteStartArray();
                        value.Operand?.ForEach(operand => Write(writer, operand, options));
                        writer.WriteEndArray();

                        writer.WriteEndObject();
                        break;
                }
            }
        }
    }
}
