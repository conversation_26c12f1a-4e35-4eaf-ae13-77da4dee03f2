using System.Text.Json.Serialization;

namespace Avanade.Geranium.Attane.Shared
{
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum SearchState
    {
        /// <summary>
        /// 検索中
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// 完了
        /// </summary>
        Completed,

        /// <summary>
        /// エラー
        /// </summary>
        /// <remarks>
        /// 検索のどこかでエラーが発生した場合。他の部分は正常に終了している可能性があり、結果もその部分については信用できる。
        /// </remarks>
        Error,

        /// <summary>
        /// キャンセル
        /// </summary>
        /// <remarks>
        /// 検索のどこかで中断/キャンセルされた場合。中断時点で登録されている結果については信用できる。
        /// </remarks>
        Cancelled,
    }
}
