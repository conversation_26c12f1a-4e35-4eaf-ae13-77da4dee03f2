/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the ユーザー entity.
    /// </summary>
    public partial class User
    {
        /// <summary>
        /// Gets or sets the 対象のユーザーID.
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Gets or sets the コンテキスト.
        /// </summary>
        public Dictionary<string, string>? Context { get; set; }
    }

    /// <summary>
    /// Represents the <see cref="User"/> collection.
    /// </summary>
    public partial class UserCollection : List<User> { }

    /// <summary>
    /// Represents the <see cref="User"/> collection result.
    /// </summary>
    public class UserCollectionResult : CollectionResult<UserCollection, User>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollectionResult"/> class.
        /// </summary>
        public UserCollectionResult() { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollectionResult"/> class with <paramref name="paging"/>.
        /// </summary>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public UserCollectionResult(PagingArgs? paging) : base(paging) { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollectionResult"/> class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public UserCollectionResult(IEnumerable<User> items, PagingArgs? paging = null) : base(paging) => Items.AddRange(items);
    }
}

#pragma warning restore
#nullable restore