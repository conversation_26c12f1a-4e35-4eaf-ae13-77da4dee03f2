/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the 検索要求 entity.
    /// </summary>
    public partial class SearchRequest : IPrimaryKey
    {
        /// <summary>
        /// Gets or sets the 検索ID.
        /// </summary>
        public Guid ReqId { get; set; }

        /// <summary>
        /// Gets or sets the 検索条件.
        /// </summary>
        public string? Condition { get; set; }

        /// <summary>
        /// Gets or sets the 対象のユーザーID.
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Gets or sets the 検索結果に関連する状態.
        /// </summary>
        public Dictionary<string, string>? Context { get; set; }

        /// <summary>
        /// Gets or sets the 解析済検索条件.
        /// </summary>
        [JsonIgnore]
        public Avanade.Geranium.Attane.Shared.SearchConditionTree? ParsedCondition { get; set; }
        
        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The primary <see cref="CompositeKey"/>.</returns>
        /// <param name="reqId">The <see cref="ReqId"/>.</param>
        public static CompositeKey CreatePrimaryKey(Guid reqId) => new CompositeKey(reqId);

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): <see cref="ReqId"/>).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey(ReqId);
    }

    /// <summary>
    /// Represents the <see cref="SearchRequest"/> collection.
    /// </summary>
    public partial class SearchRequestCollection : List<SearchRequest> { }

    /// <summary>
    /// Represents the <see cref="SearchRequest"/> collection result.
    /// </summary>
    public class SearchRequestCollectionResult : CollectionResult<SearchRequestCollection, SearchRequest>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollectionResult"/> class.
        /// </summary>
        public SearchRequestCollectionResult() { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollectionResult"/> class with <paramref name="paging"/>.
        /// </summary>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public SearchRequestCollectionResult(PagingArgs? paging) : base(paging) { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollectionResult"/> class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public SearchRequestCollectionResult(IEnumerable<SearchRequest> items, PagingArgs? paging = null) : base(paging) => Items.AddRange(items);
    }
}

#pragma warning restore
#nullable restore