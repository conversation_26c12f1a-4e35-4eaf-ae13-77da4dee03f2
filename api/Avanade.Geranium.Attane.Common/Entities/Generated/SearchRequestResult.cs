/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the 検索要求の結果 entity.
    /// </summary>
    public partial class SearchRequestResult : SearchRequest
    {
        /// <summary>
        /// Gets or sets the トークン分割した検索条件.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.SearchConditionTree? ConditionKeywords { get; set; }

        /// <summary>
        /// Gets or sets the 検索要求の状態.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.SearchState? State { get; set; }

        /// <summary>
        /// Gets or sets the 検索結果.
        /// </summary>
        public SearchProcessRequestResult[]? Results { get; set; }
    }
}

#pragma warning restore
#nullable restore