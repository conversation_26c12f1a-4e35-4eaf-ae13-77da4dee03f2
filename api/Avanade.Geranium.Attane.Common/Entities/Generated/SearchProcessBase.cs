/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the 検索プロセス entity.
    /// </summary>
    public partial class SearchProcessBase
    {
        /// <summary>
        /// Gets or sets the 対象のユーザーID.
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Gets or sets the 検索ID.
        /// </summary>
        public Guid ReqId { get; set; }
    }
}

#pragma warning restore
#nullable restore