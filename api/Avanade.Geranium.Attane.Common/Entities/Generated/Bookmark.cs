/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the お気に入り entity.
    /// </summary>
    public partial class Bookmark : IPrimaryKey
    {
        /// <summary>
        /// Gets or sets the User Id.
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// Gets or sets the データソースの種類.
        /// </summary>
        public string Kind { get; set; }

        /// <summary>
        /// Gets or sets the 個々のデータソースを特定する属性.
        /// </summary>
        public string Properties { get; set; }

        /// <summary>
        /// Gets or sets the Id.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the Title.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the Note.
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Gets or sets the Display Date.
        /// </summary>
        public DateTime DisplayDate { get; set; }

        /// <summary>
        /// Gets or sets the Repos Created Date.
        /// </summary>
        public DateTime ReposCreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the Repos Updated Date.
        /// </summary>
        public DateTime ReposUpdatedDate { get; set; }

        /// <summary>
        /// Gets or sets the Time Stamp.
        /// </summary>
        public DateTime? TimeStamp { get; set; }
        
        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The primary <see cref="CompositeKey"/>.</returns>
        /// <param name="userId">The <see cref="UserId"/>.</param>
        /// <param name="id">The <see cref="Id"/>.</param>
        public static CompositeKey CreatePrimaryKey(string userId, string id) => new CompositeKey(userId, id);

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): <see cref="UserId"/>, <see cref="Id"/>).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey(UserId, Id);
    }
}

#pragma warning restore
#nullable restore