/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the データソース entity.
    /// </summary>
    public partial class DataSource
    {
        /// <summary>
        /// Gets or sets the データソースの種類.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.DataSourceKind? Kind { get; set; }

        /// <summary>
        /// Gets or sets the データソースを特定する属性.
        /// </summary>
        public Dictionary<string, string>? Properties { get; set; }
    }
}

#pragma warning restore
#nullable restore