/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the 検索結果要求 entity.
    /// </summary>
    public partial class SearchResultRequest
    {
        /// <summary>
        /// Gets or sets the 検索ID.
        /// </summary>
        public Guid ReqId { get; set; }

        /// <summary>
        /// Gets or sets the 既知の検索処理ID.
        /// </summary>
        public string[]? KnownPids { get; set; }
    }
}

#pragma warning restore
#nullable restore