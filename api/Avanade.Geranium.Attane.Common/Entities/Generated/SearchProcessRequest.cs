/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using CoreEx.Entities;

namespace Avanade.Geranium.Attane.Common.Entities
{
    /// <summary>
    /// Represents the 単一のデータソースに対する検索要求 entity.
    /// </summary>
    public partial class SearchProcessRequest : SearchProcessBase, IPrimaryKey
    {
        /// <summary>
        /// Gets or sets the 検索処理ID.
        /// </summary>
        public Guid Pid { get; set; }

        /// <summary>
        /// Gets or sets the データソースの情報.
        /// </summary>
        public DataSources? DataSources { get; set; }
        
        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The primary <see cref="CompositeKey"/>.</returns>
        /// <param name="pid">The <see cref="Pid"/>.</param>
        public static CompositeKey CreatePrimaryKey(Guid pid) => new CompositeKey(pid);

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): <see cref="Pid"/>).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey(Pid);
    }
}

#pragma warning restore
#nullable restore