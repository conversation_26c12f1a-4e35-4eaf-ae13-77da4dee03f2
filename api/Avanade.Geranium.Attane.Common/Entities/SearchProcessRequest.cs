﻿using System;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Text;

namespace Avanade.Geranium.Attane.Common.Entities
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S4035:Classes implementing \"IEquatable<T>\" should be sealed", Justification = "自動生成のため")]
    partial class SearchProcessRequest
    {
        public static SearchProcessRequest FromDataSource(string userId, Guid reqId, DataSources dataSources) =>
            new()
            {
                UserId = userId,
                ReqId = reqId,
                Pid = Guid.NewGuid(),
                DataSources = dataSources,
            };
    }
}
