/*
 * This file is automatically generated; any changes will be lost.
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoreEx.Configuration;
using CoreEx.Entities;
using CoreEx.Http;
using CoreEx.Json;
using Microsoft.Extensions.Logging;
using Avanade.Geranium.Attane.Common.Entities;
using RefDataNamespace = Avanade.Geranium.Attane.Common.Entities;

namespace Avanade.Geranium.Attane.Common.Agents
{
    /// <summary>
    /// Defines the <see cref="SearchRequest"/> HTTP agent.
    /// </summary>
    public partial interface ISearchRequestAgent
    {
        /// <summary>
        /// 与えられたUserIdに対応する検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<SearchRequestResult?>> GetAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果について、既知の結果を除いたものを取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The 検索結果要求.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<SearchRequestResult?>> GetWithAsync(string userId, SearchResultRequest? request, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索要求を登録します.
        /// </summary>
        /// <param name="value">The <see cref="SearchRequest"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<SearchRequest>> RegisterAsync(SearchRequest value, string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索をキャンセルします.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<string?>> CancellAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<System.Collections.Generic.Dictionary<string, string>?>> GetContextAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Dictionary{string, string}"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<System.Collections.Generic.Dictionary<string, string>>> UpdateContextAsync(Dictionary<string, string> value, string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult> DeleteContextAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<string?>> GetContextFieldAsync(string userId, string fieldName, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="value">The <see cref="string"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<string?>> UpdateContextFieldAsync(string value, string userId, string fieldName, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult> DeleteContextFieldAsync(string userId, string fieldName, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Provides the <see cref="SearchRequest"/> HTTP agent.
    /// </summary>
    public partial class SearchRequestAgent : TypedHttpClientBase<SearchRequestAgent>, ISearchRequestAgent
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestAgent"/> class.
        /// </summary>
        /// <param name="client">The underlying <see cref="HttpClient"/>.</param>
        /// <param name="jsonSerializer">The <see cref="IJsonSerializer"/>.</param>
        /// <param name="executionContext">The <see cref="CoreEx.ExecutionContext"/>.</param>
        /// <param name="settings">The <see cref="SettingsBase"/>.</param>
        /// <param name="logger">The <see cref="ILogger"/>.</param>
        public SearchRequestAgent(HttpClient client, IJsonSerializer jsonSerializer, CoreEx.ExecutionContext executionContext, SettingsBase settings, ILogger<SearchRequestAgent> logger) 
            : base(client, jsonSerializer, executionContext, settings, logger) { }

        /// <summary>
        /// 与えられたUserIdに対応する検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<SearchRequestResult?>> GetAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => GetAsync<SearchRequestResult?>("users/{userId}/search/result", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果について、既知の結果を除いたものを取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The 検索結果要求.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<SearchRequestResult?>> GetWithAsync(string userId, SearchResultRequest? request, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => PostAsync<SearchRequestResult?>("users/{userId}/search/result", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId), new HttpArg<SearchResultRequest?>("request", request, HttpArgType.FromBody)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索要求を登録します.
        /// </summary>
        /// <param name="value">The <see cref="SearchRequest"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<SearchRequest>> RegisterAsync(SearchRequest value, string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => PostAsync<SearchRequest, SearchRequest>("users/{userId}/search", value, requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索をキャンセルします.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<string?>> CancellAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => PostAsync<string?>("users/{userId}/search/cancellation", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<System.Collections.Generic.Dictionary<string, string>?>> GetContextAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => GetAsync<System.Collections.Generic.Dictionary<string, string>?>("users/{userId}/search/context", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Dictionary{string, string}"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<System.Collections.Generic.Dictionary<string, string>>> UpdateContextAsync(Dictionary<string, string> value, string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => PutAsync<Dictionary<string, string>, System.Collections.Generic.Dictionary<string, string>>("users/{userId}/search/context", value, requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult> DeleteContextAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => DeleteAsync("users/{userId}/search/context", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<string?>> GetContextFieldAsync(string userId, string fieldName, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => GetAsync<string?>("users/{userId}/search/context/{fieldName}", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId), new HttpArg<string>("fieldName", fieldName)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="value">The <see cref="string"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<string?>> UpdateContextFieldAsync(string value, string userId, string fieldName, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => PutAsync<string, string?>("users/{userId}/search/context/{fieldName}", value, requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId), new HttpArg<string>("fieldName", fieldName)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult> DeleteContextFieldAsync(string userId, string fieldName, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => DeleteAsync("users/{userId}/search/context/{fieldName}", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId), new HttpArg<string>("fieldName", fieldName)), cancellationToken: cancellationToken);
    }
}

#pragma warning restore
#nullable restore