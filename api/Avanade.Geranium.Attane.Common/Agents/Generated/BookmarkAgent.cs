/*
 * This file is automatically generated; any changes will be lost.
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoreEx.Configuration;
using CoreEx.Entities;
using CoreEx.Http;
using CoreEx.Json;
using Microsoft.Extensions.Logging;
using Avanade.Geranium.Attane.Common.Entities;
using RefDataNamespace = Avanade.Geranium.Attane.Common.Entities;

namespace Avanade.Geranium.Attane.Common.Agents
{
    /// <summary>
    /// Defines the <see cref="Bookmark"/> HTTP agent.
    /// </summary>
    public partial interface IBookmarkAgent
    {
        /// <summary>
        /// 与えられたUserIdに対応する<see cref="Bookmark"/>の配列を取得します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<Bookmark[]?>> GetBookmarksAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// <see cref="Bookmark"/>を登録し、既に存在している場合は内容を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Bookmark"/>.</param>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult<Bookmark>> PutBookmarkAsync(Bookmark value, string userId, string articleId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 与えられたキーに対応する<see cref="Bookmark"/>を削除します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        Task<HttpResult> DeleteBookmarkAsync(string userId, string articleId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Provides the <see cref="Bookmark"/> HTTP agent.
    /// </summary>
    public partial class BookmarkAgent : TypedHttpClientBase<BookmarkAgent>, IBookmarkAgent
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BookmarkAgent"/> class.
        /// </summary>
        /// <param name="client">The underlying <see cref="HttpClient"/>.</param>
        /// <param name="jsonSerializer">The <see cref="IJsonSerializer"/>.</param>
        /// <param name="executionContext">The <see cref="CoreEx.ExecutionContext"/>.</param>
        /// <param name="settings">The <see cref="SettingsBase"/>.</param>
        /// <param name="logger">The <see cref="ILogger"/>.</param>
        public BookmarkAgent(HttpClient client, IJsonSerializer jsonSerializer, CoreEx.ExecutionContext executionContext, SettingsBase settings, ILogger<BookmarkAgent> logger) 
            : base(client, jsonSerializer, executionContext, settings, logger) { }

        /// <summary>
        /// 与えられたUserIdに対応する<see cref="Bookmark"/>の配列を取得します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<Bookmark[]?>> GetBookmarksAsync(string userId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => GetAsync<Bookmark[]?>("users/{userId}/bookmarks", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId)), cancellationToken: cancellationToken);

        /// <summary>
        /// <see cref="Bookmark"/>を登録し、既に存在している場合は内容を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Bookmark"/>.</param>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult<Bookmark>> PutBookmarkAsync(Bookmark value, string userId, string articleId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => PutAsync<Bookmark, Bookmark>("users/{userId}/bookmarks/{articleId}", value, requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId), new HttpArg<string>("articleId", articleId)), cancellationToken: cancellationToken);

        /// <summary>
        /// 与えられたキーに対応する<see cref="Bookmark"/>を削除します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public Task<HttpResult> DeleteBookmarkAsync(string userId, string articleId, HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => DeleteAsync("users/{userId}/bookmarks/{articleId}", requestOptions: requestOptions, args: HttpArgs.Create(new HttpArg<string>("userId", userId), new HttpArg<string>("articleId", articleId)), cancellationToken: cancellationToken);
    }
}

#pragma warning restore
#nullable restore