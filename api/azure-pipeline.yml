pool:
  name: Azure Pipelines
  demands: npm
  vmImage: windows-latest

# CIの実行トリガー
# mainブランチのapi階層以下が更新されたときにだけトリガー
trigger:
  branches:
    include:
    - main
  paths:
    include:
    - api
    exclude:
    - api/.vs
    - api/.vscode

variables:
  BuildConfiguration: Release
  NUGET_PACKAGES: $(Pipeline.Workspace)/.nuget/packages

steps:
  # Cache
  - task: Cache@2
    displayName: Cache
    inputs:
      key: 'nuget-20230321 | "$(Agent.OS)" | **/packages.lock.json'
      path: '$(NUGET_PACKAGES)'
      cacheHitVar: 'CACHE_RESTORED'
  # NuGet
  - task: DotNetCoreCLI@2
    displayName: Restore
    condition: ne(variables.CACHE_RESTORED, true)
    inputs:
      command: 'restore'
      projects: 'api/**/*.csproj'
      feedsToUse: 'config'
      nugetConfigPath: 'api/nuget.config'

  # buildコマンドを実施
  - task: DotNetCoreCLI@2
    displayName: Build
    inputs:
      projects: 'api/**/*.csproj'
      arguments: '--configuration $(BuildConfiguration) -flp1:warningsonly;append;logfile=warnings.txt'

  # 警告をチェック
  - powershell: |
      $warnings = Test-Path $env:System_DefaultWorkingDirectory/warnings.txt
      if ($warnings) {
         Write-Host "##vso[task.complete result=SucceededWithIssues;]There are build warnings"
      }

    displayName: 'Build warnings check'

  # testコマンドを実施
  - task: DotNetCoreCLI@2
    displayName: Test
    inputs:
      command: test
      projects: 'api/**/*.Test.csproj'
      arguments: '--configuration $(BuildConfiguration) --collect:"XPlat Code coverage"'

  # テストリポート
  - task: Palmmedia.reportgenerator.reportgenerator-build-release-task.reportgenerator@5
    displayName: ReportGenerator
    inputs:
      reports: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'
      targetdir: '$(System.DefaultWorkingDirectory)\TestResults\Coverage\Reports'

  # コードカバレッジをpublish
  - task: PublishCodeCoverageResults@1
    displayName: 'Publish code coverage from $(System.DefaultWorkingDirectory)\TestResults\Coverage\Reports\Cobertura.xml'
    inputs:
      codeCoverageTool: Cobertura
      # summaryFileLocation: '$(System.DefaultWorkingDirectory)\TestResults\Coverage\Reports\Cobertura.xml'
      summaryFileLocation: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'

  # publishコマンドを実施
  - task: DotNetCoreCLI@2
    displayName: Publish
    inputs:
      command: publish
      publishWebProjects: false
      projects: api/Avanade.Geranium.Attane.Api/Avanade.Geranium.Attane.Api.csproj
      arguments: ' --configuration $(BuildConfiguration) --output $(Build.ArtifactStagingDirectory)'
      zipAfterPublish: true

  # APIのパイプラインのAritifactを展開
  - task: ExtractFiles@1
    inputs:
      archiveFilePatterns: $(Build.ArtifactStagingDirectory)/Avanade.Geranium.Attane.Api.zip
      destinationFolder: '$(Pipeline.Workspace)/temp'
      cleanDestinationFolder: true
      overwriteExistingFiles: false

  # api/tempディレクトリから不要ファイルを削除
  - task: DeleteFiles@1
    inputs:
      SourceFolder: $(Pipeline.Workspace)/temp
      Contents: |
        appsettings.Local.json

  # apiのモジュールをzip圧縮
  - task: ArchiveFiles@2
    displayName: 'tempをアーカイブ'
    inputs:
      rootFolderOrFile: $(Pipeline.Workspace)/temp
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/mec/$(Build.BuildId).zip'

  # アーティファクトをpublish
  - task: PublishPipelineArtifact@1
    displayName: 'Publish Pipeline Artifact'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)'
      artifact: drop

