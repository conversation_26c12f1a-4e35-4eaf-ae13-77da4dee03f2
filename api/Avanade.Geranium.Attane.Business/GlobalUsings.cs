﻿global using CoreEx;
global using CoreEx.Caching;
global using CoreEx.Configuration;
global using CoreEx.Entities;
global using CoreEx.Entities.Extended;
global using CoreEx.Events;
global using CoreEx.Http;
global using CoreEx.Http.Extended;
global using CoreEx.Invokers;
global using CoreEx.Json;
global using CoreEx.Mapping;
global using CoreEx.Mapping.Converters;
global using CoreEx.RefData;
global using CoreEx.RefData.Extended;
global using CoreEx.Validation;
global using CoreEx.Validation.Rules;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.Logging;
global using System;
global using System.Collections.Generic;
global using System.Diagnostics;
global using System.Linq;
global using System.Text.Json.Serialization;
global using System.Text.RegularExpressions;
global using System.Net.Http;
global using System.Threading;
global using System.Threading.Tasks;
global using Avanade.Geranium.Attane.Business;
global using Avanade.Geranium.Attane.Business.Entities;
global using Avanade.Geranium.Attane.Business.Data;
global using Avanade.Geranium.Attane.Business.DataSvc;
global using Avanade.Geranium.Attane.Business.Validation;
global using RefDataNamespace = Avanade.Geranium.Attane.Business.Entities;