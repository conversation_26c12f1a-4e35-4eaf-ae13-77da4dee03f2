using Avanade.Geranium.Attane.Business.Entities;
using CoreEx.Validation;
using System;

namespace Avanade.Geranium.Attane.Business.Validation
{
    /// <summary>
    /// Bookmark EntityのValidator <see cref="Bookmark">
    /// </summary>
    public class BookmarkValidator : Validator<Bookmark>
    {
        // Table Storageが処理できるDateTimeの最小値
        private static readonly DateTime ValidMinimum = new(1601, 1, 1);

        public BookmarkValidator()
        {
            Property(x => x.UserId).Mandatory();
            Property(x => x.Kind).Mandatory();
            Property(x => x.Id).Mandatory();
            Property(x => x.DisplayDate).CompareValue(CompareOperator.GreaterThanEqual, ValidMinimum);
            Property(x => x.ReposCreatedDate).CompareValue(CompareOperator.GreaterThanEqual, ValidMinimum);
            Property(x => x.ReposUpdatedDate).CompareValue(CompareOperator.GreaterThanEqual, ValidMinimum);
        }
    }
}
