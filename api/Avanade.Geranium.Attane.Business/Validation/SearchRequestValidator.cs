using Avanade.Geranium.Attane.Business.Configuration;
using Avanade.Geranium.Attane.Business.Entities;
using CoreEx.Validation;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Avanade.Geranium.Attane.Business.Validation
{
    /// <summary>
    /// <see cref="SearchRequest"/> の Validator
    /// </summary>
    public class SearchRequestValidator : Validator<SearchRequest>
    {
        private const int MaxKeywordCount = 100;

        public SearchRequestValidator()
        {
            Property(x => x.Condition)
                .Mandatory()
                .String(minLength: 1, maxLength: 1000)
                .Custom(ValidateKeywordsCount)
                .Custom(ValidateConditions);
        }

        private void ValidateKeywordsCount(PropertyContext<SearchRequest, string> context)
        {
            var value = context.Value;
            var keywords = value?.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (keywords?.Length > MaxKeywordCount)
            {
                context.CreateErrorMessage($"キーワードの数は{MaxKeywordCount}以下でなければなりません");
            }
        }

        private void ValidateConditions(PropertyContext<SearchRequest, string> context)
        {
            var value = context.Value;
            try
            {
                // 結果を設定することも可能だが、Validatorは状態を変えないようにする
                // context.Parent.Value.ParsedCondition = SearchConditionParser.Parse(value!);
                SearchConditionParser.Parse(value!);
            }
            catch (SyntaxErrorException ex)
            {
                context.CreateErrorMessage(ex.Message);
            }
        }
    }
}
