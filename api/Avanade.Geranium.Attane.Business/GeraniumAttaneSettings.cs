using Avanade.Geranium.Attane.Business.Configuration;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Configuration;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Avanade.Geranium.Attane.Business;

/// <summary>
/// Provides the <see cref="IConfiguration"/> settings.
/// </summary>
public class GeraniumAttaneSettings : SettingsBase
{
    /// <summary>
    /// Gets the setting prefixes in order of precedence.
    /// </summary>
    public static string[] Prefixes { get; } = { "Geranium.Attane/", "Common/" };


    /// <summary>
    /// Initializes a new instance of the <see cref="Geranium.AttaneSettings"/> class.
    /// </summary>
    /// <param name="configuration">The <see cref="IConfiguration"/>.</param>
    public GeraniumAttaneSettings(IConfiguration configuration) : base(configuration, Prefixes) => ValidationArgs.DefaultUseJsonNames = true;

    /// <summary>Service Busの接続文字列</summary>
    public string ServiceBusConnection => GetValue(defaultValue: ServiceBus__ConnectionString!);

    /// <summary>Service Busのキュー名</summary>
    public string QueueName => GetValue(defaultValue: Geranium__Search__QueueName);

    /// <summary>Storage Accountの接続文字列</summary>
    public string? StorageAccountConnection => GetValue(defaultValue: StorageAccount__ConnectionString);

    /// <summary>Application Insightsの接続文字列</summary>
    public string? AppInsightsConnection => GetValue(defaultValue: ApplicationInsights__ConnectionString);

    #region 構成ファイル内の実装
    // Service Busの接続文字列
    private string? ServiceBus__ConnectionString => GetValue<string?>();

    // Service Busのキュー名
    private string Geranium__Search__QueueName => GetValue(defaultValue: "search-process");

    // Storage Accountの接続文字列
    private string StorageAccount__ConnectionString => GetValue<string>();

    // Application Insightsの接続文字列
    private string? ApplicationInsights__ConnectionString => GetValue<string?>();
    #endregion
}
