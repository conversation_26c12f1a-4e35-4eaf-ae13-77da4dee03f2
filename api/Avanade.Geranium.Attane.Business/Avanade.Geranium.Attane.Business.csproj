﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
     <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="CoreEx" Version="2.10.1" />
    <PackageReference Include="CoreEx.Validation" Version="2.10.1" />
    <PackageReference Include="Zipangu" Version="1.1.8" />
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
    <PackageReference Include="Azure.Search.Documents" Version="11.7.0-beta.1" />
    <PackageReference Include="Azure.Identity" Version="1.13.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Avanade.Geranium.Attane.Infrastructure\Avanade.Geranium.Attane.Infrastructure.csproj" />
    <ProjectReference Include="..\Avanade.Geranium.Attane.Shared\Avanade.Geranium.Attane.Shared.csproj" />
  </ItemGroup>
</Project>
