using System;
using System.Runtime.Serialization;

namespace Avanade.Geranium.Attane.Business.Exceptions
{
    /// <summary>
    /// 削除しようとしたお気に入りは既に削除されている
    /// </summary>
    [Serializable]
    public class BookmarkDeleteException : Exception
    {
        /// <summary>
        /// <see cref="BookmarkDeleteException"/> クラスの新しいインスタンスを作成します。
        /// </summary>
        public BookmarkDeleteException() { }

        public BookmarkDeleteException(string? message) : base(message)
        {
        }

        public BookmarkDeleteException(string? message, Exception? innerException) : base(message, innerException)
        {
        }
    }
}
