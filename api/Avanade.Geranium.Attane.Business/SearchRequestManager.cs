using Avanade.Geranium.Attane.Shared;

namespace Avanade.Geranium.Attane.Business
{
    partial class SearchRequestManager
    {
        public Task<SearchRequestResult?> GetOnImplementationAsync(string userId) =>
            GetWithOnImplementationAsync(userId, null);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果について、既知の結果を除いたものを取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The 検索結果要求.</param>
        /// <returns>A resultant <see cref="SearchRequestResult"/>.</returns>
        /// <remarks>
        /// <see cref="SearchRequestManager.GetWithAsync(string, SearchResultRequest?)"/> の実装
        /// </remarks>
        public async Task<SearchRequestResult?> GetWithOnImplementationAsync(string userId, SearchResultRequest? request)
        {
            var currentRequest = await _dataService.GetRequestAsync(userId).ConfigureAwait(false);
            if (currentRequest == null)
            {
                // リクエストが見つからなかったら終了
                return null;
            }
            var resultForSources = (await _dataService.GetResultsAsync(userId).ConfigureAwait(false)).ToArray();

            var state = currentRequest.State switch
            {
                SearchState.InProgress => resultForSources.Count(r => !r.HasNext) == currentRequest.ProcessCount ?
                    SearchState.Completed : currentRequest.State,
                _ => currentRequest.State,
            };

            var filteredResult = (currentRequest.ReqId == request?.ReqId && request.KnownPids != null)
                ? resultForSources.ExceptBy(request.KnownPids, r => r.Pid)
                : resultForSources;

            var result = new SearchRequestResult(currentRequest, state, filteredResult);
            return result;
        }

        /// <summary>
        /// 与えられたUserIdに対応する検索要求を登録します.
        /// </summary>
        /// <param name="value">The <see cref="SearchRequest"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="tokens">The リクエストのトークン.</param>
        /// <returns>The created <see cref="SearchRequest"/>.</returns>
        /// <remarks>
        /// <see cref="SearchRequestManager.RegisterAsync(SearchRequest, string, Task{Dictionary{string, string}})"/>の実装
        /// </remarks>
        public async Task<SearchRequest> RegisterOnImplementationAsync(SearchRequest searchRequest, string userId, Task<Dictionary<string, string>> tokens)
        {
            var result = await searchRequest.Validate().Entity().With<SearchRequestValidator>().ValidateAsync();
            result.ThrowOnError();
            // Validate時点でもParseしているが、わかりやすさのために明示的に処理を分離している
            searchRequest.ParsedCondition = SearchConditionParser.Parse(searchRequest.Condition!);

            await _dataService.DeleteRequestAsync(userId).ConfigureAwait(false);
            if (searchRequest.ReqId == Guid.Empty)
            {
                searchRequest.ReqId = await _identifierGenerator.GenerateIdentifierAsync<Guid, SearchRequest>().ConfigureAwait(false);
            }

            var internalSearchRequest = new InternalSearchRequest(searchRequest, await tokens.ConfigureAwait(false))
            {
                ProcessCount = _configuration.Value.DataSources?.Aggregate(0, (prev, source) => prev + (source.Properties?.Length ?? 0)) ?? 0,
                State = SearchState.InProgress,
                ConditionKeywords = searchRequest.ParsedCondition,
            };

            await _dataService.CreateOrUpdateRequestAsync(userId, internalSearchRequest).ConfigureAwait(false);
            if (_configuration.Value.DataSources != null)
            {
                // 	appsettings.jsonのDataSources配列をループし、それぞれのデータソース（例:Chat,Mail,SPO)を検索要求（SearchRequest）に適用している。
                await Task.WhenAll(_configuration.Value.DataSources.Select(dataSource =>
                    // SearchProcessRequest.FromDataSourceがそれぞれのkind(例: Chat, Mail, SPO)を設定する。
                    _dataService.CreateProcessAsync(userId, SearchProcessRequest.FromDataSource(userId, searchRequest.ReqId, dataSource))
                )).ConfigureAwait(false);
                await _evtPub.SendAsync().ConfigureAwait(false);
            }
            return searchRequest;
        }

        /// <summary>
        /// 与えられたUserIdに対応する検索のStateをSearchState.Cancelledに更新します.
        /// </summary>
        public async Task<string?> CancellOnImplementationAsync(string userId)
        {
            var request = await _dataService.GetRequestAsync(userId).ConfigureAwait(false)
                ?? throw new NotFoundException();
            request.State = SearchState.Cancelled;
            await _dataService.CreateOrUpdateRequestAsync(userId, request);
            return "Cancelled";
        }

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="Dictionary{string, string}"/> where found.</returns>
        /// <remarks>
        /// <see cref="SearchRequestManager.GetContextAsync(string)"/> の実装
        /// </remarks>
        public async Task<Dictionary<string, string>?> GetContextOnImplementationAsync(string userId) =>
            (await _dataService.GetRequestAsync(userId).ConfigureAwait(false))?.Context;

        public async Task<Dictionary<string, string>> UpdateContextOnImplementationAsync(Dictionary<string, string> value, string userId)
        {
            var request = await _dataService.GetRequestAsync(userId).ConfigureAwait(false)
                ?? throw new NotFoundException();
            request.Context = value;

            await _dataService.CreateOrUpdateRequestAsync(userId, request);
            return value;
        }

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <remarks>
        /// <see cref="SearchRequestManager.DeleteContextAsync(string)"/> の実装
        /// </remarks>
        public async Task DeleteContextOnImplementationAsync(string userId)
        {
            var request = await _dataService.GetRequestAsync(userId).ConfigureAwait(false);
            if (request?.Context == null)
            {
                return;
            }
            request.Context = null;
            await _dataService.CreateOrUpdateRequestAsync(userId, request);
        }

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The selected <see cref="string"/> where found.</returns>
        /// <remarks>
        /// <see cref="SearchRequestManager.GetContextFieldAsync(string, string)"/> の実装
        /// </remarks>
        public async Task<string?> GetContextFieldOnImplementationAsync(string userId, string fieldName)
        {
            var request = await _dataService.GetRequestAsync(userId).ConfigureAwait(false);
            return (request?.Context?.TryGetValue(fieldName, out var value) ?? false) ? value : null;
        }

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="value">The <see cref="string"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The updated <see cref="string"/>.</returns>
        /// <exception cref="NotFoundException">検索要求が存在しません</exception>
        /// <remarks>
        /// <see cref="UpdateContextFieldAsync(string, string, string)"/> の実装
        /// </remarks>
        public async Task<string?> UpdateContextFieldOnImplementationAsync(string value, string userId, string fieldName)
        {
            var request = await _dataService.GetRequestAsync(userId).ConfigureAwait(false)
                ?? throw new NotFoundException();
            request.Context ??= new Dictionary<string, string>();
            request.Context[fieldName] = value;
            await _dataService.CreateOrUpdateRequestAsync(userId, request);
            return value;
        }

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <remarks>
        /// <see cref="DeleteContextFieldAsync(string, string)"/> の実装
        /// </remarks>
        public async Task DeleteContextFieldOnImplementationAsync(string userId, string fieldName)
        {
            var request = await _dataService.GetRequestAsync(userId).ConfigureAwait(false);
            if (request?.Context?.ContainsKey(fieldName) ?? false)
            {
                request.Context.Remove(fieldName);
                await _dataService.CreateOrUpdateRequestAsync(userId, request);
            }
        }

        /// <summary>
        /// 検索キーワードを抽出します。
        /// </summary>
        /// <param name="condition">検索条件文字列</param>
        /// <returns>キーワードの配列</returns>
        /// <remarks>
        /// 検索の効率化とキーワードハイライトの都合のため、重複するキーワードは除去されます。
        /// 重複の比較の差異は、大文字・小文字は同一視
        /// </remarks>
        /// <example>
        ///     <ul>
        ///         <li>Tokenize("abc ab") => ["abc"]</li>
        ///     </ul>
        /// </example>
        public static string[] Tokenize(string condition)
        {
            var tokens = condition.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (tokens == null)
            {
                return Array.Empty<string>();
            }
            var normalizedTokens = tokens.Select(token => Zipangu.CharConversion.Convert(token.ToLower(), Zipangu.KanaConv.HalfKatakanaToKatakana, Zipangu.AsciiConv.ToNarrow)).ToArray();
            if (normalizedTokens == null)
            {
                return [];
            }

            return tokens.Where((token, i) =>
            {
                var normalizedToken = normalizedTokens[i];
                for (var j = 0; j < tokens.Length; j++)
                {
                    if (j == i)
                    {
                        // 自分自身とは比較しない
                        continue;
                    }

                    if (normalizedToken == normalizedTokens[j])
                    {
                        // 自分より前のトークンと同じ内容ならスキップ
                        if (j < i)
                        {
                            return false;
                        }
                    }
                    else if (normalizedTokens[j].Contains(normalizedToken))
                    {
                        // 他のトークンと同一の内容でなく、他のトークンに含まれるならスキップ
                        return false;
                    }
                }
                return true;
            }).ToArray();
        }
    }
}
