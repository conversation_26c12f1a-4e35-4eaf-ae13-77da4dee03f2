/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.DataSvc
{
    /// <summary>
    /// Provides the <see cref="Bookmark"/> data repository services.
    /// </summary>
    public partial class BookmarkDataSvc : IBookmarkDataSvc
    {
        private readonly IBookmarkData _data;
        private readonly IRequestCache _cache;
        private readonly Microsoft.Extensions.Logging.ILogger<BookmarkDataSvc> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="BookmarkDataSvc"/> class.
        /// </summary>
        /// <param name="data">The <see cref="IBookmarkData"/>.</param>
        /// <param name="cache">The <see cref="IRequestCache"/>.</param>
        /// <param name="logger">The <see cref="Microsoft.Extensions.Logging.ILogger{BookmarkDataSvc}"/>.</param>
        public BookmarkDataSvc(IBookmarkData data, IRequestCache cache, Microsoft.Extensions.Logging.ILogger<BookmarkDataSvc> logger)
            { _data = data ?? throw new ArgumentNullException(nameof(data)); _cache = cache ?? throw new ArgumentNullException(nameof(cache)); _logger = logger ?? throw new ArgumentNullException(nameof(logger)); BookmarkDataSvcCtor(); }

        partial void BookmarkDataSvcCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 与えられたUserIdに対応する<see cref="Bookmark"/>の配列を取得します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <returns>The selected <see cref="Bookmark[]"/> where found.</returns>
        public Task<Bookmark[]?> GetBookmarksAsync(string userId) => _cache.GetOrAddAsync(userId, () => _data.GetBookmarksAsync(userId));

        /// <summary>
        /// <see cref="Bookmark"/>を登録し、既に存在している場合は内容を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Bookmark"/>.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <returns>The created or updated <see cref="Bookmark"/>.</returns>
        public async Task<Bookmark> PutBookmarkAsync(Bookmark value, string articleId)
        {
            var __result = await _data.PutBookmarkAsync(value ?? throw new ArgumentNullException(nameof(value)), articleId).ConfigureAwait(false);
            return _cache.SetValue(__result);
        }

        /// <summary>
        /// 与えられたキーに対応する<see cref="Bookmark"/>を削除します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        public async Task DeleteBookmarkAsync(string userId, string articleId)
        {
            _cache.Remove<Bookmark>(new CompositeKey(userId, articleId));
            await _data.DeleteBookmarkAsync(userId, articleId).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets the specified user represented as <see cref="Bookmark"/>.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <returns>The selected user where found.</returns>
        public Task<Bookmark?> GetBookmarkUserAsync(string userId) => _cache.GetOrAddAsync(userId, () => _data.GetBookmarkUserAsync(userId));
    }
}

#pragma warning restore
#nullable restore