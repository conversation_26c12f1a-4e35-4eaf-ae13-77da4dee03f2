/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.DataSvc
{
    /// <summary>
    /// Provides the <see cref="User"/> data repository services.
    /// </summary>
    public partial class UserDataSvc : IUserDataSvc
    {
        private readonly IUserData _data;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserDataSvc"/> class.
        /// </summary>
        /// <param name="data">The <see cref="IUserData"/>.</param>
        public UserDataSvc(IUserData data)
            { _data = data ?? throw new ArgumentNullException(nameof(data)); UserDataSvcCtor(); }

        partial void UserDataSvcCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 指定したユーザーの情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="User"/>.</returns>
        public Task<User?> GetAsync(string userId) => _data.GetAsync(userId);

        /// <summary>
        /// 指定したユーザーの情報を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="user">The User (see <see cref="Entities.User"/>).</param>
        public Task CreateOrUpdateAsync(string userId, User user) => _data.CreateOrUpdateAsync(userId, user);

        /// <summary>
        /// 指定したユーザーの情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        public Task DeleteAsync(string userId) => _data.DeleteAsync(userId);
    }
}

#pragma warning restore
#nullable restore