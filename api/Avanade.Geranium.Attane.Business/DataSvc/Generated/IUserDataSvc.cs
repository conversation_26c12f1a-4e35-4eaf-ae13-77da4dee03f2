/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.DataSvc
{
    /// <summary>
    /// Defines the <see cref="User"/> data repository services.
    /// </summary>
    public partial interface IUserDataSvc
    {
        /// <summary>
        /// 指定したユーザーの情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="User"/>.</returns>
        Task<User?> GetAsync(string userId);

        /// <summary>
        /// 指定したユーザーの情報を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="user">The User (see <see cref="Entities.User"/>).</param>
        Task CreateOrUpdateAsync(string userId, User user);

        /// <summary>
        /// 指定したユーザーの情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        Task DeleteAsync(string userId);
    }
}

#pragma warning restore
#nullable restore