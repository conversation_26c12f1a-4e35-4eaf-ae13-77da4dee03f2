/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.DataSvc
{
    /// <summary>
    /// Provides the <see cref="SearchRequest"/> data repository services.
    /// </summary>
    public partial class SearchRequestDataSvc : ISearchRequestDataSvc
    {
        private readonly ISearchRequestData _data;
        private readonly IRequestCache _cache;

        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestDataSvc"/> class.
        /// </summary>
        /// <param name="data">The <see cref="ISearchRequestData"/>.</param>
        /// <param name="cache">The <see cref="IRequestCache"/>.</param>
        public SearchRequestDataSvc(ISearchRequestData data, IRequestCache cache)
            { _data = data ?? throw new ArgumentNullException(nameof(data)); _cache = cache ?? throw new ArgumentNullException(nameof(cache)); SearchRequestDataSvcCtor(); }

        partial void SearchRequestDataSvcCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 指定したユーザーの現在の検索要求を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="InternalSearchRequest"/>.</returns>
        public Task<InternalSearchRequest> GetRequestAsync(string userId) => _data.GetRequestAsync(userId);

        /// <summary>
        /// 指定したユーザーの検索要求を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The Request (see <see cref="Entities.InternalSearchRequest"/>).</param>
        public Task CreateOrUpdateRequestAsync(string userId, InternalSearchRequest? request) => _data.CreateOrUpdateRequestAsync(userId, request);

        /// <summary>
        /// 指定したユーザーの検索要求および結果を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        public Task DeleteRequestAsync(string userId) => _data.DeleteRequestAsync(userId);

        /// <summary>
        /// 指定したユーザーの、特定のデータソースに対する検索処理を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="searchProcessRequest">The Search Process Request (see <see cref="Entities.SearchProcessRequest"/>).</param>
        public Task CreateProcessAsync(string userId, SearchProcessRequest searchProcessRequest) => _data.CreateProcessAsync(userId, searchProcessRequest);

        /// <summary>
        /// 各データソースからの検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>各データソースからの検索結果の列挙.</returns>
        public Task<IEnumerable<SearchProcessRequestResult>> GetResultsAsync(string userId) => _data.GetResultsAsync(userId);
    }
}

#pragma warning restore
#nullable restore