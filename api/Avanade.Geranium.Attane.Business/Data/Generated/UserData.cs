/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Data
{
    /// <summary>
    /// Provides the <see cref="User"/> data access.
    /// </summary>
    public partial class UserData : IUserData
    {
        private readonly Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient _tableStorageClient;
        private readonly Microsoft.Extensions.Logging.ILogger<UserData> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserData"/> class.
        /// </summary>
        /// <param name="tableStorageClient">The <see cref="Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient"/>.</param>
        /// <param name="logger">The <see cref="Microsoft.Extensions.Logging.ILogger{UserData}"/>.</param>
        public UserData(Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient tableStorageClient, Microsoft.Extensions.Logging.ILogger<UserData> logger)
            { _tableStorageClient = tableStorageClient ?? throw new ArgumentNullException(nameof(tableStorageClient)); _logger = logger ?? throw new ArgumentNullException(nameof(logger)); UserDataCtor(); }

        partial void UserDataCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 指定したユーザーの情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="User"/>.</returns>
        public Task<User?> GetAsync(string userId) => GetOnImplementationAsync(userId);

        /// <summary>
        /// 指定したユーザーの情報を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="user">The User (see <see cref="Entities.User"/>).</param>
        public Task CreateOrUpdateAsync(string userId, User user) => CreateOrUpdateOnImplementationAsync(userId, user);

        /// <summary>
        /// 指定したユーザーの情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        public Task DeleteAsync(string userId) => DeleteOnImplementationAsync(userId);
    }
}

#pragma warning restore
#nullable restore