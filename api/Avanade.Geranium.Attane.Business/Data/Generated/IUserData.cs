/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Data
{
    /// <summary>
    /// Defines the <see cref="User"/> data access.
    /// </summary>
    public partial interface IUserData
    {
        /// <summary>
        /// 指定したユーザーの情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="User"/>.</returns>
        Task<User?> GetAsync(string userId);

        /// <summary>
        /// 指定したユーザーの情報を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="user">The User (see <see cref="Entities.User"/>).</param>
        Task CreateOrUpdateAsync(string userId, User user);

        /// <summary>
        /// 指定したユーザーの情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        Task DeleteAsync(string userId);
    }
}

#pragma warning restore
#nullable restore