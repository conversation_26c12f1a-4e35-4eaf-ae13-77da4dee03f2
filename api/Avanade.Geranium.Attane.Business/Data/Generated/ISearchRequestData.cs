/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Data
{
    /// <summary>
    /// Defines the <see cref="SearchRequest"/> data access.
    /// </summary>
    public partial interface ISearchRequestData
    {
        /// <summary>
        /// 指定したユーザーの現在の検索要求を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="InternalSearchRequest"/>.</returns>
        Task<InternalSearchRequest> GetRequestAsync(string userId);

        /// <summary>
        /// 指定したユーザーの検索要求を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The Request (see <see cref="Entities.InternalSearchRequest"/>).</param>
        Task CreateOrUpdateRequestAsync(string userId, InternalSearchRequest? request);

        /// <summary>
        /// 指定したユーザーの検索要求および結果を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        Task DeleteRequestAsync(string userId);

        /// <summary>
        /// 指定したユーザーの、特定のデータソースに対する検索処理を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="searchProcessRequest">The Search Process Request (see <see cref="Entities.SearchProcessRequest"/>).</param>
        Task CreateProcessAsync(string userId, SearchProcessRequest searchProcessRequest);

        /// <summary>
        /// 各データソースからの検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>各データソースからの検索結果の列挙.</returns>
        Task<IEnumerable<SearchProcessRequestResult>> GetResultsAsync(string userId);
    }
}

#pragma warning restore
#nullable restore