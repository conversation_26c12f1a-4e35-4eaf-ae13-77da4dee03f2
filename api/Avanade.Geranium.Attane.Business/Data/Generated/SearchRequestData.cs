/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Data
{
    /// <summary>
    /// Provides the <see cref="SearchRequest"/> data access.
    /// </summary>
    public partial class SearchRequestData : ISearchRequestData
    {
        private readonly Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient _tableStorageClient;
        private readonly Avanade.Geranium.Attane.Infrastructure.Data.Clients.IQueueStorageClient _queueStorageClient;
        private readonly Microsoft.Extensions.Logging.ILogger<SearchRequestData> _logger;
        private readonly Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration> _configuration;
        private readonly CoreEx.Events.IEventPublisher _evtPub;

        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestData"/> class.
        /// </summary>
        /// <param name="tableStorageClient">The <see cref="Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient"/>.</param>
        /// <param name="queueStorageClient">The <see cref="Avanade.Geranium.Attane.Infrastructure.Data.Clients.IQueueStorageClient"/>.</param>
        /// <param name="logger">The <see cref="Microsoft.Extensions.Logging.ILogger{SearchRequestData}"/>.</param>
        /// <param name="configuration">The <see cref="Microsoft.Extensions.Options.IOptions{Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration}"/>.</param>
        /// <param name="evtPub">The <see cref="CoreEx.Events.IEventPublisher"/>.</param>
        public SearchRequestData(Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient tableStorageClient, Avanade.Geranium.Attane.Infrastructure.Data.Clients.IQueueStorageClient queueStorageClient, Microsoft.Extensions.Logging.ILogger<SearchRequestData> logger, Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration> configuration, CoreEx.Events.IEventPublisher evtPub)
        {
            _tableStorageClient = tableStorageClient ?? throw new ArgumentNullException(nameof(tableStorageClient));
            _queueStorageClient = queueStorageClient ?? throw new ArgumentNullException(nameof(queueStorageClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _evtPub = evtPub ?? throw new ArgumentNullException(nameof(evtPub));
            SearchRequestDataCtor();
        }

        partial void SearchRequestDataCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 指定したユーザーの現在の検索要求を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="InternalSearchRequest"/>.</returns>
        public Task<InternalSearchRequest> GetRequestAsync(string userId) => GetRequestOnImplementationAsync(userId);

        /// <summary>
        /// 指定したユーザーの検索要求を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The Request (see <see cref="Entities.InternalSearchRequest"/>).</param>
        public Task CreateOrUpdateRequestAsync(string userId, InternalSearchRequest? request) => CreateOrUpdateRequestOnImplementationAsync(userId, request);

        /// <summary>
        /// 指定したユーザーの検索要求および結果を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        public Task DeleteRequestAsync(string userId) => DeleteRequestOnImplementationAsync(userId);

        /// <summary>
        /// 指定したユーザーの、特定のデータソースに対する検索処理を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="searchProcessRequest">The Search Process Request (see <see cref="Entities.SearchProcessRequest"/>).</param>
        public Task CreateProcessAsync(string userId, SearchProcessRequest searchProcessRequest) => CreateProcessOnImplementationAsync(userId, searchProcessRequest);

        /// <summary>
        /// 各データソースからの検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>各データソースからの検索結果の列挙.</returns>
        public Task<IEnumerable<SearchProcessRequestResult>> GetResultsAsync(string userId) => GetResultsOnImplementationAsync(userId);
    }
}

#pragma warning restore
#nullable restore