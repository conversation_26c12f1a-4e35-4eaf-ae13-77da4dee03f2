using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
using Avanade.Geranium.Attane.Shared;
using Azure;
using Azure.Data.Tables;
// TableStorageに登録される値のAsIsを維持するためSystem.Text.Jsonのシリアライザを使う
using SystemTextJsonSerializer = System.Text.Json.JsonSerializer;

namespace Avanade.Geranium.Attane.Business.Data
{
    /// <summary>
    /// 検索関連の定数
    /// </summary>
    internal static class SearchRequestDataConst
    {
        // Table Storageのテーブル名
        public const string TableName = "search";
        // Queue Storageのキュー名
        public const string DefaultQueueName = "search-process";

        /// <summary>
        /// 検索要求に対応するRowKey
        /// </summary>
        public const string RequestRowKey = "request";

        /// <summary>
        /// 検索結果に対応するRowKeyの先頭部分
        /// </summary>
        public const string ResultRowKeyPrefix = "result";
    }
    partial class SearchRequestData
    {
        private string _queueName;
        partial void SearchRequestDataCtor()
        {
            _queueName = _configuration?.Value?.QueueName ?? SearchRequestDataConst.DefaultQueueName;
        }

        public async Task<InternalSearchRequest?> GetRequestOnImplementationAsync(string userId) =>
            (await _tableStorageClient.GetByKey<SearchTableEntity>(SearchRequestDataConst.TableName, userId, SearchRequestDataConst.RequestRowKey).ConfigureAwait(false))?.ToInternalSearchRequest();

        public async Task CreateOrUpdateRequestOnImplementationAsync(string _, InternalSearchRequest request)
        {
            var entity = SearchTableEntity.FromInternalSearchRequest(request);
            var operation = new TableOperation<SearchTableEntity>
            {
                ActionType = TableTransactionActionType.UpsertReplace,
                Entity = entity
            };
            await _tableStorageClient.ExecuteTableOperation(SearchRequestDataConst.TableName, operation).ConfigureAwait(false);
        }

        public async Task DeleteRequestOnImplementationAsync(string userId)
        {
            // 検索要求
            var request = await _tableStorageClient.GetByKey<SearchTableEntity>(SearchRequestDataConst.TableName, userId, SearchRequestDataConst.RequestRowKey).ConfigureAwait(false);
            if (request != null)
            {
                await _tableStorageClient.Delete(SearchRequestDataConst.TableName, request).ConfigureAwait(false);
            }

            // 結果
            // prefixの最後の文字を次の文字にすることでprefixの範囲を示す
            // 厳密には文字が7FFF/FFFFの場合を考慮する必要があるが、実用上ないので考慮しない
            var nextPrefix = $"{SearchRequestDataConst.ResultRowKeyPrefix[0..^1]}{(char)(SearchRequestDataConst.ResultRowKeyPrefix[^1] + 1)}";
            // RowKey指定した開始・終了に含まれる範囲にフィルタ
            var filter = $"PartitionKey eq '{userId}' and RowKey ge '{SearchRequestDataConst.ResultRowKeyPrefix}' and RowKey lt '{nextPrefix}'";
            var processes = (await
                _tableStorageClient.Get<SearchTableEntity>(SearchRequestDataConst.TableName, filter))
                .ToArray();
            await Task.WhenAll(processes.Select(p => _tableStorageClient.Delete(SearchRequestDataConst.TableName, p))).ConfigureAwait(false);
        }

#if UseQueueStorage
        public async Task CreateProcessOnImplementationAsync(string userId, SearchProcessRequest searchProcessRequest)
#else
        public Task CreateProcessOnImplementationAsync(string userId, SearchProcessRequest searchProcessRequest)
#endif
        {
#if UseQueueStorage
            // searchProcessRequestをキューに登録
            await _queueStorageClient.EnqueueAsync(_queueName, SearchQueueEntry.FromSearchProcessRequest(searchProcessRequest)).ConfigureAwait(false);
#else
            _evtPub.PublishValueEvent(
                SearchQueueEntry.FromSearchProcessRequest(searchProcessRequest),
                _queueName);
            return Task.CompletedTask;
#endif
        }

        public async Task<IEnumerable<SearchProcessRequestResult>> GetResultsOnImplementationAsync(string userId)
        {
            var nextPrefix = $"{SearchRequestDataConst.ResultRowKeyPrefix[0..^1]}{(char)(SearchRequestDataConst.ResultRowKeyPrefix[^1] + 1)}";
            var filter = $"PartitionKey eq '{userId}' and RowKey ge '{SearchRequestDataConst.ResultRowKeyPrefix}' and RowKey lt '{nextPrefix}'";
            return (await _tableStorageClient.Get<SearchTableEntity>(SearchRequestDataConst.TableName, filter))
                .Select(entity => entity.ToSearchProcessRequestResult())
                .ToArray();
        }
    }

    /// <summary>
    /// Table Storage入出力用クラス
    /// </summary>
    /// <remarks>
    /// <see cref="InternalSearchRequest"/>, <see cref="SearchProcessRequestResult"/> の両方を扱えるように全プロパティを持つ
    /// </remarks>
    public sealed class SearchTableEntity : ITableEntity
    {
        #region Properties
        #region 共通
        [JsonPropertyName("reqId")]
        public Guid ReqId { get; set; }

        #endregion

        #region InternalSearchRequest

        [JsonPropertyName("userId")]
        public string? UserId { get; set; }

        [JsonPropertyName("condition")]
        public string? Condition { get; set; }

        [JsonPropertyName("conditionKeywords")]
        public string? ConditionKeywords { get; set; }

        /// <summary>
        /// Gets or sets the 検索要求の状態.
        /// </summary>
        [JsonPropertyName("state")]
        public string? State { get; set; }

        /// <summary>
        /// Gets or sets the OBOトークン.
        /// </summary>
        [JsonPropertyName("tokens")]
        public string? Tokens { get; set; }

        /// <summary>
        /// Gets or sets the 処理数.
        /// </summary>
        [JsonPropertyName("processCount")]
        public int ProcessCount { get; set; }

        [JsonPropertyName("context")]
        public string? Context { get; set; }
        #endregion

        #region SearchProcessRequestResult
        [JsonPropertyName("pid")]
        public string? Pid { get; set; }
        [JsonPropertyName("kind")]
        public string? Kind { get; set; }
        [JsonPropertyName("properties")]
        public string? Properties { get; set; }
        [JsonPropertyName("ids")]
        public string? Ids { get; set; }
        [JsonPropertyName("hasNext")]
        public bool HasNext { get; set; }
        public string? PartitionKey { get; set; }
        public string? RowKey { get; set; }
        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }
        #endregion

        #endregion

        public static SearchTableEntity FromInternalSearchRequest(InternalSearchRequest request) =>
            new()
            {
                PartitionKey = request.UserId,
                RowKey = SearchRequestDataConst.RequestRowKey,
                UserId = request.UserId!,
                ReqId = request.ReqId,
                Condition = request.Condition,
                ConditionKeywords = request.ConditionKeywords != null ? SystemTextJsonSerializer.Serialize(request.ConditionKeywords) : null,
                State = request.State?.ToString(),
                Tokens = request.Tokens.Count > 0 ? SystemTextJsonSerializer.Serialize(request.Tokens) : null,
                ProcessCount = request.ProcessCount,
                Context = request.Context != null ? SystemTextJsonSerializer.Serialize(request.Context) : null,
            };

        internal InternalSearchRequest ToInternalSearchRequest() =>
            new()
            {
                UserId = this.UserId,
                ReqId = this.ReqId,
                Condition = this.Condition,
                ConditionKeywords = this.ConditionKeywords switch
                {
                    null => null,
                    var value when IsOldFormatKeywords(value) => new SearchConditionTree(
                        SearchConditionOperator.And,
                        SystemTextJsonSerializer.Deserialize<string[]>(value)!.Select(v => new SearchConditionTree(v))),
                    var value => SystemTextJsonSerializer.Deserialize<SearchConditionTree?>(value)
                },
                State = this.State != null ? Enum.Parse<SearchState>(this.State) : null,
                Tokens = (this.Tokens != null ? SystemTextJsonSerializer.Deserialize<Dictionary<string, string>>(this.Tokens) : null) ?? new Dictionary<string, string>(),
                ProcessCount = this.ProcessCount,
                Context = (this.Context != null ? SystemTextJsonSerializer.Deserialize<Dictionary<string, string>>(this.Context) : null),
            };
        internal bool IsOldFormatKeywords(string keywords)
        {
            // jsonの文字列配列となっているはずなので、
            return keywords.Length > 0 && keywords[0] == '[';
        }


        public static SearchTableEntity FromSearchProcessRequestResult(string partitionKey, SearchProcessRequestResult searchResult) =>
            new()
            {
                PartitionKey = partitionKey,
                RowKey = $"{SearchRequestDataConst.ResultRowKeyPrefix}-{searchResult.ReqId}-{searchResult.Pid}",
                ReqId = searchResult.ReqId,
                Pid = searchResult.Pid,
                Kind = searchResult.DataSource!.Kind!.ToString(),
                Properties = SystemTextJsonSerializer.Serialize(searchResult.DataSource.Properties),
                Ids = SystemTextJsonSerializer.Serialize(searchResult.Ids),
                HasNext = searchResult.HasNext,
            };
        internal SearchProcessRequestResult ToSearchProcessRequestResult() =>
            new()
            {
                ReqId = this.ReqId,
                Pid = this.Pid,
                DataSource = new DataSource
                {
                    Kind = this.Kind != null ? Enum.Parse<DataSourceKind>(this.Kind) : null,
                    Properties = this.Properties != null ? SystemTextJsonSerializer.Deserialize<Dictionary<string, string>>(this.Properties) : null,
                },
                Ids = this.Ids != null ? SystemTextJsonSerializer.Deserialize<string[]?>(this.Ids) : null,
                HasNext = this.HasNext,
            };
    }

    /// <summary>
    /// Queue Storage出力用クラス
    /// </summary>
    public class SearchQueueEntry
    {
        #region Properties
        [JsonPropertyName("userId")]
        public string? UserId { get; set; }

        [JsonPropertyName("reqId")]
        public Guid ReqId { get; set; }

        [JsonPropertyName("pid")]
        public Guid Pid { get; set; }

        [JsonPropertyName("kind")]
        public DataSourceKind? Kind { get; set; }

        [JsonPropertyName("globalProperties")]
        public Dictionary<string, string>? GlobalProperties { get; set; }

        [JsonPropertyName("properties")]
        public Dictionary<string, string>[]? Properties { get; set; }
        #endregion

        internal static SearchQueueEntry FromSearchProcessRequest(SearchProcessRequest searchProcessRequest) =>
            new()
            {
                UserId = searchProcessRequest.UserId,
                ReqId = searchProcessRequest.ReqId,
                Pid = searchProcessRequest.Pid,
                Kind = searchProcessRequest.DataSources?.Kind,
                GlobalProperties = searchProcessRequest.DataSources?.GlobalProperties,
                Properties = searchProcessRequest.DataSources?.Properties,
            };
    }
}
