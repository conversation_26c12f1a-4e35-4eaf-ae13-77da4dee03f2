using Avanade.Geranium.Attane.Business.Exceptions;
using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
using Azure;
using Azure.Data.Tables;
using System;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Linq;

namespace Avanade.Geranium.Attane.Business.Data
{
    public partial class BookmarkData : IBookmarkData
    {
        /// <summary>
        /// 操作するTable Storageのテーブル名
        /// </summary>
        private const string TableName = "bookmarks";

        /// <summary>
        /// 記事に対するエントリに絞り込むRowKeyのプレフィクス
        /// </summary>
        private const string ArticlePrefix = "spo_";

        /// <summary>
        /// ユーザーに対するエントリに絞り込むRowKey
        /// </summary>
        private const string UserRowKey = "user";

        /// <summary>
        /// Table Storageクライアント
        /// </summary>
        private readonly ITableStorageClient _tableStorageClient;

        private readonly ILogger<BookmarkData> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="BookmarkData"/> class.
        /// </summary>
        /// <param name="cloudTableClient">The <see cref="CloudTableClient"/>.</param>
        /// <param name="logger">The <see cref="Microsoft.Extensions.Logging.ILogger{BookmarkManager}"/>.</param>
        public BookmarkData(ITableStorageClient tableStorageClient, ILogger<BookmarkData> logger)
        {
            _tableStorageClient = tableStorageClient;
            _logger = logger;
        }

        /// <summary>
        /// Deletes the specified <see cref="Bookmark"/>.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        public async Task DeleteBookmarkAsync(string userId, string articleId)
        {
            // 削除対象を取得する
            var result = await _tableStorageClient.GetByKey<BookmarkEntity>(TableName, userId, $"spo_{articleId}").ConfigureAwait(false)
                // 存在しなかった場合は終了
                // beefの制約上void戻りにせざるを得ないのでExceptionで区別する
                ?? throw new BookmarkDeleteException();

            // 削除処理を実行
            await _tableStorageClient.Delete(TableName, result).ConfigureAwait(false);
        }

        /// <summary>
        /// Creates a new <see cref="Bookmark"/> if not found, otherwise updates the existing one.
        /// </summary>
        /// <param name="value">The <see cref="Bookmark"/>.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <returns>The created or updated <see cref="Bookmark"/>.</returns>
        public async Task<Bookmark> PutBookmarkAsync(Bookmark value, string articleId)
        {

            var bookmarks = new List<Tuple<TableTransactionActionType, ITableEntity>>()
            {
                new Tuple<TableTransactionActionType, ITableEntity>(
                    TableTransactionActionType.UpsertReplace,
                    new BookmarkEntity(value) { PartitionKey = value.UserId, RowKey = $"spo_{articleId}" }),
                new Tuple<TableTransactionActionType, ITableEntity>(
                    TableTransactionActionType.UpsertReplace,
                    new BookmarkEntity(value.UserId) { PartitionKey = value.UserId, RowKey = UserRowKey })
            };
            var result = await _tableStorageClient.ExecuteBatchAsync<BookmarkEntity>(
                TableName,
                bookmarks
                );
            return (bookmarks[0].Item2 as BookmarkEntity)?.ToBookmark() ?? value;
        }

        /// <summary>
        /// Gets the specified <see cref="Bookmark[]"/>.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <returns>The selected <see cref="Bookmark[]"/> where found.</returns>
        public async Task<Bookmark[]?> GetBookmarksAsync(string userId)
        {
            var nextPrefix = $"{ArticlePrefix[0..^1]}{(char)(ArticlePrefix[^1] + 1)}";
            var filter = $"PartitionKey eq '{userId}' and RowKey ge '{ArticlePrefix}' and RowKey lt '{nextPrefix}'";
            return (await
            _tableStorageClient.Get<BookmarkEntity>(TableName, filter))
                .Select(entity => entity.ToBookmark())
                .ToArray();
        }

        /// <summary>
        /// Gets the specified user represented as <see cref="Bookmark"/>.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <returns>The selected user where found.</returns>
        public async Task<Bookmark?> GetBookmarkUserAsync(string userId)
        {
            _logger.LogTrace("AppContext.BaseDirectory: {baseDirectory}", AppContext.BaseDirectory);
            _logger.LogTrace("Assembly.GetExecutingAssembly().Location: {location}", Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location));

            var filter = $"PartitionKey eq '{userId}' and RowKey eq '{UserRowKey}'";
            return (await
            _tableStorageClient.Get<BookmarkEntity>(TableName, filter))
                .FirstOrDefault() // この時点でクエリが発行される
                ?.ToBookmark();
        }

        /// <summary>
        /// Table Storageとの入出力に用いるエンティティ
        /// </summary>
        /// <remarks>
        /// Table Storageとの入出力には <see cref="TableEntity"/> から派生したエンティティを使用する必要がある
        /// </remarks>
        public class BookmarkEntity : ITableEntity
        {
            /// <summary>
            /// 与えられた <see cref="Bookmark"/> に対応する <see cref="BookmarkEntity"/> のインスタンスを生成します。
            /// </summary>
            /// <param name="value">The <see cref="Bookmark"/></param>
            public BookmarkEntity(Bookmark value)
            {
                UserId = value.UserId;
                Kind = value.Kind;
                Properties = value.Properties;
                Id = value.Id;
                Title = value.Title;
                Note = value.Note;
                DisplayDate = value.DisplayDate;
                ReposCreatedDate = value.ReposCreatedDate;
                ReposUpdatedDate = value.ReposUpdatedDate;
                PartitionKey = "";
                RowKey = "";
                Timestamp = new DateTimeOffset();
                ETag = new ETag();
            }

            /// <summary>
            /// 与えられたユーザーIDに対応するユーザーを表す <see cref="BookmarkEntity"/> のインスタンスを生成します。
            /// </summary>
            /// <param name="userId">ユーザーID</param>
            public BookmarkEntity(string userId)
            {
                UserId = userId;
                Kind = "";
                Properties = "";
                Id = "";
                Title = "";
                Note = "";
                DisplayDate = DateTime.UtcNow;
                ReposCreatedDate = DateTime.UtcNow;
                ReposUpdatedDate = DateTime.UtcNow;
                PartitionKey = "";
                RowKey = "";
                Timestamp = DateTimeOffset.UtcNow;
                ETag = new ETag();
            }

            /// <summary>
            /// Initializes a new instance of the <see cref="BookmarkEntity"/> class.
            /// </summary>
            public BookmarkEntity()
            {
                UserId = "";
                Kind = "";
                Properties = "";
                Id = "";
                Title = "";
                Note = "";
                DisplayDate = new DateTime();
                ReposCreatedDate = new DateTime();
                ReposUpdatedDate = new DateTime();
                PartitionKey = "";
                RowKey = "";
                Timestamp = new DateTimeOffset();
                ETag = new ETag();
            }

            public string UserId { get; set; }
            public string Kind { get; set; }
            public string Properties { get; set; }
            public string Title { get; set; }
            public string Id { get; set; }
            public DateTime DisplayDate { get; set; }
            public string Note { get; set; }
            public DateTime ReposCreatedDate { get; set; }
            public DateTime ReposUpdatedDate { get; set; }

            public string PartitionKey { get; set; }
            public string RowKey { get; set; }
            public DateTimeOffset? Timestamp { get; set; }
            public ETag ETag { get; set; }



            /// <summary>
            /// この <see cref="BookmarkEntity"/> に対応する <see cref="Bookmark"/> のインスタンスを生成します。
            /// </summary>
            /// <returns>The generated <see cref="Bookmark"/>.</returns>
            public Bookmark ToBookmark()
            {
                return new Bookmark()
                {
                    UserId = this.UserId,
                    Kind = this.Kind,
                    Properties = this.Properties,
                    Id = this.Id,
                    Title = this.Title,
                    DisplayDate = this.DisplayDate,
                    Note = this.Note,
                    ReposCreatedDate = this.ReposCreatedDate,
                    ReposUpdatedDate = this.ReposUpdatedDate,
                    TimeStamp = this.Timestamp?.DateTime
                };
            }
        }
    }
}