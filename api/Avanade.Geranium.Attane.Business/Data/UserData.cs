using Avanade.Geranium.Attane.Infrastructure.Data.Clients;
using Azure;
using Azure.Data.Tables;

namespace Avanade.Geranium.Attane.Business.Data
{
    internal static class UserRequestDataConst
    {
        // Table Storageのテーブル名
        public const string TableName = "users";

        /// <summary>
        /// RowKey
        /// </summary>
        public const string UserRowKey = "user";
    }

    public partial class UserData
    {

        /// <summary>
        /// 指定したユーザーの情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="User"/>.</returns>
        /// <remarks>
        /// <see cref="GetAsync(string)"/> の実装
        /// </remarks>
        public async Task<User?> GetOnImplementationAsync(string userId) =>
            (await _tableStorageClient.GetByKey<UserTableEntity>(UserRequestDataConst.TableName, userId, UserRequestDataConst.UserRowKey).ConfigureAwait(false))?.ToUser();

        /// <summary>
        /// 指定したユーザーの情報を登録します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="user">The User (see <see cref="Entities.User"/>).</param>
        /// <remarks>
        /// <see cref="CreateOrUpdateAsync(string, User)"/> の実装
        /// </remarks>
        public async Task CreateOrUpdateOnImplementationAsync(string _, User user)
        {
            var entity = UserTableEntity.FromUser(user);
            var operation = new TableOperation<UserTableEntity>
            {
                ActionType = TableTransactionActionType.UpsertReplace,
                Entity = entity
            };
            await _tableStorageClient.ExecuteTableOperation(UserRequestDataConst.TableName, operation).ConfigureAwait(false);
        }

        /// <summary>
        /// 指定したユーザーの情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <remarks>
        /// <see cref="DeleteAsync(string)"/> の実装
        /// </remarks>
        public async Task DeleteOnImplementationAsync(string userId)
        {
            // 検索要求
            var request = await _tableStorageClient.GetByKey<UserTableEntity>(UserRequestDataConst.TableName, userId, UserRequestDataConst.UserRowKey).ConfigureAwait(false);
            if (request != null)
            {
                await _tableStorageClient.Delete(UserRequestDataConst.TableName, request).ConfigureAwait(false);
            }
        }
    }

    /// <summary>
    /// Table Storage入出力用クラス
    /// </summary>
    public sealed class UserTableEntity : ITableEntity
    {
        #region Properties

        [JsonPropertyName("userId")]
        public string? UserId { get; set; }

        [JsonPropertyName("context")]
        public string? Context { get; set; }
        public string? PartitionKey { get; set; }
        public string? RowKey { get; set; }
        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }
        #endregion

        public static UserTableEntity FromUser(User request) =>
            new()
            {
                PartitionKey = request.UserId,
                RowKey = UserRequestDataConst.UserRowKey,
                UserId = request.UserId!,
                Context = request.Context != null ? JsonSerializer.Default.Serialize(request.Context) : null,
            };

        internal User ToUser() =>
            new()
            {
                UserId = this.UserId,
                Context = (this.Context != null ? JsonSerializer.Default.Deserialize<Dictionary<string, string>>(this.Context) : null),
            };
    }
}
