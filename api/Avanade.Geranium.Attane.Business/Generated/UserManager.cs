/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Provides the <see cref="User"/> business functionality.
    /// </summary>
    public partial class UserManager : IUserManager
    {
        private readonly IUserDataSvc _dataService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserManager"/> class.
        /// </summary>
        /// <param name="dataService">The <see cref="IUserDataSvc"/>.</param>
        public UserManager(IUserDataSvc dataService)
            { _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService)); UserManagerCtor(); }

        partial void UserManagerCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="System.Collections.Generic.Dictionary{string, string}"/> where found.</returns>
        public Task<System.Collections.Generic.Dictionary<string, string>?> GetContextAsync(string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await GetContextOnImplementationAsync(userId).ConfigureAwait(false);
        }, InvokerArgs.Read);

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Dictionary{string, string}"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The updated <see cref="System.Collections.Generic.Dictionary{string, string}"/>.</returns>
        public Task<System.Collections.Generic.Dictionary<string, string>> UpdateContextAsync(Dictionary<string, string> value, string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await UpdateContextOnImplementationAsync(value.EnsureValue(), userId).ConfigureAwait(false);
        }, InvokerArgs.Update);

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        public Task DeleteContextAsync(string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            await DeleteContextOnImplementationAsync(userId).ConfigureAwait(false);
        }, InvokerArgs.Delete);

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The selected <see cref="string"/> where found.</returns>
        public Task<string?> GetContextFieldAsync(string userId, string fieldName) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await GetContextFieldOnImplementationAsync(userId, fieldName).ConfigureAwait(false);
        }, InvokerArgs.Read);

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="value">The <see cref="string"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The updated <see cref="string"/>.</returns>
        public Task<string?> UpdateContextFieldAsync(string value, string userId, string fieldName) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await UpdateContextFieldOnImplementationAsync(value.EnsureValue(), userId, fieldName).ConfigureAwait(false);
        }, InvokerArgs.Update);

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        public Task DeleteContextFieldAsync(string userId, string fieldName) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            await DeleteContextFieldOnImplementationAsync(userId, fieldName).ConfigureAwait(false);
        }, InvokerArgs.Delete);
    }
}

#pragma warning restore
#nullable restore
