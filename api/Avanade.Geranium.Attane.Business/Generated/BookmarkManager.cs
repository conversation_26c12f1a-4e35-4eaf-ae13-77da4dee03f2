/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Provides the <see cref="Bookmark"/> business functionality.
    /// </summary>
    public partial class BookmarkManager : IBookmarkManager
    {
        private readonly IBookmarkDataSvc _dataService;
        private readonly Microsoft.Extensions.Logging.ILogger<BookmarkManager> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="BookmarkManager"/> class.
        /// </summary>
        /// <param name="dataService">The <see cref="IBookmarkDataSvc"/>.</param>
        /// <param name="logger">The <see cref="Microsoft.Extensions.Logging.ILogger{BookmarkManager}"/>.</param>
        public BookmarkManager(IBookmarkDataSvc dataService, Microsoft.Extensions.Logging.ILogger<BookmarkManager> logger)
            { _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService)); _logger = logger ?? throw new ArgumentNullException(nameof(logger)); BookmarkManagerCtor(); }

        partial void BookmarkManagerCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// <see cref="Bookmark"/>を登録し、既に存在している場合は内容を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Bookmark"/>.</param>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <returns>The created or updated <see cref="Bookmark"/>.</returns>
        public Task<Bookmark> PutBookmarkAsync(Bookmark value, string userId, string articleId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            value.EnsureValue().UserId = userId;
            await value.Validate().Entity().With<BookmarkValidator>().ValidateAsync(true).ConfigureAwait(false);
        return await _dataService.PutBookmarkAsync(value, articleId).ConfigureAwait(false);
        }, InvokerArgs.Update);

        /// <summary>
        /// 与えられたキーに対応する<see cref="Bookmark"/>を削除します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        public Task DeleteBookmarkAsync(string userId, string articleId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            await DeleteBookmarkOnImplementationAsync(userId, articleId).ConfigureAwait(false);
        }, InvokerArgs.Delete);
    }
}

#pragma warning restore
#nullable restore
