/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Defines the <see cref="SearchRequest"/> business functionality.
    /// </summary>
    public partial interface ISearchRequestManager
    {
        /// <summary>
        /// 与えられたUserIdに対応する検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="SearchRequestResult"/> where found.</returns>
        Task<SearchRequestResult?> GetAsync(string userId);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果について、既知の結果を除いたものを取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The 検索結果要求.</param>
        /// <returns>A resultant <see cref="SearchRequestResult"/>.</returns>
        Task<SearchRequestResult?> GetWithAsync(string userId, SearchResultRequest? request);

        /// <summary>
        /// 与えられたUserIdに対応する検索をキャンセルします.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="string"/>.</returns>
        Task<string?> CancellAsync(string userId);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="System.Collections.Generic.Dictionary{string, string}"/> where found.</returns>
        Task<System.Collections.Generic.Dictionary<string, string>?> GetContextAsync(string userId);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Dictionary{string, string}"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The updated <see cref="System.Collections.Generic.Dictionary{string, string}"/>.</returns>
        Task<System.Collections.Generic.Dictionary<string, string>> UpdateContextAsync(Dictionary<string, string> value, string userId);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        Task DeleteContextAsync(string userId);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The selected <see cref="string"/> where found.</returns>
        Task<string?> GetContextFieldAsync(string userId, string fieldName);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="value">The <see cref="string"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The updated <see cref="string"/>.</returns>
        Task<string?> UpdateContextFieldAsync(string value, string userId, string fieldName);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        Task DeleteContextFieldAsync(string userId, string fieldName);

        /// <summary>
        /// 与えられたUserIdに対応する検索要求を登録します.
        /// </summary>
        /// <param name="value">The <see cref="SearchRequest"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="tokens">The リクエストのトークン.</param>
        /// <returns>The created <see cref="SearchRequest"/>.</returns>
        Task<SearchRequest> RegisterAsync(SearchRequest value, string userId, Task<Dictionary<string, string>> tokens);
    }
}

#pragma warning restore
#nullable restore