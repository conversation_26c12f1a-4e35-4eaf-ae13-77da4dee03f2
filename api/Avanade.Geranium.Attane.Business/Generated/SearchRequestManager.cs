/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Provides the <see cref="SearchRequest"/> business functionality.
    /// </summary>
    public partial class SearchRequestManager : ISearchRequestManager
    {
        private readonly ISearchRequestDataSvc _dataService;
        private readonly Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration> _configuration;
        private readonly IIdentifierGenerator _identifierGenerator;
        private readonly CoreEx.Events.IEventPublisher _evtPub;

        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestManager"/> class.
        /// </summary>
        /// <param name="dataService">The <see cref="ISearchRequestDataSvc"/>.</param>
        /// <param name="configuration">The <see cref="Microsoft.Extensions.Options.IOptions{Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration}"/>.</param>
        /// <param name="identifierGenerator">The <see cref="IIdentifierGenerator"/>.</param>
        /// <param name="evtPub">The <see cref="CoreEx.Events.IEventPublisher"/>.</param>
        public SearchRequestManager(ISearchRequestDataSvc dataService, Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration> configuration, IIdentifierGenerator identifierGenerator, CoreEx.Events.IEventPublisher evtPub)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _identifierGenerator = identifierGenerator ?? throw new ArgumentNullException(nameof(identifierGenerator));
            _evtPub = evtPub ?? throw new ArgumentNullException(nameof(evtPub));
            SearchRequestManagerCtor();
        }

        partial void SearchRequestManagerCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// 与えられたUserIdに対応する検索結果を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="SearchRequestResult"/> where found.</returns>
        public Task<SearchRequestResult?> GetAsync(string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await GetOnImplementationAsync(userId).ConfigureAwait(false);
        }, InvokerArgs.Read);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果について、既知の結果を除いたものを取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="request">The 検索結果要求.</param>
        /// <returns>A resultant <see cref="SearchRequestResult"/>.</returns>
        public Task<SearchRequestResult?> GetWithAsync(string userId, SearchResultRequest? request) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await GetWithOnImplementationAsync(userId, request).ConfigureAwait(false);
        }, InvokerArgs.Unspecified);

        /// <summary>
        /// 与えられたUserIdに対応する検索をキャンセルします.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A resultant <see cref="string"/>.</returns>
        public Task<string?> CancellAsync(string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await CancellOnImplementationAsync(userId).ConfigureAwait(false);
        }, InvokerArgs.Unspecified);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="System.Collections.Generic.Dictionary{string, string}"/> where found.</returns>
        public Task<System.Collections.Generic.Dictionary<string, string>?> GetContextAsync(string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await GetContextOnImplementationAsync(userId).ConfigureAwait(false);
        }, InvokerArgs.Read);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Dictionary{string, string}"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The updated <see cref="System.Collections.Generic.Dictionary{string, string}"/>.</returns>
        public Task<System.Collections.Generic.Dictionary<string, string>> UpdateContextAsync(Dictionary<string, string> value, string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await UpdateContextOnImplementationAsync(value.EnsureValue(), userId).ConfigureAwait(false);
        }, InvokerArgs.Update);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        public Task DeleteContextAsync(string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            await DeleteContextOnImplementationAsync(userId).ConfigureAwait(false);
        }, InvokerArgs.Delete);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The selected <see cref="string"/> where found.</returns>
        public Task<string?> GetContextFieldAsync(string userId, string fieldName) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await GetContextFieldOnImplementationAsync(userId, fieldName).ConfigureAwait(false);
        }, InvokerArgs.Read);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="value">The <see cref="string"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The updated <see cref="string"/>.</returns>
        public Task<string?> UpdateContextFieldAsync(string value, string userId, string fieldName) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await UpdateContextFieldOnImplementationAsync(value.EnsureValue(), userId, fieldName).ConfigureAwait(false);
        }, InvokerArgs.Update);

        /// <summary>
        /// 与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        public Task DeleteContextFieldAsync(string userId, string fieldName) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            await DeleteContextFieldOnImplementationAsync(userId, fieldName).ConfigureAwait(false);
        }, InvokerArgs.Delete);

        /// <summary>
        /// 与えられたUserIdに対応する検索要求を登録します.
        /// </summary>
        /// <param name="value">The <see cref="SearchRequest"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="tokens">The リクエストのトークン.</param>
        /// <returns>The created <see cref="SearchRequest"/>.</returns>
        public Task<SearchRequest> RegisterAsync(SearchRequest value, string userId, Task<Dictionary<string, string>> tokens) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            value.EnsureValue().UserId = userId;
            return await RegisterOnImplementationAsync(value, userId, tokens).ConfigureAwait(false);
        }, InvokerArgs.Create);
    }
}

#pragma warning restore
#nullable restore
