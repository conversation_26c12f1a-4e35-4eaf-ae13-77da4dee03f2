/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Defines the <see cref="Bookmark"/> business functionality.
    /// </summary>
    public partial interface IBookmarkManager
    {
        /// <summary>
        /// 与えられたUserIdに対応する<see cref="Bookmark"/>の配列を取得します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <returns>The selected <see cref="Bookmark[]"/> where found.</returns>
        Task<Bookmark[]?> GetBookmarksAsync(string userId);

        /// <summary>
        /// <see cref="Bookmark"/>を登録し、既に存在している場合は内容を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Bookmark"/>.</param>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        /// <returns>The created or updated <see cref="Bookmark"/>.</returns>
        Task<Bookmark> PutBookmarkAsync(Bookmark value, string userId, string articleId);

        /// <summary>
        /// 与えられたキーに対応する<see cref="Bookmark"/>を削除します.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <param name="articleId">The Article Id.</param>
        Task DeleteBookmarkAsync(string userId, string articleId);
    }
}

#pragma warning restore
#nullable restore