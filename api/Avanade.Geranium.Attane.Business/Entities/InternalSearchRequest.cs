using System;
using System.Collections.Generic;
using System.Text;

namespace Avanade.Geranium.Attane.Business.Entities
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S4035:Classes implementing \"IEquatable<T>\" should be sealed", Justification = "自動生成コードのため")]
    partial class InternalSearchRequest
    {
        public InternalSearchRequest()
        {
            _tokens = new Dictionary<string, string>();
        }

        public InternalSearchRequest(SearchRequest searchRequest, Dictionary<string, string> tokens) : base()
        {
            base.CopyFrom(searchRequest);
            _tokens = tokens;
        }
    }
}
