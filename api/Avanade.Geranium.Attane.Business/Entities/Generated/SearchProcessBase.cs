/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 検索プロセス entity.
    /// </summary>
    public partial class SearchProcessBase : EntityBase
    {
        private string? _userId;
        private Guid _reqId;

        /// <summary>
        /// Gets or sets the 対象のユーザーID.
        /// </summary>
        public string? UserId { get => _userId; set => SetValue(ref _userId, value); }

        /// <summary>
        /// Gets or sets the 検索ID.
        /// </summary>
        public Guid ReqId { get => _reqId; set => SetValue(ref _reqId, value); }

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            yield return CreateProperty(nameof(UserId), UserId, v => UserId = v);
            yield return CreateProperty(nameof(ReqId), ReqId, v => ReqId = v);
        }
    }
}

#pragma warning restore
#nullable restore