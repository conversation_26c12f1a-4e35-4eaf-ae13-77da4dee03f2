/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 検索要求の結果 entity.
    /// </summary>
    public partial class SearchRequestResult : SearchRequest
    {
        private Avanade.Geranium.Attane.Shared.SearchConditionTree? _conditionKeywords;
        private Avanade.Geranium.Attane.Shared.SearchState? _state;
        private SearchProcessRequestResult[]? _results;

        /// <summary>
        /// Gets or sets the トークン分割した検索条件.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.SearchConditionTree? ConditionKeywords { get => _conditionKeywords; set => SetValue(ref _conditionKeywords, value); }

        /// <summary>
        /// Gets or sets the 検索要求の状態.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.SearchState? State { get => _state; set => SetValue(ref _state, value); }

        /// <summary>
        /// Gets or sets the 検索結果.
        /// </summary>
        public SearchProcessRequestResult[]? Results { get => _results; set => SetValue(ref _results, value); }

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            foreach (var pv in base.GetPropertyValues())
                yield return pv;

            yield return CreateProperty(nameof(ConditionKeywords), ConditionKeywords, v => ConditionKeywords = v);
            yield return CreateProperty(nameof(State), State, v => State = v);
            yield return CreateProperty(nameof(Results), Results, v => Results = v);
        }
    }
}

#pragma warning restore
#nullable restore