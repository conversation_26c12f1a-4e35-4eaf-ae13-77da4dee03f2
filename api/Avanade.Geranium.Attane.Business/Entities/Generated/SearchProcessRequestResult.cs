/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 単一のデータソースからの検索結果 entity.
    /// </summary>
    public partial class SearchProcessRequestResult : SearchProcessBase, IPrimaryKey
    {
        private string? _pid;
        private DataSource? _dataSource;
        private string[]? _ids;
        private bool _hasNext;

        /// <summary>
        /// Gets or sets the 検索処理ID.
        /// </summary>
        public string? Pid { get => _pid; set => SetValue(ref _pid, value); }

        /// <summary>
        /// Gets or sets the データソースの情報.
        /// </summary>
        public DataSource? DataSource { get => _dataSource; set => SetValue(ref _dataSource, value); }

        /// <summary>
        /// Gets or sets the データキー.
        /// </summary>
        public string[]? Ids { get => _ids; set => SetValue(ref _ids, value); }

        /// <summary>
        /// Indicates whether 次ページの有無.
        /// </summary>
        [JsonIgnore]
        public bool HasNext { get => _hasNext; set => SetValue(ref _hasNext, value); }

        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The <see cref="CompositeKey"/>.</returns>
        /// <param name="pid">The <see cref="Pid"/>.</param>
        public static CompositeKey CreatePrimaryKey(string? pid) => new CompositeKey(pid);

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): <see cref="Pid"/>).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey(Pid);

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            foreach (var pv in base.GetPropertyValues())
                yield return pv;

            yield return CreateProperty(nameof(Pid), Pid, v => Pid = v);
            yield return CreateProperty(nameof(DataSource), DataSource, v => DataSource = v);
            yield return CreateProperty(nameof(Ids), Ids, v => Ids = v);
            yield return CreateProperty(nameof(HasNext), HasNext, v => HasNext = v);
        }
    }
}

#pragma warning restore
#nullable restore