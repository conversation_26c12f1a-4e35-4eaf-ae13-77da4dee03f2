/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the ユーザー entity.
    /// </summary>
    public partial class User : EntityBase
    {
        private string? _userId;
        private Dictionary<string, string>? _context;

        /// <summary>
        /// Gets or sets the 対象のユーザーID.
        /// </summary>
        public string? UserId { get => _userId; set => SetValue(ref _userId, value); }

        /// <summary>
        /// Gets or sets the コンテキスト.
        /// </summary>
        public Dictionary<string, string>? Context { get => _context; set => SetValue(ref _context, value); }

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            yield return CreateProperty(nameof(UserId), UserId, v => UserId = v);
            yield return CreateProperty(nameof(Context), Context, v => Context = v);
        }
    }

    /// <summary>
    /// Represents the <see cref="User"/> collection.
    /// </summary>
    public partial class UserCollection : EntityBaseCollection<User, UserCollection>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollection"/> class.
        /// </summary>
        public UserCollection() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollection"/> class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        public UserCollection(IEnumerable<User> items) => AddRange(items);
    }

    /// <summary>
    /// Represents the <see cref="User"/> collection result.
    /// </summary>
    public class UserCollectionResult : EntityCollectionResult<UserCollection, User, UserCollectionResult>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollectionResult"/> class.
        /// </summary>
        public UserCollectionResult() { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollectionResult"/> class with <paramref name="paging"/>.
        /// </summary>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public UserCollectionResult(PagingArgs? paging) : base(paging) { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="UserCollectionResult"/> class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        /// <param name="paging">The optional <see cref="PagingArgs"/>.</param>
        public UserCollectionResult(IEnumerable<User> items, PagingArgs? paging = null) : base(paging) => Items.AddRange(items);
    }
}

#pragma warning restore
#nullable restore