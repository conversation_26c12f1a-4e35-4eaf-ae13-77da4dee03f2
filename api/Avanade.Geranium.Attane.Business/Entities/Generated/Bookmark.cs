/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the お気に入り entity.
    /// </summary>
    public partial class Bookmark : EntityBase, IPrimaryKey
    {
        private string _userId;
        private string _kind;
        private string _properties;
        private string _id;
        private string _title;
        private string _note;
        private DateTime _displayDate;
        private DateTime _reposCreatedDate;
        private DateTime _reposUpdatedDate;
        private DateTime? _timeStamp;

        /// <summary>
        /// Gets or sets the User Id.
        /// </summary>
        public string UserId { get => _userId; set => SetValue(ref _userId, value); }

        /// <summary>
        /// Gets or sets the データソースの種類.
        /// </summary>
        public string Kind { get => _kind; set => SetValue(ref _kind, value); }

        /// <summary>
        /// Gets or sets the 個々のデータソースを特定する属性.
        /// </summary>
        public string Properties { get => _properties; set => SetValue(ref _properties, value); }

        /// <summary>
        /// Gets or sets the Id.
        /// </summary>
        public string Id { get => _id; set => SetValue(ref _id, value); }

        /// <summary>
        /// Gets or sets the Title.
        /// </summary>
        public string Title { get => _title; set => SetValue(ref _title, value); }

        /// <summary>
        /// Gets or sets the Note.
        /// </summary>
        public string Note { get => _note; set => SetValue(ref _note, value); }

        /// <summary>
        /// Gets or sets the Display Date.
        /// </summary>
        public DateTime DisplayDate { get => _displayDate; set => SetValue(ref _displayDate, value); }

        /// <summary>
        /// Gets or sets the Repos Created Date.
        /// </summary>
        public DateTime ReposCreatedDate { get => _reposCreatedDate; set => SetValue(ref _reposCreatedDate, value); }

        /// <summary>
        /// Gets or sets the Repos Updated Date.
        /// </summary>
        public DateTime ReposUpdatedDate { get => _reposUpdatedDate; set => SetValue(ref _reposUpdatedDate, value); }

        /// <summary>
        /// Gets or sets the Time Stamp.
        /// </summary>
        public DateTime? TimeStamp { get => _timeStamp; set => SetValue(ref _timeStamp, value); }

        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The <see cref="CompositeKey"/>.</returns>
        /// <param name="userId">The <see cref="UserId"/>.</param>
        /// <param name="id">The <see cref="Id"/>.</param>
        public static CompositeKey CreatePrimaryKey(string userId, string id) => new CompositeKey(userId, id);

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): <see cref="UserId"/>, <see cref="Id"/>).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey(UserId, Id);

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            yield return CreateProperty(nameof(UserId), UserId, v => UserId = v);
            yield return CreateProperty(nameof(Kind), Kind, v => Kind = v);
            yield return CreateProperty(nameof(Properties), Properties, v => Properties = v);
            yield return CreateProperty(nameof(Id), Id, v => Id = v);
            yield return CreateProperty(nameof(Title), Title, v => Title = v);
            yield return CreateProperty(nameof(Note), Note, v => Note = v);
            yield return CreateProperty(nameof(DisplayDate), DisplayDate, v => DisplayDate = v);
            yield return CreateProperty(nameof(ReposCreatedDate), ReposCreatedDate, v => ReposCreatedDate = v);
            yield return CreateProperty(nameof(ReposUpdatedDate), ReposUpdatedDate, v => ReposUpdatedDate = v);
            yield return CreateProperty(nameof(TimeStamp), TimeStamp, v => TimeStamp = v);
        }
    }
}

#pragma warning restore
#nullable restore