/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 検索結果要求 entity.
    /// </summary>
    public partial class SearchResultRequest : EntityBase
    {
        private Guid _reqId;
        private string[]? _knownPids;

        /// <summary>
        /// Gets or sets the 検索ID.
        /// </summary>
        public Guid ReqId { get => _reqId; set => SetValue(ref _reqId, value); }

        /// <summary>
        /// Gets or sets the 既知の検索処理ID.
        /// </summary>
        public string[]? KnownPids { get => _knownPids; set => SetValue(ref _knownPids, value); }

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            yield return CreateProperty(nameof(ReqId), ReqId, v => ReqId = v);
            yield return CreateProperty(nameof(KnownPids), KnownPids, v => KnownPids = v);
        }
    }
}

#pragma warning restore
#nullable restore