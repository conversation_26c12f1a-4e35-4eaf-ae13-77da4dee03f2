/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 単一のデータソースに対する検索要求 entity.
    /// </summary>
    public partial class SearchProcessRequest : SearchProcessBase, IPrimaryKey
    {
        private Guid _pid;
        private DataSources? _dataSources;

        /// <summary>
        /// Gets or sets the 検索処理ID.
        /// </summary>
        public Guid Pid { get => _pid; set => SetValue(ref _pid, value); }

        /// <summary>
        /// Gets or sets the データソースの情報.
        /// </summary>
        public DataSources? DataSources { get => _dataSources; set => SetValue(ref _dataSources, value); }

        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The <see cref="CompositeKey"/>.</returns>
        /// <param name="pid">The <see cref="Pid"/>.</param>
        public static CompositeKey CreatePrimaryKey(Guid pid) => new CompositeKey(pid);

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): <see cref="Pid"/>).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey(Pid);

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            foreach (var pv in base.GetPropertyValues())
                yield return pv;

            yield return CreateProperty(nameof(Pid), Pid, v => Pid = v);
            yield return CreateProperty(nameof(DataSources), DataSources, v => DataSources = v);
        }
    }
}

#pragma warning restore
#nullable restore