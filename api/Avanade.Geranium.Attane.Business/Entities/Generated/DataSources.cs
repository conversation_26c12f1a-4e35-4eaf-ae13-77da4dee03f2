/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 複数データソース entity.
    /// </summary>
    public partial class DataSources : EntityBase
    {
        private Avanade.Geranium.Attane.Shared.DataSourceKind? _kind;
        private Dictionary<string, string>? _globalProperties;
        private Dictionary<string, string>[]? _properties;

        /// <summary>
        /// Gets or sets the データソースの種類.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.DataSourceKind? Kind { get => _kind; set => SetValue(ref _kind, value); }

        /// <summary>
        /// Gets or sets the 複数データソース全体に対する属性.
        /// </summary>
        public Dictionary<string, string>? GlobalProperties { get => _globalProperties; set => SetValue(ref _globalProperties, value); }

        /// <summary>
        /// Gets or sets the 個々のデータソースを特定する属性.
        /// </summary>
        public Dictionary<string, string>[]? Properties { get => _properties; set => SetValue(ref _properties, value); }

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            yield return CreateProperty(nameof(Kind), Kind, v => Kind = v);
            yield return CreateProperty(nameof(GlobalProperties), GlobalProperties, v => GlobalProperties = v);
            yield return CreateProperty(nameof(Properties), Properties, v => Properties = v);
        }
    }
}

#pragma warning restore
#nullable restore