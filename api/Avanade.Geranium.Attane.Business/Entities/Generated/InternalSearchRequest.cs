/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 内部状態を含んだ検索要求 entity.
    /// </summary>
    public partial class InternalSearchRequest : SearchRequest
    {
        private Avanade.Geranium.Attane.Shared.SearchConditionTree? _conditionKeywords;
        private Avanade.Geranium.Attane.Shared.SearchState? _state;
        private Dictionary<string, string> _tokens;
        private int _processCount;

        /// <summary>
        /// Gets or sets the トークン分割した検索条件.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.SearchConditionTree? ConditionKeywords { get => _conditionKeywords; set => SetValue(ref _conditionKeywords, value); }

        /// <summary>
        /// Gets or sets the 検索要求の状態.
        /// </summary>
        public Avanade.Geranium.Attane.Shared.SearchState? State { get => _state; set => SetValue(ref _state, value); }

        /// <summary>
        /// Gets or sets the OBOトークン.
        /// </summary>
        public Dictionary<string, string> Tokens { get => _tokens; set => SetValue(ref _tokens, value); }

        /// <summary>
        /// Gets or sets the 処理数.
        /// </summary>
        public int ProcessCount { get => _processCount; set => SetValue(ref _processCount, value); }

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            foreach (var pv in base.GetPropertyValues())
                yield return pv;

            yield return CreateProperty(nameof(ConditionKeywords), ConditionKeywords, v => ConditionKeywords = v);
            yield return CreateProperty(nameof(State), State, v => State = v);
            yield return CreateProperty(nameof(Tokens), Tokens, v => Tokens = v);
            yield return CreateProperty(nameof(ProcessCount), ProcessCount, v => ProcessCount = v);
        }
    }
}

#pragma warning restore
#nullable restore