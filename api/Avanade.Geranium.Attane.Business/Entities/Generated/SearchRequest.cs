/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Avanade.Geranium.Attane.Business.Entities
{
    /// <summary>
    /// Represents the 検索要求 entity.
    /// </summary>
    public partial class SearchRequest : EntityBase, IPrimaryKey
    {
        private Guid _reqId;
        private string? _condition;
        private string? _userId;
        private Dictionary<string, string>? _context;
        private Avanade.Geranium.Attane.Shared.SearchConditionTree? _parsedCondition;

        /// <summary>
        /// Gets or sets the 検索ID.
        /// </summary>
        public Guid ReqId { get => _reqId; set => SetValue(ref _reqId, value); }

        /// <summary>
        /// Gets or sets the 検索条件.
        /// </summary>
        public string? Condition { get => _condition; set => SetValue(ref _condition, value); }

        /// <summary>
        /// Gets or sets the 対象のユーザーID.
        /// </summary>
        public string? UserId { get => _userId; set => SetValue(ref _userId, value); }

        /// <summary>
        /// Gets or sets the 検索結果に関連する状態.
        /// </summary>
        public Dictionary<string, string>? Context { get => _context; set => SetValue(ref _context, value); }

        /// <summary>
        /// Gets or sets the 解析済検索条件.
        /// </summary>
        [JsonIgnore]
        public Avanade.Geranium.Attane.Shared.SearchConditionTree? ParsedCondition { get => _parsedCondition; set => SetValue(ref _parsedCondition, value); }

        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The <see cref="CompositeKey"/>.</returns>
        /// <param name="reqId">The <see cref="ReqId"/>.</param>
        public static CompositeKey CreatePrimaryKey(Guid reqId) => new CompositeKey(reqId);

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): <see cref="ReqId"/>).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey(ReqId);

        /// <inheritdoc/>
        protected override IEnumerable<IPropertyValue> GetPropertyValues()
        {
            yield return CreateProperty(nameof(ReqId), ReqId, v => ReqId = v);
            yield return CreateProperty(nameof(Condition), Condition, v => Condition = v);
            yield return CreateProperty(nameof(UserId), UserId, v => UserId = v);
            yield return CreateProperty(nameof(Context), Context, v => Context = v);
            yield return CreateProperty(nameof(ParsedCondition), ParsedCondition, v => ParsedCondition = v);
        }
    }

    /// <summary>
    /// Represents the <see cref="SearchRequest"/> collection.
    /// </summary>
    public partial class SearchRequestCollection : EntityBaseCollection<SearchRequest, SearchRequestCollection>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollection"/> class.
        /// </summary>
        public SearchRequestCollection() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollection"/> class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        public SearchRequestCollection(IEnumerable<SearchRequest> items) => AddRange(items);
    }

    /// <summary>
    /// Represents the <see cref="SearchRequest"/> collection result.
    /// </summary>
    public class SearchRequestCollectionResult : EntityCollectionResult<SearchRequestCollection, SearchRequest, SearchRequestCollectionResult>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollectionResult"/> class.
        /// </summary>
        public SearchRequestCollectionResult() { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollectionResult"/> class with <paramref name="paging"/>.
        /// </summary>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public SearchRequestCollectionResult(PagingArgs? paging) : base(paging) { }
        
        /// <summary>
        /// Initializes a new instance of the <see cref="SearchRequestCollectionResult"/> class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        /// <param name="paging">The optional <see cref="PagingArgs"/>.</param>
        public SearchRequestCollectionResult(IEnumerable<SearchRequest> items, PagingArgs? paging = null) : base(paging) => Items.AddRange(items);
    }
}

#pragma warning restore
#nullable restore