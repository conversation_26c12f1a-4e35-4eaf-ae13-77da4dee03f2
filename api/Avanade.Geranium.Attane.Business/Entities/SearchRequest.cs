namespace Avanade.Geranium.Attane.Business.Entities
{
    public partial class SearchRequest
    {
        public override void CopyFrom(object? other)
        {
            if (other != null && other is SearchRequest)
            {
                CopyFrom((SearchRequest)other);
            }
        }

        public void CopyFrom(SearchRequest request)
        {
            // TODO: make it tidy
            ReqId = request.ReqId;
            Condition = request.Condition;
            UserId = request.UserId;
            Context = request.Context;
            ParsedCondition = request.ParsedCondition;
        }
    }
}