using Avanade.Geranium.Attane.Shared;

namespace Avanade.Geranium.Attane.Business.Entities
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Major Code Smell", "S4035:Classes implementing \"IEquatable<T>\" should be sealed", Justification = "自動生成コードのため")]
    partial class SearchRequestResult
    {
        public SearchRequestResult() { }

        public SearchRequestResult(InternalSearchRequest request, SearchState? state, IEnumerable<SearchProcessRequestResult> searchProcessRequestResults) : this()
        {
            var sr = new SearchRequest();
            sr.CopyFrom(request);
            CopyFrom(sr);
            _state = state;
            _conditionKeywords = request.ConditionKeywords;
            _results = searchProcessRequestResults.ToArray();
        }
    }
}
