using Avanade.Geranium.Attane.Business.Entities;
using System;
using System.Collections.Generic;
using System.Text;

namespace Avanade.Geranium.Attane.Business.Configuration
{
    /// <summary>
    /// 検索データソースに対する構成
    /// </summary>
    public class SearchConfiguration
    {
        /// <summary>
        /// 取得するトークンのキー
        /// </summary>
        public string[]? TokenKeys { get; set; }

        /// <summary>
        /// キュー名
        /// </summary>
        public string? QueueName { get; set; }

        public DataSources[]? DataSources { get; set; }
    }
}
