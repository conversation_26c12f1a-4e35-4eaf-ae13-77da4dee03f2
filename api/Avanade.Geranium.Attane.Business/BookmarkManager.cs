using Avanade.Geranium.Attane.Business.Entities;
using Avanade.Geranium.Attane.Common.Constants;
using Avanade.Geranium.Attane.Business.Exceptions;
using CoreEx.Entities;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Avanade.Geranium.Attane.Business
{
    partial class BookmarkManager
    {
        /// <summary>
        /// Gets the specified <see cref="Bookmark[]"/>.
        /// </summary>
        /// <param name="userId">The User Id.</param>
        /// <returns>The selected <see cref="Bookmark[]"/> where found.</returns>
        public async Task<Bookmark[]?> GetBookmarksAsync(string userId) => await ManagerInvoker.Current.InvokeAsync(this, async (token) =>
        {
            var user = await _dataService.GetBookmarkUserAsync(userId).ConfigureAwait(false);
            if (user == null)
            {
                return null;
            }

            Cleaner.CleanUp(userId);

            return Cleaner.Clean(await _dataService.GetBookmarksAsync(userId).ConfigureAwait(false));
        }, InvokerArgs.Read).ConfigureAwait(false);

        public async Task DeleteBookmarkOnImplementationAsync(string userId, string articleId)
        {
            Cleaner.CleanUp(userId, articleId);
            try
            {
                await _dataService.DeleteBookmarkAsync(userId, articleId).ConfigureAwait(false);
            }
            catch (BookmarkDeleteException)
            {
                _logger.LogTrace(TraceResultMessage.ResourceNotFound, nameof(DeleteBookmarkAsync));
            }
        }
    }
}
