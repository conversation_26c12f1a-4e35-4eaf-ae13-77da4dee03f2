namespace Avanade.Geranium.Attane.Business
{
    public partial class UserManager
    {
        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The selected <see cref="Dictionary{string, string}"/> where found.</returns>
        /// <remarks>
        /// <see cref="GetContextAsync(string)"/> の実装
        /// </remarks>
        public async Task<Dictionary<string, string>?> GetContextOnImplementationAsync(string userId) =>
            (await _dataService.GetAsync(userId).ConfigureAwait(false))?.Context;

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報を更新します.
        /// </summary>
        /// <param name="value">The <see cref="Dictionary{string, string}"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The updated <see cref="Dictionary{string, string}"/>.</returns>
        /// <remarks>
        /// <see cref="UpdateContextAsync(Dictionary{string, string}, string)"/> の実装
        /// </remarks>
        public async Task<Dictionary<string, string>> UpdateContextOnImplementationAsync(Dictionary<string, string> value, string userId)
        {
            var user = (await _dataService.GetAsync(userId).ConfigureAwait(false)) ?? new User { UserId = userId };
            user.Context = value;

            await _dataService.CreateOrUpdateAsync(userId, user);
            return value;
        }

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <remarks>
        /// <see cref="DeleteContextAsync(string)"/> の実装
        /// </remarks>
        public async Task DeleteContextOnImplementationAsync(string userId)
        {
            var user = await _dataService.GetAsync(userId).ConfigureAwait(false);
            if (user?.Context == null)
            {
                return;
            }
            await _dataService.DeleteAsync(userId);
        }

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報の特定の項目を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The selected <see cref="string"/> where found.</returns>
        /// <remarks>
        /// <see cref="GetContextFieldAsync(string, string)"/> の実装
        /// </remarks>
        public async Task<string?> GetContextFieldOnImplementationAsync(string userId, string fieldName)
        {
            var user = await _dataService.GetAsync(userId).ConfigureAwait(false);
            return (user?.Context?.TryGetValue(fieldName, out var value) ?? false) ? value : null;
        }

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報の特定の項目を更新します.
        /// </summary>
        /// <param name="value">The <see cref="string"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <returns>The updated <see cref="string"/>.</returns>
        /// <remarks>
        /// <see cref="UpdateContextFieldAsync(string, string, string)"/> の実装
        /// </remarks>
        public async Task<string?> UpdateContextFieldOnImplementationAsync(string value, string userId, string fieldName)
        {
            var user = (await _dataService.GetAsync(userId).ConfigureAwait(false)) ?? new User { UserId = userId };
            if (user.Context == null)
            {
                user.Context = new Dictionary<string, string>();
            }
            user.Context[fieldName] = value;
            await _dataService.CreateOrUpdateAsync(userId, user);
            return value;
        }

        /// <summary>
        /// 与えられたUserIdに対応するコンテキスト情報の特定の項目を削除します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <param name="fieldName">The 対象のフィールド名.</param>
        /// <remarks>
        /// <see cref="DeleteContextFieldAsync(string, string)"/> の実装
        /// </remarks>
        public async Task DeleteContextFieldOnImplementationAsync(string userId, string fieldName)
        {
            var user = await _dataService.GetAsync(userId).ConfigureAwait(false);
            if (user?.Context?.ContainsKey(fieldName) ?? false)
            {
                user.Context.Remove(fieldName);
                await _dataService.CreateOrUpdateAsync(userId, user);
            }
        }
    }
}
