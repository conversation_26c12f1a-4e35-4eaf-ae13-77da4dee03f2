using Avanade.Geranium.Attane.Shared;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;

namespace Avanade.Geranium.Attane.Business
{
    public static class SearchConditionParser
    {
        public const string OPERATOR_OR = "OR";
        public const string OPERATOR_AND = "AND";
        public const string OPERATOR_OPEN_PAREN = "(";
        public const string OPERATOR_CLOSE_PAREN = ")";

        public static SearchConditionTree? Parse(string searchWords) =>
            ParseExpression(Tokenize(searchWords));
        public static SearchConditionTree? ParseExpression(string[] searchWords)
        {
            var stack = new Stack<string>(searchWords.Reverse());
            // Stackは末尾から要素を取得するため、逆順にしている
            var result = ParseLvN(stack);

            if (stack.Count > 0)
            {
                throw new SyntaxErrorException("閉じ括弧に対応する開き括弧がありません");
            }
            return result;
        }


        /// <summary>
        /// 検索キーワードを抽出します。
        /// </summary>
        /// <param name="condition">検索条件文字列</param>
        /// <returns>キーワードの配列</returns>
        public static string[] Tokenize(string condition)
        {
            var quoted = new Regex("^([^\"]*)(\"[^\"]*\")(.*)$");

            var rest = condition;
            var tokens = new List<string>();
            for (var quotedMatch = quoted.Match(rest); quotedMatch.Success; quotedMatch = quoted.Match(rest))
            {
                var pre = quotedMatch.Groups[1].Value;
                tokens.AddRange(SplitWords(pre));

                var matched = quotedMatch.Groups[2].Value;
                tokens.Add(matched);

                rest = quotedMatch.Groups[3].Value;
            }
            tokens.AddRange(SplitWords(rest));
            if (tokens.Count == 0)
            {
                return Array.Empty<string>();
            }
            var normalizedTokens = tokens.Select(token => Zipangu.CharConversion.Convert(token.ToLower(), Zipangu.KanaConv.HalfKatakanaToKatakana, Zipangu.AsciiConv.ToNarrow)).ToArray();
            if (normalizedTokens == null)
            {
                return Array.Empty<string>();
            }

            return tokens.ToArray();

            static string[] SplitWords(string source)
            {
                // 括弧は前後にスペースを入れることで単一トークン化する
                return source
                    .Replace("(", " ( ")
                    .Replace(")", " ) ")
                    .Split(' ', StringSplitOptions.RemoveEmptyEntries);
            }
        }

        /// <summary>
        /// 次の「式」(キーワードを並べたもの)を解析します。
        /// </summary>
        /// <param name="stack">検索キーワードの格納されたスタック</param>
        /// <returns>式</returns>
        /// <exception cref="SyntaxErrorException">構文が誤っています</exception>
        public static SearchConditionTree? ParseLvN(Stack<string> stack)
        {
            var leftHand = ParseLv3(stack);
            var result = new List<SearchConditionTree>();

            // termは通常項目または開き括弧で始まる
            while (IsNormalItem(stack.PeekOrNull()) || stack.PeekOrNull() == "(")
            {
                var rightHand = ParseLv3(stack);
                if (rightHand == null)
                {
                    throw new SyntaxErrorException("要素が必要な場所に要素がありません");
                }
                result.Add(rightHand);
            }
            if (result.Count == 0)
            {
                return leftHand;
            }
            if (leftHand == null)
            {
                throw new SyntaxErrorException("要素が必要な場所に要素がありません");
            }
            result.Insert(0, leftHand);
            return new SearchConditionTree(SearchConditionOperator.And, result);
        }

        private static bool IsNormalItem(string? value) =>
            value switch
            {
                null => false,
                OPERATOR_AND => false,
                OPERATOR_OR => false,
                OPERATOR_OPEN_PAREN => false,
                OPERATOR_CLOSE_PAREN => false,
                _ => true,
            };

        /// <summary>
        /// 次の「項」(ORで結合したもの)を解析します。
        /// </summary>
        /// <param name="stack">検索キーワードの格納されたスタック</param>
        /// <returns>項</returns>
        /// <exception cref="SyntaxErrorException">構文が誤っています</exception>
        private static SearchConditionTree? ParseLv3(Stack<string> stack)
            => ParseBinaryOperator(stack, OPERATOR_OR, ParseLv2, "OR", SearchConditionOperator.Or);

        /// <summary>
        /// 次の「項」(ANDで結合したもの)を解析します。
        /// </summary>
        /// <param name="stack">検索キーワードの格納されたスタック</param>
        /// <returns>項</returns>
        /// <exception cref="SyntaxErrorException">構文が誤っています</exception>
        private static SearchConditionTree? ParseLv2(Stack<string> stack)
            => ParseBinaryOperator(stack, OPERATOR_AND, ParseLv1, "AND", SearchConditionOperator.And);

        /// <summary>
        /// 次の「因数」(単独、または括弧で囲ったもの)を解析します。
        /// </summary>
        /// <param name="stack">検索キーワードの格納されたスタック</param>
        /// <returns>項</returns>
        /// <exception cref="SyntaxErrorException">構文が誤っています</exception>
        private static SearchConditionTree? ParseLv1(Stack<string> stack)
        {
            if (stack.PeekOrNull() == OPERATOR_OPEN_PAREN)
            {
                stack.Pop();
                if (stack.PeekOrNull() == OPERATOR_CLOSE_PAREN)
                {
                    throw new SyntaxErrorException("括弧の中が空です");
                }
                var result = ParseLvN(stack);

                if (stack.PeekOrNull() != OPERATOR_CLOSE_PAREN)
                {
                    throw new SyntaxErrorException("開き括弧に対応する閉じ括弧がありません");
                }
                stack.Pop();
                return result;
            }
            else if (IsNormalItem(stack.PeekOrNull()))
            {
                return new SearchConditionTree(Dequote(stack.Pop()));
            }
            else
            {
                return stack.PeekOrNull() switch
                {
                    OPERATOR_CLOSE_PAREN => throw new SyntaxErrorException("閉じ括弧に対応する開き括弧がありません"),
                    OPERATOR_AND => throw new SyntaxErrorException("ANDの左辺がありません"),
                    OPERATOR_OR => throw new SyntaxErrorException("ORの左辺がありません"),
                    null => null,
                    _ => throw new SyntaxErrorException("要素が必要な場所に要素がありません"),
                };
            }

        }

        /// <summary>
        /// 文字列のクォートがあれば除去します
        /// </summary>
        /// <param name="value">対象文字列</param>
        /// <returns>クォートされていれば除去した文字列、されていなければ元の文字列</returns>
        private static string Dequote(string value) =>
            value.Length > 1 && value.StartsWith("\"") && value.EndsWith("\"") ?
                value[1..^1] : value;

        private static SearchConditionTree? ParseBinaryOperator(Stack<string> stack, string operatorString, Func<Stack<string>, SearchConditionTree?> nextLevelParse, string operatorMessage, SearchConditionOperator searchRequestOperator)
        {
            var leftHand = nextLevelParse(stack);
            var result = new List<SearchConditionTree>();

            while (stack.PeekOrNull() == operatorString)
            {
                stack.Pop();

                var rightHand = nextLevelParse(stack);
                if (rightHand == null)
                {
                    throw new SyntaxErrorException($"{operatorMessage}の右辺がありません");
                }
                result.Add(rightHand);
            }
            if (result.Count == 0)
            {
                return leftHand;
            }
            if (leftHand == null)
            {
                throw new SyntaxErrorException($"{operatorMessage}の左辺がありません");
            }

            result.Insert(0, leftHand);
            return new SearchConditionTree(searchRequestOperator, result);
        }

        public static T? PeekOrNull<T>(this Stack<T> stack) where T : class? =>
            stack.Count == 0 ? (T?)null : stack.Peek();
    }
}
