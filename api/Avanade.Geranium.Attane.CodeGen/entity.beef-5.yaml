eventSubjectRoot: Search
# eventSubjectFormat: NameOnly
eventActionFormat: PastTense
# eventCasing: Lower
eventSourceRoot: Avanade/Geranium.Attane
eventSourceKind: Relative
eventPublish: None
# appBasedAgentArgs: true
webApiAutoLocation: true
refDataText: true
jsonSerializer: SystemText
entities:
- name: Bookmark
  text: お気に入り
  validator: BookmarkValidator
  autoImplement: None
  excludeData: Exclude
  webApiRoutePrefix: users
  webApiAuthorize: Authorize
  webApiCtorParams:
    - 'Microsoft.Extensions.Logging.ILogger<BookmarkController>^Logger'
    - 'Caller.ICallerProvider^CallerProvider'
  managerCtorParams:
    - "Microsoft.Extensions.Logging.ILogger<BookmarkManager>^Logger"
  dataSvcCtorParams:
    - "Microsoft.Extensions.Logging.ILogger<BookmarkDataSvc>^Logger"
  properties: [
    { name: UserId, type: string, nullable: false, primaryKey: true },
    { name: Kind, text: 'データソースの種類', type: string, nullable: false },
    { name: Properties, text: '個々のデータソースを特定する属性', type: 'string', nullable: false },
    { name: Id, type: string, nullable: false, primaryKey: true },
    { name: Title, type: string, nullable: false },
    { name: Note, type: string, nullable: false },
    { name: DisplayDate, type: DateTime, nullable: false },
    { name: ReposCreatedDate, type: DateTime },
    { name: ReposUpdatedDate, type: DateTime },
    { name: TimeStamp, type: DateTime, nullable: true },
  ]
  operations:
    # service
    - { name: GetBookmarks, type: Get, webApiRoute: '{userId}/bookmarks', returnType: 'Bookmark[]',
        text: '与えられたUserIdに対応する{{Bookmark}}の配列を取得します',
        validateUserId: userId,
        excludeManager: true,
        webApiAlternateStatus: NoContent,
        logging: true,
        parameters: [
          { name: UserId, type: string, nullable: false },
        ]
      }
    - { name: PutBookmark, type: Update, webApiRoute: '{userId}/bookmarks/{articleId}',
        text: '{{Bookmark}}を登録し、既に存在している場合は内容を更新します',
        returnText: 'The created or updated {{Bookmark}}',
        webApiMethod: HttpPut,
        webApiCustom: true,
        webApiStatus: Created,
        validateUserId: userId,
        logging: true,
        parameters: [
          { name: UserId, type: string, nullable: false, layerPassing: ToManagerSet },
          { name: ArticleId, type: string, nullable: false, },
        ]
      }
    - { name: DeleteBookmark, type: Delete, webApiRoute: '{userId}/bookmarks/{articleId}',
        text: '与えられたキーに対応する{{Bookmark}}を削除します',
        webApiMethod: HttpDelete,
        webApiCustom: true,
        managerCustom: true,
        validateUserId: userId,
        logging: true,
        parameters: [
          { name: UserId, type: string, nullable: false, },
          { name: ArticleId, type: string, nullable: false, },
        ]
      }
    # internal service
    - { name: GetBookmarkUser, type: Get, returnType: Bookmark,
        text: 'Gets the specified user represented as {{Bookmark}}',
        returnText: The selected user where found,
        excludeWebApi: true,
        excludeWebApiAgent: true,
        excludeIManager: true,
        excludeManager: true,
        parameters : [
          { name: UserId, type: string, nullable: false, },
        ]
      }
- name: SearchRequest
  text: '検索要求'
  isEntity: true
  collection: true
  collectionResult: true
  webApiRoutePrefix: users
  webApiCtorParams:
    - 'Microsoft.Extensions.Logging.ILogger<SearchRequestController>^Logger'
    - 'Caller.ICallerProvider^CallerProvider'
    - 'Token.ITokenIssuer^TokenIssuer'
    - 'Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration>^Configuration'
    - 'Avanade.Teams.Auth.TokenControllerHelper^TokenControllerHelper'
  managerCtorParams:
    - 'Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration>^Configuration'
    - 'IIdentifierGenerator^IdentifierGenerator'
    - 'CoreEx.Events.IEventPublisher^EvtPub'
  dataCtorParams:
    - 'Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient^TableStorageClient'
    - 'Avanade.Geranium.Attane.Infrastructure.Data.Clients.IQueueStorageClient^QueueStorageClient'
    - 'Microsoft.Extensions.Logging.ILogger<SearchRequestData>^Logger'
    - 'Microsoft.Extensions.Options.IOptions<Avanade.Geranium.Attane.Business.Configuration.SearchConfiguration>^Configuration'
    - 'CoreEx.Events.IEventPublisher^EvtPub'
  webApiUsing: 'using Microsoft.Extensions.Logging;'
  webApiAuthorize: Authorize
  properties:
    - { name: ReqId, text: '検索ID', type: Guid, primaryKey: true, identifierGenerator: IGuidIdentifierGenerator }
    - { name: Condition, text: '検索条件', type: string }
    - { name: UserId, text: '対象のユーザーID', type: string }
    - { name: Context, text: '検索結果に関連する状態', type: 'Dictionary<string, string>' }
    - { name: ParsedCondition, text: '解析済検索条件', type: 'Avanade.Geranium.Attane.Shared.SearchConditionTree', serializationIgnore: true }
  operations:
    # service interface
    - { name: Get, type: Get, webApiRoute: '{userId}/search/result', returnType: SearchRequestResult, webApiAlternateStatus: NoContent,
        text: '与えられたUserIdに対応する検索結果を取得します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
      }
    - { name: GetWith, type: Custom, webApiMethod: HttpPost, webApiRoute: '{userId}/search/result', returnType: SearchRequestResult, returnTypeNullable: true, webApiAlternateStatus: NoContent,
        text: '与えられたUserIdに対応する検索結果について、既知の結果を除いたものを取得します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
          { name: Request, text: '検索結果要求', type: SearchResultRequest, nullable: true, webApiFrom: FromBody },
        ],
      }
    - { name: Register, type: Create, webApiRoute: '{userId}/search',
        text: '与えられたUserIdに対応する検索要求を登録します',
        webApiStatus: Accepted,
        validateUserId: userId, logging: true,
        validator: SearchRequestValidator,
        excludeIManager: true, excludeManager: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        managerAdditionalArguments: ', IssueTokensAsync()',
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
      }
    - {
        name: Cancell,
        type: Custom,
        webApiRoute: '{userId}/search/cancellation',
        text: '与えられたUserIdに対応する検索をキャンセルします',
        managerCustom: true,
        validateUserId: userId,
        logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
        returnType: string,
      }
    - { name: GetContext, type: Get, webApiRoute: '{userId}/search/context',
        text: '与えられたUserIdに対応する検索結果に付随するコンテキスト情報を取得します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
        returnType: 'System.Collections.Generic.Dictionary<string, string>', returnTypeNullable: true,
      }
    - { name: UpdateContext, type: Update, webApiRoute: '{userId}/search/context',
        text: '与えられたUserIdに対応する検索結果に付随するコンテキスト情報を更新します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        valueType: 'Dictionary<string, string>', valueTypeFull: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
        returnType: 'System.Collections.Generic.Dictionary<string, string>'
      }
    - { name: DeleteContext, type: Delete, webApiRoute: '{userId}/search/context',
        text: '与えられたUserIdに対応する検索結果に付随するコンテキスト情報を削除します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
      }
    - { name: GetContextField, type: Get, webApiRoute: '{userId}/search/context/{fieldName}',
        text: '与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を取得します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
          { name: FieldName, text: '対象のフィールド名', type: string, nullable: false },
        ],
        returnType: string, returnTypeNullable: true,
      }
    - { name: UpdateContextField, type: Update, webApiRoute: '{userId}/search/context/{fieldName}',
        text: '与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を更新します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        valueType: 'string', valueTypeFull: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
          { name: FieldName, text: '対象のフィールド名', type: string, nullable: false },
        ],
        returnType: string,
      }
    - { name: DeleteContextField, type: Delete, webApiRoute: '{userId}/search/context/{fieldName}',
        text: '与えられたUserIdに対応する検索結果に付随するコンテキスト情報の特定の項目を削除します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
          { name: FieldName, text: '対象のフィールド名', type: string, nullable: false },
        ],
      }
    # manager only
    - { name: Register, type: Create,
        text: '与えられたUserIdに対応する検索要求を登録します',
        managerCustom: true,
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false, layerPassing: ToManagerSet },
          { name: Tokens, text: 'リクエストのトークン', type: 'Task<Dictionary<string, string>>', nullable: false }
        ],
      }
    # implementation
    - { name: GetRequest, type: Custom, returnType: InternalSearchRequest,
        text: '指定したユーザーの現在の検索要求を取得します',
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
        ]
      }
    - { name: CreateOrUpdateRequest, type: Custom,
        text: '指定したユーザーの検索要求を登録します',
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
          { name: Request, type: InternalSearchRequest },
        ],
      }
    - { name: DeleteRequest, type: Custom,
        text: '指定したユーザーの検索要求および結果を削除します', 
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
        ],
      }
    - { name: CreateProcess, type: Custom,
        text: '指定したユーザーの、特定のデータソースに対する検索処理を登録します', 
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
          { name: searchProcessRequest, type: SearchProcessRequest, nullable: false },
        ],
      }
    - { name: GetResults, type: Custom,
        text: '各データソースからの検索結果を取得します', 
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        returnType: 'IEnumerable<SearchProcessRequestResult>', returnTypeNullable: false, returnText: '各データソースからの検索結果の列挙',
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
        ],
      }

- name: User
  text: 'ユーザー'
  # isEntity: true
  collection: true
  collectionResult: true
  webApiRoutePrefix: users
  webApiCtorParams:
    - 'Microsoft.Extensions.Logging.ILogger<UserController>^Logger'
    - 'Caller.ICallerProvider^CallerProvider'
    - 'Token.ITokenIssuer^TokenIssuer'
    - 'Avanade.Teams.Auth.TokenControllerHelper^TokenControllerHelper'
  dataCtorParams:
    - 'Avanade.Geranium.Attane.Infrastructure.Data.Clients.ITableStorageClient^TableStorageClient'
    - 'Microsoft.Extensions.Logging.ILogger<UserData>^Logger'
  webApiUsing: 'using Microsoft.Extensions.Logging;'
  webApiAuthorize: Authorize
  properties:
    - { name: UserId, text: '対象のユーザーID', type: string }
    - { name: Context, text: 'コンテキスト', type: 'Dictionary<string, string>' }
  operations:
    # service interface
    - { name: GetContext, type: Get, webApiRoute: '{userId}/context',
        text: '与えられたUserIdに対応するコンテキスト情報を取得します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
        returnType: 'System.Collections.Generic.Dictionary<string, string>', returnTypeNullable: true,
      }
    - { name: UpdateContext, type: Update, webApiRoute: '{userId}/context',
        text: '与えられたUserIdに対応するコンテキスト情報を更新します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        valueType: 'Dictionary<string, string>', valueTypeFull: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
        returnType: 'System.Collections.Generic.Dictionary<string, string>'
      }
    - { name: DeleteContext, type: Delete, webApiRoute: '{userId}/context',
        text: '与えられたUserIdに対応するコンテキスト情報を削除します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
        ],
      }
    - { name: GetContextField, type: Get, webApiRoute: '{userId}/context/{fieldName}',
        text: '与えられたUserIdに対応するコンテキスト情報の特定の項目を取得します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
          { name: FieldName, text: '対象のフィールド名', type: string, nullable: false },
        ],
        returnType: string, returnTypeNullable: true,
      }
    - { name: UpdateContextField, type: Update, webApiRoute: '{userId}/context/{fieldName}',
        text: '与えられたUserIdに対応するコンテキスト情報の特定の項目を更新します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        valueType: 'string', valueTypeFull: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
          { name: FieldName, text: '対象のフィールド名', type: string, nullable: false },
        ],
        returnType: string,
      }
    - { name: DeleteContextField, type: Delete, webApiRoute: '{userId}/context/{fieldName}',
        text: '与えられたUserIdに対応するコンテキスト情報の特定の項目を削除します',
        managerCustom: true,
        validateUserId: userId, logging: true,
        excludeIDataSvc: true, excludeDataSvc: true, excludeIData: true, excludeData: true,
        parameters: [
          { name: UserId, text: '対象のユーザーID', type: string, nullable: false },
          { name: FieldName, text: '対象のフィールド名', type: string, nullable: false },
        ],
      }
    # implementation
    - { name: Get, type: Custom,
        text: '指定したユーザーの情報を取得します',
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
        ],
        returnType: User, returnTypeNullable: true
      }
    - { name: CreateOrUpdate, type: Custom,
        text: '指定したユーザーの情報を登録します',
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
          { name: User, type: User, nullable: false },
        ],
      }
    - { name: Delete, type: Custom,
        text: '指定したユーザーの情報を削除します',
        excludeGrpcAgent: true, excludeWebApiAgent: true, excludeWebApi: true, excludeIManager: true, excludeManager: true,
        parameters: [
          { name: userId, text: '対象のユーザーID', type: string, nullable: false },
        ],
      }



# 内部エンティティ
- name: InternalSearchRequest
  text: '内部状態を含んだ検索要求'
  inherits: SearchRequest
  properties:
    - { name: ConditionKeywords, text: 'トークン分割した検索条件', type: 'Avanade.Geranium.Attane.Shared.SearchConditionTree' }
    - { name: State, text: '検索要求の状態', type: 'Avanade.Geranium.Attane.Shared.SearchState' }
    - { name: Tokens, text: 'OBOトークン', type: 'Dictionary<string, string>', nullable: false }
    - { name: ProcessCount, text: '処理数', type: int }

- name: SearchRequestResult
  text: '検索要求の結果'
  inherits: SearchRequest
  properties:
    - { name: ConditionKeywords, text: 'トークン分割した検索条件', type: 'Avanade.Geranium.Attane.Shared.SearchConditionTree' }
    - { name: State, text: '検索要求の状態', type: 'Avanade.Geranium.Attane.Shared.SearchState' }
    - { name: Results, text: '検索結果', type: 'SearchProcessRequestResult[]' }

# 検索要求
- name: SearchProcessBase
  text: '検索プロセス'
  properties:
    - { name: UserId, text: '対象のユーザーID', type: string }
    - { name: ReqId, text: '検索ID', type: Guid }

# 検索要求
- name: SearchProcessRequest
  text: '単一のデータソースに対する検索要求'
  inherits: SearchProcessBase
  properties:
    - { name: Pid, text: '検索処理ID', type: Guid, primaryKey: true, identifierGenerator: IGuidIdentifierGenerator }
    - { name: DataSources, text: 'データソースの情報', type: DataSources }

- name: DataSources
  text: '複数データソース'
  properties:
    - { name: Kind, text: 'データソースの種類', type: 'Avanade.Geranium.Attane.Shared.DataSourceKind' }
    - { name: GlobalProperties, text: '複数データソース全体に対する属性', type: 'Dictionary<string, string>' }
    - { name: Properties, text: '個々のデータソースを特定する属性', type: 'Dictionary<string, string>[]' }

# 検索結果
- name: SearchResultRequest
  text: '検索結果要求'
  properties:
    - { name: ReqId, text: '検索ID', type: Guid }
    - { name: KnownPids, text: '既知の検索処理ID', type: 'string[]' }

- name: SearchProcessRequestResult
  text: '単一のデータソースからの検索結果'
  inherits: SearchProcessBase
  properties:
    - { name: Pid, text: '検索処理ID', type: string, primaryKey: true }
    - { name: DataSource, text: 'データソースの情報', type: DataSource }
    - { name: Ids, text: 'データキー', type: 'string[]' }
    - { name: HasNext, text: '次ページの有無', type: bool, serializationIgnore: true }

- name: DataSource
  text: 'データソース'
  properties:
    - { name: Kind, text: 'データソースの種類', type: 'Avanade.Geranium.Attane.Shared.DataSourceKind' }
    - { name: Properties, text: 'データソースを特定する属性', type: 'Dictionary<string, string>' }
