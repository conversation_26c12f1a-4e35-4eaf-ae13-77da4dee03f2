﻿/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceBusiness}}.Data
{
    /// <summary>
    /// Provides the <b>ReferenceData</b> data access.
    /// </summary>
    public partial interface IReferenceDataData
    {
{{#each RefDataEntities}}
  {{#unless @first}}

  {{/unless}}
        /// <summary>
        /// Gets all the {{see-comments RefDataQualifiedEntityName}} items.
        /// </summary>
  {{#if Root.CancellationToken}}
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
  {{/if}}
        /// <returns>The {{see-comments RefDataQualifiedEntityCollectionName}}.</returns>
        Task<{{RefDataQualifiedEntityCollectionName}}> {{Name}}GetAllAsync({{#if Root.CancellationToken}}CancellationToken cancellationToken = default{{/if}});
{{/each}}
    }
}

#pragma warning restore
#nullable restore