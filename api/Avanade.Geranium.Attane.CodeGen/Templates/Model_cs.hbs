﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable
{{set-value 'HasUsingStatement' false}}
{{! ===== Using ===== }}
{{#unless Root.IsDataModel}}
using System;{{set-value 'HasUsingStatement' true}}
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
  {{#ifeq JsonSerializer 'SystemText'}}
using System.Text.Json.Serialization;
  {{/ifeq}}
using CoreEx.Entities;
  {{#ifval RefDataType}}
using CoreEx.RefData;
  {{/ifval}}
{{/unless}}
{{#ifeq JsonSerializer 'Newtonsoft'}}
using Newtonsoft.Json;{{set-value 'HasUsingStatement' true}}
{{/ifeq}}
{{#unless Root.IsDataModel}}
  {{#if Root.UsingNamespace1}}
using {{Root.UsingNamespace1}};{{set-value 'HasUsingStatement' true}}
  {{/if}}
  {{#if Root.UsingNamespace2}}
using {{Root.UsingNamespace2}};{{set-value 'HasUsingStatement' true}}
  {{/if}}
  {{#if Root.UsingNamespace3}}
using {{Root.UsingNamespace3}};{{set-value 'HasUsingStatement' true}}
  {{/if}}
{{/unless}}
{{#if HasUsingStatement}}

{{/if}}
{{! ===== Class ===== }}
namespace {{#if Root.IsDataModel}}{{Root.NamespaceBusiness}}.Data.Model{{else}}{{#ifeq Root.RuntimeEntityScope 'Common'}}{{Root.NamespaceCommon}}{{else}}{{Root.NamespaceBusiness}}{{/ifeq}}.Entities{{/if}}
{
    /// <summary>
    /// Represents the {{{Text}}} {{#if Root.IsDataModel}}model{{else}}entity{{/if}}.
    /// </summary>
{{#if Root.IsDataModel}}
    public {{#if Abstract}}abstract {{/if}}partial class {{{EntityName}}}{{#ifval ModelInherits}} : {{{ModelInherits}}}{{/ifval}}{{#ifval ModelImplements}}{{#ifval ModelInherits}}, {{else}} : {{/ifval}}{{{ModelImplements}}}{{/ifval}}{{#ifval RefDataType}}{{#ifeq CoreProperties.Count 0}} { }{{/ifeq}}{{/ifval}}
{{else}}
    public {{#if Abstract}}abstract {{/if}}partial class {{{EntityName}}}{{#ifval ModelInherits}} : {{{ModelInherits}}}{{/ifval}}{{#ifval CommonImplements}}{{#ifval ModelInherits}}, {{else}} : {{/ifval}}{{{CommonImplements}}}{{/ifval}}{{#ifval RefDataType}}{{#ifeq CoreProperties.Count 0}} { }{{/ifeq}}{{/ifval}}
{{/if}}
{{#ifnull RefDataType}}
    {
{{else}}
  {{#ifne CoreProperties.Count 0}}
    {
  {{/ifne}}
{{/ifnull}}
{{! ===== Properties ===== }}
{{#each CoreProperties}}
  {{#unless @first}}

  {{/unless}}
  {{#ifval RefDataType}}
    {{#unless SerializationIgnore}}
      {{#unless RefDataList}}
        {{#unless Root.IsDataModel}}
          {{#ifeq RefDataText 'Optional' 'Always'}}
        /// <summary>
        /// {{{SummaryRefDataText}}}
        /// </summary>
            {{#ifval JsonName}}
              {{#ifeq Parent.JsonSerializer 'SystemText'}}
        [JsonPropertyName("{{#if Root.IsDataModel}}{{JsonDataModelName}}{{else}}{{JsonName}}{{/if}}Text")]
              {{else}}
        [JsonProperty("{{#if Root.IsDataModel}}{{JsonDataModelName}}{{else}}{{JsonName}}{{/if}}Text")]
              {{/ifeq}}
            {{/ifval}}
        public string? {{RefDataTextName}} { get; set; }

          {{/ifeq}}
        {{/unless}}
      {{/unless}}
    {{/unless}}
  {{/ifval}}
        /// <summary>
        /// {{{ModelSummaryText}}}
        /// </summary>
  {{#if Root.IsDataModel}}   
    {{#if DataModelSerializationIgnore}}
        [JsonIgnore]
    {{else}}
      {{#ifval JsonDataModelName}}
        {{#ifeq Parent.JsonSerializer 'Newtonsoft'}}
        [JsonProperty("{{JsonDataModelName}}"{{#if SerializationAlwaysInclude}}, DefaultValueHandling.Include{{/if}})]
        {{else}}
        [JsonPropertyName("{{JsonDataModelName}}")]
        {{/ifeq}}
      {{/ifval}}
      {{#ifeq Parent.JsonSerializer 'SystemText'}}
        {{#if SerializationAlwaysInclude}}
        [JsonIgnore(Condition=JsonIgnoreCondition.Never)]
        {{/if}}
      {{else}}
        {{#ifnull JsonDataModelName}}
          {{#if SerializationAlwaysInclude}}
        [JsonProperty({{#if SerializationAlwaysInclude}}DefaultValueHandling.Include{{/if}})]
          {{/if}}
        {{/ifnull}}
      {{/ifeq}}
    {{/if}}
  {{else}}
    {{#if SerializationIgnore}}
        [JsonIgnore]
    {{else}}
      {{#ifval JsonName}}
        {{#ifeq Parent.JsonSerializer 'Newtonsoft'}}
        [JsonProperty("{{JsonName}}"{{#if SerializationAlwaysInclude}}, DefaultValueHandling.Include{{/if}})]
        {{else}}
        [JsonPropertyName("{{JsonName}}")]
        {{/ifeq}}
      {{/ifval}}
      {{#ifeq Parent.JsonSerializer 'SystemText'}}
        {{#if SerializationAlwaysInclude}}
        [JsonIgnore(Condition = JsonIgnoreCondition.Never)]
        {{/if}}
      {{else}}
        {{#ifnull JsonName}}
          {{#if SerializationAlwaysInclude}}
        [JsonProperty({{#if SerializationAlwaysInclude}}DefaultValueHandling.Include{{/if}})]
          {{/if}}
        {{/ifnull}}
      {{/ifeq}}
    {{/if}}
  {{/if}}
    {{#unless Root.IsDataModel}}
      {{#ifval Annotation1}}
        {{Annotation1}}
      {{/ifval}}
      {{#ifval Annotation2}}
        {{Annotation2}}
      {{/ifval}}
      {{#ifval Annotation3}}
        {{Annotation3}}
      {{/ifval}}
    {{/unless}}
        public {{{ModelType}}} {{Name}} { get; set; }
{{/each}}
{{#unless HasIdentifier}}
  {{#unless RefDataType}}
    {{#ifne PrimaryKeyProperties.Count 0}}
        
        /// <summary>
        /// Creates the primary <see cref="CompositeKey"/>.
        /// </summary>
        /// <returns>The primary <see cref="CompositeKey"/>.</returns>
    {{#each PrimaryKeyProperties}}
        /// <param name="{{ArgumentName}}">The {{{PropertyNameSeeComments}}}.</param>
    {{/each}}
        public static CompositeKey CreatePrimaryKey({{#each PrimaryKeyProperties}}{{#unless @first}}, {{/unless}}{{{PrivateType}}} {{PropertyArgumentName}}{{/each}}) => new CompositeKey({{#each PrimaryKeyProperties}}{{#unless @first}}, {{/unless}}{{PropertyArgumentName}}{{/each}});

        /// <summary>
        /// Gets the primary <see cref="CompositeKey"/> (consists of the following property(s): {{#each PrimaryKeyProperties}}{{#unless @first}}, {{/unless}}{{{PropertyNameSeeComments}}}{{/each}}).
        /// </summary>
        [JsonIgnore]
        public CompositeKey PrimaryKey => CreatePrimaryKey({{#each PrimaryKeyProperties}}{{#unless @first}}, {{/unless}}{{PropertyName}}{{/each}});
    {{/ifne}}
  {{/unless}}
{{/unless}}
{{#ifnull RefDataType}}
    }
{{else}}
  {{#ifne CoreProperties.Count 0}}
    }
  {{/ifne}}
{{/ifnull}}
{{! ===== Collection ===== }}
{{#if Collection}}

    /// <summary>
    /// Represents the {{{EntityNameSeeComments}}} collection.
    /// </summary>
    public partial class {{{EntityCollectionName}}} : {{{CollectionInherits}}} { }
{{/if}}
{{#if CollectionResult}}
  {{#unless Root.IsDataModel}}

    /// <summary>
    /// Represents the {{{EntityNameSeeComments}}} collection result.
    /// </summary>
    public class {{{EntityCollectionResultName}}} : {{{CollectionResultInherits}}}
    {
        /// <summary>
        /// Initializes a new instance of the {{{see-comments EntityCollectionResultName}}} class.
        /// </summary>
        public {{{EntityCollectionResultName}}}() { }
        
        /// <summary>
        /// Initializes a new instance of the {{{see-comments EntityCollectionResultName}}} class with <paramref name="paging"/>.
        /// </summary>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public {{{EntityCollectionResultName}}}(PagingArgs? paging) : base(paging) { }
        
        /// <summary>
        /// Initializes a new instance of the {{{see-comments EntityCollectionResultName}}} class with <paramref name="items"/> to add.
        /// </summary>
        /// <param name="items">The items to add.</param>
        /// <param name="paging">The <see cref="PagingArgs"/>.</param>
        public {{{EntityCollectionResultName}}}(IEnumerable<{{{EntityName}}}> items, PagingArgs? paging = null) : base(paging) => Items.AddRange(items);
    }
  {{/unless}}
{{/if}}
}

#pragma warning restore
#nullable restore