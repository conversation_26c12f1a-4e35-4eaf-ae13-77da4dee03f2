﻿/*
 * This file is automatically generated; any changes will be lost. 
 */

syntax = "proto3";
package {{lower Root.NamespaceCommon}}.grpc.proto;
import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

/*
* Common...
*/

message PagingArgs {
  int64 skip = 1;
  int64 take = 2;
  bool getCount = 3;
}

message PagingResult {
  int64 skip = 1;
  int64 take = 2;
  google.protobuf.Int64Value totalCount = 3;
}

message ChangeLog {
  google.protobuf.Timestamp createdDate = 1;
  google.protobuf.StringValue createdBy = 2;
  google.protobuf.Timestamp updatedDate = 3;
  google.protobuf.StringValue updatedBy = 4;
}

message DateOnly {
  int32 year = 1;
  int32 month = 2;
  int32 day = 3;
}

message Decimal {
  int64 units = 1; // Whole units part of the amount
  sfixed32 nanos = 2; // Nano units of the amount (10^-9); must be same sign as units
}

/*
* Messages...
*/

{{#each GrpcEntities}}
message {{Name}} {
  {{#each GrpcProperties}}
  {{GrpcType}} {{ArgumentName}} = {{GrpcFieldNo}};
  {{/each}}
}
  {{#if Collection}}

message {{Name}}Collection {
  repeated {{Name}} items = 1;
}
  {{/if}}
  {{#if CollectionResult}}

message {{Name}}CollectionResult {
  repeated {{Name}} result = 1;
  PagingResult paging = 2;
}
  {{/if}}

{{/each}}
/*
* Services...
*/
{{#each GrpcEntities}}
  {{#each GrpcOperations}}
    {{#if @first}}

service {{Parent.Name}}GrpcService {
    {{/if}}
  rpc {{Name}} ({{Parent.Name}}{{Name}}Request) returns ({{GrpcReturnType}});
    {{#if @last}}
}
    {{/if}}
  {{/each}}
  {{#each GrpcOperations}}

message {{Parent.Name}}{{Name}}Request {
    {{#each PagingLessParameters}}
  {{GrpcType}} {{ArgumentName}} = {{add @index 1}};
    {{/each}}
    {{#if Paging}}
  PagingArgs Paging = 88;
    {{/if}}
}
  {{/each}}
{{/each}}