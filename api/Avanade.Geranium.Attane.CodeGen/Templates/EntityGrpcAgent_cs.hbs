﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost.
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Beef.Entities;
using Beef.Grpc;
using Beef.WebApi;
using {{Root.NamespaceCommon}}.Entities;
using proto = {{Root.NamespaceCommon}}.Grpc.Proto;
{{#ifval Root.RefDataCommonNamespace}}
using RefDataNamespace = {{Root.RefDataCommonNamespace}};
{{/ifval}}

namespace {{Root.NamespaceCommon}}.Grpc
{
    /// <summary>
    /// Defines the {{{EntityNameSeeComments}}} gRPC agent.
    /// </summary>
    public partial interface I{{Name}}Agent
    {
{{#each GrpcOperations}}
  {{#unless @first}}

  {{/unless}}
        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#ifeq Type 'Patch'}}
        /// <param name="patchOption">The <see cref="WebApiPatchOption"/>.</param>
  {{/ifeq}}
  {{#each Parameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
        /// <param name="requestOptions">The optional <see cref="GrpcRequestOptions"/>.</param>
        /// <returns>A <see cref="GrpcAgentResult"/>.</returns>
        {{{GrpcAgentOperationTaskReturnType}}} {{Name}}Async({{#each Parameters}}{{ParameterType}} {{ArgumentName}}{{#if IsPagingArgs}} = null{{/if}}, {{/each}}GrpcRequestOptions? requestOptions = null);
{{/each}}
    }

    /// <summary>
    /// Provides the {{{EntityNameSeeComments}}} gRPC agent.
    /// </summary>
    public partial class {{Name}}Agent : GrpcAgentBase<proto.{{Name}}GrpcService.{{Name}}GrpcServiceClient>, I{{Name}}Agent
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="{{Name}}Agent"/> class.
        /// </summary>
        /// <param name="args">The <see cref="{{#if Root.AppBasedAgentArgs}}Common.Agents.I{{Root.AppName}}WebApiAgentArgs{{else}}IWebApiAgentArgs{{/if}}"/>.</param>
        /// <param name="mapper">The <see cref="IMapper"/>.</param>
        public {{Name}}Agent({{#if Root.AppBasedAgentArgs}}Common.Agents.I{{Root.AppName}}WebApiAgentArgs{{else}}IWebApiAgentArgs{{/if}} args, AutoMapper.IMapper mapper) : base(args, mapper) { }
{{#each GrpcOperations}}

        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#ifeq Type 'Patch'}}
        /// <param name="patchOption">The <see cref="WebApiPatchOption"/>.</param>
  {{/ifeq}}
  {{#each Parameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
        /// <param name="requestOptions">The optional <see cref="GrpcRequestOptions"/>.</param>
        /// <returns>A <see cref="GrpcAgentResult"/>.</returns>
        public {{{GrpcAgentOperationTaskReturnType}}} {{Name}}Async({{#each Parameters}}{{ParameterType}} {{ArgumentName}}{{#if IsPagingArgs}} = null{{/if}}, {{/each}}GrpcRequestOptions? requestOptions = null)
        {
  {{#ifne Parameters.Count 0}}
            var __req = new proto.{{Parent.Name}}{{Name}}Request { {{#each Parameters}}{{#unless @first}}, {{/unless}}{{Name}} = {{#if IsValueArg}}Mapper.Map<{{Type}}, proto.{{Type}}>(Check.NotNull(value, nameof(value))){{else}}{{#if IsPagingArgs}}Transformers.PagingArgsToPagingArgsConverter.ConvertToDest(paging!){{else}}{{#if GrpcConverter}}Transformers.{{GrpcConverter}}.ConvertToDest({{ArgumentName}}){{else}}{{#if GrpcMapper}}Mapper.Map<{{Type}}, proto.{{Type}}>({{ArgumentName}}){{else}}{{ArgumentName}}{{/if}}{{/if}}{{/if}}{{/if}}{{/each}} };
  {{/ifne}}
            return InvokeAsync{{#if GrpcReturnMapper}}<{{GrpcReturnType}}, proto.{{GrpcReturnType}}>{{/if}}((c, o) => c.{{Name}}Async({{#ifne Parameters.Count 0}}__req, {{/ifne}}o), {{#ifne Parameters.Count 0}}__req, {{/ifne}}{{#if HasReturnValue}}{{#if GrpcReturnConverter}}Transformers.{{GrpcReturnConverter}}, {{/if}}{{/if}}requestOptions);
        }
{{/each}}
    }
}

#pragma warning restore
#nullable restore