﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceApi}}.Controllers{{#if (hasKey ExtraProperties 'webApiNamespace')}}{{valueOf ExtraProperties 'webApiNamespace'}}{{/if}}
{
    /// <summary>
    /// Provides the {{{EntityNameSeeComments}}} Web API functionality.
    /// </summary>
{{#ifval WebApiAuthorize}}
    [{{{WebApiAuthorize}}}]
{{/ifval}}
{{#ifval WebApiRoutePrefix}}
    [Route("{{WebApiRoutePrefix}}")]
{{/ifval}}
    [Produces(System.Net.Mime.MediaTypeNames.Application.Json)]
    public partial class {{Name}}Controller : ControllerBase
    {
{{#each WebApiCtorParameters}}
        private readonly {{Type}} {{PrivateName}};
  {{#if @last}}

  {{/if}}
{{/each}}
        /// <summary>
        /// Initializes a new instance of the <see cref="{{Name}}Controller"/> class.
        /// </summary>
{{#each WebApiCtorParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
{{/each}}
        {{lower WebApiCtor}} {{Name}}Controller({{#each WebApiCtorParameters}}{{#unless @first}}, {{/unless}}{{Type}} {{ArgumentName}}{{/each}})
{{#ifle WebApiCtorParameters.Count 2}}
            { {{#each WebApiCtorParameters}}{{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}})); {{/each}}{{Name}}ControllerCtor(); }
{{else}}
        {
  {{#each WebApiCtorParameters}}
            {{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}}));
  {{/each}}
            {{Name}}ControllerCtor();
        }
{{/ifle}}

        partial void {{Name}}ControllerCtor(); // Enables additional functionality to be added to the constructor.

{{#each WebApiOperations}}
  {{#unless @first}}

  {{/unless}} 
  {{#if (hasKey ExtraProperties 'webApiCommentOut')}}
        /*
  {{/if}}
        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#each CoreParameters}}
   {{#ifeq WebApiFrom 'FromEntityProperties'}}
     {{#each RelatedEntity.Properties}}
        /// <param name="{{ArgumentName}}">{{{ParameterSummaryText}}}</param>
     {{/each}}
   {{else}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
   {{/ifeq}}
  {{/each}}
  {{#if HasReturnValue}}
        /// <returns>{{{WebApiReturnText}}}</returns>
  {{/if}}
  {{#ifval WebApiAuthorize}}
        [{{{WebApiAuthorize}}}]
  {{/ifval}}
        [{{WebApiMethod}}("{{WebApiRoute}}")]
  {{#if HasValue}}
        [AcceptsBody(typeof({{#unless (hasKey ExtraProperties 'valueTypeFull')}}Common.Entities.{{/unless}}{{ValueType}}){{#ifeq WebApiMethod 'HttpPatch'}}, HttpConsts.MergePatchMediaTypeName{{/ifeq}})]
  {{/if}}
        [ProducesResponseType({{#if HasReturnValue}}typeof({{#if IsReturnTypeEntity}}Common.Entities.{{/if}}{{#ifeq Type 'GetColl'}}{{BaseReturnType}}Collection{{else}}{{BaseReturnType}}{{/ifeq}}), {{/if}}(int)HttpStatusCode.{{WebApiStatus}})]
  {{#if HasReturnValue}}
    {{#ifne WebApiAlternateStatus 'null'}}
        [ProducesResponseType((int)HttpStatusCode.{{WebApiAlternateStatus}})]
    {{/ifne}}
  {{/if}}
        public Task<IActionResult> {{Name}}({{#each CoreParameters}}{{#unless @first}}, {{/unless}}{{#ifeq WebApiFrom 'FromEntityProperties'}}{{#each RelatedEntity.Properties}}{{#unless @first}}, {{/unless}}{{#ifval JsonName}}[FromQuery(Name="{{JsonName}}")] {{/ifval}}{{{WebApiParameterType}}} {{ArgumentName}} = {{#ifval Default}}{{Default}}{{else}}default{{/ifval}}{{/each}}{{else}}{{#ifne WebApiFrom 'FromQuery'}}[{{WebApiFrom}}] {{/ifne}}{{{WebApiParameterType}}} {{ArgumentName}}{{#ifval Default}} = {{Default}}{{/ifval}}{{/ifeq}}{{/each}}){{#unless HasFromEntityPropertiesParameters}} =>{{/unless}}
  {{#if HasFromEntityPropertiesParameters}} 
        {
  {{/if}}
  {{#unless (hasKey ExtraProperties 'logging')}}
    {{#if (hasKey ExtraProperties 'validateUserId')}}
        {
    {{/if}}
  {{/unless}}
  {{#each Parameters}}
    {{#ifeq WebApiFrom 'FromEntityProperties'}}
            var {{ArgumentName}} = new {{Type}} { {{#each RelatedEntity.Properties}}{{#unless @first}}, {{/unless}}{{PropertyName}} = {{ArgumentName}}{{/each}} };
    {{/ifeq}}
  {{/each}}
  {{#if (hasKey ExtraProperties 'logging')}}
            {{#if HasFromEntityPropertiesParameters}}return {{/if}}_logger.LogBlockAsync(() =>
            {
  {{/if}}
  {{#if (hasKey ExtraProperties 'validateUserId')}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_callerProvider.ValidateUserId(this, _logger, {{valueOf ExtraProperties 'validateUserId'}});
  {{/if}}
  {{#ifeq WebApiMethod 'HttpGet'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}_webApi.{{ControllerOperationWebApiMethod}}(Request, p => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}_manager.{{Name}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}p.Value!{{else}}{{#if IsPagingArgs}}p.RequestOptions.Paging{{else}}{{ArgumentName}}{{/if}}{{/if}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}}){{/if}}{{/if}}){{#ifne WebApiStatus 'OK'}}, statusCode: HttpStatusCode.{{WebApiStatus}}{{/ifne}}{{#if HasReturnValue}}{{#ifne WebApiAlternateStatus 'NotFound'}}, alternateStatusCode: HttpStatusCode.{{WebApiAlternateStatus}}{{/ifne}}{{/if}}{{#ifne ManagerOperationType 'Read'}}, operationType: CoreEx.OperationType.{{ManagerOperationType}}{{/ifne}});
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpPost'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}_webApi.{{ControllerOperationWebApiMethod}}(Request, p => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}_manager.{{Name}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}p.Value!{{else}}{{#if IsPagingArgs}}p.RequestOptions.Paging{{else}}{{ArgumentName}}{{/if}}{{/if}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}}){{/if}}{{/if}}){{#ifne WebApiStatus 'OK'}}, statusCode: HttpStatusCode.{{WebApiStatus}}{{/ifne}}{{#if HasReturnValue}}{{#ifne WebApiAlternateStatus 'null'}}, alternateStatusCode: HttpStatusCode.{{WebApiAlternateStatus}}{{/ifne}}{{/if}}{{#ifne ManagerOperationType 'Create'}}, operationType: CoreEx.OperationType.{{ManagerOperationType}}{{/ifne}}{{#ifval WebApiLocation}}, locationUri: {{#if HasReturnValue}}r{{else}}(){{/if}} => new Uri($"{{WebApiLocation}}", UriKind.Relative){{/ifval}});
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpPut'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}_webApi.{{ControllerOperationWebApiMethod}}(Request, {{#if WebApiConcurrency}}get: _ => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}{{WebApiGetVariable}}.{{WebApiGetOperation}}Async({{#each ValueLessParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}}){{/if}}{{/if}}), put: {{/if}}p => {{WebApiUpdateVariable}}.{{#if WebApiConcurrency}}{{WebApiUpdateOperation}}{{else}}{{Name}}{{/if}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}p.Value!{{else}}{{ArgumentName}}{{/if}}{{/each}}){{#ifne WebApiStatus 'OK'}}, statusCode: HttpStatusCode.{{WebApiStatus}}{{/ifne}}{{#if HasReturnValue}}{{#ifne WebApiAlternateStatus 'null'}}, alternateStatusCode: HttpStatusCode.{{WebApiAlternateStatus}}{{/ifne}}{{/if}}{{#ifne ManagerOperationType 'Update'}}, operationType: CoreEx.OperationType.{{ManagerOperationType}}{{/ifne}}{{#if WebApiConcurrency}}, simulatedConcurrency: {{lower WebApiConcurrency}}{{/if}});
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpPatch'}}
            {{#if HasFromEntityPropertiesParameters}}return {{/if}}_webApi.{{ControllerOperationWebApiMethod}}(Request, get: _ => {{WebApiGetVariable}}.{{WebApiGetOperation}}Async({{#each ValueLessParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}), put: p => {{WebApiUpdateVariable}}.{{WebApiUpdateOperation}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}p.Value!{{else}}{{ArgumentName}}{{/if}}{{/each}}){{#ifne WebApiStatus 'OK'}}, statusCode: HttpStatusCode.{{WebApiStatus}}{{/ifne}}{{#if HasReturnValue}}{{#ifne WebApiAlternateStatus 'null'}}, alternateStatusCode: HttpStatusCode.{{WebApiAlternateStatus}}{{/ifne}}{{/if}}{{#ifne ManagerOperationType 'Update'}}, operationType: CoreEx.OperationType.{{ManagerOperationType}}{{/ifne}}{{#if WebApiConcurrency}}, simulatedConcurrency: {{lower WebApiConcurrency}}{{/if}});
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpDelete'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}_webApi.{{ControllerOperationWebApiMethod}}(Request, p => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}_manager.{{Name}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}p.Value!{{else}}{{#if IsPagingArgs}}p.RequestOptions.Paging{{else}}{{ArgumentName}}{{/if}}{{/if}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}}){{/if}}{{/if}}{{#ifne WebApiStatus 'NoContent'}}, statusCode: HttpStatusCode.{{WebApiStatus}}{{/ifne}}{{#ifne ManagerOperationType 'Delete'}}, operationType: CoreEx.OperationType.{{ManagerOperationType}}{{/ifne}});
  {{/ifeq}}
  {{#if (hasKey ExtraProperties 'logging')}}
            }, nameof({{Name}}), $"{{#each PagingLessParameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}{{else}}{{#if IsPagingArgs}}{{else}}{{ArgumentName}}: { {{ArgumentName}} }{{/if}}{{/if}}{{/each}}");
  {{/if}}
  {{#unless (hasKey ExtraProperties 'logging')}}
    {{#if (hasKey ExtraProperties 'validateUserId')}}
        }
    {{/if}}
  {{/unless}}
  {{#if HasFromEntityPropertiesParameters}} 
        }
  {{/if}}
  {{#if (hasKey ExtraProperties 'webApiCommentOut')}}
        */
  {{/if}}
{{/each}}
    }
}

#pragma warning restore
#nullable restore
