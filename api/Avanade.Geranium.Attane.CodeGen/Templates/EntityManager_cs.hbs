﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceBusiness}}
{
    /// <summary>
    /// Provides the {{{EntityNameSeeComments}}} business functionality.
    /// </summary>
    public partial class {{Name}}Manager{{#if GenericWithT}}<T>{{/if}} : I{{Name}}Manager{{#if GenericWithT}}<T>{{/if}}
    {
{{#each ManagerCtorParameters}}
        private readonly {{Type}} {{PrivateName}};
  {{#if @last}}

  {{/if}}
{{/each}}
{{#if HasManagerExtensions}}
        #region Extensions

  {{#each ManagerAutoOperations}}
    {{#if ManagerExtensions}}
      {{#unless SingleValidateParameters}}
        private Func<{{#each Parameters}}{{{ParameterType}}}, {{/each}}{{#if Root.CancellationToken}}CancellationToken, {{/if}}Task>? {{PrivateName}}OnPreValidateAsync;
        private Action<MultiValidator{{#each Parameters}}, {{{ParameterType}}}{{/each}}>? {{PrivateName}}OnValidate;
      {{/unless}}
        private Func<{{#each Parameters}}{{{ParameterType}}}, {{/each}}{{#if Root.CancellationToken}}CancellationToken, {{/if}}Task>? {{PrivateName}}OnBeforeAsync;
        private Func<{{#if HasReturnValue}}{{OperationReturnType}}, {{/if}}{{#each ValueLessParameters}}{{{ParameterType}}}, {{/each}}{{#if Root.CancellationToken}}CancellationToken, {{/if}}Task>? {{PrivateName}}OnAfterAsync;

    {{/if}}
  {{/each}}
        #endregion

{{/if}}
        /// <summary>
        /// Initializes a new instance of the <see cref="{{Name}}Manager"/> class.
        /// </summary>
{{#each ManagerCtorParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
{{/each}}
        {{lower ManagerCtor}} {{Name}}Manager({{#each ManagerCtorParameters}}{{#unless @first}}, {{/unless}}{{Type}} {{ArgumentName}}{{/each}})
{{#ifle ManagerCtorParameters.Count 2}}
            { {{#each ManagerCtorParameters}}{{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}})); {{/each}}{{Name}}ManagerCtor(); }
{{else}}
        {
  {{#each ManagerCtorParameters}}
            {{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}}));
  {{/each}}
            {{Name}}ManagerCtor();
        }
{{/ifle}}

        partial void {{Name}}ManagerCtor(); // Enables additional functionality to be added to the constructor.

{{#each ManagerOperations}}
  {{#unless @first}}

  {{/unless}} 
        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#each Parameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
  {{#if Root.CancellationToken}}
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
  {{/if}}
  {{#if HasReturnValue}}
        /// <returns>{{{ReturnText}}}</returns>
  {{/if}}
        public {{{OperationTaskReturnType}}} {{Name}}Async({{#each Parameters}}{{{ParameterType}}} {{ArgumentName}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, CancellationToken cancellationToken = default{{/if}}) => ManagerInvoker.Current.InvokeAsync(this, async {{#if Root.CancellationToken}}ct{{else}}_{{/if}} =>
        {
  {{#ifval AuthRole}}
            _executionContext.IsInRole("{{AuthRole}}", true);
  {{/ifval}}
  {{#ifval AuthPermission}}
            _executionContext.IsAuthorized("{{AuthPermission}}", true);
  {{/ifval}}
  {{#ifeq Type 'Create'}}
    {{#each Parent.Properties}}
      {{#if IdentifierGenerator}}
            value{{#ifeq ../../EnsureValueCount 0}}.EnsureValue(){{/ifeq}}.{{Name}} = await _identifierGenerator.GenerateIdentifierAsync<{{Type}}, {{../../ValueType}}>({{#if Root.CancellationToken}}ct{{/if}}).ConfigureAwait(false);
      {{/if}}
    {{/each}}
  {{/ifeq}}
  {{#each Parameters}}
    {{#ifeq LayerPassing 'ToManagerSet'}}
            value{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}.{{Name}} = {{ArgumentName}};
    {{/ifeq}}
    {{#ifeq LayerPassing 'ToManagerCollSet'}}
            value{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}.ForEach(__item => __item.{{Name}} = {{ArgumentName}});
    {{/ifeq}}
  {{/each}}
  {{#if ManagerCustom}}
            {{#if HasReturnValue}}return {{#if ManagerCleanUp}}Cleaner.Clean({{/if}}{{/if}}await {{Name}}OnImplementationAsync({{#each Parameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}{{/if}}{{/each}}{{#if Root.CancellationToken}}{{ifne Parameters.Count 0}}, {{/ifne}}ct{{/if}}).ConfigureAwait(false){{#if HasReturnValue}}{{#if ManagerCleanUp}}){{/if}}{{/if}};
  {{else}}
    {{#ifne CleanerParameters.Count 0}}
            Cleaner.CleanUp({{#each CleanerParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}{{/if}}{{/each}});
    {{/ifne}}
    {{#if ManagerExtensions}}
      {{#unless SingleValidateParameters}}
            await Invoker.InvokeAsync({{PrivateName}}OnPreValidateAsync?.Invoke({{#each Parameters}}{{#unless @first}}, {{/unless}}{{{ArgumentName}}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}{{/if}}{{/each}}{{#if Root.CancellationToken}}{{ifne Parameters.Count 0}}, {{/ifne}}ct{{/if}})).ConfigureAwait(false);

      {{/unless}}
    {{/if}}
    {{#if SingleValidateParameters}}
      {{#each ValidateParameters}}
            await {{ArgumentName}}.Validate({{#ifne ArgumentName 'value'}}nameof({{ArgumentName}}){{/ifne}}){{#if IsMandatory}}.Mandatory(){{else}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.Mandatory(){{/ifeq}}{{/if}}{{/if}}{{#ifval Validator}}.Entity().With<{{Validator}}>(){{/ifval}}{{#ifval ValidatorCode}}.{{ValidatorCode}}{{/ifval}}.ValidateAsync(true{{#if Root.CancellationToken}}, ct{{/if}}).ConfigureAwait(false);
      {{/each}}
    {{else}}
            await MultiValidator.Create()
      {{#each ValidateParameters}}
                .Add({{ArgumentName}}.Validate(nameof({{ArgumentName}})){{#if IsMandatory}}.Mandatory(){{else}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.Mandatory(){{/ifeq}}{{/if}}{{/if}}{{#ifval Validator}}.Entity().With<{{Validator}}>(){{/ifval}}{{#ifval ValidatorCode}}.{{ValidatorCode}}{{/ifval}})
      {{/each}}
      {{#if ManagerExtensions}}
        {{#unless SingleValidateParameters}}
                .Additional((__mv) => {{PrivateName}}OnValidate?.Invoke(__mv{{#each Parameters}}, {{ArgumentName}}{{/each}}))
        {{/unless}}
      {{/if}}
                .ValidateAsync(true{{#if Root.CancellationToken}}, ct{{/if}}).ConfigureAwait(false);

    {{/if}}
    {{#if ManagerExtensions}}
            await Invoker.InvokeAsync({{PrivateName}}OnBeforeAsync?.Invoke({{#each Parameters}}{{#unless @first}}, {{/unless}}{{{ArgumentName}}}{{/each}}{{#if Root.CancellationToken}}{{ifne Parameters.Count 0}}, {{/ifne}}ct{{/if}})).ConfigureAwait(false);
            {{#unless (hasKey ExtraProperties 'managerCustomEx')}}await {{Name}}OnImplementationAsync({{#each Parameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}{{/if}}{{/each}}{{#if Root.CancellationToken}}{{ifne Parameters.Count 0}}, {{/ifne}}ct{{/if}}).ConfigureAwait(false);{{/unless}}
            {{#if HasReturnValue}}var __result = {{/if}}{{#if (hasKey ExtraProperties 'managerCustomEx')}}await {{Name}}OnImplementationAsync({{#each Parameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}{{/if}}{{/each}}{{#if Root.CancellationToken}}{{ifne Parameters.Count 0}}, {{/ifne}}ct{{/if}}).ConfigureAwait(false);{{else}}await _dataService.{{Name}}Async({{#each DataParameters}}{{#unless @first}}, {{/unless}}{{{ArgumentName}}}{{/each}}{{#if Root.CancellationToken}}{{ifne DataParameters.Count 0}}, {{/ifne}}ct{{/if}}).ConfigureAwait(false);{{/if}}
            await Invoker.InvokeAsync({{PrivateName}}OnAfterAsync?.Invoke({{#if HasReturnValue}}__result{{/if}}{{#each ValueLessParameters}}{{#if @first}}{{#if Parent.HasReturnValue}}, {{/if}}{{else}}, {{/if}}{{{ArgumentName}}}{{/each}}{{#if Root.CancellationToken}}{{ifne ValueLessParameters.Count 0}}, {{else}}{{#if HasReturnValue}}, {{/if}}{{/ifne}}ct{{/if}})).ConfigureAwait(false);
      {{#if HasReturnValue}}
            return {{#if ManagerCleanUp}}Cleaner.Clean({{/if}}__result{{#if ManagerCleanUp}}){{/if}};
      {{/if}}
    {{else}}
        {{#if HasReturnValue}}return {{#if ManagerCleanUp}}Cleaner.Clean({{/if}}{{/if}}{{#if (hasKey ExtraProperties 'managerCustomEx')}}await {{Name}}OnImplementationAsync({{#each Parameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{#if IsValueArg}}{{#ifeq Parent.EnsureValueCount 0}}.EnsureValue(){{/ifeq}}{{/if}}{{/each}}{{#if Root.CancellationToken}}{{ifne Parameters.Count 0}}, {{/ifne}}ct{{/if}}).ConfigureAwait(false){{else}}await _dataService.{{Name}}Async({{#each DataParameters}}{{{ArgumentName}}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, ct{{/if}}).ConfigureAwait(false){{/if}}{{#if HasReturnValue}}{{#if ManagerCleanUp}}){{/if}}{{/if}};
    {{/if}}
  {{/if}}
  {{#if ManagerTransaction}}
        }, new InvokerArgs { IncludeTransactionScope = true, OperationType = OperationType.{{ManagerOperationType}} }{{#if Root.CancellationToken}}, cancellationToken{{/if}});
  {{else}}
        }, InvokerArgs.{{ManagerOperationType}}{{#if Root.CancellationToken}}, cancellationToken{{/if}});
  {{/if}}
{{/each}}
    }
}

#pragma warning restore
#nullable restore
