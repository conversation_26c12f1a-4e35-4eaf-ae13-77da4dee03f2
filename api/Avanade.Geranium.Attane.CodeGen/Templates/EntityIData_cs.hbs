﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceBusiness}}.Data
{
    /// <summary>
    /// Defines the {{{EntityNameSeeComments}}} data access.
    /// </summary>
    public partial interface I{{Name}}Data{{#if GenericWithT}}<T>{{/if}}
    {
{{#each IDataOperations}}
  {{#unless @first}}

  {{/unless}} 
        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#each DataParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
  {{#if Root.CancellationToken}}
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
  {{/if}}
  {{#if HasReturnValue}}
        /// <returns>{{{ReturnText}}}</returns>
  {{/if}}
        {{{OperationTaskReturnType}}} {{Name}}Async({{#each DataParameters}}{{{ParameterType}}} {{ArgumentName}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, CancellationToken cancellationToken = default{{/if}});
{{/each}}
    }
}

#pragma warning restore
#nullable restore