﻿/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceBusiness}}.DataSvc
{
    /// <summary>
    /// Provides the <b>ReferenceData</b> data services.
    /// </summary>
    public partial class ReferenceDataDataSvc : IReferenceDataDataSvc
    {
        private readonly IReferenceDataData _data;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReferenceDataDataSvc" /> class.
        /// </summary>
        /// <param name="data">The <see cref="IReferenceDataData"/>.</param>
        public ReferenceDataDataSvc(IReferenceDataData data) { _data = data ?? throw new ArgumentNullException(nameof(data)); ReferenceDataDataSvcCtor(); }

        partial void ReferenceDataDataSvcCtor(); // Enables the ReferenceDataDataSvc constructor to be extended.

        /// <inheritdoc/>
        public async Task<IReferenceDataCollection> GetAsync(Type type{{#if Root.CancellationToken}}, CancellationToken cancellationToken = default{{/if}}) => type switch
        {
{{#each RefDataEntities}}
            Type _ when type == typeof({{RefDataQualifiedEntityName}}) => await _data.{{Name}}GetAllAsync({{#if Root.CancellationToken}}cancellationToken{{/if}}).ConfigureAwait(false),
{{/each}}
            _ => throw new InvalidOperationException($"Type {type.FullName} is not a known {nameof(IReferenceData)}.")
        };
    }
}

#pragma warning restore
#nullable restore