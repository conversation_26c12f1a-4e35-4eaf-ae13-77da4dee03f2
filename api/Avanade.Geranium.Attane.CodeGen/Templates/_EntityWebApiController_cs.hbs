﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Beef;
using Beef.AspNetCore.WebApi;
using Beef.Entities;
using {{Root.NamespaceBusiness}};
{{#ifeq EntityUsing 'Business' 'All'}}
using {{Root.NamespaceBusiness}}.Entities;
{{/ifeq}}
{{#ifeq EntityUsing 'Common' 'All'}}
using {{Root.NamespaceCommon}}.Entities;
{{/ifeq}}
{{#ifval Root.RefDataNamespace}}
using RefDataNamespace = {{Root.RefDataNamespace}};
{{/ifval}}
{{#ifval Root.RefDataBusNamespace}}
using RefDataNamespace = {{Root.RefDataBusNamespace}};
{{/ifval}}
{{#if (hasKey ExtraProperties 'webApiUsing')}}
{{replace (valueOf ExtraProperties 'webApiUsing') '; ' ';
'}}
{{/if}}

namespace {{Root.NamespaceApi}}.Controllers{{#if (hasKey ExtraProperties 'webApiNamespace')}}{{valueOf ExtraProperties 'webApiNamespace'}}{{/if}}
{
    /// <summary>
    /// Provides the {{{EntityNameSeeComments}}} Web API functionality.
    /// </summary>
{{#ifval WebApiAuthorize}}
    [{{{WebApiAuthorize}}}]
{{/ifval}}
{{#ifval WebApiRoutePrefix}}
    [Route("{{WebApiRoutePrefix}}")]
{{/ifval}}
    public partial class {{Name}}Controller : ControllerBase
    {
{{#each WebApiCtorParameters}}
        private readonly {{Type}} {{PrivateName}};
  {{#if @last}}

  {{/if}}
{{/each}}
        /// <summary>
        /// Initializes a new instance of the <see cref="{{Name}}Controller"/> class.
        /// </summary>
{{#each WebApiCtorParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
{{/each}}
        {{lower WebApiCtor}} {{Name}}Controller({{#each WebApiCtorParameters}}{{#unless @first}}, {{/unless}}{{Type}} {{ArgumentName}}{{/each}})
{{#ifle WebApiCtorParameters.Count 2}}
            { {{#each WebApiCtorParameters}}{{PrivateName}} = Check.NotNull({{ArgumentName}}, nameof({{ArgumentName}})); {{/each}}{{Name}}ControllerCtor(); }
{{else}}
        {
  {{#each WebApiCtorParameters}}
            {{PrivateName}} = Check.NotNull({{ArgumentName}}, nameof({{ArgumentName}}));
  {{/each}}
            {{Name}}ControllerCtor();
        }
{{/ifle}}

        partial void {{Name}}ControllerCtor(); // Enables additional functionality to be added to the constructor.

{{#each WebApiOperations}}
  {{#unless @first}}

  {{/unless}} 
  {{#if (hasKey ExtraProperties 'webApiCommentOut')}}
        /*
  {{/if}}
        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#each PagingLessParameters}}
   {{#ifeq WebApiFrom 'FromEntityProperties'}}
     {{#each RelatedEntity.Properties}}
        /// <param name="{{ArgumentName}}">{{{ParameterSummaryText}}}</param>
     {{/each}}
   {{else}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
   {{/ifeq}}
  {{/each}}
  {{#if HasReturnValue}}
        /// <returns>{{{WebApiReturnText}}}</returns>
  {{/if}}
  {{#ifval WebApiAuthorize}}
        [{{{WebApiAuthorize}}}]
  {{/ifval}}
        [{{WebApiMethod}}("{{WebApiRoute}}")]
        [ProducesResponseType({{#if HasReturnValue}}typeof({{#ifeq Type 'GetColl'}}{{BaseReturnType}}Collection{{else}}{{BaseReturnType}}{{/ifeq}}), {{/if}}(int)HttpStatusCode.{{WebApiStatus}})]
  {{#if HasReturnValue}}
    {{#ifne WebApiAlternateStatus 'ThrowException'}}
        [ProducesResponseType((int)HttpStatusCode.{{WebApiAlternateStatus}})]
    {{/ifne}}
  {{/if}}
        public IActionResult {{Name}}({{#each PagingLessParameters}}{{#unless @first}}, {{/unless}}{{#ifeq WebApiFrom 'FromEntityProperties'}}{{#each RelatedEntity.Properties}}{{#unless @first}}, {{/unless}}{{#ifne ArgumentName JsonName}}[FromQuery(Name = "{{JsonName}}")] {{/ifne}}{{{WebApiParameterType}}} {{ArgumentName}} = {{#ifval Default}}{{Default}}{{else}}default{{/ifval}}{{/each}}{{else}}{{#ifne WebApiFrom 'FromQuery'}}[{{WebApiFrom}}] {{/ifne}}{{{WebApiParameterType}}} {{ArgumentName}}{{#ifval Default}} = {{Default}}{{/ifval}}{{/ifeq}}{{/each}}){{#unless HasFromEntityPropertiesParameters}}{{#ifor (hasKey ExtraProperties 'logging') (notHasKey ExtraProperties 'validateUserId')}} =>{{/ifor}}{{/unless}}
  {{#if HasFromEntityPropertiesParameters}} 
        {
  {{/if}}
  {{#unless (hasKey ExtraProperties 'logging')}}
    {{#if (hasKey ExtraProperties 'validateUserId')}}
        {
    {{/if}}
  {{/unless}}
  {{#each Parameters}}
    {{#ifeq WebApiFrom 'FromEntityProperties'}}
            var {{ArgumentName}} = new {{Type}} { {{#each RelatedEntity.Properties}}{{#unless @first}}, {{/unless}}{{PropertyName}} = {{#ifval WebApiQueryStringConverter}}new {{WebApiQueryStringConverter}}().ConvertToSrce({{ArgumentName}}){{else}}{{ArgumentName}}{{/ifval}}{{/each}} };
    {{/ifeq}}
  {{/each}}
  {{#if (hasKey ExtraProperties 'logging')}}
            {{#if HasFromEntityPropertiesParameters}}return {{/if}}_logger.LogBlock(() =>
            {
  {{/if}}
  {{#if (hasKey ExtraProperties 'validateUserId')}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_callerProvider.ValidateUserId(this, _logger, {{valueOf ExtraProperties 'validateUserId'}});
  {{/if}}
  {{#ifeq WebApiMethod 'HttpGet'}}
    {{#ifeq Type 'GetColl'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}new WebApiGet<{{BaseReturnType}}CollectionResult, {{BaseReturnType}}Collection, {{BaseReturnType}}>(this, () => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}_manager.{{Name}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsPagingArgs}}WebApiQueryString.CreatePagingArgs(this){{else}}{{ArgumentName}}{{/if}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}})){{/if}}{{/if}},
    {{else}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}new WebApiGet<{{OperationReturnType}}>(this, () => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}_manager.{{Name}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}WebApiActionBase.Value({{ArgumentName}}){{else}}{{#if IsPagingArgs}}WebApiQueryString.CreatePagingArgs(this){{else}}{{ArgumentName}}{{/if}}{{/if}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}})){{/if}}{{/if}},
    {{/ifeq}}
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpPost'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}new WebApiPost{{#if HasReturnValue}}<{{OperationReturnType}}>{{/if}}(this, () => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}_manager.{{Name}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}WebApiActionBase.Value({{ArgumentName}}){{else}}{{#if IsPagingArgs}}WebApiQueryString.CreatePagingArgs(this){{else}}{{ArgumentName}}{{/if}}{{/if}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}})){{/if}}{{/if}},
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpPut'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}new WebApiPut{{#if HasReturnValue}}<{{OperationReturnType}}>{{/if}}(this, {{#if WebApiConcurrency}}{{#ifeq Type 'Update'}}value, () => {{WebApiGetVariable}}.{{WebApiGetOperation}}Async({{#each ValueLessParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}), {{/ifeq}}{{/if}}() => {{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}_manager.{{Name}}Async({{#each Parameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}WebApiActionBase.Value({{ArgumentName}}){{else}}{{#if IsPagingArgs}}WebApiQueryString.CreatePagingArgs(this){{else}}{{ArgumentName}}{{/if}}{{/if}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}})){{/if}}{{/if}},
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpPatch'}}
            {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#ifor HasFromEntityPropertiesParameters (hasKey ExtraProperties 'validateUserId')}}return {{/ifor}}new WebApiPatch{{#if HasReturnValue}}<{{OperationReturnType}}>{{/if}}(this, value, () => {{WebApiGetVariable}}.{{WebApiGetOperation}}Async({{#each ValueLessParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}{{#if (hasKey ExtraProperties 'managerAdditionalArguments')}}{{valueOf ExtraProperties 'managerAdditionalArguments'}}{{/if}}), {{#ifne OperationReturnType ValueType}}async {{/ifne}}(__value) => {{#ifeq OperationReturnType ValueType}}{{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}_logger.TraceResultAsync({{/if}}{{/if}}{{WebApiUpdateVariable}}.{{WebApiUpdateOperation}}Async(__value{{#each ValueLessParameters}}, {{ArgumentName}}{{/each}}){{#if (hasKey ExtraProperties 'logging')}}{{#if HasReturnValue}}, nameof({{Name}})){{/if}}{{/if}}{{else}}{
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}var __copy = new {{ValueType}}();
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}__copy.CopyFrom(__value);
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}{{#if HasReturnValue}}return {{#if (hasKey ExtraProperties 'logging')}}await _logger.TraceResultAsync({{/if}}{{/if}}{{WebApiUpdateVariable}}.{{WebApiUpdateOperation}}Async(__copy{{#each ValueLessParameters}}, {{ArgumentName}}{{/each}}){{#if HasReturnValue}}{{#if (hasKey ExtraProperties 'logging')}}, nameof({{Name}})){{/if}}{{/if}}; }{{/ifeq}},
  {{/ifeq}}
  {{#ifeq WebApiMethod 'HttpDelete'}}
  {{/ifeq}}
                {{#if (hasKey ExtraProperties 'logging')}}    {{/if}}operationType: OperationType.{{ManagerOperationType}}, statusCode: HttpStatusCode.{{WebApiStatus}}{{#if HasReturnValue}}, alternateStatusCode: {{#ifeq WebApiAlternateStatus 'ThrowException'}}null{{else}}HttpStatusCode.{{WebApiAlternateStatus}}{{/ifeq}}{{/if}}{{#ifval WebApiLocation}}, locationUri: ({{#if HasReturnValue}}r{{/if}}) => new Uri($"{{WebApiLocation}}", UriKind.Relative){{/ifval}}{{#if WebApiConcurrency}}{{#ifeq WebApiMethod 'HttpPatch'}}, autoConcurrency: {{lower WebApiConcurrency}}{{/ifeq}}{{/if}});
  {{#if (hasKey ExtraProperties 'logging')}}
            }, nameof({{Name}}), $"{{#each PagingLessParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}: { {{ArgumentName}} }{{/each}}");
  {{/if}}
  {{#unless (hasKey ExtraProperties 'logging')}}
    {{#if (hasKey ExtraProperties 'validateUserId')}}
        }
    {{/if}}
  {{/unless}}
  {{#if HasFromEntityPropertiesParameters}}
        }
  {{/if}}
  {{#if (hasKey ExtraProperties 'webApiCommentOut')}}
        */
  {{/if}}
{{/each}}
    }
}

#pragma warning restore
#nullable restore
