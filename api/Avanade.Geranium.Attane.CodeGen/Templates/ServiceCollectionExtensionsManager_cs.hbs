﻿/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace Microsoft.Extensions.DependencyInjection
{
    /// <summary>
    /// Provides the generated <b>Manager</b>-layer services.
    /// </summary>
    public static partial class {{#if IsRefData}}ReferenceData{{/if}}ServiceCollectionsExtension
    {
        /// <summary>
        /// Adds the generated <b>Manager</b>-layer services.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection"/>.</param>
        /// <returns>The <see cref="IServiceCollection"/>.</returns>
        public static IServiceCollection AddGenerated{{#if IsRefData}}ReferenceData{{/if}}ManagerServices(this IServiceCollection services)
{{#if IsRefData}}
            => services.AddScoped<IReferenceDataProvider, ReferenceDataProvider>(){{#ifeq IManagerEntities.Count 0}};{{/ifeq}}
{{else}}        
        {
{{/if}}
{{#each IManagerEntities}}
            {{#if @first}}{{#if Root.IsRefData}}           {{else}}return services{{/if}}{{else}}               {{/if}}.AddScoped<I{{Name}}Manager{{#if GenericWithT}}<T>{{/if}}, {{Name}}Manager{{#if GenericWithT}}<T>{{/if}}>(){{#if @last}};{{/if}}
{{/each}}
{{#unless IsRefData}}
        }
{{/unless}}
    }
}

#pragma warning restore
#nullable restore