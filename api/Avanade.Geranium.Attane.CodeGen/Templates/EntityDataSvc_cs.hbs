﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceBusiness}}.DataSvc
{
    /// <summary>
    /// Provides the {{{EntityNameSeeComments}}} data repository services.
    /// </summary>
    public partial class {{Name}}DataSvc{{#if GenericWithT}}<T>{{/if}} : I{{Name}}DataSvc{{#if GenericWithT}}<T>{{/if}}
    {
{{#each DataSvcCtorParameters}}
        private readonly {{Type}} {{PrivateName}};
  {{#if @last}}

  {{/if}}
{{/each}}
{{#if HasDataSvcExtensions}}
        #region Extensions

  {{#each DataSvcAutoOperations}}
    {{#if DataSvcExtensions}}
        private Func<{{#if HasReturnValue}}{{OperationReturnType}}, {{/if}}{{#each ValueLessDataParameters}}{{{ParameterType}}}, {{/each}}{{#if Root.CancellationToken}}CancellationToken, {{/if}}Task>? {{PrivateName}}OnAfterAsync;
    {{/if}}
  {{/each}}

        #endregion

{{/if}}
        /// <summary>
        /// Initializes a new instance of the <see cref="{{Name}}DataSvc"/> class.
        /// </summary>
{{#each DataSvcCtorParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
{{/each}}
        {{lower DataSvcCtor}} {{Name}}DataSvc({{#each DataSvcCtorParameters}}{{#unless @first}}, {{/unless}}{{Type}} {{ArgumentName}}{{/each}})
{{#ifle DataSvcCtorParameters.Count 3}}
            { {{#each DataSvcCtorParameters}}{{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}})); {{/each}}{{Name}}DataSvcCtor(); }
{{else}}
        {
  {{#each DataSvcCtorParameters}}
            {{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}}));
  {{/each}}
            {{Name}}DataSvcCtor();
        }
{{/ifle}}

        partial void {{Name}}DataSvcCtor(); // Enables additional functionality to be added to the constructor.
{{#each DataSvcOperations}}

        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#each DataParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
  {{#if Root.CancellationToken}}
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
  {{/if}}
  {{#if HasReturnValue}}
        /// <returns>{{{ReturnText}}}</returns>
  {{/if}}
        public {{#unless DataSvcInvoker}}{{#unless DataSvcCustom}}{{#unless DataSvcSingleLine}}async {{/unless}}{{/unless}}{{/unless}}{{{OperationTaskReturnType}}} {{Name}}Async({{#each DataParameters}}{{{ParameterType}}} {{ArgumentName}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, CancellationToken cancellationToken = default{{/if}}){{#if DataSvcCustom}}{{#if DataSvcInvoker}} => DataSvcInvoker.Current.InvokeAsync(this, {{#if Root.CancellationToken}}cancellationToken => {{/if}}{{else}}{{#if DataSvcInvoker}}_{{/if}}{{/if}}{{#if DataSvcInvoker}}_{{/if}} => {{Name}}OnImplementationAsync({{#each DataParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}{{#if Root.CancellationToken}}{{#ifne DataParameters.Count 0}}, {{/ifne}}cancellationToken{{/if}}){{#if DataSvcTransaction}}, new InvokerArgs { IncludeTransactionScope = true{{#ifeq EventPublish 'DataSvc'}}, EventPublisher = _events{{/ifeq}} }{{else}}{{#ifeq EventPublish 'DataSvc'}}, new InvokerArgs { EventPublisher = _events }{{/ifeq}}{{/if}}{{#if Root.CancellationToken}}, cancellationToken{{/if}}{{#if DataSvcInvoker}}){{/if}};{{else}}{{#if DataSvcInvoker}} => DataSvcInvoker.Current.InvokeAsync(this, {{#unless DataSvcSingleLine}}async {{/unless}}{{#if Root.CancellationToken}}cancellationToken =>{{else}}_ =>{{/if}}{{/if}}{{/if}}{{#if DataSvcSingleLine}}{{#unless DataSvcInvoker}}{{#unless DataSvcCustom}} =>{{/unless}}{{/unless}} {{#ifeq Type 'Get'}}{{#if SupportsCaching}}_cache.GetOrAddAsync({{#ifne ValueLessParameters.Count 1}}new CompositeKey({{/ifne}}{{#each ValueLessDataParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}{{#ifne ValueLessParameters.Count 1}}){{/ifne}}, () => {{/if}}{{/ifeq}}{{#unless DataSvcCustom}}_data.{{Name}}Async({{#each DataParameters}}{{#if IsValueArg}}value ?? throw new ArgumentNullException(nameof(value)){{else}}{{ArgumentName}}{{/if}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}}){{#ifeq Type 'Get'}}{{#if SupportsCaching}}){{/if}}{{/ifeq}}{{#if DataSvcInvoker}}){{/if}};{{/unless}}{{/if}}
  {{#unless DataSvcCustom}}
  {{#unless DataSvcSingleLine}}
        {
    {{#if SupportsCaching}}
      {{#ifeq Type 'Get'}}
            if (_cache.TryGetValue({{#ifne ValueLessParameters.Count 1}}new CompositeKey({{/ifne}}{{#each ValueLessDataParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}{{#ifne ValueLessParameters.Count 1}}){{/ifne}}, out {{OperationReturnType}} __val))
                return __val;

      {{/ifeq}}
      {{#ifeq Type 'Delete'}}
            _cache.Remove<{{Parent.Name}}>({{#ifne ValueLessParameters.Count 1}}new CompositeKey({{/ifne}}{{#each ValueLessDataParameters}}{{#unless @first}}, {{/unless}}{{ArgumentName}}{{/each}}{{#ifne ValueLessParameters.Count 1}}){{/ifne}});
      {{/ifeq}}
    {{/if}}
            {{#if HasReturnValue}}var __result = {{/if}}await _data.{{Name}}Async({{#each DataParameters}}{{#if IsValueArg}}value ?? throw new ArgumentNullException(nameof(value)){{else}}{{ArgumentName}}{{/if}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}}).ConfigureAwait(false);
    {{#if DataSvcExtensions}}
            await Invoker.InvokeAsync({{PrivateName}}OnAfterAsync?.Invoke({{#if HasReturnValue}}__result{{/if}}{{#each ValueLessDataParameters}}{{#if @first}}{{#if Parent.HasReturnValue}}, {{/if}}{{else}}, {{/if}}{{{ArgumentName}}}{{/each}}{{#if Root.CancellationToken}}{{#ifne ValueLessDataParamaters 0}}, {{else}}{{#if HasReturnValue}}, {{/if}}{{/ifne}}cancellationToken{{/if}})).ConfigureAwait(false);
    {{/if}}
    {{#ifeq EventPublish 'DataSvc'}}
      {{#ifeq Events.Count 1}}
        {{#each Events}}
            _events.Publish{{#ifval Value}}Value{{/ifval}}Event({{#ifval Value}}{{Value}}, {{/ifval}}{{#ifval Source}}new Uri($"{{Source}}", UriKind.{{../../Root.EventSourceKind}}), {{/ifval}}$"{{Subject}}", "{{Action}}");
        {{/each}}
      {{else}}
        {{#ifeq Events.Count 0}}
        {{else}}
            _events.Publish(
          {{#each Events}}
                _events.Create{{#ifval Value}}Value{{/ifval}}Event({{#ifval Value}}{{Value}}, {{/ifval}}{{#ifval Source}}new Uri($"{{Source}}", UriKind.{{../../Root.EventSourceKind}}), {{/ifval}}$"{{Subject}}", "{{Action}}"){{#if @last}});{{else}},{{/if}}
            {{#if @last}}

            {{/if}}
          {{/each}}
        {{/ifeq}}
      {{/ifeq}}
    {{/ifeq}}
    {{#if SupportsCaching}}
      {{#ifne Type 'Delete'}}
            return _cache.SetValue(__result);
      {{/ifne}}
    {{else}}   
      {{#if HasReturnValue}}
            return __result;
      {{/if}}
    {{/if}}
        }{{#if DataSvcInvoker}}{{#if DataSvcTransaction}}, new InvokerArgs { IncludeTransactionScope = true{{#ifeq EventPublish 'DataSvc'}}, EventPublisher = _events{{/ifeq}} }{{else}}{{#ifeq EventPublish 'DataSvc'}}, new InvokerArgs { EventPublisher = _events }{{/ifeq}}{{/if}}{{#if Root.CancellationToken}}, cancellationToken{{/if}});{{/if}}
  {{/unless}}
  {{/unless}}
{{/each}}
    }
}

#pragma warning restore
#nullable restore