﻿/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceBusiness}}.DataSvc
{
    /// <summary>
    /// Provides the <b>ReferenceData</b> data services.
    /// </summary>
    public partial interface IReferenceDataDataSvc
    {
        /// <summary>
        /// Gets the <see cref="IReferenceDataCollection"/> for the specified <see cref="IReferenceData"/> <see cref="Type"/>.
        /// </summary>
        /// <param name="type">The <see cref="IReferenceData"/> <see cref="Type"/>.</param>
{{#if Root.CancellationToken}}
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
{{/if}}
        /// <returns>The corresponding <see cref="IReferenceDataCollection"/>.</returns>
        Task<IReferenceDataCollection> GetAsync(Type type{{#if Root.CancellationToken}}, CancellationToken cancellationToken = default{{/if}});
    }
}

#pragma warning restore
#nullable restore