﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost. 
 */

#nullable enable
#pragma warning disable

namespace {{Root.NamespaceBusiness}}.Data
{
    /// <summary>
    /// Provides the {{{EntityNameSeeComments}}} data access.
    /// </summary>
    public partial class {{Name}}Data{{#if GenericWithT}}<T>{{/if}}{{#ifne Operations.Count 0}} : I{{Name}}Data{{#if GenericWithT}}<T>{{/if}}{{/ifne}}
    {
{{#ifne Operations.Count 0}}
  {{#each DataCtorParameters}}
        private readonly {{Type}} {{PrivateName}};
  {{/each}}
  {{#if DataExtensionsRequired}}
    {{#if HasDataExtensions}}

        #region Extensions

    {{/if}}
    {{#each DataOperations}}
      {{#ifeq Type 'GetColl'}}
        {{#ifeq AutoImplement 'Database'}}
        private Action<DatabaseParameterCollection{{#each CoreDataParameters}}, {{{ParameterType}}}{{/each}}>? {{PrivateName}}OnQuery;
        {{/ifeq}}
        {{#ifeq AutoImplement 'EntityFramework'}}
        private Func<IQueryable<{{EntityFrameworkModel}}>, {{#each CoreDataParameters}}{{{ParameterType}}}, {{/each}}IQueryable<{{EntityFrameworkModel}}>>? {{PrivateName}}OnQuery;
        {{/ifeq}}   
        {{#ifeq AutoImplement 'Cosmos'}}
          {{#ifval Parent.CosmosValueContainer}}
        private Func<IQueryable<CosmosDbValue<{{CosmosModel}}>>, {{#each CoreDataParameters}}{{{ParameterType}}}, {{/each}}IQueryable<CosmosDbValue<{{CosmosModel}}>>>? {{PrivateName}}OnQuery;
          {{else}}
        private Func<IQueryable<{{CosmosModel}}>, {{#each CoreDataParameters}}{{{ParameterType}}}, {{/each}}IQueryable<{{CosmosModel}}>>? {{PrivateName}}OnQuery;
          {{/ifval}}
        {{/ifeq}}  
        {{#ifeq AutoImplement 'OData'}}
        private Func<Soc.IBoundClient<{{Parent.ODataModel}}>, {{#each CoreDataParameters}}{{{ParameterType}}}, {{/each}}Soc.IBoundClient<{{Parent.ODataModel}}>>? {{PrivateName}}OnQuery;
        {{/ifeq}} 
      {{/ifeq}}
      {{#if DataExtensions}}
        {{#ifne AutoImplement 'None'}}
        private Func<{{#each PagingLessDataParameters}}{{{ParameterType}}}, {{/each}}{{#if Root.CancellationToken}}CancellationToken, {{/if}}Task>? {{PrivateName}}OnBeforeAsync;
        private Func<{{#if HasReturnValue}}{{OperationReturnType}}, {{/if}}{{#each CoreDataParameters}}{{{ParameterType}}}, {{/each}}{{#if Root.CancellationToken}}CancellationToken, {{/if}}Task>? {{PrivateName}}OnAfterAsync;
        {{/ifne}}
        private Action<Exception>? {{PrivateName}}OnException;
      {{/if}}
    {{#if HasDataExtensions}}

    {{/if}}
    {{/each}}
    {{#if HasDataExtensions}}

        #endregion
    {{/if}}
  {{/if}}

        /// <summary>
        /// Initializes a new instance of the <see cref="{{Name}}Data"/> class.
        /// </summary>
  {{#each DataCtorParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
        {{lower DataCtor}} {{Name}}Data({{#each DataCtorParameters}}{{#unless @first}}, {{/unless}}{{Type}} {{ArgumentName}}{{/each}})
{{#ifle DataCtorParameters.Count 3}}
            { {{#each DataCtorParameters}}{{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}})); {{/each}}{{Name}}DataCtor(); }
{{else}}
        {
  {{#each DataCtorParameters}}
            {{PrivateName}} = {{ArgumentName}} ?? throw new ArgumentNullException(nameof({{ArgumentName}}));
  {{/each}}
            {{Name}}DataCtor();
        }
{{/ifle}}

        partial void {{Name}}DataCtor(); // Enables additional functionality to be added to the constructor.
  {{#each DataOperations}}

        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
    {{#each DataParameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
    {{/each}}
    {{#if Root.CancellationToken}}
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
    {{/if}}
    {{#if HasReturnValue}}
        /// <returns>{{{ReturnText}}}</returns>
    {{/if}}
    {{#if DataNoneSingleLine}}
        public {{{OperationTaskReturnType}}} {{Name}}Async({{#each DataParameters}}{{{ParameterType}}} {{ArgumentName}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, CancellationToken cancellationToken = default{{/if}}) => {{#if DataInvoker}}DataInvoker.Current.InvokeAsync(this, {{#if Root.CancellationToken}}cancellationToken{{else}}_{{/if}} => {{/if}}{{Name}}OnImplementationAsync({{#each DataParameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}value ?? throw new ArgumentNullException(nameof(value)){{else}}{{ArgumentName}}{{/if}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}}){{#if DataInvoker}}, new InvokerArgs { {{#if DataExtensions}}ExceptionHandler = {{PrivateName}}OnException{{/if}}{{#if DataTransaction}}{{#if DataExtension}}, {{/if}}IncludeTransactionScope = true{{/if}}{{#ifeq EventPublish 'Data'}}{{#ifor DataExtensions DataTransaction}}, {{/ifor}}EventPublisher = _events{{/ifeq}} }{{/if}}{{#if DataInvoker}}){{/if}};
    {{else}}
        public {{#ifeq AutoImplement 'HttpAgent'}}{{#unless DataInvoker}}async {{/unless}}{{/ifeq}}{{{OperationTaskReturnType}}} {{Name}}Async({{#each DataParameters}}{{{ParameterType}}} {{ArgumentName}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, CancellationToken cancellationToken = default{{/if}}){{#if DataInvoker}} => DataInvoker.Current.InvokeAsync(this, {{#unless DataSingleLine}}async {{/unless}}{{#if Root.CancellationToken}}cancellationToken{{else}}_{{/if}} => {{/if}}
        {
      {{#ifeq AutoImplement 'OData'}}
            var __dataArgs = ODataArgs.Create(_mapper{{#if Paging}}, __result.Paging!{{/if}}{{#ifval ODataCollectionName}}, "{{ODataCollectionName}}"{{/ifval}});
      {{/ifeq}}
      {{#if DataExtensions}}
            await Invoker.InvokeAsync({{PrivateName}}OnBeforeAsync?.Invoke({{#each PagingLessDataParameters}}{{{ArgumentName}}}{{#unless @last}}, {{/unless}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}})).ConfigureAwait(false);
      {{/if}}
      {{#ifeq AutoImplement 'None'}}
            {{#ifor DataExtensions HasDataEvents}}{{#if HasReturnValue}}var __result = {{/if}}{{else}}{{#ifor DataSingleLine HasReturnValue}}return {{/ifor}}{{/ifor}}{{#unless DataSingleLine}}await {{/unless}}{{Name}}OnImplementationAsync({{#each DataParameters}}{{#unless @first}}, {{/unless}}{{#if IsValueArg}}value ?? throw new ArgumentNullException(nameof(value)){{else}}{{ArgumentName}}{{/if}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}});
      {{/ifeq}}
      {{#ifeq AutoImplement 'Database'}}
        {{#ifeq Type 'GetColl'}}
            {{#if DataExtensions}}var __result = await{{else}}return{{/if}} {{DataArgs.Name}}.StoredProcedure("{{FullyQualifiedStoredProcedureName}}").Query({{DataEntityMapper}}.Default, p => {{PrivateName}}OnQuery?.Invoke(p{{#each CoreDataParameters}}, {{{ArgumentName}}}{{/each}})){{#if Paging}}.WithPaging({{PagingParameter.ArgumentName}}){{/if}}.SelectResultAsync<{{BaseReturnType}}CollectionResult, {{BaseReturnType}}Collection>({{#if Root.CancellationToken}}cancellationToken{{/if}}){{#if DataExtensions}}.ConfigureAwait(false){{/if}};
        {{else}}
            {{#ifor DataExtensions HasDataEvents}}{{#if HasReturnValue}}var __result = {{/if}}{{else}}{{#ifor DataSingleLine HasReturnValue}}return {{/ifor}}{{/ifor}}{{#unless DataSingleLine}}await {{/unless}}{{DataArgs.Name}}.StoredProcedure("{{FullyQualifiedStoredProcedureName}}").{{Type}}Async({{DataEntityMapper}}.Default{{#if HasValue}}, value ?? throw new ArgumentNullException(nameof(value)){{/if}}{{#each ValueLessDataParameters}}{{#if @first}}, {{#ifne Parent.ValueLessDataParameters.Count 1}}CompositeKey.Create({{/ifne}}{{else}}, {{/if}}{{ParameterConverted}}{{#if @last}}{{#ifne Parent.ValueLessDataParameters.Count 1}}){{/ifne}}{{/if}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}}){{#unless DataSingleLine}}.ConfigureAwait(false){{/unless}};
        {{/ifeq}}
      {{/ifeq}}
      {{#ifeq AutoImplement 'EntityFramework'}}
        {{#ifeq Type 'GetColl'}}
            {{#if DataExtensions}}var __result = await{{else}}return{{/if}} {{DataArgs.Name}}.Query<{{BaseReturnType}}, {{EntityFrameworkModel}}>(q => {{PrivateName}}OnQuery?.Invoke(q{{#each CoreDataParameters}}, {{{ArgumentName}}}{{/each}}) ?? q){{#if Paging}}.WithPaging({{PagingParameter.ArgumentName}}){{/if}}.SelectResultAsync<{{BaseReturnType}}CollectionResult, {{BaseReturnType}}Collection>({{#if Root.CancellationToken}}cancellationToken{{/if}}){{#if DataExtensions}}.ConfigureAwait(false){{/if}};
        {{else}}
            {{#ifor DataExtensions HasDataEvents}}{{#if HasReturnValue}}var __result = {{/if}}{{else}}{{#ifor DataSingleLine HasReturnValue}}return {{/ifor}}{{/ifor}}{{#unless DataSingleLine}}await {{/unless}}{{DataArgs.Name}}.{{Type}}Async<{{#ifeq BaseReturnType 'void'}}{{Parent.EntityName}}{{else}}{{BaseReturnType}}{{/ifeq}}, {{EntityFrameworkModel}}>({{#if HasValue}}value ?? throw new ArgumentNullException(nameof(value)){{#ifne ValueLessDataParameters.Count 0}}, {{/ifne}}{{/if}}{{#each ValueLessDataParameters}}{{#if @first}}{{#ifne Parent.ValueLessDataParameters.Count 1}}CompositeKey.Create({{/ifne}}{{/if}}{{ParameterConverted}}{{#if @last}}{{#ifne Parent.ValueLessDataParameters.Count 1}}){{/ifne}}{{else}}), {{/if}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}}){{#unless DataSingleLine}}.ConfigureAwait(false){{/unless}};
        {{/ifeq}}
      {{/ifeq}}
      {{#ifeq AutoImplement 'Cosmos'}}
        {{#ifeq Type 'GetColl'}}
            {{#if DataExtensions}}var __result = await{{else}}return{{/if}} {{DataArgs.Name}}.{{CosmosContainerId}}.Query({{#ifval CosmosPartitionKeyCode}}{{CosmosPartitionKeyCode}}, {{/ifval}}q => {{PrivateName}}OnQuery?.Invoke(q{{#each CoreDataParameters}}, {{{ArgumentName}}}{{/each}}) ?? q){{#if Paging}}.WithPaging({{PagingParameter.ArgumentName}}){{/if}}.SelectResultAsync<{{BaseReturnType}}CollectionResult, {{BaseReturnType}}Collection>({{#if Root.CancellationToken}}cancellationToken{{/if}}){{#if DataExtensions}}.ConfigureAwait(false){{/if}};
        {{else}}
            {{#ifor DataExtensions HasDataEvents}}{{#if HasReturnValue}}var __result = {{/if}}{{else}}{{#ifor DataSingleLine HasReturnValue}}return {{/ifor}}{{/ifor}}{{#unless DataSingleLine}}await {{/unless}}{{DataArgs.Name}}.{{CosmosContainerId}}.{{Type}}Async({{#if HasValue}}value ?? throw new ArgumentNullException(nameof(value)){{#ifne ValueLessDataParameters.Count 0}}, {{/ifne}}{{/if}}{{#each ValueLessDataParameters}}{{#unless @first}}, {{/unless}}{{ParameterConverted}}{{/each}}{{#if Root.CancellationToken}}, cancellationToken{{/if}}){{#unless DataSingleLine}}.ConfigureAwait(false){{/unless}};
        {{/ifeq}}
      {{/ifeq}}
      {{#ifeq AutoImplement 'OData'}}
        {{#ifeq Type 'GetColl'}}
            __result.Collection = {{DataArgs.Name}}.Query<{{BaseReturnType}}, {{Parent.ODataModel}}>(__dataArgs, q => {{PrivateName}}OnQuery?.Invoke(q, {{#each CoreDataParameters}}{{{ArgumentName}}}, {{/each}}__dataArgs) ?? q).SelectQuery<{{BaseReturnType}}Collection>();
        {{else}}
            {{#unless DataExtensions}}{{#if HasDataEvents}}{{#if HasReturnValue}}var {{/if}}{{/if}}{{/unless}}{{#ifor DataExtensions HasDataEvents}}{{#if HasReturnValue}}__result = {{/if}}{{else}}{{#if HasReturnValue}}return {{/if}}{{/ifor}}await {{DataArgs.Name}}.{{Type}}Async<{{#ifeq BaseReturnType 'void'}}{{Parent.EntityName}}{{else}}{{BaseReturnType}}{{/ifeq}}, {{Parent.ODataModel}}>(__dataArgs{{#each PagingLessDataParameters}}, {{#if IsValueArg}}value ?? throw new ArgumentNullException(nameof(value)){{else}}{{ParameterConverted}}{{/if}}{{/each}}).ConfigureAwait(false);
        {{/ifeq}}
      {{/ifeq}}
      {{#ifeq AutoImplement 'HttpAgent'}}
            {{#unless DataExtensions}}{{#if HasDataEvents}}{{#if HasReturnValue}}var {{/if}}{{/if}}{{/unless}}{{#ifor DataExtensions HasDataEvents}}{{#if HasReturnValue}}__result = {{/if}}{{else}}{{#if DataSingleLine}}{{#if HasReturnValue}}return {{/if}}{{/if}}{{/ifor}}{{HttpAgentSendStatement}}
      {{/ifeq}}
      {{#if DataExtensions}}
            await Invoker.InvokeAsync({{PrivateName}}OnAfterAsync?.Invoke({{#if HasReturnValue}}__result{{/if}}{{#each CoreDataParameters}}{{#if @first}}{{#if Parent.HasReturnValue}}, {{/if}}{{else}}, {{/if}}{{ArgumentName}}{{/each}})).ConfigureAwait(false);
      {{/if}}
      {{#ifeq EventPublish 'Data'}}
        {{#ifeq Events.Count 1}}
          {{#each Events}}
            _events.Publish{{#ifval Value}}Value{{/ifval}}Event({{#ifval Value}}{{Value}}, {{/ifval}}{{#ifval Source}}new Uri($"{{Source}}", UriKind.{{../../Root.EventSourceKind}}), {{/ifval}}$"{{Subject}}", "{{Action}}");
          {{/each}}
        {{else}}
          {{#ifeq Events.Count 0}}
          {{else}}
            _events.Publish(
            {{#each Events}}
                EventData.Create{{#ifval Value}}Value{{/ifval}}Event({{#ifval Value}}{{Value}}, {{/ifval}}{{#ifval Source}}new Uri($"{{Source}}", UriKind.{{../../Root.EventSourceKind}}), {{/ifval}}$"{{Subject}}", "{{Action}}")){{#if @last}});{{else}},{{/if}}
              {{#if @last}}

              {{/if}}
            {{/each}}
          {{/ifeq}}
        {{/ifeq}}
      {{/ifeq}}
      {{#if DataExtensions}}
        {{#if HasReturnValue}}
            return __result;
        {{/if}}
        }, new InvokerArgs { ExceptionHandler = {{PrivateName}}OnException{{#if DataTransaction}}, IncludeTransactionScope = true{{/if}}{{#ifeq EventPublish 'Data'}}, EventPublisher = _events{{/ifeq}} }{{#if Root.CancellationToken}}, cancellationToken{{/if}});
      {{else}}
        {{#if HasReturnValue}}
          {{#ifne Type 'GetColl'}}
            {{#if HasDataEvents}}
            return __result;
            {{/if}}
          {{/ifne}}
        {{/if}}
        }{{#if DataInvoker}}{{#if DataTransaction}}, new InvokerArgs { IncludeTransactionScope = true{{#ifeq EventPublish 'Data'}}, EventPublisher = _events{{/ifeq}} }{{else}}{{#ifeq EventPublish 'Data'}}, new InvokerArgs { EventPublisher = _events }{{/ifeq}}{{/if}}{{#if Root.CancellationToken}}, cancellationToken{{/if}});{{/if}}
      {{/if}}
    {{/if}}
  {{/each}}
{{/ifne}}
{{#if UsesDatabase}}
  {{#unless DatabaseCustomMapper}}

        /// <summary>
        /// Provides the {{{EntityNameSeeComments}}} property and database column mapping.
        /// </summary>
        public partial class DbMapper : DatabaseMapper<{{EntityName}}, DbMapper>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="DbMapper"/> class.
            /// </summary>
            public DbMapper()
            {
    {{#ifval DatabaseMapperInheritsFrom}}
                InheritPropertiesFrom({{DatabaseMapperInheritsFrom}}.Default);
    {{/ifval}}
    {{#each DatabaseMapperProperties}}
                Property(s => s.{{DataMapperPropertyName}}{{#ifval DataName}}, "{{DataName}}"{{/ifval}}{{#ifne DataOperationTypes 'Any'}}, operationTypes: OperationTypes.{{DataOperationTypes}}{{/ifne}}){{#if PrimaryKey}}.SetPrimaryKey({{#if DataAutoGenerated}}true{{else}}false{{/if}}){{/if}}{{#ifval DataConverterCode}}{{{DataConverterCode}}}{{/ifval}}{{#ifval DatabaseDbType}}.SetDbType(System.Data.DbType.{{DatabaseDbType}}){{/ifval}}{{#ifval DatabaseMapper}}.SetMapper({{DatabaseMapper}}.Default!){{/ifval}};
    {{/each}}
    {{#if HasDatabaseETagProperty}}
                Property(s => s.ETag, "RowVersion", operationTypes: OperationTypes.AnyExceptCreate).SetConverter({{Root.ETagDefaultMapperConverter}}.Default);
    {{/if}}
    {{#if  HasDatabaseChangeLogProperty}}
                Property(s => s.ChangeLog).SetMapper(ChangeLogExDatabaseMapper.Default);
    {{/if}}
                DbMapperCtor();
            }
            
            partial void DbMapperCtor(); // Enables the DbMapper constructor to be extended.
        }
  {{/unless}}
{{/if}}
{{#if UsesEntityFramework}}
 {{#ifval EntityFrameworkModel}}
  {{#unless EntityFrameworkCustomMapper}}

        /// <summary>
        /// Provides the {{{EntityNameSeeComments}}} to Entity Framework {{{see-comments EntityFrameworkModel}}} mapping.
        /// </summary>
        public partial class EntityToModelEfMapper : Mapper<{{EntityName}}, {{EntityFrameworkModel}}>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="EntityToModelEfMapper"/> class.
            /// </summary>
            public EntityToModelEfMapper()
            {
    {{#ifval EntityFrameworkMapperBase}}
                Base<{{EntityFrameworkMapperBase}}.EntityToModelEfMapper>();
    {{/ifval}}
    {{#each EntityFrameworkMapperProperties}}
                {{EntityFrameworkDataMapperToModelCode}}
    {{/each}}
                EntityToModelEfMapperCtor();
            }

            partial void EntityToModelEfMapperCtor(); // Enables the constructor to be extended.
    {{#if HasEntityFrameworkChangeLogProperty}}

            /// <inheritdoc/>
            protected override void OnRegister(Mapper<{{EntityName}}, {{EntityFrameworkModel}}> mapper) => mapper.Owner.Register(new Mapper<ChangeLogEx, {{EntityFrameworkModel}}>()
                .Map((s, d) => d.CreatedBy = s.CreatedBy, OperationTypes.AnyExceptUpdate)
                .Map((s, d) => d.CreatedDate = s.CreatedDate, OperationTypes.AnyExceptUpdate)
                .Map((s, d) => d.UpdatedBy = s.UpdatedBy, OperationTypes.AnyExceptCreate)
                .Map((s, d) => d.UpdatedDate = s.UpdatedDate, OperationTypes.AnyExceptCreate));
    {{/if}}
        }

        /// <summary>
        /// Provides the Entity Framework {{{see-comments EntityFrameworkModel}}} to {{{EntityNameSeeComments}}} mapping.
        /// </summary>
        public partial class ModelToEntityEfMapper : Mapper<{{EntityFrameworkModel}}, {{EntityName}}>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="ModelToEntityEfMapper"/> class.
            /// </summary>
            public ModelToEntityEfMapper()
            {
    {{#ifval EntityFrameworkMapperBase}}
                Base<{{EntityFrameworkMapperBase}}.ModelToEntityEfMapper>();
    {{/ifval}}
    {{#each EntityFrameworkMapperProperties}}
                {{EntityFrameworkDataMapperFromModelCode}}
    {{/each}}
                ModelToEntityEfMapperCtor();
            }

            partial void ModelToEntityEfMapperCtor(); // Enables the constructor to be extended.
    {{#if HasEntityFrameworkChangeLogProperty}}

            /// <inheritdoc/>
            protected override void OnRegister(Mapper<{{EntityFrameworkModel}}, {{EntityName}}> mapper) => mapper.Owner.Register(new Mapper<{{EntityFrameworkModel}}, ChangeLogEx>()
                .Map((s, d) => d.CreatedBy = s.CreatedBy, OperationTypes.AnyExceptUpdate)
                .Map((s, d) => d.CreatedDate = s.CreatedDate, OperationTypes.AnyExceptUpdate)
                .Map((s, d) => d.UpdatedBy = s.UpdatedBy, OperationTypes.AnyExceptCreate)
                .Map((s, d) => d.UpdatedDate = s.UpdatedDate, OperationTypes.AnyExceptCreate));
    {{/if}}
        }
  {{/unless}}
 {{/ifval}}
{{/if}}
{{#if UsesCosmos}}
 {{#ifval CosmosModel}}
  {{#unless CosmosCustomMapper}}

        /// <summary>
        /// Provides the {{{EntityNameSeeComments}}} to Entity Framework {{{see-comments CosmosModel}}} mapping.
        /// </summary>
        public partial class EntityToModelCosmosMapper : Mapper<{{EntityName}}, {{CosmosModel}}>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="EntityToModelCosmosMapper"/> class.
            /// </summary>
            public EntityToModelCosmosMapper()
            {
    {{#ifval CosmosMapperBase}}
                Base<{{CosmosMapperBase}}.EntityToModelCosmosMapper>();
    {{/ifval}}
    {{#each CosmosMapperProperties}}
                {{CosmosDataMapperToModelCode}}
    {{/each}}
                EntityToModelCosmosMapperCtor();
            }

            partial void EntityToModelCosmosMapperCtor(); // Enables the constructor to be extended.
        }

        /// <summary>
        /// Provides the Entity Framework {{{see-comments CosmosModel}}} to {{{EntityNameSeeComments}}} mapping.
        /// </summary>
        public partial class ModelToEntityCosmosMapper : Mapper<{{CosmosModel}}, {{EntityName}}>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="ModelToEntityCosmosMapper"/> class.
            /// </summary>
            public ModelToEntityCosmosMapper()
            {
    {{#ifval CosmosMapperBase}}
                Base<{{CosmosMapperBase}}.ModelToEntityCosmosMapper>();
    {{/ifval}}
    {{#each CosmosMapperProperties}}
                {{CosmosDataMapperFromModelCode}}
    {{/each}}
                ModelToEntityCosmosMapperCtor();
            }

            partial void ModelToEntityCosmosMapperCtor(); // Enables the constructor to be extended.
        }
  {{/unless}}
 {{/ifval}}
{{/if}}
{{#if UsesOData}}
  {{#unless ODataCustomMapper}}

        /// <summary>
        /// Provides the {{{EntityNameSeeComments}}} and OData {{{see-comments ODataModel}}} <i>AutoMapper</i> mapping.
        /// </summary>
        public partial class ODataMapperProfile : AutoMapper.Profile
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="ODataMapperProfile"/> class.
            /// </summary>
            public ODataMapperProfile()
            {
                var s2d = CreateMap<{{EntityName}}, {{ODataModel}}>();
    {{#each ODataMapperProperties}}
                s2d.ForMember(d => d.{{#ifval DataName}}{{DataName}}{{else}}{{Name}}{{/ifval}}, o => o.{{#ifne DataOperationTypes 'Any'}}OperationTypes(OperationTypes.{{DataOperationTypes}}).{{/ifne}}{{#ifval MapperDataConverterName}}ConvertUsing({{MapperDataConverterName}}.ToDestination, {{else}}MapFrom({{/ifval}}s => s.{{DataMapperPropertyName}}));
    {{/each}}

                var d2s = CreateMap<{{ODataModel}}, {{EntityName}}>();
    {{#each ODataMapperProperties}}
                d2s.ForMember(s => s.{{DataMapperPropertyName}}, o => o.{{#ifeq ODataMapper "Ignore"}}Ignore());{{else}}{{#ifne DataOperationTypes 'Any'}}OperationTypes(OperationTypes.{{DataOperationTypes}}).{{/ifne}}{{#ifval MapperDataConverterName}}ConvertUsing({{MapperDataConverterName}}.ToSource, {{else}}MapFrom({{/ifval}}d => d.{{#ifval DataName}}{{DataName}}{{else}}{{Name}}{{/ifval}}));{{/ifeq}}
    {{/each}}

                ODataMapperProfileCtor(s2d, d2s);
            }

            partial void ODataMapperProfileCtor(AutoMapper.IMappingExpression<{{EntityName}}, {{ODataModel}}> s2d, AutoMapper.IMappingExpression<{{ODataModel}}, {{EntityName}}> d2s); // Enables the constructor to be extended.
        }
  {{/unless}}
{{/if}}
{{#if UsesHttpAgent}}
  {{#unless HttpAgentCustomMapper}}

        /// <summary>
        /// Provides the {{{EntityNameSeeComments}}} to Entity Framework {{{see-comments HttpAgentModel}}} mapping.
        /// </summary>
        public partial class EntityToModelHttpAgentMapper : Mapper<{{EntityName}}, {{HttpAgentModel}}>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="EntityToModelHttpAgentMapper"/> class.
            /// </summary>
            public EntityToModelHttpAgentMapper()
            {
    {{#ifval HttpAgentMapperBase}}
                Base<{{HttpAgentMapperBase}}.EntityToModelHttpAgentMapper>();
    {{/ifval}}
    {{#each HttpAgentMapperProperties}}
                {{HttpAgentDataMapperToModelCode}}
    {{/each}}
                EntityToModelHttpAgentMapperCtor();
            }

            partial void EntityToModelHttpAgentMapperCtor(); // Enables the constructor to be extended.
        }

        /// <summary>
        /// Provides the Entity Framework {{{see-comments HttpAgentModel}}} to {{{EntityNameSeeComments}}} mapping.
        /// </summary>
        public partial class ModelToEntityHttpAgentMapper : Mapper<{{HttpAgentModel}}, {{EntityName}}>
        {
            /// <summary>
            /// Initializes a new instance of the <see cref="ModelToEntityHttpAgentMapper"/> class.
            /// </summary>
            public ModelToEntityHttpAgentMapper()
            {
    {{#ifval HttpAgentMapperBase}}
                Base<{{HttpAgentMapperBase}}.ModelToEntityHttpAgentMapper>();
    {{/ifval}}
    {{#each HttpAgentMapperProperties}}
                {{HttpAgentDataMapperFromModelCode}}
    {{/each}}
                ModelToEntityHttpAgentMapperCtor();
            }

            partial void ModelToEntityHttpAgentMapperCtor(); // Enables the constructor to be extended.
        }
  {{/unless}}
{{/if}}
    }
}

#pragma warning restore
#nullable restore