﻿{{! Copyright (c) Avanade. Licensed under the MIT License. See https://github.com/Avanade/Beef }}
/*
 * This file is automatically generated; any changes will be lost.
 */

#nullable enable
#pragma warning disable

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoreEx.Configuration;
using CoreEx.Entities;
using CoreEx.Http;
using CoreEx.Json;
using Microsoft.Extensions.Logging;
using {{Root.NamespaceCommon}}.Entities;
{{#ifval Root.RefDataCommonNamespace}}
using RefDataNamespace = {{Root.RefDataCommonNamespace}};
{{/ifval}}

namespace {{Root.NamespaceCommon}}.Agents
{
    /// <summary>
    /// Defines the {{{EntityNameSeeComments}}} HTTP agent.
    /// </summary>
    public partial interface I{{Name}}Agent
    {
{{#each WebApiAgentOperations}}
  {{#unless @first}}

  {{/unless}}
        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#ifeq Type 'Patch'}}
        /// <param name="patchOption">The <see cref="HttpPatchOption"/>.</param>
  {{/ifeq}}
  {{#each Parameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        {{{AgentOperationTaskReturnType}}} {{Name}}Async({{#ifeq Type 'Patch'}}HttpPatchOption patchOption, {{/ifeq}}{{#each Parameters}}{{{WebApiAgentParameterType}}} {{ArgumentName}}{{#if IsPagingArgs}} = null{{/if}}, {{/each}}HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default);
{{/each}}
    }

    /// <summary>
    /// Provides the {{{EntityNameSeeComments}}} HTTP agent.
    /// </summary>
    public partial class {{Name}}Agent : TypedHttpClientBase<{{Name}}Agent>, I{{Name}}Agent
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="{{Name}}Agent"/> class.
        /// </summary>
        /// <param name="client">The underlying <see cref="HttpClient"/>.</param>
        /// <param name="jsonSerializer">The <see cref="IJsonSerializer"/>.</param>
        /// <param name="executionContext">The <see cref="CoreEx.ExecutionContext"/>.</param>
        /// <param name="settings">The <see cref="SettingsBase"/>.</param>
        /// <param name="logger">The <see cref="ILogger"/>.</param>
        public {{Name}}Agent(HttpClient client, IJsonSerializer jsonSerializer, CoreEx.ExecutionContext executionContext, SettingsBase settings, ILogger<{{Name}}Agent> logger) 
            : base(client, jsonSerializer, executionContext, settings, logger) { }
{{#each WebApiAgentOperations}}

        /// <summary>
        /// {{{SummaryText}}}
        /// </summary>
  {{#ifeq Type 'Patch'}}
        /// <param name="patchOption">The <see cref="HttpPatchOption"/>.</param>
  {{/ifeq}}
  {{#each Parameters}}
        /// <param name="{{ArgumentName}}">{{{SummaryText}}}</param>
  {{/each}}
        /// <param name="requestOptions">The optional <see cref="HttpRequestOptions"/>.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/>.</param>
        /// <returns>A <see cref="HttpResult"/>.</returns>
        public {{{AgentOperationTaskReturnType}}} {{Name}}Async({{#ifeq Type 'Patch'}}HttpPatchOption patchOption, {{/ifeq}}{{#each Parameters}}{{{WebApiAgentParameterType}}} {{ArgumentName}}{{#if IsPagingArgs}} = null{{/if}}, {{/each}}HttpRequestOptions? requestOptions = null, CancellationToken cancellationToken = default)
            => {{AgentOperationHttpMethod}}("{{AgentWebApiRoute}}", {{#ifeq Type 'Patch'}}patchOption, {{/ifeq}}{{#if HasValue}}value, {{/if}}requestOptions: requestOptions{{#if Paging}}.IncludePaging(paging){{/if}}{{#ifne ValueLessParameters.Count 0}}, args: HttpArgs.Create({{#each CoreParameters}}{{#unless @first}}, {{/unless}}new HttpArg<{{{WebApiAgentParameterType}}}>("{{ArgumentName}}", {{ArgumentName}}{{#ifval WebApiAgentFrom}}, HttpArgType.{{WebApiAgentFrom}}{{/ifval}}){{/each}}){{/ifne}}, cancellationToken: cancellationToken);
{{/each}}
    }
}

#pragma warning restore
#nullable restore