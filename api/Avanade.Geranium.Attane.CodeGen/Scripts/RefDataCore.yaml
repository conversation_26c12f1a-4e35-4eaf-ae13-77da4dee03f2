﻿configType: Beef.CodeGen.Config.Entity.CodeGenConfig, Beef.CodeGen.Core
generators:
- { type: 'Beef.CodeGen.Generators.EntityCodeGenerator, Beef.CodeGen.Core', template: 'Entity_cs', file: '{{Name}}.cs', directory: '{{Root.PathCommon}}/Entities/Generated', EntityScope: 'Common', text: 'EntityCodeGenerator: Common/Entities' }
- { type: 'Beef.CodeGen.Generators.EntityCodeGenerator, Beef.CodeGen.Core', template: 'Entity_cs', file: '{{Name}}.cs', directory: '{{Root.PathBusiness}}/Entities/Generated', EntityScope: 'Business', text: 'EntityCodeGenerator: Business/Entities' }
- { type: 'Beef.CodeGen.Generators.EntityOmitBaseCodeGenerator, Beef.CodeGen.Core', template: 'Model_cs', file: '{{Name}}.cs', directory: '{{Root.PathCommon}}/Entities/Generated', EntityScope: 'Common', text: 'EntityOmitBaseCodeGenerator: Common/Entities' }
- { type: 'Beef.CodeGen.Generators.EntityOmitBaseCodeGenerator, Beef.CodeGen.Core', template: 'Model_cs', file: '{{Name}}.cs', directory: '{{Root.PathCommon}}/Entities/Generated', EntityScope: 'Business', text: 'EntityOmitBaseCodeGenerator: Business/Entities' }
- { type: 'Beef.CodeGen.Generators.EntityDataModelCodeGenerator, Beef.CodeGen.Core', template: 'Model_cs', file: '{{Name}}.cs', directory: '{{Root.PathBusiness}}/Data/Model/Generated', IsDataModel: 'true', EntityScope: 'Business', text: 'EntityModelCodeGenerator: Business/Data/Model' }

- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ReferenceDataIData_cs', file: 'IReferenceDataData.cs', directory: '{{Root.PathBusiness}}/Data/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business/Data' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ReferenceDataData_cs', file: 'ReferenceDataData.cs', directory: '{{Root.PathBusiness}}/Data/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business/Data' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ReferenceDataIDataSvc_cs', file: 'IReferenceDataDataSvc.cs', directory: '{{Root.PathBusiness}}/DataSvc/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business/DataSvc' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ReferenceDataDataSvc_cs', file: 'ReferenceDataDataSvc.cs', directory: '{{Root.PathBusiness}}/DataSvc/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business/DataSvc' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ReferenceDataProvider_cs', file: 'ReferenceDataProvider.cs', directory: '{{Root.PathBusiness}}/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ReferenceDataWebApiController_cs', file: 'ReferenceDataController.cs', directory: '{{Root.PathApi}}/Controllers/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Api/Controllers' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ReferenceDataWebApiAgent_cs', file: 'ReferenceDataAgent.cs', directory: '{{Root.PathCommon}}/Agents/Generated', IsRefData: 'true', text: 'EntityRootCodeGenerator: Common/Agents' }
    
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ServiceCollectionExtensionsManager_cs', file: 'ReferenceDataServiceCollectionExtensions.cs', directory: '{{Root.PathBusiness}}/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ServiceCollectionExtensionsDataSvc_cs', file: 'ReferenceDataServiceCollectionExtensions.cs', directory: '{{Root.PathBusiness}}/DataSvc/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business/DataSvc' }
- { type: 'Beef.CodeGen.Generators.EntityRootCodeGenerator, Beef.CodeGen.Core', template: 'ServiceCollectionExtensionsData_cs', file: 'ReferenceDataServiceCollectionExtensions.cs', directory: '{{Root.PathBusiness}}/Data/Generated', IsRefData: 'true', text: 'RootEntityCodeGenerator: Business/Data' }