﻿configType: Beef.CodeGen.Config.Entity.CodeGenConfig, Beef.CodeGen.Core
inherits: [ 'EntityBusiness.yaml' ]
generators:
- { type: 'Beef.CodeGen.Generators.EntityWebApiControllerCodeGenerator, Beef.CodeGen.Core', template: 'EntityWebApiController_cs', file: '{{Name}}Controller.cs', directory: '{{Root.PathApi}}/Controllers/Generated', EntityScope: 'Common', text: 'EntityWebApiControllerCodeGenerator: Api/Controllers' }
- { type: 'Beef.CodeGen.Generators.EntityWebApiAgentCodeGenerator, Beef.CodeGen.Core', template: 'EntityWebApiAgent_cs', file: '{{Name}}Agent.cs', directory: '{{Root.PathCommon}}/Agents/Generated', EntityScope: 'Common', text: 'EntityWebApiAgentCodeGenerator: Common/Agents' }

#- { type: 'Beef.CodeGen.Generators.EntityGrpcProtoCodeGenerator, Beef.CodeGen.Core', template: 'Grpc_proto', file: '{{lower Root.PathCommon}}.grpc.proto', directory: '{{Root.PathCommon}}/Grpc/Generated', EntityScope: 'Common', text: 'EntityGrpcProtoCodeGenerator: Common/Grpc' }
#- { type: 'Beef.CodeGen.Generators.EntityGrpcProtoCodeGenerator, Beef.CodeGen.Core', template: 'GrpcTransformers_cs', file: 'Transformers.cs', directory: '{{Root.PathCommon}}/Grpc/Generated', EntityScope: 'Common', text: 'EntityGrpcProtoCodeGenerator: Common/Grpc' }
#- { type: 'Beef.CodeGen.Generators.EntityGrpcServiceCodeGenerator, Beef.CodeGen.Core', template: 'EntityGrpcService_cs', file: '{{Name}}Service.cs', directory: '{{Root.PathApi}}/Grpc/Generated', EntityScope: 'Common', text: 'EntityGrpcServiceCodeGenerator: Api/Grpc' }
#- { type: 'Beef.CodeGen.Generators.EntityGrpcAgentCodeGenerator, Beef.CodeGen.Core', template: 'EntityGrpcAgent_cs', file: '{{Name}}Agent.cs', directory: '{{Root.PathCommon}}/Grpc/Generated', EntityScope: 'Common', text: 'EntityGrpcAgentCodeGenerator: Common/Grpc' }