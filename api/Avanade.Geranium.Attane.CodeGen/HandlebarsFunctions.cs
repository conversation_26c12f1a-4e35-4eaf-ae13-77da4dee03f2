using HandlebarsDotNet;
using Newtonsoft.Json.Linq;
using System;

namespace Avanade.Geranium.Attane.CodeGen
{
    public static class HandlebarsFunctions
    {
        public static void Register()
        {
            RegisterNotHasKey();
            RegisterHasKey();
            RegisterValueOf();
            RegisterReplace();
        }

        private static void RegisterNotHasKey()
        {
            Handlebars.RegisterHelper("notHasKey", (context, args) => !HasKeyImpl(args));
        }

        private static void RegisterHasKey()
        {
            Handlebars.RegisterHelper("hasKey", (context, args) => HasKeyImpl(args));
        }

        private static bool HasKeyImpl(Arguments args)
        {
            if (args.Length != 2)
                throw new System.ArgumentException("hasKey should receive collection and index");

            var result = false;
            bool ContainsKeyInCollectionOf<T>()
            {
                if (args[0] is System.Collections.Generic.IDictionary<string, T> collection && args[1] is string index)
                {
                    result = collection.ContainsKey(index);
                    return true;
                }
                return false;
            }

            if (args[0] == null)
                return false;
            // for ExtraProperties
            if (ContainsKeyInCollectionOf<JToken>())
                return result;
            // for CustomProperties
            if (ContainsKeyInCollectionOf<object>())
                return result;
            throw new System.ArgumentException("hasKey should receive IDictionary<string, JToken> or IDictionary<string, object>");
        }

        private static void RegisterValueOf()
        {
            Handlebars.RegisterHelper("valueOf", (context, args) =>
            {
                if (args.Length != 2)
                    throw new System.ArgumentException("valueOf should receive collection and index");

                object? result = null;
                bool GetValueInCollectionOf<T>(Func<T?, object?> converter)
                {
                    if (args[0] is System.Collections.Generic.IDictionary<string, T> collection && args[1] is string index)
                    {
                        if (collection.TryGetValue(index, out var collectionValue))
                            result = converter(collectionValue);

                        return true;
                    }
                    return false;
                }

                // for ExtraProperties
                if (GetValueInCollectionOf<JToken>(t => t?.Value<string>()))
                    return result;
                // for CustomProperties
                if (GetValueInCollectionOf<object>(t => t))
                    return result;

                throw new System.ArgumentException("valueOf should receive IDictionary<string, JToken> or IDictionary<string, object>");
            });
        }
        private static void RegisterReplace()
        {
            Handlebars.RegisterHelper("replace", (context, args) =>
            {
                if (args.Length != 3)
                    throw new System.ArgumentException("replace should receive target, oldValue and newValue");
                var target = args[0]?.ToString();
                if (target == null)
                {
                    return null;
                }

                var oldValue = args[1]?.ToString();
                if (oldValue == null)
                {
                    return args[0].ToString();
                }

                return target.Replace(oldValue, args[2]?.ToString() ?? "");
            });
        }
    }
}
