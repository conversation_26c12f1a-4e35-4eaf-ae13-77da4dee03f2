{"title": "JSON Schema for Beef Entity code-generation (https://github.com/Avanade/Beef).", "$schema": "https://json-schema.org/draft-04/schema#", "definitions": {"CodeGeneration": {"type": "object", "title": "'CodeGeneration' object (entity-driven)", "description": "The 'CodeGeneration' object defines global properties that are used to drive the underlying entity-driven code generation.", "properties": {"refDataNamespace": {"type": "string", "title": "The namespace for the Reference Data entities (adds as a c# 'using' statement).", "description": "Defaults to 'Company' + '.' (literal) + AppName + '.Business.Entities' (literal)."}, "refDataCommonNamespace": {"type": "string", "title": "The namespace for the Reference Data common entities (adds as a c# 'using' statement).", "description": "Defaults to 'Company' + '.' (literal) + AppName + '.Common.Entities' (literal)."}, "refDataText": {"type": "boolean", "title": "Indicates whether a corresponding 'Text' property is added when generating a Reference Data 'Property' for an 'Entity'.", "description": "This is used where serializing within the Web API 'Controller' and the 'ExecutionContext.IsRefDataTextSerializationEnabled' is set to 'true' (which is automatically set where the url contains '$text=true'). This can be further configured on the 'Entity' and for each 'Property'."}, "refDataWebApiRoute": {"type": "string", "title": "The 'RouteAtttribute' for the Reference Data Web API controller required for named pre-fetching. The 'WebApiRoutePrefix' will be prepended where specified."}, "refDataDataCtorParams": {"type": "array", "title": "The list of additional (non-inferred) Dependency Injection (DI) parameters for the generated 'ReferenceDataData' constructor.", "description": "Each constructor parameter should be formatted as 'Type' + '^' + 'Name'; e.g. 'IConfiguration^Config'. Where the 'Name' portion is not specified it will be inferred. Where the 'Type' matches an already inferred value it will be ignored.", "items": {"type": "string"}}, "jsonSerializer": {"type": "string", "title": "The JSON Serializer to use for JSON property attribution.", "description": "Defaults to 'SystemText'. This can be overridden within the 'Entity'(s).", "enum": ["SystemText", "Newtonsoft"]}, "etagJsonName": {"type": "string", "title": "The default JSON name for the 'ETag' property.", "description": "Defaults to 'etag'. Note that the 'JsonName' can be set individually per property where required.", "enum": ["etag", "eTag", "_etag", "_eTag", "ETag", "ETAG"]}, "usingNamespace1": {"type": "string", "title": "The additional Namespace using statement to be added to the generated 'Entity' code.", "description": "Typically used where referening a 'Type' from a Namespace that is not generated by default."}, "usingNamespace2": {"type": "string", "title": "The additional Namespace using statement to be added to the generated 'Entity' code.", "description": "Typically used where referening a 'Type' from a Namespace that is not generated by default."}, "usingNamespace3": {"type": "string", "title": "The additional Namespace using statement to be added to the generated 'Entity' code.", "description": "Typically used where referening a 'Type' from a Namespace that is not generated by default."}, "webApiAuthorize": {"type": "string", "title": "The authorize attribute value to be used for the corresponding entity Web API controller; generally either 'Authorize' or 'AllowAnonymous'.", "description": "This can be overridden within the 'Entity'(s) and/or their corresponding 'Operation'(s)."}, "webApiAutoLocation": {"type": "boolean", "title": "Indicates whether the HTTP Response Location Header route ('Operation.WebApiLocation') is automatically inferred.", "description": "This will automatically set the 'Operation.WebApiLocation' for an 'Operation' named 'Create' where there is a corresponding named 'Get'. This can be overridden within the 'Entity'(s)."}, "webApiRoutePrefix": {"type": "string", "title": "The 'RoutePrefixAtttribute' for the corresponding entity Web API controller.", "description": "This is the base (prefix) 'URI' prepended to all entity and underlying 'Operation'(s)."}, "databaseName": {"type": "string", "title": "The .NET database interface name (used where 'Operation.AutoImplement' is 'Database').", "description": "Defaults to 'IDatabase'. This can be overridden within the 'Entity'(s)."}, "databaseSchema": {"type": "string", "title": "The default database schema name.", "description": "Defaults to 'dbo'."}, "databaseProvider": {"type": "string", "title": "The default database schema name.", "description": "Defaults to 'SqlServer'. Enables specific database provider functionality/formatting/etc. where applicable.", "enum": ["SqlServer", "MySQL"]}, "entityFrameworkName": {"type": "string", "title": "The .NET Entity Framework interface name used where 'Operation.AutoImplement' is 'EntityFramework'.", "description": "Defaults to 'IEfDb'. This can be overridden within the 'Entity'(s)."}, "cosmosName": {"type": "string", "title": "The .NET Entity Framework interface name used where 'Operation.AutoImplement' is 'Cosmos'.", "description": "Defaults to 'ICosmosDb'. This can be overridden within the 'Entity'(s)."}, "odataName": {"type": "string", "title": "The .NET OData interface name used where 'Operation.AutoImplement' is 'OData'.", "description": "Defaults to 'IOData'. This can be overridden within the 'Entity'(s)."}, "httpAgentName": {"type": "string", "title": "The default .NET HTTP Agent interface name used where 'Operation.AutoImplement' is 'HttpAgent'.", "description": "Defaults to 'IHttpAgent'. This can be overridden within the 'Entity'(s)."}, "etagDefaultMapperConverter": {"type": "string", "title": "The default ETag to/from RowVersion column Mapping Converter used where 'Operation.AutoImplement' is 'Database' or 'EntityFramework'.", "description": "Defaults to 'StringToBase64Converter'."}, "refDataDefaultMapperConverter": {"type": "string", "title": "The default Reference Data property 'Converter' used by the generated 'Mapper'(s) where not specifically defined.", "description": "Defaults to 'ReferenceDataCodeConverter<T>'. Where this value is suffixed by '<T>' or '{T}' this will automatically be set to the 'Type'.", "enum": ["ReferenceDataCodeConverter", "ReferenceDataCodeConverter{T}", "ReferenceDataCodeConverter<T>", "ReferenceDataIdConverter{T, int}", "ReferenceDataIdConverter<T, int>", "ReferenceDataIdConverter{T, int?}", "ReferenceDataIdConverter<T, int?>", "ReferenceDataIdConverter{T, long}", "ReferenceDataIdConverter<T, long>", "ReferenceDataIdConverter{T, long?}", "ReferenceDataIdConverter<T, long?>", "ReferenceDataIdConverter{T, Guid}", "ReferenceDataIdConverter<T, Guid>", "ReferenceDataIdConverter{T, Guid?}", "ReferenceDataIdConverter<T, Guid?>", "ReferenceDataInt32IdConverter", "ReferenceDataInt32IdConverter{T}", "ReferenceDataInt32IdConverter<T>", "ReferenceDataNullableInt32IdConverter", "ReferenceDataNullableInt32IdConverter{T}", "ReferenceDataNullableInt32IdConverter<T>", "ReferenceDataInt64IdConverter", "ReferenceDataInt64IdConverter{T}", "ReferenceDataInt64IdConverter<T>", "ReferenceDataNullableInt64IdConverter", "ReferenceDataNullableInt64IdConverter{T}", "ReferenceDataNullableInt64IdConverter<T>", "ReferenceDataGuidIdConverter", "ReferenceDataGuidIdConverter{T}", "ReferenceDataGuidIdConverter<T>", "ReferenceDataNullableGuidIdConverter", "ReferenceDataNullableGuidIdConverter{T}", "ReferenceDataNullableGuidIdConverter<T>"]}, "refDataCodeDataName": {"type": "string", "title": "The Reference Data 'Code' data name.", "description": "Defaults to 'Code' (literal)."}, "refDataTextDataName": {"type": "string", "title": "The Reference Data 'Text' data name.", "description": "Defaults to 'Text' (literal)."}, "refDataIsActiveDataName": {"type": "string", "title": "The Reference Data 'IsActive' data name.", "description": "Defaults to 'IsActive' (literal)."}, "refDataSortOrderDataName": {"type": "string", "title": "The Reference Data 'SortOrder' data name.", "description": "Defaults to 'SortOrder' (literal)."}, "refDataETagDataName": {"type": "string", "title": "The Reference Data 'ETag' data name.", "description": "Defaults to 'RowVers<PERSON>' (literal)."}, "eventPublish": {"type": "string", "title": "The layer to add logic to publish an event for a 'Create', 'Update' or 'Delete' operation.", "description": "Defaults to 'DataSvc'. Used to enable the sending of messages to the likes of EventHub, ServiceBus, SignalR, etc. This can be overridden within the 'Entity'(s).", "enum": ["None", "DataSvc", "Data"]}, "eventSourceRoot": {"type": "string", "title": "The URI root for the event source by prepending to all event source URIs.", "description": "The event source is only updated where an 'EventSourceKind' is not 'None'. This can be extended within the 'Entity'(s)."}, "eventSourceKind": {"type": "string", "title": "The URI kind for the event source URIs.", "description": "Defaults to 'None' (being the event source is not updated).", "enum": ["None", "Absolute", "Relative", "RelativeOrAbsolute"]}, "eventSubjectRoot": {"type": "string", "title": "The root for the event Subject name by prepending to all event subject names.", "description": "Used to enable the sending of messages to the likes of EventHub, ServiceBus, SignalR, etc. This can be overridden within the 'Entity'(s)."}, "eventSubjectSeparator": {"type": "string", "title": "The subject path separator.", "description": "Defaults to '.'. Used only where the subject is automatically inferred."}, "eventActionFormat": {"type": "string", "title": "The formatting for the Action when an Event is published.", "description": "Defaults to 'None' (no formatting required, i.e. as-is)'.", "enum": ["None", "PastTense"]}, "eventTransaction": {"type": "boolean", "title": "Indicates whether a 'System.TransactionScope' should be created and orchestrated at the 'DataSvc'-layer whereever generating event publishing logic.", "description": "Usage will force a rollback of any underlying data transaction (where the provider supports TransactionScope) on failure, such as an 'EventPublish' error. This is by no means implying a Distributed Transaction (DTC) should be invoked; this is only intended for a single data source that supports a TransactionScope to guarantee reliable event publishing. Defaults to 'false'. This essentially defaults the 'Entity.EventTransaction' where not otherwise specified. This should only be used where 'EventPublish' is 'DataSvc' and a transactionally-aware data source is being used."}, "grpc": {"type": "boolean", "title": "Indicates whether gRPC support (more specifically service-side) is required.", "description": "gRPC support is an explicit opt-in model. Must be set to 'true' for any of the subordinate gRPC capabilities to be code-generated. Will require each 'Entity', and corresponding 'Property' and 'Operation' to be opted-in specifically."}, "pathBase": {"type": "string", "title": "The base path (directory) prefix for the artefacts; other 'Path*' properties append to this value when they are not specifically overridden.", "description": "Defaults to 'Company' (runtime parameter) + '.' + 'AppName' (runtime parameter). For example 'Beef.Demo'."}, "pathCommon": {"type": "string", "title": "The path (directory) for the Database-related artefacts.", "description": "Defaults to 'PathBase' + '.Common' (literal). For example 'Beef.Demo.Common'."}, "pathBusiness": {"type": "string", "title": "The path (directory) for the Business-related (.NET) artefacts.", "description": "Defaults to 'PathBase' + '.Business' (literal). For example 'Beef.Demo.Business'."}, "pathApi": {"type": "string", "title": "The path (directory) for the API-related (.NET) artefacts.", "description": "Defaults to 'PathBase' + '.' + 'ApiName' (runtime parameter). For example 'Beef.Demo.Api'."}, "namespaceBase": {"type": "string", "title": "The base Namespace (root) for the .NET artefacts.", "description": "Defaults to 'Company' (runtime parameter) + '.' + 'AppName' (runtime parameter). For example 'Beef.Demo'."}, "namespaceCommon": {"type": "string", "title": "The Namespace (root) for the Common-related .NET artefacts.", "description": "Defaults to 'NamespaceBase' + '.Common' (literal). For example 'Beef.Demo.Common'."}, "namespaceBusiness": {"type": "string", "title": "The Namespace (root) for the Business-related .NET artefacts.", "description": "Defaults to 'NamespaceBase' + '.Business' (literal). For example 'Beef.Demo.Business'."}, "namespaceApi": {"type": "string", "title": "The Namespace (root) for the Api-related .NET artefacts.", "description": "Defaults to 'NamespaceBase' + '.' + 'ApiName' (runtime parameter). For example 'Beef.Demo.Api'."}, "entities": {"type": "array", "title": "The corresponding 'Entity' collection.", "items": {"$ref": "#/definitions/Entity"}}}}, "Entity": {"type": "object", "title": "'Entity' object (entity-driven)", "description": "The 'Entity' is used as the primary configuration for driving the entity-driven code generation.", "properties": {"name": {"type": "string", "title": "The unique entity name."}, "text": {"type": "string", "title": "The overriding text for use in comments.", "description": "Overrides the Name (as sentence text) for the summary comments. It will be formatted as: 'Represents the {Text} entity.'. To create a '<see cref=\"XXX\"/>' within use moustache shorthand (e.g. {{Xxx}})."}, "fileName": {"type": "string", "title": "The overriding file name.", "description": "Overrides the Name as the code-generated file name."}, "privateName": {"type": "string", "title": "The overriding private name.", "description": "Overrides the 'Name' to be used for private fields. By default reformatted from 'Name'; e.g. 'FirstName' as '_firstName'."}, "argumentName": {"type": "string", "title": "The overriding argument name.", "description": "Overrides the 'Name' to be used for argument parameters. By default reformatted from 'Name'; e.g. 'FirstName' as 'firstName'."}, "constType": {"type": "string", "title": "The Const .NET Type option.", "description": "The .NET Type to be used for the 'const' values. Defaults to 'string'.", "enum": ["int", "long", "Guid", "string"]}, "isInitialOverride": {"type": "boolean", "title": "Indicates whether to override the 'IInitial.IsInitial' property.", "description": "Set to either 'true' or 'false' to override as specified; otherwise, 'null' to check each property. Defaults to 'null'."}, "refDataType": {"type": "string", "title": "The Reference Data identifier Type option.", "description": "Required to identify an entity as being Reference Data. Specifies the underlying .NET Type used for the Reference Data identifier.", "enum": ["int", "long", "Guid", "string"]}, "refDataText": {"type": "boolean", "title": "Indicates whether a corresponding 'Text' property is added when generating a Reference Data 'Property' overriding the 'CodeGeneration.RefDataText' selection.", "description": "This is used where serializing within the Web API'Controller' and the 'ExecutionContext.IsRefDataTextSerializationEnabled' is set to 'true' (which is automatically set where the url contains '$text=true'). Defaults from 'CodeGeneration.RefDataText'."}, "refDataSortOrder": {"type": "string", "title": "The Reference Data sort order option.", "description": "Specifies the default sort order for the underlying Reference Data collection. Defaults to 'SortOrder'.", "enum": ["SortOrder", "Id", "Code", "Text"]}, "inherits": {"type": "string", "title": "The base class that the entity inherits from.", "description": "Defaults to 'EntityBase' for a standard entity. For Reference Data it will default to 'ReferenceDataBaseEx<xxx>' depending on the corresponding 'RefDataType' value. See 'OmitEntityBase' if the desired outcome is to not inherit from any of the aforementioned base classes."}, "implements": {"type": "string", "title": "The list of comma separated interfaces that are to be declared for the entity class."}, "implementsAutoInfer": {"type": "boolean", "title": "Indicates whether to automatically infer the interface implements for the entity from the properties declared.", "description": "Will attempt to infer the following: 'IIdentifier<Guid>', 'IIdentifier<int>', 'IIdentifier<long>', 'IIdentifier<string>', 'IETag' and 'IChangeLog'. Defaults to 'true'."}, "abstract": {"type": "boolean", "title": "Indicates whether the class should be defined as abstract."}, "genericWithT": {"type": "boolean", "title": "Indicates whether the class should be defined as a generic with a single parameter 'T'."}, "namespace": {"type": "string", "title": "The entity namespace to be appended.", "description": "Appended to the end of the standard structure as follows: '{Company}.{AppName}.Business.Entities.{Namespace}'."}, "omitEntityBase": {"type": "boolean", "title": "Indicates that the entity should not inherit from 'EntityBase'.", "description": "As such any of the 'EntityBase' related capabilites are not supported (are omitted from generation). The intention for this is more for the generation of simple internal entities."}, "jsonSerializer": {"type": "string", "title": "The JSON Serializer to use for JSON property attribution.", "description": "Defaults to the 'CodeGeneration.JsonSerializer' configuration property where specified; otherwise, 'SystemText'.", "enum": ["SystemText", "Newtonsoft"]}, "internalOnly": {"type": "boolean", "title": "Indicates whether the entity is for internal use only; declared in Business entities only."}, "collection": {"type": "boolean", "title": "Indicates whether a corresponding entity collection class should be created."}, "collectionResult": {"type": "boolean", "title": "Indicates whether a corresponding entity collection result class should be created", "description": "Enables the likes of additional paging state to be stored with the underlying collection."}, "collectionKeyed": {"type": "boolean", "title": "Indicates whether the entity collection is keyed using the properties defined as forming part of the unique key."}, "collectionInherits": {"type": "string", "title": "The base class that a 'Collection' inherits from.", "description": "Defaults to 'EntityBaseCollection' or 'EntityBaseKeyedCollection' depending on 'CollectionKeyed'. For Reference Data it will default to 'ReferenceDataCollectionBase'."}, "collectionResultInherits": {"type": "string", "title": "The base class that a 'CollectionResult' inherits from.", "description": "Defaults to 'EntityCollectionResult'."}, "get": {"type": "boolean", "title": "Indicates that a 'Get' operation will be automatically generated where not otherwise explicitly specified."}, "getAll": {"type": "boolean", "title": "Indicates that a 'GetAll' operation will be automatically generated where not otherwise explicitly specified."}, "create": {"type": "boolean", "title": "Indicates that a 'Create' operation will be automatically generated where not otherwise explicitly specified."}, "update": {"type": "boolean", "title": "Indicates that a 'Update' operation will be automatically generated where not otherwise explicitly specified."}, "patch": {"type": "boolean", "title": "Indicates that a 'Patch' operation will be automatically generated where not otherwise explicitly specified."}, "delete": {"type": "boolean", "title": "Indicates that a 'Delete' operation will be automatically generated where not otherwise explicitly specified."}, "autoImplement": {"type": "string", "title": "The data source auto-implementation option.", "description": "Defaults to 'None'. Indicates that the implementation for the underlying 'Operations' will be auto-implemented using the selected data source (unless explicity overridden). When selected some of the related attributes will also be required (as documented). Additionally, the 'AutoImplement' indicator must be selected for each underlying 'Operation' that is to be auto-implemented.", "enum": ["Database", "EntityFramework", "Cosmos", "OData", "HttpAgent", "None"]}, "dataCtor": {"type": "string", "title": "The access modifier for the generated 'Data' constructor.", "description": "Defaults to 'Public'.", "enum": ["Public", "Private", "Protected"]}, "dataCtorParams": {"type": "array", "title": "The list of additional (non-inferred) Dependency Injection (DI) parameters for the generated 'Data' constructor.", "description": "Each constructor parameter should be formatted as 'Type' + '^' + 'Name'; e.g. 'IConfiguration^Config'. Where the 'Name' portion is not specified it will be inferred. Where the 'Type' matches an already inferred value it will be ignored.", "items": {"type": "string"}}, "dataExtensions": {"type": "boolean", "title": "Indicates whether the 'Data' extensions logic should be generated.", "description": "This can be overridden using 'Operation.DataExtensions'."}, "refDataIdDataName": {"type": "string", "title": "The Reference Data 'Id' data name.", "description": "Defaults to 'Name' + 'Id' (literal)."}, "refDataCodeDataName": {"type": "string", "title": "The Reference Data 'Code' data name.", "description": "Defaults to 'Code' (literal)."}, "refDataTextDataName": {"type": "string", "title": "The Reference Data 'Text' data name.", "description": "Defaults to 'Text' (literal)."}, "refDataIsActiveDataName": {"type": "string", "title": "The Reference Data 'IsActive' data name.", "description": "Defaults to 'IsActive' (literal)."}, "refDataSortOrderDataName": {"type": "string", "title": "The Reference Data 'SortOrder' data name.", "description": "Defaults to 'SortOrder' (literal)."}, "refDataETagDataName": {"type": "string", "title": "The Reference Data 'ETag' data name.", "description": "Defaults to 'RowVers<PERSON>' (literal)."}, "refDataStoredProcedureName": {"type": "string", "title": "The Reference Data database stored procedure name.", "description": "Defaults to 'sp' (literal) + 'Name' + 'GetAll' (literal)."}, "databaseName": {"type": "string", "title": "The .NET database interface name (used where 'AutoImplement' is 'Database').", "description": "Defaults to the 'CodeGeneration.DatabaseName' configuration property (its default value is 'IDatabase')."}, "databaseSchema": {"type": "string", "title": "The database schema name (used where 'AutoImplement' is 'Database').", "description": "Defaults to 'dbo'."}, "databaseMapperInheritsFrom": {"type": "string", "title": "The name of the 'Mapper' that the generated Database 'Mapper' inherits from."}, "databaseCustomerMapper": {"type": "boolean", "title": "Indicates that a custom Database 'Mapper' will be used; i.e. not generated.", "description": "Otherwise, by default, a 'Mapper' will be generated."}, "entityFrameworkName": {"type": "string", "title": "The .NET Entity Framework interface name used where 'AutoImplement' is 'EntityFramework'.", "description": "Defaults to 'CodeGeneration.EntityFrameworkName'."}, "entityFrameworkModel": {"type": "string", "title": "The corresponding Entity Framework model name (required where 'AutoImplement' is 'EntityFramework')."}, "entityFrameworkCustomMapper": {"type": "boolean", "title": "Indicates that a custom Entity Framework 'Mapper' will be used; i.e. not generated.", "description": "Otherwise, by default, a 'Mapper' will be generated."}, "entityFrameworkMapperBase": {"type": "string", "title": "The EntityFramework data-layer name that should be used for base mappings."}, "cosmosName": {"type": "string", "title": "The .NET Cosmos interface name used where 'AutoImplement' is 'Cosmos'.", "description": "Defaults to the 'CodeGeneration.CosmosName' configuration property (its default value is 'ICosmosDb')."}, "cosmosModel": {"type": "string", "title": "The corresponding Cosmos model name (required where 'AutoImplement' is 'Cosmos')."}, "cosmosContainerId": {"type": "string", "title": "The Cosmos 'ContainerId' required where 'AutoImplement' is 'Cosmos'."}, "cosmosPartitionKey": {"type": "string", "title": "The C# code to be used for setting the optional Cosmos 'PartitionKey' where 'AutoImplement' is 'Cosmos'.", "description": "The value 'PartitionKey.None' can be specified. Literals will need to be quoted."}, "cosmosValueContainer": {"type": "boolean", "title": "Indicates whether the 'CosmosDbValueContainer' is to be used; otherwise, 'CosmosDbContainer'."}, "cosmosCustomMapper": {"type": "boolean", "title": "Indicates that a custom Cosmos 'Mapper' will be used; i.e. not generated.", "description": "Otherwise, by default, a 'Mapper' will be generated."}, "cosmosMapperBase": {"type": "string", "title": "The Cosmos data-layer name that should be used for base mappings."}, "odataName": {"type": "string", "title": "The .NET OData interface name used where 'AutoImplement' is 'OData'.", "description": "Defaults to the 'CodeGeneration.ODataName' configuration property (its default value is 'IOData')."}, "odataModel": {"type": "string", "title": "The corresponding OData model name (required where 'AutoImplement' is 'OData')."}, "odataCollectionName": {"type": "string", "title": "The name of the underlying OData collection where 'AutoImplement' is 'OData'.", "description": "The underlying 'Simple.OData.Client' will attempt to infer."}, "odataCustomMapper": {"type": "boolean", "title": "Indicates that a custom OData 'Mapper' will be used; i.e. not generated.", "description": "Otherwise, by default, a 'Mapper' will be generated."}, "httpAgentName": {"type": "string", "title": "The .NET HTTP Agent interface name used where 'Operation.AutoImplement' is 'HttpAgent'.", "description": "Defaults to 'CodeGeneration.HttpAgentName' configuration property (its default value is 'IHttpAgent')."}, "httpAgentRoutePrefix": {"type": "string", "title": "The base HTTP Agent API route where 'Operation.AutoImplement' is 'HttpAgent'.", "description": "This is the base (prefix) 'URI' for the HTTP Agent endpoint and can be further extended when defining the underlying 'Operation'(s)."}, "httpAgentModel": {"type": "string", "title": "The corresponding HTTP Agent model name (required where 'AutoImplement' is 'HttpAgent').", "description": "This can be overridden within the 'Operation'(s)."}, "httpAgentReturnModel": {"type": "string", "title": "The corresponding HTTP Agent model name (required where 'AutoImplement' is 'HttpAgent').", "description": "This can be overridden within the 'Operation'(s)."}, "httpAgentCode": {"type": "string", "title": "The fluent-style method-chaining C# HTTP Agent API code to include where 'Operation.AutoImplement' is 'HttpAgent'.", "description": "Prepended to 'Operation.HttpAgentCode' where specified to enable standardized functionality."}, "httpAgentMapperBase": {"type": "string", "title": "The HTTP Agent data-layer name that should be used for base mappings."}, "dataSvcCaching": {"type": "boolean", "title": "Indicates whether request-based 'IRequestCache' caching is to be performed at the 'DataSvc' layer to improve performance (i.e. reduce chattiness).", "description": "Defaults to 'true'."}, "dataSvcCtor": {"type": "string", "title": "The access modifier for the generated 'DataSvc' constructor.", "description": "Defaults to 'Public'.", "enum": ["Public", "Private", "Protected"]}, "dataSvcCtorParams": {"type": "array", "title": "The list of additional (non-inferred) Dependency Injection (DI) parameters for the generated 'DataSvc' constructor.", "description": "Each constructor parameter should be formatted as 'Type' + '^' + 'Name'; e.g. 'IConfiguration^Config'. Where the 'Name' portion is not specified it will be inferred. Where the 'Type' matches an already inferred value it will be ignored.", "items": {"type": "string"}}, "dataSvcExtensions": {"type": "boolean", "title": "Indicates whether the 'DataSvc' extensions logic should be generated.", "description": "This can be overridden using 'Operation.DataSvcExtensions'."}, "eventPublish": {"type": "string", "title": "The layer to add logic to publish an event for a 'Create', 'Update' or 'Delete' operation.", "description": "Defaults to the 'CodeGeneration.EventPublish' configuration property (inherits) where not specified. Used to enable the sending of messages to the likes of EventGrid, Service Broker, SignalR, etc. This can be overridden within the 'Operation'(s).", "enum": ["None", "DataSvc", "Data"]}, "eventSource": {"type": "string", "title": "The Event Source.", "description": "Defaults to 'Name' (as lowercase) appended with the '/{$key}' placeholder. Note: when used in code-generation the 'CodeGeneration.EventSourceRoot' will be prepended where specified. To include the entity id/key include a '{$key}' placeholder ('Create', 'Update' or 'Delete' operation only); for example: 'person/{$key}'. This can be overridden for the 'Operation'."}, "eventTransaction": {"type": "boolean", "title": "Indicates whether a 'System.TransactionScope' should be created and orchestrated whereever generating event publishing logic.", "description": "Usage will force a rollback of any underlying data transaction (where the provider supports TransactionScope) on failure, such as an 'EventPublish' error. This is by no means implying a Distributed Transaction (DTC) should be invoked; this is only intended for a single data source that supports a TransactionScope to guarantee reliable event publishing. Defaults to 'CodeGeneration.EventTransaction'. This essentially defaults the 'Operation.DataSvcTransaction' where not otherwise specified. This should only be used where a transactionally-aware data source is being used."}, "managerCtor": {"type": "string", "title": "The access modifier for the generated 'Manager' constructor.", "description": "Defaults to 'Public'.", "enum": ["Public", "Private", "Protected"]}, "managerCtorParams": {"type": "array", "title": "The list of additional (non-inferred) Dependency Injection (DI) parameters for the generated 'Manager' constructor.", "description": "Each constructor parameter should be formatted as 'Type' + '^' + 'Name'; e.g. 'IConfiguration^Config'. Where the 'Name' portion is not specified it will be inferred. Where the 'Type' matches an already inferred value it will be ignored.", "items": {"type": "string"}}, "managerExtensions": {"type": "boolean", "title": "Indicates whether the 'Manager' extensions logic should be generated.", "description": "This can be overridden using 'Operation.ManagerExtensions'."}, "validator": {"type": "string", "title": "The name of the .NET implementing 'Type' or interface 'Type' that will perform the validation.", "description": "Only used for defaulting the 'Create' and 'Update' operation types ('Operation.Type') where not specified explicitly."}, "identifierGenerator": {"type": "boolean", "title": "Indicates whether the 'IIdentifierGenerator' should be used to generate the 'Id' property where the operation types ('Operation.Type') is 'Create'."}, "webApiRoutePrefix": {"type": "string", "title": "The 'RoutePrefixAtttribute' for the corresponding entity Web API controller.", "description": "This is the base (prefix) 'URI' for the entity and can be further extended when defining the underlying 'Operation'(s). The 'CodeGeneration.WebApiRoutePrefix' will be prepended where specified."}, "webApiAuthorize": {"type": "string", "title": "The authorize attribute value to be used for the corresponding entity Web API controller; generally either 'Authorize' or 'AllowAnonymous'.", "description": "Defaults to the 'CodeGeneration.WebApiAuthorize' configuration property (inherits) where not specified; can be overridden at the 'Operation' level also."}, "webApiCtor": {"type": "string", "title": "The access modifier for the generated Web API 'Controller' constructor.", "description": "Defaults to 'Public'.", "enum": ["Public", "Private", "Protected"]}, "webApiCtorParams": {"type": "array", "title": "The list of additional (non-inferred) Dependency Injection (DI) parameters for the generated 'WebApi' constructor.", "description": "Each constructor parameter should be formatted as 'Type' + '^' + 'Name'; e.g. 'IConfiguration^Config'. Where the 'Name' portion is not specified it will be inferred. Where the 'Type' matches an already inferred value it will be ignored.", "items": {"type": "string"}}, "webApiAutoLocation": {"type": "boolean", "title": "Indicates whether the HTTP Response Location Header route ('Operation.WebApiLocation') is automatically inferred.", "description": "This will automatically set the 'Operation.WebApiLocation' for an 'Operation' named 'Create' where there is a corresponding named 'Get'. This is defaulted from the 'CodeGen.WebApiAutoLocation'."}, "webApiConcurrency": {"type": "boolean", "title": "Indicates whether the Web API is responsible for managing (simulating) concurrency via auto-generated ETag.", "description": "This provides an alternative where the underlying data source does not natively support optimistic concurrency (native support should always be leveraged as a priority). Where the 'Operation.Type' is 'Update' or 'Patch', the request ETag will be matched against the response for a corresponding 'Get' operation to verify no changes have been made prior to updating. For this to function correctly the .NET response Type for the 'Get' must be the same as that returned from the corresponding 'Create', 'Update' and 'Patch' (where applicable) as the generated ETag is a SHA256 hash of the resulting JSON. This defaults the 'Operation.WebApiConcurrency'."}, "webApiGetOperation": {"type": "string", "title": "The corresponding 'Get' method name (in the 'XxxManager') where the 'Operation.Type' is 'Update' and 'SimulateConcurrency' is 'true'.", "description": "Defaults to 'Get'. Specify either just the method name (e.g. 'OperationName') or, interface and method name (e.g. 'IXxxManager.OperationName') to be invoked where in a different 'YyyManager.OperationName'."}, "dataModel": {"type": "boolean", "title": "Indicates whether a data 'model' version of the entity should also be generated (output to '.\\Business\\Data\\Model').", "description": "The model will be generated with 'OmitEntityBase = true'. Any reference data properties will be defined using their 'RefDataType' intrinsic 'Type' versus their corresponding (actual) reference data 'Type'."}, "excludeEntity": {"type": "boolean", "title": "Indicates whether to exclude the generation of the 'Entity' class ('Xxx.cs')."}, "excludeAll": {"type": "boolean", "title": "Indicates whether to exclude the generation of all 'Operation' related artefacts; excluding the 'Entity' class.", "description": "Is a shorthand means for setting all of the other 'Exclude*' properties (with the exception of 'ExcludeEntity') to exclude."}, "excludeIData": {"type": "boolean", "title": "Indicates whether to exclude the generation of the 'Data' interface ('IXxxData.cs')."}, "excludeData": {"type": "string", "title": "The option to exclude the generation of the 'Data' class ('XxxData.cs').", "description": "Defaults to 'Include' indicating _not_ to exlude. A value of 'Exclude' indicates to exclude all output; alternatively, 'RequiresMapper' indicates to at least output the corresponding 'Mapper' class.", "enum": ["Include", "Exclude", "RequiresMapper"]}, "excludeIDataSvc": {"type": "boolean", "title": "Indicates whether to exclude the generation of the 'DataSvc' interface ('IXxxDataSvc.cs')."}, "excludeDataSvc": {"type": "boolean", "title": "Indicates whether to exclude the generation of the 'DataSvc' class ('XxxDataSvc.cs')."}, "excludeIManager": {"type": "boolean", "title": "Indicates whether to exclude the generation of the 'Manager' interface ('IXxxManager.cs')."}, "excludeManager": {"type": "boolean", "title": "Indicates whether to exclude the generation of the 'Manager' class ('XxxManager.cs')."}, "excludeWebApi": {"type": "boolean", "title": "The option to exclude the generation of the WebAPI 'Controller' class ('XxxController.cs')."}, "excludeWebApiAgent": {"type": "boolean", "title": "Indicates whether to exclude the generation of the WebAPI consuming 'Agent' class ('XxxAgent.cs')."}, "excludeGrpcAgent": {"type": "boolean", "title": "Indicates whether to exclude the generation of the gRPC consuming 'Agent' class ('XxxAgent.cs')."}, "authRole": {"type": "string", "title": "The role (permission) used by the 'ExecutionContext.IsInRole(role)' for each 'Operation'.", "description": "Used where not overridden specifically for an 'Operation'; i.e. acts as the default."}, "grpc": {"type": "boolean", "title": "Indicates whether gRPC support (more specifically service-side) is required for the Entity.", "description": "gRPC support is an explicit opt-in model (see 'CodeGeneration.Grpc' configuration); therefore, each corresponding 'Property' and 'Operation' will also need to be opted-in specifically."}, "properties": {"type": "array", "title": "The corresponding 'Property' collection.", "items": {"$ref": "#/definitions/Property"}}, "operations": {"type": "array", "title": "The corresponding 'Operation' collection.", "items": {"$ref": "#/definitions/Operation"}}, "consts": {"type": "array", "title": "The corresponding 'Consts' collection.", "items": {"$ref": "#/definitions/Const"}}}, "required": ["name"]}, "Property": {"type": "object", "title": "'Property' object (entity-driven)", "description": "The 'Property' object defines an 'Entity' property and its charateristics.", "properties": {"name": {"type": "string", "title": "The unique property name."}, "text": {"type": "string", "title": "The overriding text for use in comments.", "description": "By default the 'Text' will be the 'Name' reformatted as sentence casing. Depending on whether the 'Type' is 'bool', will appear in one of the two generated sentences. Where not 'bool' it will be: Gets or sets a value indicating whether {text}.'. Otherwise, it will be: Gets or sets the {text}.'. To create a '<see cref=\"XXX\"/>' within use moustache shorthand (e.g. {{Xxx}})."}, "modelText": {"type": "string", "title": "The overriding model text for use in comments.", "description": "By default the 'ModelText' will be the 'Name' reformatted as sentence casing. Depending on whether the 'Type' is 'bool', will appear in one of the two generated sentences. Where not 'bool' it will be: Gets or sets a value indicating whether {text}.'. Otherwise, it will be: Gets or sets the {text}.'. To create a '<see cref=\"XXX\"/>' within use moustache shorthand (e.g. {{Xxx}})."}, "type": {"type": "string", "title": "The .NET 'Type'.", "description": "Defaults to 'string'. To reference a Reference Data 'Type' always prefix with 'RefDataNamespace' (e.g. 'RefDataNamespace.Gender') or shortcut '^' (e.g. '^Gender'). This will ensure that the appropriate Reference Data 'using' statement is used. _Shortcut:_ Where the 'Type' starts with (prefix) 'RefDataNamespace.' or '^', and the correspondong 'RefDataType' attribute is not specified it will automatically default the 'RefDataType' to 'string.'"}, "nullable": {"type": "boolean", "title": "Indicates whether the .NET 'Type' should be declared as nullable; e.g. 'string?'. Will be inferred where the 'Type' is denoted as nullable; i.e. suffixed by a '?'."}, "inherited": {"type": "boolean", "title": "Indicates whether the property is inherited and therefore should not be output within the generated Entity class."}, "privateName": {"type": "string", "title": "The overriding private name.", "description": "Overrides the 'Name' to be used for private fields. By default reformatted from 'Name'; e.g. 'FirstName' as '_firstName'."}, "argumentName": {"type": "string", "title": "The overriding argument name.", "description": "Overrides the 'Name' to be used for argument parameters. By default reformatted from 'Name'; e.g. 'FirstName' as 'firstName'."}, "primaryKey": {"type": "boolean", "title": "Indicates whether the property is considered part of the primary (unique) key.", "description": "This is also used to simplify the parameter specification for an Entity Operation by inferrence."}, "isEntity": {"type": "boolean", "title": "Indicates that the property 'Type' is another generated entity / collection and therefore specific capabilities can be assumed (e.g. 'CopyFrom' and 'Clone').", "description": "Will be inferred (default to 'true') where the 'Type' is 'ChangeLog' or the 'Type' is found as another 'Entity' within the code-generation configuration file."}, "immutable": {"type": "boolean", "title": "Indicates that the value is immutable and therefore cannot be changed once set."}, "dateTimeTransform": {"type": "string", "title": "The 'DateTime' transformation to be performed on 'Set' and 'CleanUp'.", "description": "Defaults to 'UseDefault'. This is only applied where the 'Type' is 'DateTime'.", "enum": ["UseDefault", "None", "DateOnly", "DateTimeLocal", "DateTimeUtc", "DateTimeUnspecified"]}, "stringTrim": {"type": "string", "title": "The 'string' trimming of white space characters to be performed on 'Set' and 'CleanUp'.", "description": "Defaults to 'UseDefault'. This is only applied where the 'Type' is 'string'.", "enum": ["UseDefault", "None", "Start", "End", "Both"]}, "stringTransform": {"type": "string", "title": "The 'string' transformation to be performed on 'Set' and 'CleanUp'.", "description": "Defaults to 'UseDefault'. This is only applied where the 'Type' is 'string'.", "enum": ["UseDefault", "None", "NullToEmpty", "EmptyToNull"]}, "autoCreate": {"type": "boolean", "title": "Indicates whether an instance of the 'Type' is to be automatically created/instantiated when the property is first accessed (i.e. lazy instantiation)."}, "default": {"type": "string", "title": "The C# code to default the value.", "description": "Where the 'Type' is 'string' then the specified default value will need to be delimited. Any valid value assignment C# code can be used."}, "partitionKey": {"type": "boolean", "title": "Indicates whether the property is considered part of the Partition Key.", "description": "This will implement 'IPartitionKey' for the generated entity."}, "internalOnly": {"type": "boolean", "title": "Indicates whether the property is for internal use only; declared in Business entities only.", "description": "In this instance the 'Property' will be excluded from the 'Common' entity declaration and 'Business' JSON serialization."}, "refDataType": {"type": "string", "title": "The underlying Reference Data Type that is also used as the Reference Data serialization identifier (SID).", "description": "Defaults to 'string' (being the 'ReferenceDataBase.Code') where not specified and the corresponding 'Type' starts with (prefix) 'RefDataNamespace.' or '^'. Note: an 'Id' of type 'string' is currently not supported; the use of the 'Code' is the recommended approach.", "enum": ["string", "int", "Guid"]}, "refDataList": {"type": "boolean", "title": "Indicates that the Reference Data property is to be a serializable list ('ReferenceDataSidList').", "description": "This is required to enable a list of Reference Data values (as per 'RefDataType') to be passed as an argument for example."}, "refDataText": {"type": "string", "title": "Indicates whether a corresponding 'Text' property is added when generating a Reference Data property, overriding the 'Entity.RefDataText' selection.", "description": "This is used where serializing within the Web API 'Controller' and the 'ExecutionContext.IsRefDataTextSerializationEnabled' is set to 'true' (which is automatically set where the url contains '$text=true').'Optional' indicates when 'ExecutionContext.IsRefDataTextSerializationEnabled' is set to 'true' then a value is output, 'Always' indicates that the value is _always_ output, and 'Never' indicates that feature is turned off.", "enum": ["Optional", "Always", "Never"]}, "refDataTextName": {"type": "string", "title": "The corresponding reference data 'Text' property name; defaults to 'Name' + 'Text'."}, "refDataMapping": {"type": "boolean", "title": "Indicates whether the property should use the underlying Reference Data mapping capabilities.", "description": "Mapped properties are a special Reference Data property type that ensure value uniqueness; this allows the likes of additional to/from mappings to occur between systems where applicable."}, "jsonName": {"type": "string", "title": "The JSON property name.", "description": "Defaults to 'ArgumentName' where not specified (i.e. camelCase); however, where the property is 'ETag' it will default to the 'Config.ETagJsonName'."}, "jsonDataModelName": {"type": "string", "title": "The JSON property name for the corresponding data model (see 'Entity.DataModel').", "description": "Defaults to 'JsonName' where not specified."}, "serializationIgnore": {"type": "boolean", "title": "Indicates whether the property is not to be serialized.", "description": "All properties are serialized by default."}, "serializationAlwaysInclude": {"type": "boolean", "title": "Indicates whether to include the value (default or otherwise) when serializing."}, "dataModelIgnore": {"type": "boolean", "title": "Indicates whether the property is to be included within the data model.", "description": "All properties are included in the data model by default."}, "dataModelSerializationIgnore": {"type": "boolean", "title": "Indicates whether the property is not to be serialized where outputting as a data model.", "description": "All properties are included in the data model by default."}, "dataName": {"type": "string", "title": "The data name where Entity.AutoImplement is selected.", "description": "Defaults to the property 'Name'. Represents the column name for a 'Database', or the correspinding property name for the other options."}, "dataConverter": {"type": "string", "title": "The data 'Converter' class name where 'Entity.AutoImplement' is selected.", "description": "A 'Converter' is used to convert a data source value to/from a .NET 'Type' where no standard data conversion can be applied. Where this value is suffixed by '<T>' or '{T}' this will automatically set 'Type'."}, "dataMapperIgnore": {"type": "boolean", "title": "Indicates whether the property should be ignored (excluded) from the 'Data'-layer / data 'Mapper' generated output.", "description": "All properties are included by default."}, "dataAutoGenerated": {"type": "boolean", "title": "Indicates whether the 'PrimaryKey' property value is automatically generated by the data source on 'Create'."}, "dataOperationTypes": {"type": "string", "title": "The operations types ('ExecutionContext.OperationType') selection to enable inclusion and exclusion of property mapping.", "description": "Defaults to 'Any'.", "enum": ["Any", "AnyExceptCreate", "AnyExceptUpdate", "AnyExceptGet", "Get", "Create", "Update", "Delete"]}, "databaseMapper": {"type": "string", "title": "The database property 'Mapper' class name where 'Entity.AutoImplement' is selected.", "description": "A 'Mapper' is used to map a data source value to/from a .NET complex 'Type' (i.e. class with one or more properties)."}, "databaseIgnore": {"type": "boolean", "title": "Indicates whether the property should be ignored (excluded) from the database 'Mapper' generated output."}, "databaseDbType": {"type": "string", "title": "The database 'DbType' override (versus inferring from the corresponding .NET Type).", "description": "Overrides the inferred database type; i.e. can specify 'Date' or 'DateTime2', for .NET Type 'System.DateTime'."}, "entityFrameworkMapper": {"type": "string", "title": "The Entity Framework 'Mapper' approach for the property.", "description": "Defaults to 'Set'.", "enum": ["Set", "Ignore", "Map", "<PERSON><PERSON>"]}, "cosmosMapper": {"type": "string", "title": "The Cosmos 'Mapper' approach for the property.", "description": "Defaults to 'Set'.", "enum": ["Set", "Ignore", "Map", "<PERSON><PERSON>"]}, "odataMapper": {"type": "string", "title": "The OData 'Mapper' approach for the property.", "description": "Defaults to 'Map' which indicates the property will be explicitly mapped. A value of 'Ignore' will explicitly 'Ignore', whilst a value of 'Skip' will skip code-generated mapping altogether.", "enum": ["Map", "Ignore", "<PERSON><PERSON>"]}, "httpAgentMapper": {"type": "string", "title": "The HttpAgent 'Mapper' approach for the property.", "description": "Defaults to 'Set'.", "enum": ["Set", "Ignore", "Map", "<PERSON><PERSON>"]}, "displayName": {"type": "string", "title": "The display name used in the likes of error messages for the property.", "description": "Defaults to the 'Name' as sentence case."}, "annotation1": {"type": "string", "title": "The property annotation (e.g. attribute) declaration code."}, "annotation2": {"type": "string", "title": "The property annotation (e.g. attribute) declaration code."}, "annotation3": {"type": "string", "title": "The property annotation (e.g. attribute) declaration code."}, "grpcFieldNo": {"type": "integer", "title": "The unique (immutable) field number required to enable gRPC support."}, "grpcType": {"type": "string", "title": "The underlying gRPC data type; will be inferred where not specified."}}, "required": ["name"]}, "Operation": {"type": "object", "title": "'CodeGeneration' object (entity-driven)", "description": "The code generation for an 'Operation' is primarily driven by the 'Type' property. This encourages (enforces) a consistent implementation for the standardised **CRUD** (Create, Read, Update and Delete) actions, as well as supporting fully customised operations as required.", "properties": {"name": {"type": "string", "title": "The unique operation name."}, "type": {"type": "string", "title": "The type of operation that is to be code-generated.", "enum": ["Get", "GetColl", "Create", "Update", "Patch", "Delete", "Custom"]}, "text": {"type": "string", "title": "The text for use in comments.", "description": "The 'Text' will be defaulted for all the 'Operation.Type' options with the exception of 'Custom'. To create a '<see cref=\"XXX\"/>' within use moustache shorthand (e.g. {{Xxx}})."}, "primaryKey": {"type": "boolean", "title": "Indicates whether the properties marked as a primary key ('Property.PrimaryKey') are to be used as the parameters.", "description": "This simplifies the specification of these properties versus having to declare each specifically."}, "paging": {"type": "boolean", "title": "Indicates whether a 'PagingArgs' argument is to be added to the operation to enable (standardized) paging related logic."}, "valueType": {"type": "string", "title": "The .NET value parameter 'Type' for the operation.", "description": "Defaults to the parent 'Entity.Name' where the 'Operation.Type' options are 'Create' or 'Update'."}, "returnType": {"type": "string", "title": "The .NET return 'Type' for the operation.", "description": "Defaults to the parent 'Entity.Name' where the 'Operation.Type' options are 'Get', 'GetColl', 'Create' or 'Update'; otherwise, defaults to 'void'."}, "returnTypeNullable": {"type": "boolean", "title": "Indicates whether the 'ReturnType' is nullable for the operation.", "description": "This is only applicable for an 'Operation.Type' of 'Custom'. Will be inferred where the 'ReturnType' is denoted as nullable; i.e. suffixed by a '?'."}, "returnText": {"type": "string", "title": "The text for use in comments to describe the 'ReturnType'.", "description": "A default will be created where not specified. To create a '<see cref=\"XXX\"/>' within use moustache shorthand (e.g. {{Xxx}})."}, "privateName": {"type": "string", "title": "The overriding private name.", "description": "Overrides the 'Name' to be used for private usage. By default reformatted from 'Name'; e.g. 'GetByArgs' as '_getByArgs'."}, "autoImplement": {"type": "string", "title": "The operation override for the 'Entity.AutoImplement'.", "description": "Defaults to 'Entity.AutoImplement'. The corresponding 'Entity.AutoImplement' must be defined for this to be enacted. Auto-implementation is applicable for all 'Operation.Type' options with the exception of 'Custom'.", "enum": ["Database", "EntityFramework", "Cosmos", "OData", "HttpAgent", "None"]}, "dataEntityMapper": {"type": "string", "title": "The override for the data entity 'Mapper'.", "description": "Used where the default generated 'Mapper' is not applicable."}, "dataExtensions": {"type": "boolean", "title": "Indicates whether the 'Data' extensions logic should be generated.", "description": "Defaults to 'Entity.DataExtensions'."}, "dataInvoker": {"type": "boolean", "title": "Indicates whether a 'DataInvoker' should orchestrate the 'Data'-layer.", "description": "Where 'Dataransaction' or 'EventPublish' is 'Data' then orchestration will default to 'true'."}, "dataTransaction": {"type": "boolean", "title": "Indicates whether a 'System.TransactionScope' should be created and orchestrated at the 'Data'-layer.", "description": "Where using an 'EventOutbox' this is ignored as it is implied through its usage."}, "databaseStoredProc": {"type": "string", "title": "The database stored procedure name used where 'Operation.AutoImplement' is 'Database'.", "description": "Defaults to 'sp' + 'Entity.Name' + 'Operation.Name'; e.g. 'spPersonCreate'."}, "entityFrameworkModel": {"type": "string", "title": "The corresponding Entity Framework model name (required where 'AutoImplement' is 'EntityFramework').", "description": "Overrides the 'Entity.EntityFrameworkModel'."}, "cosmosModel": {"type": "string", "title": "The corresponding Cosmos model name (required where 'AutoImplement' is 'Cosmos').", "description": "Overrides the 'Entity.CosmosModel'."}, "cosmosContainerId": {"type": "string", "title": "The Cosmos 'ContainerId' override used where 'Operation.AutoImplement' is 'Cosmos'.", "description": "Overrides the 'Entity.CosmosContainerId'."}, "cosmosPartitionKey": {"type": "string", "title": "The C# code override to be used for setting the optional Cosmos 'PartitionKey' used where 'Operation.AutoImplement' is 'Cosmos'.", "description": "Overrides the 'Entity.CosmosPartitionKey'."}, "odataCollectionName": {"type": "string", "title": "The override name of the underlying OData collection where 'Operation.AutoImplement' is 'OData'.", "description": "Overriddes the 'Entity.ODataCollectionName'; otherwise, the underlying 'Simple.OData.Client' will attempt to infer."}, "httpAgentRoute": {"type": "string", "title": "The HTTP Agent API route where 'Operation.AutoImplement' is 'HttpAgent'.", "description": "This is appended to the 'Entity.HttpAgentRoutePrefix'."}, "httpAgentMethod": {"type": "string", "title": "The HTTP Agent Method for the operation.", "description": "Defaults to 'Operation.WebApiMethod'.", "enum": ["HttpGet", "HttpPost", "HttpPut", "HttpDelete", "HttpPatch"]}, "httpAgentModel": {"type": "string", "title": "The corresponding HTTP Agent model name (required where 'AutoImplement' is 'HttpAgent').", "description": "This can be overridden within the 'Operation'(s)."}, "httpAgentReturnModel": {"type": "string", "title": "The corresponding HTTP Agent model name (required where 'AutoImplement' is 'HttpAgent').", "description": "Defaults to 'Operation.HttpAgentModel' where the 'Operation.ReturnType' is equal to 'Entity.Name' (same type). This can be overridden within the 'Operation'(s)."}, "httpAgentCode": {"type": "string", "title": "The fluent-style method-chaining C# HTTP Agent API code to include where 'Operation.AutoImplement' is 'HttpAgent'.", "description": "Appended to 'Entity.HttpAgentCode' where specified to extend."}, "managerCustom": {"type": "boolean", "title": "Indicates whether the 'Manager' logic is a custom implementation; i.e. no auto-'DataSvc' invocation logic is to be generated."}, "managerTransaction": {"type": "boolean", "title": "Indicates whether a 'System.TransactionScope' should be created and orchestrated at the 'Manager'-layer."}, "managerExtensions": {"type": "boolean", "title": "Indicates whether the 'Manager' extensions logic should be generated.", "description": "Defaults to 'Entity.ManagerExtensions'."}, "validator": {"type": "string", "title": "The name of the .NET implementing 'Type' or interface 'Type' that will perform the validation.", "description": "Defaults to the 'Entity.Validator' where not specified explicitly (where 'Operation.Type' options 'Create' or 'Update')."}, "managerOperationType": {"type": "string", "title": "The 'ExecutionContext.OperationType' (CRUD denotation) defined at the 'Manager'-layer.", "description": "The default will be inferred from the 'Operation.Type'; however, where the 'Operation.Type' is 'Custom' it will default to 'Unspecified'.", "enum": ["Create", "Read", "Update", "Delete", "Unspecified"]}, "dataSvcCustom": {"type": "boolean", "title": "Indicates whether the 'DataSvc' logic is a custom implementation; i.e. no auto-'DataSvc' invocation logic is to be generated."}, "dataSvcTransaction": {"type": "boolean", "title": "Indicates whether a 'System.TransactionScope' should be created and orchestrated at the 'DataSvc'-layer."}, "dataSvcInvoker": {"type": "boolean", "title": "Indicates whether a 'DataSvcInvoker' should orchestrate the 'DataSvc'-layer.", "description": "Where 'DataSvcTransaction' or 'EventPublish' is 'DataSvc' then orchestration will default to 'true'."}, "dataSvcExtensions": {"type": "boolean", "title": "Indicates whether the 'DataSvc' extensions logic should be generated.", "description": "Defaults to 'Entity.ManagerExtensions'."}, "eventPublish": {"type": "string", "title": "The layer to add logic to publish an event for a 'Create', 'Update' or 'Delete' operation.", "description": "Defaults to the 'Entity.EventPublish' configuration property (inherits) where not specified. Used to enable the sending of messages to the likes of EventGrid, Service Broker, SignalR, etc.", "enum": ["None", "DataSvc", "Data"]}, "eventSource": {"type": "string", "title": "The Event Source.", "description": "Defaults to 'Entity.EventSource'. Note: when used in code-generation the 'CodeGeneration.EventSourceRoot' will be prepended where specified. To include the entity id/key include a '{$key}' placeholder ('Create', 'Update' or 'Delete' operation only); for example: 'person/{$key}'."}, "eventSubject": {"type": "string", "title": "The event subject template and corresponding event action pair (separated by a colon).", "description": "The event subject template defaults to '{AppName}.{Entity.Name}', plus each of the unique key placeholders comma separated; e.g. 'Domain.Entity.{id1},{id2}' (depending on whether 'Entity.EventSubjectFormat' is 'NameAndKey' or 'NameOnly'). The event action defaults to 'WebApiOperationType' or 'Operation.Type' where not specified. Multiple events can be raised by specifying more than one subject/action pair separated by a semicolon. E.g. 'Demo.Person.{id}:Create;Demo.Other.{id}:Update'."}, "webApiRoute": {"type": "string", "title": "The Web API 'RouteAtttribute' to be appended to the 'Entity.WebApiRoutePrefix'."}, "webApiAuthorize": {"type": "string", "title": "The authorize attribute value to be used for the corresponding entity Web API controller; generally either 'Authorize' or 'AllowAnonymous'.", "description": "Where not specified no attribute output will occur; it will then inherit as supported by .NET."}, "webApiMethod": {"type": "string", "title": "The HTTP Method for the operation.", "description": "The value defaults as follows: 'HttpGet' for 'Operation.Type' value 'Get' or 'GetColl', 'HttpPost' for 'Operation.Type' value 'Create' or 'Custom', 'HttpPut' for 'Operation.Type' value 'Update', and 'HttpDelete' for 'Operation.Type' value 'Delete'. An 'Operation.Type' value 'Patch' can not be specified and will always default to 'HttpPatch'.", "enum": ["HttpGet", "HttpPost", "HttpPut", "HttpDelete"]}, "webApiStatus": {"type": "string", "title": "The primary HTTP Status Code that will be returned for the operation where there is a non-'null' return value.", "description": "The value defaults as follows: 'OK' for 'Operation.Type' value 'Get', 'GetColl', 'Update', 'Delete' or 'Custom', 'Created' for 'Operation.Type' value 'Create'.", "enum": ["OK", "Accepted", "Created", "NoContent", "NotFound"]}, "webApiAlternateStatus": {"type": "string", "title": "The primary HTTP Status Code that will be returned for the operation where there is a 'null' return value.", "description": "The value defaults as follows: 'NotFound' for 'Operation.Type' value 'Get' and 'NoContent' for 'Operation.Type' value 'GetColl'; otherwise, 'null'.", "enum": ["OK", "Accepted", "Created", "NoContent", "NotFound"]}, "webApiLocation": {"type": "string", "title": "The HTTP Response Location Header route.", "description": "This uses similar formatting to the 'WebApiRoute'. The response value is accessed using 'r.' notation to access underlying properties; for example '{r.Id}' or 'person/{r.Id}'. The 'Entity.WebApiRoutePrefix' will be prepended automatically; however, to disable set the first character to '!', e.g. '!person/{r.Id}'. The URI can be inferred from another 'Operation' by using a lookup '^'; for example '^Get' indicates to infer from the named 'Get' operation (where only '^' is specified this is shorthand for '^Get' as this is the most common value). The Location URI will ensure the first character is a '/' so it acts a 'relative URL absolute path'."}, "webApiConcurrency": {"type": "boolean", "title": "Indicates whether the Web API is responsible for managing (simulating) concurrency via auto-generated ETag.", "description": "This provides an alternative where the underlying data source does not natively support optimistic concurrency (native support should always be leveraged as a priority). Where the 'Operation.Type' is 'Update' or 'Patch', the request ETag will be matched against the response for a corresponding 'Get' operation to verify no changes have been made prior to updating. For this to function correctly the .NET response Type for the 'Get' must be the same as that returned from the corresponding 'Create', 'Update' and 'Patch' (where applicable) as the generated ETag is a SHA256 hash of the resulting JSON. Defaults to 'Entity.WebApiConcurrency'."}, "webApiGetOperation": {"type": "string", "title": "The corresponding 'Get' method name (in the 'XxxManager') where the 'Operation.Type' is 'Update' and 'SimulateConcurrency' is 'true'.", "description": "Defaults to 'Get'. Specify either just the method name (e.g. 'OperationName') or, interface and method name (e.g. 'IXxxManager.OperationName') to be invoked where in a different 'YyyManager.OperationName'."}, "webApiUpdateOperation": {"type": "string", "title": "The corresponding 'Update' method name (in the 'XxxManager') where the 'Operation.Type' is 'Patch'.", "description": "Defaults to 'Update'. Specify either just the method name (e.g. 'OperationName') or, interface and method name (e.g. 'IXxxManager.OperationName') to be invoked where in a different 'YyyManager.OperationName'."}, "authPermission": {"type": "string", "title": "The permission used by the 'ExecutionContext.IsAuthorized(AuthPermission)' to determine whether the user is authorized."}, "authRole": {"type": "string", "title": "The permission used by the 'ExecutionContext.IsInRole(AuthRole)' to determine whether the user is authorized."}, "excludeAll": {"type": "boolean", "title": "Indicates whether to exclude the generation of all 'Operation' related output.", "description": "Is a shorthand means for setting all of the other 'Exclude*' properties to 'true'."}, "excludeIData": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the 'Data' interface ('IXxxData.cs') output."}, "excludeData": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the 'Data' class ('XxxData.cs') output."}, "excludeIDataSvc": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the 'DataSvc' interface ('IXxxDataSvc.cs') output."}, "excludeDataSvc": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the 'DataSvc' class ('XxxDataSvc.cs') output."}, "excludeIManager": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the 'Manager' interface ('IXxxManager.cs') output."}, "excludeManager": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the 'Manager' class ('XxxManager.cs') output."}, "excludeWebApi": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the WebAPI 'Controller' class ('XxxController.cs') output."}, "excludeWebApiAgent": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the WebAPI consuming 'Agent' class ('XxxAgent.cs') output."}, "excludeGrpcAgent": {"type": "boolean", "title": "Indicates whether to exclude the generation of the operation within the gRPC consuming 'Agent' class ('XxxAgent.cs') output."}, "grpc": {"type": "boolean", "title": "Indicates whether gRPC support (more specifically service-side) is required for the Operation.", "description": "gRPC support is an explicit opt-in model (see 'CodeGeneration.Grpc' configuration); therefore, each corresponding 'Entity', 'Property' and 'Operation' will also need to be opted-in specifically."}, "parameters": {"type": "array", "title": "The corresponding 'Parameter' collection.", "items": {"$ref": "#/definitions/Parameter"}}}, "required": ["name"]}, "Parameter": {"type": "object", "title": "'Parameter' object (entity-driven)", "description": "The 'Parameter' object defines an 'Operation' parameter and its charateristics.", "properties": {"name": {"type": "string", "title": "The unique parameter name."}, "text": {"type": "string", "title": "The overriding text for use in comments.", "description": "By default the 'Text' will be the 'Name' reformatted as sentence casing."}, "type": {"type": "string", "title": "The .NET 'Type'.", "description": "Defaults to 'string'. To reference a Reference Data 'Type' always prefix with 'RefDataNamespace' (e.g. 'RefDataNamespace.Gender') or shortcut '^' (e.g. '^Gender'). This will ensure that the appropriate Reference Data 'using' statement is used. _Shortcut:_ Where the 'Type' starts with (prefix) 'RefDataNamespace.' or '^', and the correspondong 'RefDataType' attribute is not specified it will automatically default the 'RefDataType' to 'string.'"}, "nullable": {"type": "boolean", "title": "Indicates whether the .NET 'Type should be declared as nullable; e.g. 'int?'. Will be inferred where the 'Type' is denoted as nullable; i.e. suffixed by a '?'."}, "default": {"type": "string", "title": "The C# code to default the value.", "description": "Where the 'Type' is 'string' then the specified default value will need to be delimited. Any valid value assignment C# code can be used."}, "privateName": {"type": "string", "title": "The overriding private name.", "description": "Overrides the 'Name' to be used for private fields. By default reformatted from 'Name'; e.g. 'FirstName' as '_firstName'."}, "argumentName": {"type": "string", "title": "The overriding argument name.", "description": "Overrides the 'Name' to be used for argument parameters. By default reformatted from 'Name'; e.g. 'FirstName' as 'firstName'."}, "property": {"type": "string", "title": "The 'Property.Name' within the parent 'Entity' to copy (set) the configuration/characteristics from where not already defined."}, "refDataType": {"type": "string", "title": "The underlying Reference Data Type that is also used as the Reference Data serialization identifier (SID).", "description": "Defaults to 'string' where not specified and the corresponding 'Type' starts with (prefix) 'RefDataNamespace.'.", "enum": ["string", "int", "Guid"]}, "refDataList": {"type": "boolean", "title": "Indicates that the Reference Data property is to be a serializable list ('ReferenceDataSidList').", "description": "This is required to enable a list of Reference Data values (as per 'RefDataType') to be passed as an argument for example."}, "validator": {"type": "string", "title": "The name of the .NET 'Type' that will perform the validation."}, "iValidator": {"type": "string", "title": "The name of the .NET Interface that the 'Validator' implements/inherits.", "description": "Defaults to 'IValidatorEx<{Type}>' where the '{Type}' is 'Type'."}, "validatorCode": {"type": "string", "title": "The fluent-style method-chaining C# validator code to append to 'IsMandatory' and 'Validator' (where specified)."}, "isMandatory": {"type": "boolean", "title": "Indicates whether a 'ValidationException' should be thrown when the parameter value has its default value (null, zero, etc)."}, "layerPassing": {"type": "string", "title": "The option that determines the layers in which the parameter is passed.", "description": "Defaults to 'All'. To further describe, 'All' passes the parameter through all layeys, 'ToManagerSet' only passes the parameter to the 'Manager' layer and overrides the same named property within the corresponding 'value' parameter, 'ToManagerCollSet' only passes the parameter to the 'Manager' layer and overrides the same named property within the corresponding 'value' collection parameter. Where using the 'PrimaryKey' option to automatically set 'Parameters', and the 'Operation.Type' is 'Create' or 'Update' it will default to 'ToManagerSet'.", "enum": ["All", "ToManagerSet", "ToManagerCollSet"]}, "dataConverter": {"type": "string", "title": "The data 'Converter' class name where specific data conversion is required.", "description": "A 'Converter' is used to convert a data source value to/from a .NET 'Type' where no standard data conversion can be applied. Where this value is suffixed by '<T>' or '{T}' this will automatically set 'Type'."}, "webApiFrom": {"type": "string", "title": "The option for how the parameter will be delcared within the Web API Controller.", "description": "Defaults to 'FromQuery'; unless the parameter 'Type' has also been defined as an 'Entity' within the code-gen config file then it will default to 'FromEntityProperties'. Specifies that the parameter will be declared with corresponding 'FromQueryAttribute', 'FromBodyAttribute' or 'FromRouteAttribute' for the Web API method. The 'FromEntityProperties' will declare all properties of the 'Entity' as query parameters.", "enum": ["FromQuery", "FromBody", "FromRoute", "FromEntityProperties"]}, "grpcType": {"type": "string", "title": "The underlying gRPC data type; will be inferred where not specified."}}, "required": ["name"]}, "Const": {"type": "object", "title": "'Const' object (entity-driven)", "description": "The 'Const' object is used to define a .NET (C#) constant value for an 'Entity'.", "properties": {"name": {"type": "string", "title": "The unique constant name."}, "value": {"type": "string", "title": "The .NET (C#) code for the constant value.", "description": "The code generation will ensure the value is delimited properly to output correctly formed (delimited) .NET (C#) code."}, "text": {"type": "string", "title": "The overriding text for use in comments.", "description": "By default the 'Text' will be the 'Name' reformatted as sentence casing. It will be formatted as: 'Represents a {text} constant value.' To create a '<see cref=\"XXX\"/>' within use moustache shorthand (e.g. '{{Xxx}}')."}}, "required": ["name", "value"]}}, "allOf": [{"$ref": "#/definitions/CodeGeneration"}]}