namespace Avanade.Geranium.Attane.CodeGen;

/// <summary>
/// Represents the <b>code generation</b> program (capability).
/// </summary>
public static class Program
{
    /// <summary>
    /// Main startup.
    /// </summary>
    /// <param name="args">The startup arguments.</param>
    /// <returns>The status code whereby zero indicates success.</returns>
    public static Task<int> Main(string[] args)
    {
        HandlebarsFunctions.Register();
        return Beef.CodeGen.CodeGenConsole.Create("Avanade", "Geranium.Attane").Supports(entity: true, refData: false).RunAsync(args);
    }
}