/* eslint-disable quotes */
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import { viteCommonjs } from '@originjs/vite-plugin-commonjs';

// ignore the rule because this file is not a product code
/* eslint-disable @typescript-eslint/no-var-requires */
import fs from 'fs';
import path from 'path';

export default defineConfig({
  plugins: [
    react(),
    viteCommonjs(),
  ],
  resolve: {
    alias: [{
      find: /^react-custom-scrollbars-2/,
      replacement: path.resolve(
        __dirname,
        'external/react-custom-scrollbars-2-master/src/index.js',
      ),
    }],
  },
  build: {
    sourcemap: true,
    outDir: 'build',
  },
  server: {
    port: 3000,
    https: {
      key: fs.readFileSync('cert/localhost.key'),
      cert: fs.readFileSync('cert/localhost.pem'),
    },
    open: '/001/',
    proxy: {
      '/001/variables.js': {
        target: 'https://localhost:3000/variables.js',
        secure: false,
        ignorePath: true,
      },
      '/001/favicon.ico': {
        target: 'https://localhost:3000/favicon.ico',
        secure: false,
        ignorePath: true,
      },
      '/001/assets/atTane_logo.png': {
        target: 'https://localhost:3000/assets/atTane_logo.png',
        secure: false,
        ignorePath: true,
      },
      '/001/': {
        target: 'https://localhost:3000',
        secure: false,
        ignorePath: true,
      },
    },
  },
  envPrefix: "REACT_",
  // https://github.com/vitejs/vite/discussions/5079
  css: { preprocessorOptions: { scss: { charset: false } } },
  define: {
    'process.env': {},
  },
  optimizeDeps: {
    include: ['react-custom-scrollbars-2'],
  },
});
