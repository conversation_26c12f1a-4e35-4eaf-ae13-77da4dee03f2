import path from 'path';
import type { StorybookConfig } from "@storybook/react-vite";

const config: StorybookConfig = {
  stories: ["../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  // stories: ["../src/components/domains/split-view/**/*.stories.tsx"],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-interactions",
    "storybook-dark-mode"
  ],
  framework: {
    name: "@storybook/react-vite",
    options: {},
  },
  docs: {
    autodocs: "tag",
  },
  viteFinal: (config) => {
    return {
      ...config,
      // https://github.com/eirslett/storybook-builder-vite/issues/238
      base: '',
      resolve: {
        alias: [{
          find: /^react-custom-scrollbars-2/,
          replacement: path.resolve(__dirname, '../', 'external/react-custom-scrollbars-2-master/src/index.js')
        }]
      },
      css: {
        preprocessorOptions: {
          scss: {
            additionalData: `@import "../src/styles/variables"; @import "../src/styles/mixin";`,
          },
        },
      },
      // https://github.com/eirslett/storybook-builder-vite/issues/68
      publicDir: '.storybook/public',
      define: {
        'process.env': process.env
      },
      optimizeDeps: {
        include: ['react-custom-scrollbars-2']
      }
    };
  },
};
export default config;
