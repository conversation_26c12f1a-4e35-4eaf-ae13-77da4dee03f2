// Storybook用のユーティリティ

import { StoryFn } from '@storybook/react';

/**
 * StoryのargTypesへstoryWidthとstoryHeightを設定する
 * storyWidthとstoryHeightはスライダーで操作できるようになる
 * @param defWidth デフォルトの横幅(px)
 * @param defHeight デフォルトの高さ(px)
 * @param [min] 最小値。デフォルト値は100
 * @param [max] 最大値。デフォルト値は2000
 */
export const setStoryMockCommonArgTypes = (
  defWidth: number, defHeight: number, min = 100, max = 2000,
) => ({
  storyWidth: {
    control: { type: 'range', min, max },
    defaultValue: defWidth,
  },
  storyHeight: {
    control: { type: 'range', min, max },
    defaultValue: defHeight,
  },
});

/**
 * Storybookで使用。任意のPropsにstoryWidthとstoryHeightの型定義を追加する
 */
export type StoryMockProps<T = unknown> = T & {
  storyWidth: string | number;
  storyHeight: string | number;
}

/**
 * Storybookで使用。任意のDOM要素にstoryWidthとstoryHeightをstyleとして追加する
 * @param args StoryMockProps型のProps
 */
export const setStoryWidthHeight = (args: StoryMockProps<unknown>) => ({
  style: {
    width: args.storyWidth,
    height: args.storyHeight,
  },
});

/**
 * Storybookで使用。argTypesにview, messageのオプションを指定する
 * @param viewOptions
 * @param messageOptions
 */
export const setCommonArgTypes = <T extends {}, U extends {}>(viewOptions: T, messageOptions?: U) => ({
  view: {
    options: Object.values(viewOptions),
    control: { type: 'select' },
  },
  ...messageOptions
    ? {
      message: {
        options: Object.values(messageOptions),
        control: { type: 'select' },
      }
    } : undefined,
});

export const setSelectOptions = <T extends {}>(options: T) => {
  return {
    options: Object.values(options),
    control: { type: 'select' },
  }
};

/**
 * 引数を元にしたモバイルプレビュー版のStoryを生成する
 * @param template
 * @param baseStory
 */
export const cloneForMobileView = <T>(template: StoryFn<T>, baseStory: StoryFn<T>): StoryFn<T> => {
  const story = template.bind({}) as StoryFn<T>;
  story.parameters = {
    ...baseStory.parameters,
    viewport: { defaultViewport: 'iphone' },
    layout: 'fullscreen',
  };
  story.args = {
    ...baseStory.args,
    storyWidth: 375,
    storyHeight: 667,
  } as Partial<T>;
  return story;
}
