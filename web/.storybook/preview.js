import React from 'react'
import { Provider, teamsTheme, teamsDarkTheme } from '@fluentui/react-northstar';
import { MINIMAL_VIEWPORTS } from '@storybook/addon-viewport';
import { themes } from '@storybook/theming';
import { useDarkMode } from 'storybook-dark-mode';
import '../src/index.scss';
import '../public/variables';

const preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    viewport: {
      viewports: {
        ...MINIMAL_VIEWPORTS,
        iphone: {
          name: 'iPhone',
          styles: {
            width: '375px',
            height: '667px',
          },
        },
      },
    },
  },
  // https://storybook.js.org/addons/storybook-dark-mode
  darkMode: {
    dark: { ...themes.dark, appBg: 'black' },
    light: { ...themes.normal, appBg: 'white' },
    darkClass: 'theme-dark',
    lightClass: 'theme-light',
    current: 'light',
    stylePreview: true,
  },
};

// wrap with fluent-ui theme provider
export const decorators = [
  (Story) => {
    return React.createElement(Provider, {
      theme: !useDarkMode() ? teamsTheme : teamsDarkTheme,
      children: React.createElement(Story),
    })
  },
];

export default preview;
