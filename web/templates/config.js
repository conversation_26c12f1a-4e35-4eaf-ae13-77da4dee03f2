/**
 * yeoman/generator-geranium-webで使う設定ファイル
 * @param props
 */
const config = (props = { nameLowerCamel: '', nameUpperCamel: '', nameHyphenCase: '' }) => ({
  PRESENTATIONAL_COMPONENT: {
    description: 'a presentational react component with test and Storybook',
    files: [
      {
        tmpl: 'presentational.tsx.ejs',
        dest: `${props.nameHyphenCase}/${props.nameUpperCamel}.tsx`,
      },
      {
        tmpl: 'presentational.scss.ejs',
        dest: `${props.nameHyphenCase}/${props.nameUpperCamel}.scss`,
      },
      {
        tmpl: 'presentational.test.tsx.ejs',
        dest: `${props.nameHyphenCase}/${props.nameUpperCamel}.test.tsx`,
      },
      {
        tmpl: 'presentational.stories.tsx.ejs',
        dest: `${props.nameHyphenCase}/${props.nameUpperCamel}.stories.tsx`,
      },
    ],
  },
  CUSTOM_HOOK_WITH_RENDER_HOOK_TESTING: {
    description: 'a react custom hook with renderHook testing',
    files: [
      {
        tmpl: 'hook.ts.ejs',
        dest: `use${props.nameUpperCamel}.ts`,
      },
      {
        tmpl: 'hook.test.ts.ejs',
        dest: `use${props.nameUpperCamel}.test.ts`,
      },
    ],
  },
  CUSTOM_HOOK_WITH_COMPONENT_TESTING: {
    description: 'a react custom hook with component testing',
    files: [
      {
        tmpl: 'hook.ts.ejs',
        dest: `use${props.nameUpperCamel}.ts`,
      },
      {
        tmpl: 'hook.test.tsx.ejs',
        dest: `use${props.nameUpperCamel}.test.tsx`,
      },
    ],
  },
  UTILITY: {
    description: 'a utility module',
    files: [
      {
        tmpl: 'utility.ts.ejs',
        dest: `${props?.nameLowerCamel ?? ''}.ts`,
      },
      {
        tmpl: 'utility.test.ts.ejs',
        dest: `${props?.nameLowerCamel ?? ''}.test.ts`,
      },
    ],
  },
});

module.exports = config;
