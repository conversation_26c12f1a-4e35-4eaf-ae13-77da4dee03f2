import * as React from 'react';
import { Story, Meta } from '@storybook/react';
import {
  cloneForMobileView,
  setStoryMockCommonArgTypes,
  setStoryWidthHeight,
  StoryMockProps,
} from '<%= relativePath %>/../../.storybook/utils';
import <%= nameUpperCamel %>, { I<%= nameUpperCamel %>Props } from './<%= nameUpperCamel %>';

export default {
  title: '<%= nameUpperCamel %>.tsx',
  component: <%= nameUpperCamel %>,
  argTypes: {
    ...setStoryMockCommonArgTypes(1000, 500),
  },
} as Meta;

const Template: Story<StoryMockProps<I<%= nameUpperCamel %>Props>> = (args) => (
  <div {...setStoryWidthHeight(args)}>
    <div style={{ overflow: 'hidden', height: '100%' }}>
      <<%= nameUpperCamel %> {...args} />
    </div>
  </div>
);

export const Default = Template.bind({});
Default.args = {};
export const DefaultOnMobile = cloneForMobileView(Template, Default);
