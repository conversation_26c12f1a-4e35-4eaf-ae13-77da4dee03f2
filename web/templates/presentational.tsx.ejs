import * as React from 'react';
import PropTypes from 'prop-types';
import { mergedClassName } from '<%= relativePath %>/../utilities/commonFunction';
import './<%= nameUpperCamel %>.scss';

export interface I<%= nameUpperCamel %>Props {
  className?: string;
  children?: React.ReactNode | null;
}

/**
 * <%= nameUpperCamel %>
 * @param props
 */
const <%= nameUpperCamel %>: React.FC<I<%= nameUpperCamel %>Props> = (props) => {
  const {
    className,
    children,
  } = props;

  // create merged CSS className
  const rootClassName = React.useMemo(() => mergedClassName('<%= nameHyphenCase %>', className), [className]);

  return (
    <div className={rootClassName}>
      {children}
    </div>
  );

};

<%= nameUpperCamel %>.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
};

<%= nameUpperCamel %>.defaultProps = {
  className: undefined,
  children: undefined,
};

export default <%= nameUpperCamel %>;
