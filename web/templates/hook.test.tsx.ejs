import * as React from 'react';
import '@testing-library/jest-dom';
import { findByText, render } from '@testing-library/react';
import { Simulate } from 'react-dom/test-utils';
import { queryElem } from '<%= relativePath %>/utilities/test';
import use<%= nameUpperCamel %> from './use<%= nameUpperCamel %>';

jest.mock('<%= relativePath %>/utilities/environment');

describe.skip('use<%= nameUpperCamel %>', () => {
  interface TestProps {
    defaultFlag: boolean;
  }

  // ignore the rule because this is for testing
  /* eslint-disable react/prop-types */
  const TestComponent: React.FC<TestProps> = (props) => {
    const { defaultFlag } = props;
    const [flag, onClick] = use<%= nameUpperCamel %>(defaultFlag);
    return (
      <div>
        <button className="button" onClick={onClick} type="button">click</button>
        <div>{ flag ? 'yes' : 'no' }</div>
      </div>
    );
  };

  function renderComponent(props: TestProps) {
    return render(<TestComponent {...props} />);
  }

  describe('when defaultFlag = true', () => {
    const defaultFlag = true;

    it('should show "yes"', () => {
      const { container } = renderComponent({ defaultFlag });
      expect(container).toHaveTextContent('yes');
    });
  });

  describe('when defaultFlag = false', () => {
    const defaultFlag = false;

    it('should show "no"', () => {
      const { container } = renderComponent({ defaultFlag });
      expect(container).toHaveTextContent('no');
    });

    describe('when a user clicks the button', () => {
      it('should change to show "yes"', async () => {
        const { container } = renderComponent({ defaultFlag });
        Simulate.click(queryElem(container, 'button'));
        await expect(findByText(container, 'yes')).resolves.toBeInTheDocument();
      });
    });
  });

});
