import '@testing-library/jest-dom';
import { renderHook } from '@testing-library/react-hooks';
import use<%= nameUpperCamel %> from './use<%= nameUpperCamel %>';

jest.mock('<%= relativePath %>/utilities/environment');

describe.skip('use<%= nameUpperCamel %>', () => {

  type RenderHookProps = {
    defaultFlag: boolean,
  };

  function getHook(props: RenderHookProps) {
    return renderHook((p) => use<%= nameUpperCamel %>(p.defaultFlag), {
      initialProps: props,
    });
  }

  describe('when defaultFlag = true', () => {
    const defaultFlag = true;

    it('should return true', () => {
      const { result } = getHook({ defaultFlag });
      const [flag] = result.current;
      expect(flag).toBe(true);
    });

    describe('when the defaultFlag inverts', () => {
      it('should not change the value', () => {
        const { result, rerender } = getHook({ defaultFlag });
        rerender({ defaultFlag: false });
        const [flag] = result.current;
        expect(flag).toBe(true);
      });
    });
  });

  describe('when defaultFlag = false', () => {
    const defaultFlag = false;

    it('should return false', () => {
      const { result } = getHook({ defaultFlag });
      const [flag] = result.current;
      expect(flag).toBe(false);
    });
  });
});
