import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import <%= nameUpperCamel %> from './<%= nameUpperCamel %>';

describe('<%= nameUpperCamel %>', () => {

  describe('className', () => {
    const baseNameClass = '<%= nameHyphenCase %>';

    describe('when className = undefined', () => {
      const className = undefined;

      it('should have only "<%= nameHyphenCase %>"', () => {
        const { container } = render(<<%= nameUpperCamel %> {...{ className }} />);
        expect(container.children[0]).toHaveClass(baseNameClass);
      });
    });

    describe('when className = "abc"', () => {
      const className = 'abc';

      it('should have "<%= nameHyphenCase %>" and "abc"', () => {
        const { container } = render(<<%= nameUpperCamel %> {...{ className }} />);
        expect(container.children[0]).toHaveClass(baseNameClass);
        expect(container.children[0]).toHaveClass('abc');
      });
    });
  });
});
