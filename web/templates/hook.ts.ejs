import * as React from 'react';

type OnCallback = () => void;

type Use<%= nameUpperCamel %>ReturnType = [
  flag: boolean,
  onCallback: OnCallback,
];

/**
 * PLEASE SET SOME COMMENTS HERE
 * @param text
 */
function onCallbackImpl(text: string): string {
  return text;
}

/**
 * PLEASE SET SOME COMMENTS HERE
 * @param defaultFlag
 */
const use<%= nameUpperCamel %> = (defaultFlag: boolean): Use<%= nameUpperCamel %>ReturnType => {
  const [flag, setFlag] = React.useState(defaultFlag);

  const onCallback: OnCallback = React.useCallback(() => {
    onCallbackImpl('abc');
    setFlag(true);
  }, []);

  return [flag, onCallback];
};

export default use<%= nameUpperCamel %>;
