<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>

    <staticContent>
      <remove fileExtension=".css"/>
      <mimeMap fileExtension=".css" mimeType="text/css; charset=utf-8"/>
      <remove fileExtension=".html" />
      <mimeMap fileExtension=".html" mimeType="text/html; charset=utf-8" />
      <remove fileExtension=".js"/>
      <mimeMap fileExtension=".js" mimeType="text/javascript; charset=utf-8"/>
      <remove fileExtension=".ico"/>
      <mimeMap fileExtension=".ico" mimeType="image/x-icon; charset=utf-8"/>
    </staticContent>

    <httpProtocol>
      <customHeaders>
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Content-Security-Policy" value="frame-ancestors teams.microsoft.com" />
      </customHeaders>
    </httpProtocol>

    <rewrite>
      <rules>
        <rule name="Rewrite">
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(storybook)" negate="true" />
          </conditions>
          <action type="Rewrite" url="index.html" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>

  <!-- index.html のキャッシュを無効化 -->
  <location path="index.html">
    <system.webServer>
      <httpProtocol>
        <customHeaders>
          <add name="Cache-Control" value="no-store, max-age=0" />
        </customHeaders>
      </httpProtocol>
    </system.webServer>
  </location>

</configuration>
