// babel-jestでしか使われない
// ts-jestがESM非対応のためESMで提供されるパッケージをcommonjsに変換する
// 変換するモジュールはpackage.jsonのtransformIgnorePatternsに追記すること

// ファイル名を.babelrcにすると外部パッケージへの効力が無くなるためNG
// https://github.com/facebook/jest/issues/6053#issuecomment-383632515
// https://qiita.com/wintyo/items/63ab29ac7a16122fdf7b

module.exports = {
  presets: [
    [
      '@babel/preset-env',
      {
        targets: {
          node: 'current',
        },
      },
    ],
  ],
  plugins: [
    '@babel/plugin-transform-modules-commonjs',
  ],
};
