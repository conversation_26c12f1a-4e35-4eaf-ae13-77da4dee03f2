steps:
  # node.jsのバージョンを指定
  - task: NodeTool@0
    displayName: "node v20.18.1"
    inputs:
      versionSpec: "20.18.1"

  # node_modulesのキャッシュ
  # https://docs.microsoft.com/ja-jp/azure/devops/pipelines/release/caching?view=azure-devops#nodejsnpm
  # https://stackoverflow.com/questions/59221117/azure-pipelines-cache2-fails-with-errorthe-system-cannot-find-the-file-s
  - task: Cache@2
    displayName: "node_modulesのキャッシュ"
    inputs:
      key: 'npm | "$(Agent.OS)" | $(System.DefaultWorkingDirectory)/web/package-lock.json'
      restoreKeys: |
        npm | "$(Agent.OS)"
      path: $(NPM_CACHE_DIR)
      cacheHitVar: CACHE_RESTORED

  # 共有アーティファクトのリポジトリへの認証
  - task: npmAuthenticate@0
    inputs:
      workingFile: web/.npmrc

  # npmインストール
  - task: Npm@1
    displayName: "npm ci"
    inputs:
      command: custom
      workingDir: "web"
      verbose: false
      customCommand: "ci --cache $(NPM_CACHE_DIR)"
