pool:
  name: Azure Pipelines
  demands: npm
  vmImage: ubuntu-22.04

trigger: none

resources:
  pipelines:
    - pipeline: geranium-web-ci
      source: 'Geranium Web CI'
      trigger: true

variables:
  NPM_CACHE_DIR: $(Pipeline.Workspace)/.npm

steps:
  # node_modulesのキャッシュ
  # https://docs.microsoft.com/ja-jp/azure/devops/pipelines/release/caching?view=azure-devops#nodejsnpm
  # https://stackoverflow.com/questions/59221117/azure-pipelines-cache2-fails-with-errorthe-system-cannot-find-the-file-s
  - task: Cache@2
    displayName: 'node_modulesのキャッシュ'
    inputs:
      key: 'npm | "$(Agent.OS)" | $(System.DefaultWorkingDirectory)/web/storybook-vrt/package-lock.json'
      restoreKeys: |
        npm | "$(Agent.OS)"
      path: $(NPM_CACHE_DIR)
      cacheHitVar: CACHE_RESTORED

  # npmインストール
  - task: Npm@1
    displayName: 'npm ci'
    inputs:
      command: custom
      workingDir: 'web/storybook-vrt'
      verbose: false
      customCommand: 'ci --cache $(NPM_CACHE_DIR)'

  # web/storybookのアーティファクトを取得
  - task: DownloadPipelineArtifact@2
    displayName: 'web/storybookのアーティファクトを取得'
    inputs:
      buildType: 'specific'
      project: '6f0ebfa7-ab4e-4edf-8881-7c44cf261b2b'
      definition: '34'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)/web'

  # storybookのアーティファクトをweb/storybook-staticへ展開
  - task: ExtractFiles@1
    displayName: 'storybookのアーティファクトをweb/storybook-staticへ展開'
    inputs:
      archiveFilePatterns: '$(Pipeline.Workspace)/web/storybook/*.zip'
      destinationFolder: '$(System.DefaultWorkingDirectory)/web/storybook-static'

  # 前回のスクリーンショットを取得
  - task: DownloadPipelineArtifact@2
    displayName: '前回のスクリーンショットを取得 (latest)'
    condition: eq(variables.SCREENSHOT_SRC_ID, '')
    inputs:
      buildType: 'specific'
      project: '6f0ebfa7-ab4e-4edf-8881-7c44cf261b2b'
      definition: '50'
      targetPath: '$(Pipeline.Workspace)/vrt'
      allowPartiallySucceededBuilds: true
      buildVersionToDownload: 'latest'

  # 前回のスクリーンショットを取得
  - task: DownloadPipelineArtifact@2
    displayName: '前回のスクリーンショットを取得 (specific)'
    condition: ne(variables.SCREENSHOT_SRC_ID, '')
    inputs:
      buildType: 'specific'
      project: '6f0ebfa7-ab4e-4edf-8881-7c44cf261b2b'
      definition: '50'
      targetPath: '$(Pipeline.Workspace)/vrt'
      allowPartiallySucceededBuilds: true
      buildVersionToDownload: 'specific'
      buildId: '$(SCREENSHOT_SRC_ID)'

  # 前回のスクリーンショットをweb/storybook-vrt/dest/previousへ展開
  - task: ExtractFiles@1
    displayName: '前回のスクリーンショットをweb/storybook-vrt/dest/previousへ展開'
    continueOnError: true
    inputs:
      archiveFilePatterns: '$(Pipeline.Workspace)/vrt/vrt/screenshots/*.zip'
      destinationFolder: '$(System.DefaultWorkingDirectory)/web/storybook-vrt/dest/previous'

  # スクリーンショット生成
  # なぜかscriptで実装しないとプロセスが次に進まない…
  - script: |
      cd web/storybook-vrt
      npm run storycap

    displayName: 'npm run storycap'

  # 今回生成したスクリーンショット一式をアーカイブ
  - task: ArchiveFiles@2
    displayName: 'web/storybook-vrt/dest/currentをアーカイブ'
    inputs:
      rootFolderOrFile: 'web/storybook-vrt/dest/current'
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/vrt/screenshots/$(Build.BuildId).zip'

  # vrtを実施
  - task: Npm@1
    displayName: 'npm run vrt'
    continueOnError: true
    inputs:
      command: custom
      workingDir: 'web/storybook-vrt'
      verbose: false
      customCommand: 'run vrt'

  # vrtのレポートをzip圧縮
  - task: ArchiveFiles@2
    displayName: 'web/storybook-vrt/destをアーカイブ'
    inputs:
      rootFolderOrFile: 'web/storybook-vrt/dest'
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/vrt/report/$(Build.BuildId).zip'

  # スクリーンショットとレポートをアーティファクトとしてpublish
  - task: PublishPipelineArtifact@1
    displayName: 'スクリーンショットとレポートをアーティファクトとしてpublish'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/vrt'
      artifact: 'vrt'
