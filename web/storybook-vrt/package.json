{"name": "storybook-vrt", "version": "1.0.0", "description": "../storybook-staticを基準としたビジュアルリグレッションテスト(VRT)のレポートを生成する", "main": "index.js", "scripts": {"storycap": "storycap --viewportDelay 1000 --verbose -o ./dest/current --serverCmd \"http-server ../storybook-static\" http://localhost:8080", "vrt": "reg-cli ./dest/current ./dest/previous ./dest/diff -R ./dest/index.html -J ./dest/reg.json ; exit 0"}, "author": "", "license": "ISC", "dependencies": {"http-server": "^14.1.0", "puppeteer": "^12.0.1", "reg-cli": "^0.17.5", "storycap": "^3.1.5"}}