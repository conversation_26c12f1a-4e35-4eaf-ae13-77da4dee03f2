import React from 'react';
import ReactDOM from 'react-dom';
import smoothscroll from 'smoothscroll-polyfill';
import * as microsoftTeams from '@microsoft/teams-js';
import { AppInsightsProvider, initAppInsights } from '@avanade-teams/app-insights-reporter';
import { TeamsTokenContextProvider } from '@avanade-teams/auth';
import { TeamsInfoContextProvider } from '@avanade-teams/teams-info';
import { ITelemetryItem } from '@microsoft/applicationinsights-web';
import { DistributedTracingModes, CtxTagKeys } from '@microsoft/applicationinsights-common';
import App from './components/App';
import reportWebVitals from './utilities/reportWebVitals';
import environment from './utilities/environment';

// global CSS
import './index.scss';

// polyfill
smoothscroll.polyfill();

// テレメトリから位置情報を削除するためのコールバック
const maskGeolocationData = (envelope: ITelemetryItem) => {
  if (envelope.tags) {
    /* eslint-disable no-param-reassign */
    envelope.tags[CtxTagKeys.locationIp] = '0.0.0.0';
    envelope.tags[CtxTagKeys.locationCity] = 'unknown';
    envelope.tags[CtxTagKeys.locationProvince] = 'unknown';
    envelope.tags[CtxTagKeys.locationCountry] = 'unknown';
  }
};

// AppInsightsSDKを初期化
const connectionString = environment.REACT_APP_CONNECTION_STRING;
initAppInsights(connectionString, {
  distributedTracingMode: DistributedTracingModes.W3C,
  enableCorsCorrelation: true,
  /*
  correlationHeaderDomains: ['*.auzrefd.net', '*.azurewebsites.net'],
  enableRequestHeaderTracking: true,
  enableResponseHeaderTracking: true,
  */
}).addTelemetryInitializer(maskGeolocationData);

// init TeamsSDK
microsoftTeams.initialize();

// set 5 minutes for the oauth token expiration threshold
const TOKEN_EXP_THRESHOLD = 5 * 60 * 1000;

ReactDOM.render(
  <React.StrictMode>
    <AppInsightsProvider>
      <TeamsTokenContextProvider
        endpoint={environment.REACT_APP_API_URL}
        scopes={['graph', 'spo']}
        expThreshold={TOKEN_EXP_THRESHOLD}
      >
        <TeamsInfoContextProvider>
          <App />
        </TeamsInfoContextProvider>
      </TeamsTokenContextProvider>
    </AppInsightsProvider>
  </React.StrictMode>,
  document.getElementById('root'),
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
