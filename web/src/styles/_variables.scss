/*
 * CSS Variables
 */

:root {
  // common margins
  --length-margin-vertical: 16px;
  --length-margin-horizontal: 20px;
  --length-margin-horizontal-sp: 16px;

  // STYLE GUIDE COLORS (GRAYSCALE)

  // Default Foreground
  --color-guide-default-foreground: #242424;
  --color-guide-default-foreground-dark: #FFF;

  // Foreground 1
  --color-guide-foreground-1: #424242;
  --color-guide-foreground-1-dark: #D6D6D6;

  // Foreground 2
  --color-guide-foreground-2: #616161;
  --color-guide-foreground-2-dark: #ADADAD;

  // Foreground 3 (use the same color for the both theme)
  --color-guide-foreground-3: #FFF;
  --color-guide-foreground-3-dark: #FFF;

  // Foreground 6
  --color-guide-foreground-6: #E1DFDD;
  --color-guide-foreground-6-dark: #383A39;

  // Foreground Disabled
  --color-guide-foreground-disabled: #C7C7C7;
  --color-guide-foreground-disabled-dark: #5C5C5C;

  // Background
  --color-guide-background: #FFF;
  --color-guide-background-dark: #292929;

  // Background 1
  --color-guide-background-1: #FAFAFA;
  --color-guide-background-1-dark: #242424;

  // Background 2
  --color-guide-background-2: #F5F5F5;
  --color-guide-background-2-dark: #1F1F1F;

  // Background Active
  --color-guide-background-active: #D8D8D8;
  --color-guide-background-active-dark: #333;

  // Border
  --color-guide-deafult-border: #D1D1D1;
  --color-guide-deafult-border-dark: #616161;

  // STYLE GUIDE COLORS (PRIMARY COLORS)

  // Brand Main Foreground
  --color-guide-brand-main-foreground: #D56E6A;
  --color-guide-brand-main-foreground-dark: #E4A1A0;

  // Brand Main Background (use the same color for the both theme)
  --color-guide-brand-main-background: #D56E6A;
  --color-guide-brand-main-background-dark: #D56E6A;

  // Brand Foreground 2
  --color-guide-brand-foreground-2: #E9B0AF;
  --color-guide-brand-foreground-2-dark: #CE5652;

  // Brand Foreground 3
  --color-guide-brand-foreground-3: #ECC0C0;
  --color-guide-brand-foreground-3-dark: #BF3F39;

  // Brand Foreground 4
  --color-guide-brand-foreground-4: #E4A1A0;
  --color-guide-brand-foreground-4-dark: #AD5956;

  // Brand Foreground Active
  --color-guide-brand-foreground-active: #F1D0D0;
  --color-guide-brand-foreground-active-dark: #5D3E3D;

  // Brand Background 1
  --color-guide-brand-background-1: #FBEFEF;
  --color-guide-brand-background-1-dark: #3E3131;

  // text highlighting on search
  --color-guide-highlight: #E4A1A0;
  --color-guide-highlight-dark: #AD5956;

  // COLORS FOR THE SPECIFIC COMPONENTS:

  // Icons
  --color-guide-brand-icon-hover: #E9B0AF;
  --color-guide-brand-icon-hover-dark: #E4A1A0;

  // SPO Links
  --color-guide-spo-hover-icon: #D56E6A;
  --color-guide-spo-hover-icon-dark: #D6D6D6;

  // App Info
  --color-guide-app-info-label-background: #F9EBEB;

  // colors don't change in dark mode:

  // Home.scss
  --color-custom-black: #000;

  // RoundToaster.scss
  --color-custom-transparent-black-90: rgba(0, 0, 0, 0.9);

  // SearchInput.scss
  --color-placeholder-text: #686868;

  // SplitViewDetail.scss
  --color-guide-highlight-force-light: #E4A1A0;
  --color-guide-default-foreground-force-light: #242424;
  --color-guide-foreground-2-force-light: #666;
  --color-guide-main-brand-foreground-bg-force-light: #D56E6A;
  --color-guide-brand-foreground-2-force-light: #E9B0AF;
  --color-guide-foreground-6-force-light: #E1DFDD;
  --color-guide-background-force-light: #FFF;
}

// dark theme example
.theme-dark {
  // GRAYSCALE
  --color-guide-default-foreground: var(--color-guide-default-foreground-dark);
  --color-guide-foreground-1: var(--color-guide-foreground-1-dark);
  --color-guide-foreground-2: var(--color-guide-foreground-2-dark);
  --color-guide-foreground-3: var(--color-guide-foreground-3-dark);
  --color-guide-foreground-disabled: var(--color-guide-foreground-disabled-dark);
  --color-guide-background: var(--color-guide-background-dark);
  --color-guide-background-1: var(--color-guide-background-1-dark);
  --color-guide-background-2: var(--color-guide-background-2-dark);
  --color-guide-foreground-6: var(--color-guide-foreground-6-dark);
  --color-guide-background-active: var(--color-guide-background-active-dark);

  // BRAND
  --color-guide-brand-main-foreground: var(--color-guide-brand-main-foreground-dark);
  --color-guide-brand-foreground-1: var(--color-guide-brand-foreground-1-dark);
  --color-guide-brand-foreground-2: var(--color-guide-brand-foreground-2-dark);
  --color-guide-brand-foreground-3: var(--color-guide-brand-foreground-3-dark);
  --color-guide-brand-foreground-4: var(--color-guide-brand-foreground-4-dark);
  --color-guide-brand-main-background: var(--color-guide-brand-main-background-dark);
  --color-guide-brand-background-1: var(--color-guide-brand-background-1-dark);
  --color-guide-brand-foreground-active: var(--color-guide-brand-foreground-active-dark);

  // TEXT HIGHLIGHTING
  --color-guide-highlight: var(--color-guide-highlight-dark);

  // CUSTOM COLOR
  --color-guide-spo-hover-icon: var(--color-guide-spo-hover-icon-dark);
  --color-guide-brand-icon-hover: var(--color-guide-brand-icon-hover-dark);
}

// レイアウト変数
$width-split-view-list: 560 + 28px;
$width-split-view-detail: 975 + 28px;

// ドロップシャドウ
$box-shadow-1: 0 0 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-2: 0 0 6px 0 rgba(0, 0, 0, 0.1);
$box-shadow-3: 0 1px 2px 1px rgba(0, 0, 0, 0.1);
$box-shadow-4: 0 2px 2px 0 rgba(0, 0, 0, 0.05);

// ブレークポイント
$bp-iphone-pro-max: 428px;
