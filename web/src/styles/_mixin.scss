/* 1 line ellipsis */
@mixin ellipsis() {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  word-break: keep-all;
  text-overflow: ellipsis;
}

/*
 media query
 使用例：

 .example {
    // PC/SP共有スタイル
    color: black;

    // PC専用スタイル
    @media-pc {
      font-size: 16px;
    }

    // SP専用スタイル
    @media-sp {
      font-size: 14px;
    }
  }
*/
@mixin media-sp {

  // PC画面サイズ未満(=スマホサイズのみ)で適用
  @media screen and (max-width: $bp-iphone-pro-max) {
    @content;
  }
}

@mixin media-pc {

  // PC画面サイズ以上(非スマホサイズ)でのみ適用
  @media screen and (min-width: $bp-iphone-pro-max + 1) {
    @content;
  }
}

@mixin must-hide-on-sp {
  @include media-sp {
    display: none !important;
  }
}

@mixin remove-type-search-appearance {

  // type="search"の不要な装飾を削除する
  input[type='search'] {
    -webkit-appearance: none;
  }

  // https://stackoverflow.com/questions/9421551/how-do-i-remove-all-default-webkit-search-field-styling
  input[type='search']::-webkit-search-decoration,
  input[type='search']::-webkit-search-cancel-button,
  input[type='search']::-webkit-search-results-button,
  input[type='search']::-webkit-search-results-decoration {
    -webkit-appearance: none;
  }
}

@mixin hyperlink($selector: 'a', $force-light-mode: false) {
  #{$selector} {
    @if ($force-light-mode ==false) {
      color: var(--color-guide-brand-main-foreground);
    }

    @if ($force-light-mode ==true) {
      color: var(--color-guide-main-brand-foreground-bg-force-light);
    }

    text-decoration: none;

    @include media-sp {
      text-decoration: underline;
    }
  }

  #{$selector}:hover {
    @include media-pc {
      text-decoration: underline;
    }
  }

  #{$selector}:active {
    @if ($force-light-mode ==false) {
      color: var(--color-guide-brand-foreground-2);
    }

    @if ($force-light-mode ==true) {
      color: var(--color-guide-brand-foreground-2-force-light);
    }
  }
}

@mixin scrollbars-common() {

  // ローディング中, エラー表示中はスクロールを抑止
  &.is-loading,
  &.is-error {
    pointer-events: none;
  }

  >* {
    @include media-sp {
      // SPの横スワイプ時の挙動を改善
      touch-action: pan-y;
    }
  }
}

@keyframes redraw {
  0% {
    opacity: 0.99;
  }

  100% {
    opacity: 1;
  }
}

@mixin fix-ios-redraw-issue {
  // あまり良い方法では無いが、iOSで再描画されないバグを回避するためのアニメーションを挿入している
  // iOSのバージョンが上がることで本対応によって逆に表示されなくなるケースがあるため留意する
  animation: redraw 0.5s linear;
}
