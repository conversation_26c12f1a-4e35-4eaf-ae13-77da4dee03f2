/// <reference types="react-scripts" />
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test';
    REACT_APP_TITLE: string;
    REACT_APP_API_URL: string;
    REACT_APP_CONNECTION_STRING: string;
    REACT_APP_SHAREPOINT_URL: string;
    REACT_APP_SHAREPOINT_LIST_URL: string;
    REACT_APP_SHAREPOINT_REQ_TZ_OFFSET: number;
    REACT_APP__MANIFEST_ID: string;
    REACT_APP_COMPANY_REGULATION_SITE_URL: string;
    REACT_APP_COMPANY: string;
    REACT_APP_AI_SEARCH_ENABLED: boolean
  }
}
