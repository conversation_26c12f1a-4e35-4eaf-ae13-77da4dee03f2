/**
 * SharePointのサイトURLからホスト名とサイトパスをパースする
 * @param urlWithSitePath
 */
export function parseSharePointPath(
  urlWithSitePath: string,
): [hostName: string, sitePath: string] {
  const match = urlWithSitePath.match(/^https?:\/\/(.*?)\/sites\/(.*)/);
  const hostName = match?.[1];
  const sitePath = match?.[2];
  return [hostName ?? '', sitePath ?? ''];
}

/**
 * 絶対URLであるかどうかを判定する
 * @param urlString
 */
export function isAbsoluteUrl(urlString: string): boolean {
  try {
    const url = new URL(urlString);
    return !!url.protocol;
  } catch {
    // 完全なURLでない場合はnew URL()に失敗するのでfalse
    return false;
  }
}

/**
 * absUrlを基準とした絶対URLにして返却する
 * @param absUrl 末尾に"/"があるかどうかで相対パス解決の基準が変わる
 * @param relativeUrl
 */
export function complementRelativeUrl(absUrl: string, relativeUrl: string): string {
  // relativeUrlが既に絶対URLである場合はrelativeUrlをそのまま返却する
  if (isAbsoluteUrl(relativeUrl)) {
    return relativeUrl;
  }

  // relativeUrlが#で始まる場合はそのまま返却する
  if (relativeUrl.startsWith('#')) return relativeUrl;

  // absUrlが絶対URLでない場合はrelativeUrlをそのまま返却する
  if (!isAbsoluteUrl(absUrl)) {
    return relativeUrl;
  }
  // URLを補完して返却する
  return new URL(relativeUrl, absUrl).toString();
}

/**
 * create a deep link of the SharePoint post
 * return blank when the spoGuid or appCatalogueId is falsy
 * @param appCatalogueId
 * @param spoGuid
 */
export function createDeepLinkOfPost(
  appCatalogueId: string, spoGuid: string | undefined | null,
): string {
  if (!spoGuid || !appCatalogueId) return '';
  const encodedContext = encodeURIComponent(JSON.stringify({
    subEntityId: `search:spo:${spoGuid}`,
  }));
  return `https://teams.microsoft.com/l/entity/${appCatalogueId}/geranium_tab?context=${encodedContext}`;
}

/**
 * create a combined string of the generated deep link and title
 * if the deep link is not available it returns blank
 * if the title is falsy it returns only deep link
 * @param appCatalogueId
 * @param spoGuid
 * @param title
 */
export function createSharedLinkWithTitle(
  appCatalogueId: string, spoGuid: string | undefined | null, title: string | undefined | null,
): string {
  const deepLink = createDeepLinkOfPost(appCatalogueId, spoGuid);
  if (!deepLink) return '';
  return `${title ?? ''}${title ? ' ' : ''}${deepLink}`;
}
