import { EventReporter, EventReportType, IEventReporterParam } from '@avanade-teams/app-insights-reporter';

/**
 * anchorの位置へスクロールさせる
 * @param anchor "#sample"のようなクエリ文字列
 */
export function openAnchor(anchor: string): void {
  if (!anchor) return;
  document.querySelector(anchor)?.scrollIntoView({ behavior: 'smooth' });
}

/**
 * URLを新規タブで開く
 * @param url 空文字列|null|undefinedの場合は何もしない
 * @param [reportConf] ロギングする場合のコールバック[EventReporter, Partial<IEventReporterParam>]
 */
export function openWindow(
  url: string | null | undefined,
  reportConf?: [report: EventReporter, param: Partial<IEventReporterParam>],
): void {

  if (!url) return;
  window.open(url);

  if (reportConf) {
    const [report, param] = reportConf;
    report({
      type: EventReportType.USER_EVENT,
      name: '',
      customProperties: {
        webUrl: url,
      },
      ...param,
    });
  }
}

/**
 * メール添付ファイルをダウンロード
 * @param base64 ファイルのbase64
 * @param fileName ファイル名
 * @param reportConf ロギングする場合のコールバック [EventReporter, Partial<IEventReporterParam>]
 */
export function downloadFile(
  base64: string | undefined,
  fileName: string,
  reportConf?: [report: EventReporter, param: Partial<IEventReporterParam>],
): void {
  if (!base64) return;
  const link = document.createElement('a');
  // base64を元にデータURIを生成
  link.href = `data:application/octet-stream;base64,${base64}`;
  link.download = fileName;
  link.click();

  if (reportConf) {
    const [report, param] = reportConf;
    report({
      type: EventReportType.USER_EVENT,
      name: '',
      customProperties: {
        weblink: link.href,
      },
      ...param,
    });
  }
}
