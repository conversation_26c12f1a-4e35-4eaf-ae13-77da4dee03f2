import PropTypes from 'prop-types';
import { IAppInfoMessages } from '../types/IAppInfoMessages';

export type ValueOf<T> = T[keyof T];

// teamsのユーザー状態の種類
export const UserActivity = {
  AVAILABLE: 'Available',
  AVAILABLE_IDLE: 'AvailableIdle',
  AWAY: 'Away',
  BE_RIGHT_BACK: 'BeRightBack',
  BUSY: 'Busy',
  BUSY_IDLE: 'BusyIdle',
  DO_NOT_DISTURB: 'DoNotDisturb',
  OFFLINE: 'Offline',

  // 未確定状態用
  BLANK: '',
};

// teamsのユーザー状態の種類をtypeに変換したもの
export type PresenceType = ValueOf<typeof UserActivity>;

// PropTypesのバリデータ
export const propTypesPresenceType = PropTypes.oneOf<PresenceType>(
  Object.values(UserActivity),
);

/**
 * check the unknown type parameter and always return as string
 * @param param
 */
export function handleUnknownString(param: unknown): string {
  if (typeof param !== 'string') return '';
  return param;
}

/**
 * check the unknown type parameter and always return as string
 * @param param
 */
export function handleUnknownNumber(param: unknown): number {
  if (typeof param !== 'number') return -1;
  return param;
}

/**
 * check the unknown type parameter and always return as IAppInfoMessages type
 * @param param
 */
export function handleUnknownAppInfoMessages(
  param: unknown | IAppInfoMessages,
): IAppInfoMessages | null {
  if (!param || typeof param !== 'object' || Object.keys(param).length === 0) return null;
  const objParam = param as IAppInfoMessages;
  const isStringArray = (array: unknown[]) => array.every((val: unknown) => typeof val === 'string');
  const isValid = Object.values(objParam)
    .every((value) => Array.isArray(value) && isStringArray(value));
  return isValid ? objParam : null;
}
