import { GraphError } from '@microsoft/microsoft-graph-client';
import { defineExecCommand, mockExecCommand } from '../mocks/exec-command';
import mockFetch from '../mocks/fetch';
import {
  copyToClipboard, fetchUrlRes, isLogInitTiming, mergedClassName,
} from './commonFunction';

describe('isLogInitTiming', () => {
  it('should be false if the values match the view LOADING', () => {
    const viewMock = {
      LOADING: 'loading',
    };

    expect(isLogInitTiming('loading', { current: 'loading' }, viewMock)).toBeFalsy();
  });

  it('should be true if the values do not match the view LOADING', () => {
    const viewMock = {
      LOADING: 'loading',
    };

    expect(isLogInitTiming('loading', { current: 'default' }, viewMock)).toBeTruthy();
    expect(isLogInitTiming('default', { current: 'loading' }, viewMock)).toBeTruthy();
    expect(isLogInitTiming('default', { current: 'default' }, viewMock)).toBeTruthy();
  });
});

describe('mergedClassName', () => {
  it('should return joined strings with spaces', () => {
    expect(mergedClassName('a', 'b c')).toBe('a b c');
  });

  it('should return only baseClassName if second param is falsy', () => {
    expect(mergedClassName('a', null)).toBe('a');
    expect(mergedClassName('a', undefined)).toBe('a');
  });
});

describe('fetchUrlRes', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  const token = '123';

  describe('when url = ""', () => {
    const url = '';

    it('should reject with MISSING_URL', () => {
      const method = '';

      expect(fetchUrlRes(token, method, url)).rejects.toStrictEqual(
        new Error('MISSING_URL'),
      );
    });
  });

  describe('when url = "abc"', () => {
    const url = 'abc';

    describe('when fetch rejects', () => {
      beforeEach(() => {
        mockFetch.mockRejectedValue(new Error('abc'));
      });

      it('should reject with what fetch rejects', () => {
        expect(
          fetchUrlRes(token, 'GET', url),
        ).rejects.toThrow(new Error('abc'));
      });
    });

    describe('when fetch resolves a not ok response', () => {
      beforeEach(() => {
        mockFetch.mockResolvedValue({
          ok: false,
          status: 123,
        });
      });

      it('should reject with GraphError', () => {
        expect(
          fetchUrlRes(token, 'GET', url),
        ).rejects.toThrow(new GraphError(123));
      });
    });

    describe('when fetch resolves an ok response', () => {
      beforeEach(() => {
        mockFetch.mockResolvedValue({
          ok: true,
          mock: 'mock',
        });
      });

      it('should resolve what fetch returns', () => {
        expect(
          fetchUrlRes(token, 'GET', url),
        ).resolves.toStrictEqual({
          ok: true,
          mock: 'mock',
        });

        expect(mockFetch).toBeCalledWith(
          url,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            method: 'GET',
            body: undefined,
          },
        );
      });
    });
  });
});

describe('copyToClipboard', () => {
  beforeAll(() => {
    defineExecCommand();
  });

  describe('the value is blank', () => {
    it('should not call document.execCommand', () => {
      copyToClipboard('');
      expect(mockExecCommand).not.toBeCalled();
    });
  });

  describe('the value is not blank', () => {
    it('should call document.execCommand with "copy"', () => {
      copyToClipboard('abc');
      expect(mockExecCommand).toBeCalledTimes(1);
      expect(mockExecCommand).toBeCalledWith('copy');
    });
  });

});
