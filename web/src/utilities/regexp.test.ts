import { isSharedLinkCommand, splitExtensionFromFileName } from './regexp';

describe('utilities/regexp', () => {
  describe('splitExtensionFromFileName', () => {
    it('should return a string array that has a whole name, a name part, and an extension part', () => {
      expect(
        splitExtensionFromFileName('test_file-...name.pdf'),
      ).toStrictEqual(
        [
          'test_file-...name.pdf',
          'test_file-...name',
          'pdf',
        ],
      );
    });
    it('should return a string array that has all empty string when parameter is null', () => {
      expect(
        splitExtensionFromFileName(null),
      ).toStrictEqual(
        ['', '', ''],
      );
    });
    it('should return a string array that has all empty string when parameter is undefined', () => {
      expect(
        splitExtensionFromFileName(undefined),
      ).toStrictEqual(
        ['', '', ''],
      );
    });
    it('should return a string array without extension part when parameter file name does not have extension', () => {
      expect(
        splitExtensionFromFileName('test_file'),
      ).toStrictEqual(
        ['test_file', 'test_file', ''],
      );
    });
    it('should return a string array without extension part when parameter file name with cannot be considered an extension', () => {
      expect(
        splitExtensionFromFileName('test.@'),
      ).toStrictEqual(
        ['test.@', 'test.@', ''],
      );
    });
  });
});

describe('isSharedLinkCommand', () => {
  describe('when the value is falsy', () => {
    it('should return false', () => {
      expect(isSharedLinkCommand(undefined)).toBe(false);
      expect(isSharedLinkCommand(null)).toBe(false);
      expect(isSharedLinkCommand('')).toBe(false);
    });
  });

  describe('when the value is not shared link format', () => {
    it('should return false', () => {
      expect(isSharedLinkCommand('TEXT-NOT-GUID')).toBe(false);
    });
  });

  describe('when the value is in shared link format', () => {
    const value = 'search:spo:ef8f1072-30bb-4492-a0b9-b0b4edb17ca8';
    expect(isSharedLinkCommand(value)).toBe(true);
  });
});
