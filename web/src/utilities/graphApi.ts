// 型定義しかないモジュールなのでlinter警告が出るが実害は無い
// eslint-disable-next-line import/no-unresolved
import * as microsoftGraph from '@microsoft/microsoft-graph-types/microsoft-graph';

// utilityなのでdefaultエクスポートしない。複数の関数が実装されたら削除する
/* eslint-disable import/prefer-default-export */

/**
 * search/query APIの検索結果を整形してフラット化する
 * @param source search/queryのレスポンスデータ
 * @param mapper hitsContainerの整形処理の実装
 */
export function flatSearchRes<T>(
  source: microsoftGraph.SearchResponse[],
  mapper: (hit: microsoftGraph.SearchHit) => T,
): T[] {

  // 複数の検索結果をSearchHitsContainerとしてフラット化する
  const hitsContainers: microsoftGraph.SearchHitsContainer[] = source
    .filter((res): res is microsoftGraph.SearchResponse => {
      if (!Array.isArray(res.hitsContainers)) {
        return false;
      }
      return res.hitsContainers.length > 0;
    })
    .flatMap((res) => (
      // 既にfilterしているのでSearchHitsContainerであるものとみなす
      res.hitsContainers as microsoftGraph.SearchHitsContainer
    ));

  // 複数のSearchHitsContainerの内部にあるhitsを整形してフラット化する
  return hitsContainers.flatMap((hitsContainer: microsoftGraph.SearchHitsContainer) => {
    const { hits } = hitsContainer;
    if (!Array.isArray(hits)) return [];
    if (hits.length < 1) return [];

    return hits
      .filter((hit): hit is microsoftGraph.SearchHit => {
        // resourceが存在しないものは削除
        const { resource } = hit;
        return resource != null;
      })
      // mapperでの整形結果を返却
      .map<T>((hit: microsoftGraph.SearchHit) => mapper(hit));
  });
}
