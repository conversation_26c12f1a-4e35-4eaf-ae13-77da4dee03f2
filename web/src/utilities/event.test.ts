import {
  isMouseEvent,
  is<PERSON><PERSON>boardEvent,
  ReactEvent,
  isEnterKey,
  isEnterKeydownOrClick,
} from './event';

describe('event', () => {

  describe('isMouseEvent', () => {
    it('should return true if e.type is "click"', () => {
      expect(isMouseEvent({
        type: 'click',
      } as ReactEvent)).toBe(true);
    });

    it('should return false if e.type is not "click"', () => {
      expect(isMouseEvent({
        type: 'mousedown',
      } as ReactEvent)).toBe(false);
    });

    it('should return true if e.type is equal to the type', () => {
      expect(isMouseEvent({
        type: 'mousedown',
      } as ReactEvent, 'mousedown')).toBe(true);
    });

    it('should return false if e.type is not equal to the type', () => {
      expect(isMouseEvent({
        type: 'mousedown',
      } as ReactEvent, 'mouseup')).toBe(false);
    });
  });

  describe('isKeyboardEvent', () => {
    it('should return true if e.type is "keydown"', () => {
      expect(isKeyboardEvent({
        type: 'keydown',
      } as ReactEvent)).toBe(true);
    });

    it('should return false if e.type is not "keydown"', () => {
      expect(isKeyboardEvent({
        type: 'keypress',
      } as ReactEvent)).toBe(false);
    });

    it('should return true if e.type is equal to the type', () => {
      expect(isMouseEvent({
        type: 'keypress',
      } as ReactEvent, 'keypress')).toBe(true);
    });

    it('should return false if e.type is not equal to the type', () => {
      expect(isMouseEvent({
        type: 'keypress',
      } as ReactEvent, 'keyup')).toBe(false);
    });
  });

  describe('isEnterKey', () => {
    describe('when e.key is "Enter"', () => {

      describe('when the nativeEvent.isComposing is false', () => {
        it('should return true', () => {
          expect(isEnterKey({
            type: 'keydown',
            key: 'Enter',
            nativeEvent: { isComposing: false },
          } as ReactEvent)).toBe(true);
        });

        describe('when the type is equal to the specified type', () => {
          it('should return true', () => {
            expect(isEnterKey({
              type: 'keyup',
              key: 'Enter',
              nativeEvent: { isComposing: false },
            } as ReactEvent, 'keyup')).toBe(true);
          });
        });
      });

      describe('when the nativeEvent.isComposing is true', () => {
        it('should return false', () => {
          expect(isEnterKey({
            type: 'keydown',
            key: 'Enter',
            nativeEvent: { isComposing: true },
          } as ReactEvent)).toBe(false);
        });
      });

      describe('when the type is not equal to the specified type', () => {
        it('should return false', () => {
          expect(isEnterKey({
            type: 'keyup',
            key: 'Enter',
            nativeEvent: { isComposing: false },
          } as ReactEvent, 'keydown')).toBe(false);
        });
      });
    });

    describe('when e.key is not "Enter"', () => {
      it('should return false', () => {
        expect(isEnterKey({
          type: 'keydown',
          key: 'Delete',
          nativeEvent: { isComposing: false },
        } as ReactEvent)).toBe(false);
      });
    });

  });

  describe('isEnterKeydownOrClick', () => {
    it('should return true', () => {
      expect(isEnterKeydownOrClick({
        type: 'click',
      } as ReactEvent)).toBe(true);
      expect(isEnterKeydownOrClick({
        type: 'keydown',
        key: 'Enter',
        nativeEvent: { isComposing: false },
      } as ReactEvent)).toBe(true);
    });

    it('should return false', () => {
      expect(isEnterKeydownOrClick({
        type: 'mouseup',
      } as ReactEvent)).toBe(false);
      expect(isEnterKeydownOrClick({
        type: 'keydown',
        key: 'Delete',
        nativeEvent: { isComposing: false },
      } as ReactEvent)).toBe(false);
      expect(isEnterKeydownOrClick({
        type: 'keyup',
        key: 'Enter',
        nativeEvent: { isComposing: false },
      } as ReactEvent)).toBe(false);
    });
  });
});
