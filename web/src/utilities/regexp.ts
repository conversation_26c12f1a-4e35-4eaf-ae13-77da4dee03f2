// https://stackoverflow.com/questions/6582171/javascript-regex-for-matching-extracting-file-extension
export const EXTENSION_MATCH = /(.*)(\.[0-9a-z]+$)/i;

/**
 * ファイル名から拡張子部分を分割する
 *
 * @export
 * @param {(string | undefined | null)} name ファイル名
 * @return {*}  {[string, string, string]} [元の文字, 名前部分, 拡張子] の形式でreturn
 */
export function splitExtensionFromFileName(
  name: string | undefined | null,
): [string, string, string] {

  if (typeof name !== 'string') return ['', '', ''];

  const result = name.match(EXTENSION_MATCH);
  if (!result) return [name, name, ''];

  // [元の文字, 名前部分, 拡張子] の形式でreturn
  const [original, namePart, extensionPart] = result;
  return [original, namePart, extensionPart.replace(/^\./, '')];
}

/**
 * return true if the value is shared link format
 * @param value
 */
export function isSharedLinkCommand(value: string | undefined | null): boolean {
  if (typeof value !== 'string') {
    return false;
  }
  return value.match(/^search:spo:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/) != null;
}
