// 大小比較できる型
type comparableValueType = boolean | number | string | Date | undefined;
type converterType<T> = (arg: T) => comparableValueType;

type compareKeyOption<T> = {
  converter: string | converterType<T>,
  direction: number,
  nullsFirst: number,
}

/* ソート条件に対する有効なオプション
 * string: 連想配列のキーを表し、指定されたキーに対する値を用いて昇順でソート
   先頭が!の場合、キーは!を除いたものとなり、ソート順が逆順になる
 * converterType: 値の取得関数を表し、関数の返す結果を用いて昇順でソート
 * compareKeyOption: 値の取得方法、ソート順、nullの扱いを指定してソート
 */
type effectiveOption<T> = string | converterType<T> | compareKeyOption<T>;

function compareKey<T>(
  a: T,
  b: T,
  option: effectiveOption<T>,
): number {
  let castedOption: compareKeyOption<T>;

  const func = (converter: converterType<T>, direction: number, nullsFirst: number): number => {
    const valuea = converter(a);
    const valueb = converter(b);

    if (valuea === undefined && valueb === undefined) {
      return 0;
    }

    if (valuea === undefined) {
      return nullsFirst * 1;
    }

    if (valueb === undefined) {
      return nullsFirst * -1;
    }

    if (valuea > valueb) {
      return direction * 1;
    }

    if (valuea < valueb) {
      return direction * -1;
    }

    return 0;
  };

  const getComparableValue = (arg: T, key: string) => {
    const value = (arg as Record<string, unknown>)[key];
    switch (typeof value) {
      case 'string':
        return value as string;
      case 'number':
        return value as number;
      case 'boolean':
        return value as boolean;
      default:
        if (value instanceof Date) {
          return value as Date;
        }
        return undefined;
    }
  };

  switch (typeof option) {
    case 'string':
      return ((option as string).startsWith('!'))
        ? func((x) => getComparableValue(x, (option as string).substring(1)), -1, 1)
        : func((x) => getComparableValue(x, option as string), 1, 1);
    case 'function':
      return func(option as converterType<T>, 1, 1);
    default:
      castedOption = option as compareKeyOption<T>;
      switch (typeof (castedOption.converter)) {
        case 'string':
          return func(
            (x) => getComparableValue(x, castedOption.converter as string),
            castedOption.direction,
            castedOption.nullsFirst,
          );
        default:
          return func(
            castedOption.converter as converterType<T>,
            castedOption.direction,
            castedOption.nullsFirst,
          );
      }
  }
}

function compareMultipleKeys<T>(
  a: T,
  b: T,
  options: effectiveOption<T>[],
): number {
  let result = 0;
  options.some((option) => {
    result = compareKey(a, b, option);
    return !!result;
  });
  return result;
}

export { compareKey, compareMultipleKeys };
