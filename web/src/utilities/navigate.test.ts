import { EventReportType } from '@avanade-teams/app-insights-reporter';

import { openAnchor } from './navigate';
import * as navigate from './navigate';

describe('utilities/window', () => {

  describe('openAnchor', () => {
    const querySelectorMock = jest.fn();
    const scrollIntoViewMock = jest.fn();

    beforeEach(() => {
      global.document.querySelector = querySelectorMock;
      scrollIntoViewMock.mockClear();
    });

    describe('when the anchor is blank', () => {
      it('should not do anything', () => {
        openAnchor('');
        expect(querySelectorMock).not.toBeCalled();
      });
    });

    describe('when the querySelector returns null', () => {
      it('should not do anything', () => {
        querySelectorMock.mockReturnValueOnce(null);
        openAnchor('a');
      });
    });

    describe('when the querySelector returns an element', () => {
      it('should call its scrollIntoView', () => {
        querySelectorMock.mockReturnValueOnce(
          { scrollIntoView: scrollIntoViewMock } as unknown as HTMLElement,
        );
        openAnchor('a');
        expect(scrollIntoViewMock).toBeCalledTimes(1);
        expect(scrollIntoViewMock).toBeCalledWith({ behavior: 'smooth' });
      });
    });
  });

  describe('windowOpen', () => {

    beforeEach(() => {
      global.open = jest.fn();
    });

    it('should open url with window.open', () => {
      navigate.openWindow('https://developer.microsoft.com/ja-jp/graph/graph-explorer');
      expect(global.open).toBeCalled();
    });

    it('should report event if reportConf is set', () => {
      const reportMock = jest.fn();
      navigate.openWindow(('./'), [reportMock, {
        name: 'CLICK_TEST',
      }]);
      expect(global.open).toBeCalled();
      expect(reportMock).toBeCalledTimes(1);
      expect(reportMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'CLICK_TEST',
        customProperties: {
          webUrl: './',
        },
      });
    });

    it('should not do anything if input string is blank, null or undefined', () => {
      navigate.openWindow('');
      navigate.openWindow(null);
      navigate.openWindow(undefined);
      expect(global.open).not.toBeCalled();
    });
  });

  describe('downloadFile', () => {
    const base64 = 'abc';
    const fileName = 'example.txt';

    it('should report event if reportConf is set', () => {
      const reportMock = jest.fn();
      navigate.downloadFile(base64, fileName, [reportMock, {
        name: 'DOWNLOAD_TEST',
      }]);
      expect(reportMock).toBeCalledTimes(1);
      expect(reportMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'DOWNLOAD_TEST',
        customProperties: {
          weblink: 'data:application/octet-stream;base64,abc',
        },
      });
    });
  });
});
