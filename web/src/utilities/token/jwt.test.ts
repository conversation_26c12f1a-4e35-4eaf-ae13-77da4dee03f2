import { getUniqueNameByToken } from './jwt';

describe('getUniqueNameByToken', () => {
  describe('when tokenProvider rejects', () => {
    const tokenProvider = jest.fn().mockRejectedValue(undefined);

    it('should return a blank string set', () => {
      expect(getUniqueNameByToken(tokenProvider)).resolves.toStrictEqual(
        ['', ''],
      );
    });
  });

  describe('when tokenProvider resolves a token', () => {
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************.-xgNUazh7BVnEE86hvd2FMyIyH4-0Oy9A_tpKoXnEbs';
    const tokenProvider = jest.fn().mockResolvedValue(token);

    it('should return [token, "abc"]', () => {
      expect(getUniqueNameByToken(tokenProvider)).resolves.toStrictEqual(
        [token, 'abc'],
      );
    });
  });
});
