import decode from 'jwt-decode';
import { TokenProvider } from '../../types/TokenProvider';

export type IJwt = {
  header: {
    typ: string,
    alg: string,
  },
  payload: {
    exp?: number,
    oid?: string,
    appid?: string,
    azp?: string,
  },
  signature: unknown,
};

export const decodeJwt = (token: string): IJwt | undefined => {
  try {
    const header = decode<{typ: string, alg: string}>(token, { header: true });
    const payload = decode<{exp?: number, oid?: string}>(token);

    if (payload === null || header === null) {
      return undefined;
    }

    return {
      header,
      payload,
      signature: null,
    } as IJwt;

  } catch (e) {
    return undefined;
  }
};

/**
 * tokenの値とユニークユーザー名を取得する
 * @param tokenProvider
 */
export async function getUniqueNameByToken(
  tokenProvider: TokenProvider,
): Promise<[string, string]> {

  const token = await tokenProvider().catch(() => '');
  const decoded = decodeJwt(token);

  if (!decoded) {
    return [token, ''];
  }

  return [token, decoded.payload?.oid ?? ''];
}
