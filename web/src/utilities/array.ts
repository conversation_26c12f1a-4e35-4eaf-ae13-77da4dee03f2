import { fullCaseToHalfCase, halfKataToFullKata } from './text';

/**
 * 重複メンバを除去した配列を返す
 * @param list
 */
export function removeDupMembers<T>(list: T[]): T[] {
  return Array.from(new Set(list));
}

/**
 * 大文字／小文字を同一視した上で重複する配列メンバーを削除する
 * 同一視された単語は最初の1件目のパターンだけが残る
 * @param list
 */
export function removeDupMembersCaseNotSensitive(list: string[]): string[] {
  return list.reduce<string[]>((result, element) => {
    const normalizedElement = element.toLowerCase();
    if (result.every((otherElement) => otherElement.toLowerCase() !== normalizedElement)) {
      result.push(element);
    }
    return result;
  }, []);
}

/**
 * includesの全角半角大文字小文字無視版
 * @param list
 * @param word
 */
export function roughlyIncludes(list: string[], word: string): boolean {
  return list.some((item) => fullCaseToHalfCase(halfKataToFullKata(item)).toUpperCase()
    === fullCaseToHalfCase(halfKataToFullKata(word)).toUpperCase());
}

/**
 * create a dictionary of the array
 * @param array
 * @param key
 */
export function createDictByKey<T>(array: T[], key: keyof T): Record<string, T> {
  return array.reduce<Record<string, T>>((result, current) => {
    const k = current[key];
    if (typeof k !== 'string') {
      return result;
    }
    return {
      ...result,
      [k]: current,
    };
  }, {});
}

/**
 * shallow merge two collections by specific key
 * when the override array entry has a same key but some fields are blank,
 * the empty fields won't be merged
 *
 * @param key a key to detect the entries are the same
 * @param origin an original collection
 * @param override a collection to merge
 * @param skipBlankString
 */
export function mergeCollectionsByKey<T>(
  key: keyof T,
  origin: T[],
  override: T[],
  skipBlankString = true,
): T[] {

  const overrideDict = createDictByKey(override, key);

  const step1Merged = origin.map((originItem) => {
    const k = originItem[key];
    const target = overrideDict?.[typeof k === 'string' ? k : ''] as T | undefined;

    if (!target) {
      return originItem;
    }

    const overrideItem = (Object.keys(target) as (keyof T)[]).reduce<T>((result, t) => {
      const field = target[t];

      // skip null or undefined
      if (field == null) {
        return result;
      }

      // skip a blank string
      if (skipBlankString && typeof field === 'string' && !field) {
        return result;
      }

      return {
        ...result,
        [t]: target[t],
      };
    }, {} as T);

    return {
      ...originItem,
      ...overrideItem,
    };
  });

  const originDict = createDictByKey(origin, key);

  const overrideOnlyItems = override.map((overrideItem) => {
    const k = overrideItem[key];
    if (typeof k !== 'string') return undefined;
    if (!originDict[k]) {
      return overrideItem;
    }
    return undefined;
  }).filter((i): i is T => !!i);

  return step1Merged.concat(overrideOnlyItems);
}

/**
 * sort array by specified field value
 * @param array
 * @param key
 * @param order order by 'asc' or 'desc'
 */
export function sortArrayByKey<T>(array: T[], key: keyof T, order: 'asc' | 'desc' = 'asc'): T[] {
  return array
    .concat()
    .sort((a, b) => {
      const aVal = a[key];
      const bVal = b[key];

      if (typeof aVal !== 'string' && typeof aVal !== 'number') {
        return 0;
      }
      if (typeof bVal !== 'string' && typeof bVal !== 'number') {
        return 0;
      }

      const isAscOrder = order === 'asc';

      if (aVal < bVal) {
        return isAscOrder ? -1 : 1;
      }
      if (aVal > bVal) {
        return isAscOrder ? 1 : -1;
      }

      return 0;
    });
}
