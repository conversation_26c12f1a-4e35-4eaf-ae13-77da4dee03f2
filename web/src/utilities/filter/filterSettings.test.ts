import { SplitViewDetailMessage, SplitViewDetailView } from '../../components/domains/split-view/split-view-detail/SplitViewDetail';
import { SplitViewListMessage, SplitViewListView } from '../../components/domains/split-view/split-view-list/SplitViewList';
import { DataSourceKindType } from '../../types/DataSourceKind';
import { createSplitViewListSingle } from '../test';
import { formatList, getEmptyFilterOptions } from './filterSettings';

jest.mock('../../utilities/environment');

describe('formatList', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });
  beforeEach(() => {
    jest.setSystemTime(new Date('2023-04-02T00:00:00.00Z'));
  });
  afterEach(() => {
    jest.clearAllTimers();
  });
  afterAll(() => {
    jest.useRealTimers();
  });
  it('should get formed list', () => {
    expect(formatList({
      listView: SplitViewListView.LOADING,
      listMessage: SplitViewListMessage.BLANK,
      list: [
        createSplitViewListSingle({ displayDate: '2023-04-01T00:00:00.00Z' }),
        createSplitViewListSingle({ displayDate: '2023-03-31T00:00:00.00Z' }),
        createSplitViewListSingle({ displayDate: '2023-04-02T00:00:00.00Z' }),
      ],
      activeId: '',
      detailView: SplitViewDetailView.LOADING,
      detailMessage: SplitViewDetailMessage.BLANK,
      detail: undefined,
      context: {
        sort: [{ key: 'displayDate', order: 'desc', priority: 1 }],
        filter: [{ key: 'displayDate', option: 1 }],
      },
      inlineMailAttachments: [],
      chatAttachments: [],
    })).toStrictEqual([
      createSplitViewListSingle({ displayDate: '2023-04-02T00:00:00.00Z' }),
      createSplitViewListSingle({ displayDate: '2023-04-01T00:00:00.00Z' }),
    ]);
  });

  it('should get plain list', () => {
    expect(formatList({
      listView: SplitViewListView.LOADING,
      listMessage: SplitViewListMessage.BLANK,
      list: [
        createSplitViewListSingle({ displayDate: '2023-04-02T00:00:00.00Z' }),
        createSplitViewListSingle({ displayDate: '2023-04-01T00:00:00.00Z' }),
        createSplitViewListSingle({ displayDate: '2023-04-03T00:00:00.00Z' }),
      ],
      activeId: '',
      detailView: SplitViewDetailView.LOADING,
      detailMessage: SplitViewDetailMessage.BLANK,
      detail: undefined,
      context: {
        sort: [],
        filter: [],
      },
      inlineMailAttachments: [],
      chatAttachments: [],
    })).toStrictEqual([
      createSplitViewListSingle({ displayDate: '2023-04-02T00:00:00.00Z' }),
      createSplitViewListSingle({ displayDate: '2023-04-01T00:00:00.00Z' }),
      createSplitViewListSingle({ displayDate: '2023-04-03T00:00:00.00Z' }),
    ]);
  });

  describe('when range filter is set', () => {
    test.each([
      {
        option: 0,
        input: ['2022-04-01T23:59:59.99Z', '2022-04-02T00:00:00.00Z', '2022-04-02T00:00:00.01Z'],
        expected: ['2022-04-01T23:59:59.99Z', '2022-04-02T00:00:00.00Z', '2022-04-02T00:00:00.01Z'],
      },
      {
        option: 1,
        input: ['2023-03-31T23:59:59.99Z', '2023-04-01T00:00:00.00Z', '2023-04-01T00:00:00.01Z'],
        expected: ['2023-04-01T00:00:00.00Z', '2023-04-01T00:00:00.01Z'],
      },
      {
        option: 2,
        input: ['2023-03-25T23:59:59.99Z', '2023-03-26T00:00:00.00Z', '2023-03-26T00:00:00.01Z'],
        expected: ['2023-03-26T00:00:00.00Z', '2023-03-26T00:00:00.01Z'],
      },
      {
        option: 3,
        input: ['2023-03-01T23:59:59.99Z', '2023-03-02T00:00:00.00Z', '2023-03-02T00:00:00.01Z'],
        expected: ['2023-03-02T00:00:00.00Z', '2023-03-02T00:00:00.01Z'],
      },
      {
        option: 4,
        input: ['2022-10-01T23:59:59.99Z', '2022-10-02T00:00:00.00Z', '2022-10-02T00:00:00.01Z'],
        expected: ['2022-10-02T00:00:00.00Z', '2022-10-02T00:00:00.01Z'],
      },
      {
        option: 5,
        input: ['2022-04-01T23:59:59.99Z', '2022-04-02T00:00:00.00Z', '2022-04-02T00:00:00.01Z'],
        expected: ['2022-04-02T00:00:00.00Z', '2022-04-02T00:00:00.01Z'],
      },
    ])('should filter items as expected by option %option', ({ input, option, expected }) => {
      expect(formatList({
        listView: SplitViewListView.LOADING,
        listMessage: SplitViewListMessage.BLANK,
        list: input.map((displayDate) => createSplitViewListSingle({ displayDate })),
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [{ key: 'displayDate', order: 'asc', priority: 1 }],
          filter: [{ key: 'displayDate', option }],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      })).toStrictEqual(
        expected.map((displayDate) => createSplitViewListSingle({ displayDate })),
      );
    });

    it.each([
      { from: '2022-04-02', to: undefined, expectedTime: ['2022-04-01T15:00:00.000Z', '2022-04-02T14:59:59.999Z', '2022-04-02T15:00:00.000Z'] },
      { from: undefined, to: '2022-04-02', expectedTime: ['2022-04-01T14:59:59.999Z', '2022-04-01T15:00:00.000Z', '2022-04-02T14:59:59.999Z'] },
      { from: undefined, to: undefined, expectedTime: ['2022-04-01T14:59:59.999Z', '2022-04-01T15:00:00.000Z', '2022-04-02T14:59:59.999Z', '2022-04-02T15:00:00.000Z'] },
      { from: '2022-04-02', to: '2022-04-02', expectedTime: ['2022-04-01T15:00:00.000Z', '2022-04-02T14:59:59.999Z'] },
    ])('should filter items as expected by range option: $from - $to', ({ from, to, expectedTime }) => {
      const list = [
        '2022-04-01T14:59:59.999Z',
        '2022-04-01T15:00:00.000Z',
        '2022-04-02T14:59:59.999Z',
        '2022-04-02T15:00:00.000Z',
      ].map((displayDate) => createSplitViewListSingle({ displayDate }));
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const expected = expectedTime.map(
        (displayDate) => createSplitViewListSingle({ displayDate }),
      );
      expect(formatList({
        listView: SplitViewListView.LOADING,
        listMessage: SplitViewListMessage.BLANK,
        list,
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [{ key: 'displayDate', order: 'asc', priority: 1 }],
          filter: [{
            key: 'displayDate', option: 6, from, to,
          }],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      })).toStrictEqual(expected);
    });
  });

  describe('when kind filter is set', () => {
    test.each([{
      when: 'only SPO is selected', option: ['SPO'], input: ['SPO', 'Mail', 'Chat'], expected: ['SPO'],
    },
    {
      when: 'only Mail is selected', option: ['Mail'], input: ['SPO', 'Mail', 'Chat'], expected: ['Mail'],
    },
    {
      when: 'only Chat is selected', option: ['Chat'], input: ['SPO', 'Mail', 'Chat'], expected: ['Chat'],
    },
    {
      when: 'only Chat and SPO are selected', option: ['SPO', 'Chat'], input: ['SPO', 'Mail', 'Chat'], expected: ['SPO', 'Chat'],
    },
    {
      when: 'no items are selected', option: [], input: ['SPO', 'Mail', 'Chat'], expected: ['SPO', 'Mail', 'Chat'],
    },
    ])('should filter items as expected when $when', ({ option, input, expected }) => {
      expect(formatList({
        listView: SplitViewListView.LOADING,
        listMessage: SplitViewListMessage.BLANK,
        list: input.map(
          (kind) => createSplitViewListSingle({ kind: kind as DataSourceKindType }),
        ),
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [{ key: 'displayDate', order: 'asc', priority: 1 }],
          filter: [{ key: 'kind', option }],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      })).toStrictEqual(
        expected.map((kind) => createSplitViewListSingle({ kind: kind as DataSourceKindType })),
      );
    });
  });
});

describe('getEmptyFilterOptions', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });
  beforeEach(() => {
    jest.setSystemTime(new Date('2023-04-02T00:00:00.00Z'));
  });
  afterEach(() => {
    jest.clearAllTimers();
  });
  afterAll(() => {
    jest.useRealTimers();
  });

  describe('should get empty list in', () => {
    test.each([
      {
        key: 'displayDate',
        option: 0,
        input: [{ displayDate: '2022-04-01T23:59:59.99Z', kind: 'SPO' }],
        expected: {
          displayDate: [1, 0, 0, 0, 0, 0, 1],
          kind: [1, 0, 0],
        },
      },
      {
        key: 'displayDate',
        option: 1,
        input: [{ displayDate: '2023-03-31T23:59:59.99Z', kind: 'SPO' }],
        expected: {
          displayDate: [1, 0, 1, 1, 1, 1, 1],
          kind: [1, 0, 0],
        },
      },
      {
        key: 'displayDate',
        option: 2,
        input: [{ displayDate: '2023-03-25T23:59:59.99Z', kind: 'SPO' }],
        expected: {
          displayDate: [1, 0, 0, 1, 1, 1, 1],
          kind: [1, 0, 0],
        },
      },
      {
        key: 'displayDate',
        option: 3,
        input: [{ displayDate: '2023-03-01T23:59:59.99Z', kind: 'SPO' }],
        expected: {
          displayDate: [1, 0, 0, 0, 1, 1, 1],
          kind: [1, 0, 0],
        },
      },
      {
        key: 'displayDate',
        option: 4,
        input: [{ displayDate: '2022-10-01T23:59:59.99Z', kind: 'SPO' }],
        expected: {
          displayDate: [1, 0, 0, 0, 0, 1, 1],
          kind: [1, 0, 0],
        },
      },
      {
        key: 'displayDate',
        option: 5,
        input: [{ displayDate: '2022-04-01T23:59:59.99Z', kind: 'SPO' }],
        expected: {
          displayDate: [1, 0, 0, 0, 0, 0, 1],
          kind: [1, 0, 0],
        },
      },
      {
        key: 'kind',
        option: ['SPO'],
        input: [{ displayDate: '2022-04-01T23:59:59.99Z', kind: 'SPO' }],
        expected: {
          displayDate: [1, 0, 0, 0, 0, 0, 1],
          kind: [1, 0, 0],
        },
      },
      {
        key: 'kind',
        option: ['Mail'],
        input: [{ displayDate: '2022-04-01T23:59:59.99Z', kind: 'Mail' }],
        expected: {
          displayDate: [1, 0, 0, 0, 0, 0, 1],
          kind: [0, 1, 0],
        },
      },
      {
        key: 'kind',
        option: ['Chat'],
        kind: 'SPO',
        input: [{ displayDate: '2022-04-01T23:59:59.99Z', kind: 'Chat' }],
        expected: {
          displayDate: [1, 0, 0, 0, 0, 0, 1],
          kind: [0, 0, 1],
        },
      },
    ])('should get empty items as expected when keys is $key, option is $option', async ({
      input, expected,
    }) => {
      const list = input.map(({ displayDate, kind }) => createSplitViewListSingle({ displayDate, kind: kind as 'SPO' | 'Mail' | 'Chat' }));
      expect(await getEmptyFilterOptions({
        listView: SplitViewListView.LOADING,
        listMessage: SplitViewListMessage.BLANK,
        list,
        activeId: '',
        detailView: SplitViewDetailView.LOADING,
        detailMessage: SplitViewDetailMessage.BLANK,
        detail: undefined,
        context: {
          sort: [],
          filter: [],
        },
        inlineMailAttachments: [],
        chatAttachments: [],
      })).toStrictEqual(
        expected,
      );
    });
  });
});
