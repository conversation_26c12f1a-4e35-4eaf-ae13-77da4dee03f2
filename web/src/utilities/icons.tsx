import * as React from 'react';
import {
  ExcelColorIcon, FilesGenericColoredIcon,
  FilesPdfColoredIcon,
  PowerPointColorIcon,
  WordColorIcon,
  FilesZipIcon,
} from '@fluentui/react-icons-northstar';
import { ValueOf } from './type';

export const DocIcon = {
  PowerPoint: 'ppt',
  Excel: 'excel',
  Word: 'word',
  Pdf: 'pdf',
  Zip: 'zip',
};
export type DocIconType = ValueOf<typeof DocIcon>;

/**
 * ファイル拡張子から対応するアイコンタイプを返却する
 * @param fileExtension
 */
export function getIconTypeByExtension(fileExtension: string | undefined | null): string {
  if (!fileExtension) return '';
  if (/doc|DOC/.test(fileExtension)) {
    return DocIcon.Word;
  }
  if (/xls|XLS/.test(fileExtension)) {
    return DocIcon.Excel;
  }
  if (/ppt|PPT/.test(fileExtension)) {
    return DocIcon.PowerPoint;
  }
  if (/pdf|PDF/.test(fileExtension)) {
    return DocIcon.Pdf;
  }
  if (/zip|ZIP/.test(fileExtension)) {
    return DocIcon.Zip;
  }
  return '';
}

/**
 * アイコンタイプに応じたアイコンコンポーネントを返却する
 * @param type
 */
export function getAttachmentIcon(type: DocIconType | undefined | null): JSX.Element {
  switch (type) {
    case DocIcon.Word:
      return <WordColorIcon />;
    case DocIcon.Excel:
      return <ExcelColorIcon />;
    case DocIcon.PowerPoint:
      return <PowerPointColorIcon />;
    case DocIcon.Pdf:
      return <FilesPdfColoredIcon />;
    case DocIcon.Zip:
      return <FilesZipIcon />;
    default:
      return <FilesGenericColoredIcon />;
  }
}
