import * as React from 'react';
import '@testing-library/jest-dom';
import {
  WordColorIcon,
  ExcelColorIcon,
  PowerPointColorIcon,
  FilesPdfColoredIcon,
  FilesGenericColoredIcon,
  FilesZipIcon,
} from '@fluentui/react-icons-northstar';
import { DocIcon, getAttachmentIcon, getIconTypeByExtension } from './icons';

describe('utilities/icons', () => {

  describe('getIconTypeByExtension', () => {
    it('should return DocIcon.Word', () => {
      expect(getIconTypeByExtension('doc')).toBe(DocIcon.Word);
      expect(getIconTypeByExtension('docx')).toBe(DocIcon.Word);
    });

    it('should return DocIcon.Excel', () => {
      expect(getIconTypeByExtension('xls')).toBe(DocIcon.Excel);
      expect(getIconTypeByExtension('xlsx')).toBe(DocIcon.Excel);
      expect(getIconTypeByExtension('xlsm')).toBe(DocIcon.Excel);
    });

    it('should return DocIcon.PowerPoint', () => {
      expect(getIconTypeByExtension('ppt')).toBe(DocIcon.PowerPoint);
      expect(getIconTypeByExtension('pptx')).toBe(DocIcon.PowerPoint);
    });

    it('should return DocIcon.PDF', () => {
      expect(getIconTypeByExtension('pdf')).toBe(DocIcon.Pdf);
    });

    it('should return DocIcon.Zip', () => {
      expect(getIconTypeByExtension('zip')).toBe(DocIcon.Zip);
    });

    it('should return blank', () => {
      expect(getIconTypeByExtension('txt')).toBe('');
    });
  });

  describe('getAttachmentIcon', () => {
    it('should return WordColorIcon', () => {
      expect(getAttachmentIcon(DocIcon.Word)).toStrictEqual(<WordColorIcon />);
    });

    it('should return ExcelColorIcon', () => {
      expect(getAttachmentIcon(DocIcon.Excel)).toStrictEqual(<ExcelColorIcon />);
    });

    it('should return PowerPointColorIcon', () => {
      expect(getAttachmentIcon(DocIcon.PowerPoint)).toStrictEqual(<PowerPointColorIcon />);
    });

    it('should return FilesPdfColoredIcon', () => {
      expect(getAttachmentIcon(DocIcon.Pdf)).toStrictEqual(<FilesPdfColoredIcon />);
    });

    it('should return FilesZipIcon', () => {
      expect(getAttachmentIcon(DocIcon.Zip)).toStrictEqual(<FilesZipIcon />);
    });

    it('should return FilesGenericColoredIcon', () => {
      expect(getAttachmentIcon('')).toStrictEqual(<FilesGenericColoredIcon />);
    });
  });

});
