import { IChatResponse, TeamsMessageType } from '../types/IChatResponse';

const chatItemBatchResponseConverter = ({ body, id }: { body: IChatResponse, id: string }) => {
  const [type, teamId, chatId] = id.split('|');
  const messageType = type as TeamsMessageType;
  return ({
    ...(body as Omit<IChatResponse, 'messageType'>), messageType, teamId, chatId,
  });
};

export default chatItemBatchResponseConverter;
