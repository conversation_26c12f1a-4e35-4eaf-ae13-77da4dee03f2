import { ISharePointListsSingleResponse } from '../types/ISharePointListsSingleResponse';
import { ISplitViewListSingle } from '../components/domains/split-view/types/ISplitViewListSingle';
import { ISearchWords } from '../types/ISearchWords';
import { ISearchRequestResult } from '../types/ISearchRequestResult';
import { IRepositoryBookmarkQueue, ISearchRequestCache } from '../types/IGeraniumAttaneDB';
import { ISplitViewDetail } from '../components/domains/split-view/types/ISplitViewDetail';

/**
 * テストケース内で使う関数。常にnullでないquerySelectorの結果を返す
 * querySelectorの結果がnullのときはthrowする
 * @param elem
 * @param query
 */
export function queryElem<T extends HTMLElement, R extends Element>(elem: R, query: string): T {
  const $result = elem.querySelector<T>(query);
  if (!$result) throw new Error(`QUERY_SELECTOR_NULL_RESULT: ${query}`);
  return $result;
}

/**
 * テストケース内で使う関数。常にnullでないquerySelectorAllの結果を返す
 * querySelectorの結果がnullのときはthrowする
 * @param elem
 * @param query
 * @param index
 */
export function queryElems<T extends HTMLElement, R extends Element>(
  elem: R, query: string, index: number,
): T {
  const $results = elem.querySelectorAll<T>(query);
  if (!$results[index]) throw new Error('QUERY_SELECTOR_ALL_INDEX_NOT_FOUND');
  return $results[index];
}

/**
 * 検索要求API戻り値のモックデータを生成する
 * @param override
 */
export function createSearchRequestResultReturnValue(
  override?: Partial<ISearchRequestResult>,
): ISearchRequestResult {
  return {
    condition: 'api test',
    conditionKeywords: {
      operator: 'And',
      operand: ['api', 'test'],
    },
    userId: 'user-id-01',
    reqId: 'request-id-01',
    state: 'InProgress',
    results: [],
    ...override,
  };
}

/**
 * 一覧APIのエントリー1件文のモックデータを生成する
 * @param override
 */
export function createListApiSingleEntry(
  override: Partial<ISharePointListsSingleResponse>,
): ISharePointListsSingleResponse {
  return {
    'odata.editLink': 'abcd',
    Title: 'List Api Entry 1',
    GUID: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
    Modified: '2020-12-02T02:39:48.000Z',
    Created: '2021-08-24T03:00:59.000Z',
    category: 'カテゴリ名1',
    Attachments: false,
    ...override,
  };
}

/**
 * 一覧エントリー1件文のモックデータを生成する
 * @param override
 */
export function createSplitViewListSingle(
  override: Partial<ISplitViewListSingle>,
): ISplitViewListSingle {
  return {
    id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
    kind: 'SPO',
    title: 'List Api Entry 1',
    displayDate: '2020-12-02T02:39:48.000Z',
    note: 'カテゴリ名1',
    properties: {
      createdDate: '2021-08-24T03:00:59.000Z',
      hasAttachments: false,
      editLink: 'abcd',
    },
    ...override,
  };
}

/**
 * 詳細記事一件分のモックデータをISplitViewListSingleに変換したものを生成する
 * @param override
 */
export function createSplitViewDetail(
  override: Partial<ISplitViewDetail>,
): ISplitViewDetail {
  return {
    id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
    kind: 'SPO',
    title: 'List Api Entry 1',
    note: '',
    displayDate: '',
    properties: {
      siteUrl: '',
      listUrl: '',
      listId: '',
      listName: '',
      editLink: '',
      createdDate: '',
      updatedDate: '',
      hasAttachments: false,
      categoryKeyName: 'category1',
    },
    ...override,
  };
}

/**
 * お気に入りのエントリー1件文のモックデータを生成する
 * @param override
 */
export function createReposBookmarkItem(
  override: Partial<ISplitViewListSingle>,
): ISplitViewListSingle {
  return {
    id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
    kind: 'SPO',
    title: 'List Api Entry 1',
    note: 'カテゴリ名1',
    displayDate: '2020-12-02T02:39:48.000Z',
    properties: {
      siteUrl: 'siteUrl',
      listUrl: 'listUrl',
      listId: 'listId',
      listName: 'listName',
      editLink: 'abc',
      createdDate: '2021-08-24T03:00:59.000Z',
      updatedDate: '2020-12-02T02:39:48.000Z',
      hasAttachments: false,
    },
    reposCreatedDate: '2020-12-02T02:39:48.000Z',
    reposUpdatedDate: '2020-12-02T02:39:48.000Z',
    ...override,
  };
}

/**
 * お気に入りキューエントリーのモックデータを生成する
 * @param override
 */
export function createReposBookmarkQueue(
  override: {
    type?: 'PUT' | 'DELETE',
    data?: Partial<ISplitViewListSingle>,
    date?: Date,
  },
): IRepositoryBookmarkQueue {
  return {
    type: override.type || 'PUT',
    data: createReposBookmarkItem(override?.data ?? {}),
    date: override.date || new Date('2020-12-02T02:39:48.000Z'),
  };
}

/**
 * 検索要求キャッシュのモックデータを生成する
 * @param override
 */
export function createReposSearchRequestCache(
  override?: Partial<ISearchRequestCache>,
): ISearchRequestCache {
  return {
    status: 'RequestInProgress',
    result: {
      condition: 'api test',
      conditionKeywords: {
        operator: 'And',
        operand: ['api', 'test'],
      },
      userId: 'user-id-01',
      reqId: 'request-id-01',
      state: 'InProgress',
      results: [],
      ...override,
    },
  };
}

/**
 * ISearchWords型を生成する
 * @param userInputs
 * @param synonyms
 */
export function createSearchWordsMock(userInputs: string, synonyms = {}): ISearchWords {
  return {
    userInputs,
    synonyms,
    combinedWords: userInputs,
  };
}
