import { handleUnknownAppInfoMessages, handleUnknownString } from './type';

describe('handleUnknownString', () => {
  describe('when the param is not string', () => {
    it('should return a blank', () => {
      expect(handleUnknownString(null)).toBe('');
      expect(handleUnknownString(undefined)).toBe('');
      expect(handleUnknownString({})).toBe('');
      expect(handleUnknownString([])).toBe('');
      expect(handleUnknownString(0)).toBe('');
      expect(handleUnknownString(1)).toBe('');
      expect(handleUnknownString(true)).toBe('');
      expect(handleUnknownString(false)).toBe('');
    });
  });

  describe('when the param is string', () => {
    it('should return the param as is', () => {
      expect(handleUnknownString('')).toBe('');
      expect(handleUnknownString('abc')).toBe('abc');
      expect(handleUnknownString('true')).toBe('true');
      expect(handleUnknownString('false')).toBe('false');
      expect(handleUnknownString('1')).toBe('1');
      expect(handleUnknownString('0')).toBe('0');
    });
  });
});

describe('handleUnknownAppInfoMessages', () => {
  describe('when the param is not IAppInfoMessages', () => {
    it('should return a empty object', () => {
      expect(handleUnknownAppInfoMessages(null)).toEqual(null);
      expect(handleUnknownAppInfoMessages(undefined)).toEqual(null);
      expect(handleUnknownAppInfoMessages('abc')).toEqual(null);
      expect(handleUnknownAppInfoMessages(0)).toEqual(null);
      expect(handleUnknownAppInfoMessages(1)).toEqual(null);
      expect(handleUnknownAppInfoMessages(true)).toEqual(null);
      expect(handleUnknownAppInfoMessages(false)).toEqual(null);
      expect(handleUnknownAppInfoMessages({})).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: '',
      })).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: null,
      })).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: undefined,
      })).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: 'abc',
      })).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: 'abc',
        searchCategory: 'abc',
        searchableItems: 'abc',
        searchCriteria: 'abc',
        sortOrder: 'abc',
        contactUs: 'abc',
      })).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: 'abc',
        searchCategory: 'abc',
        searchableItems: 'abc',
        searchCriteria: 'abc',
        sortOrder: 'abc',
        contactUs: 'abc',
      })).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: 'abc',
        searchCategory: ['abc'],
        searchableItems: ['abc'],
        searchCriteria: ['abc'],
        sortOrder: ['abc'],
        contactUs: ['abc'],
      })).toEqual(null);
      expect(handleUnknownAppInfoMessages({
        overview: 'abc',
        searchCategory: ['abc'],
        searchableItems: ['abc'],
        searchCriteria: ['abc'],
        sortOrder: ['abc'],
        contactUs: ['abc'],
      })).toEqual(null);
    });
  });

  describe('when the param is IAppInfoMessages', () => {
    it('should return the param as is', () => {
      expect(handleUnknownAppInfoMessages({
        overview: ['abc'],
      })).toEqual({
        overview: ['abc'],
      });
      expect(handleUnknownAppInfoMessages({
        overview: ['abc'],
        searchCategory: ['abc', 'edf'],
        searchableItems: ['abc', 'edf'],
        searchCriteria: ['abc'],
        sortOrder: ['abc'],
        contactUs: ['abc'],
      })).toEqual({
        overview: ['abc'],
        searchCategory: ['abc', 'edf'],
        searchableItems: ['abc', 'edf'],
        searchCriteria: ['abc'],
        sortOrder: ['abc'],
        contactUs: ['abc'],
      });
      expect(handleUnknownAppInfoMessages({
        overview: [],
        searchCategory: ['abc'],
        searchableItems: ['abc'],
        searchCriteria: ['abc'],
        sortOrder: ['abc'],
        contactUs: ['abc'],
      })).toEqual({
        overview: [],
        searchCategory: ['abc'],
        searchableItems: ['abc'],
        searchCriteria: ['abc'],
        sortOrder: ['abc'],
        contactUs: ['abc'],
      });
      expect(handleUnknownAppInfoMessages({
        overview: [],
        searchCategory: [],
        searchableItems: [],
        searchCriteria: [],
        sortOrder: [],
        contactUs: [],
      })).toEqual({
        overview: [],
        searchCategory: [],
        searchableItems: [],
        searchCriteria: [],
        sortOrder: [],
        contactUs: [],
      });
      expect(handleUnknownAppInfoMessages({
        overview: [''],
        contactUs: [],
      })).toEqual({
        overview: [''],
        contactUs: [],
      });
      expect(handleUnknownAppInfoMessages({
        overview: ['abc'],
        contactUs: [],
      })).toEqual({
        overview: ['abc'],
        contactUs: [],
      });
      expect(handleUnknownAppInfoMessages({
        overview: ['abc'],
        contactUs: [],
        testParam: ['test1', 'test2'],
      })).toEqual({
        overview: ['abc'],
        contactUs: [],
        testParam: ['test1', 'test2'],
      });
    });
  });
});
