import dayjs, { UnitType } from 'dayjs';
import 'dayjs/locale/ja';
import { ValueOf } from './type';

type NullableFunctionResult<TParam, TResult> = (
  (_: TParam) => TResult
) & (
    (_: null) => null
  ) & (
    (_: undefined) => null
  ) & (
    (_: TParam | null | undefined) => TResult | null
  );

// テスト環境下では日本語ロケールを指定する
// ブラウザ環境では実行環境のデフォルトを指定したいが、Teamsアプリのmanifestが日本語環境対応されていないため一旦固定している
const TEST_ENV_LOCALE = process.env.JEST_WORKER_ID ? 'ja-JP' : 'ja-JP';

export const DateFormatTemplate = {
  YearMonthDayTimeWithWeekday: 'YYYY/M/D（dd）HH:mm',
  YearMonthDay: 'YYYY/M/D',
  MonthDay: 'M/D',
};
export type DateFormatTemplateType = ValueOf<typeof DateFormatTemplate>;

/**
 * 入力値をdate型か空文字列にする
 * @param d
 */
const toDate = (d: Date | string | null | undefined): Date | '' => {
  if (d instanceof Date) return d;
  if (d == null) return '';
  if (!d) return '';
  const date = new Date(d);
  // 評価不能な場合は空文字列を返却
  if (Number.isNaN(date)) return '';
  return date;
};

// 実行者のロケール、タイムゾーンに応じた日付文字列を取得する
export const toDateString = ((
  d: Date | string | null | undefined,
  formatType?: DateFormatTemplateType | undefined,
): string => {
  const date = toDate(d);
  if (!date) return '';

  return formatType
    ? dayjs(date).locale('ja').format(formatType)
    : new Intl.DateTimeFormat(TEST_ENV_LOCALE).format(date);
});

// 実行者のロケール、タイムゾーンに応じた時刻文字列を取得する
export const toTimeString = ((d: Date | string | null | undefined): string => {
  const date = toDate(d);
  if (!date) return '';
  return new Intl.DateTimeFormat(TEST_ENV_LOCALE, { timeStyle: 'short' }).format(date);
});

// 実行者のタイムゾーンに応じた日時の日付部分を取得する
export const toDatePart = ((d: Date | null | undefined): Date | null => {
  if (d === null || d === undefined) {
    return null;
  }
  const work = new Date(d); // setHoursでインスタンスの中身が変化するため、コピーを作成する
  work.setHours(0, 0, 0, 0);
  return work;
}) as NullableFunctionResult<Date, Date>;

// 実行者のタイムゾーンに応じた日付の時刻部分を取得する
export const toTimePart = ((d: Date | null | undefined): Date | null => {
  if (d === null || d === undefined) {
    return null;
  }
  return new Date(d.valueOf() - toDatePart(d).valueOf());
}) as NullableFunctionResult<Date, Date>;

// 実行者のタイムゾーンに応じた日時の日付部分のYYYY-MM-DD表記の文字列を取得する
export const toApiDate = ((d: Date | null | undefined): string | null => {
  const valuesPerMinute = 60000; // 1分に対応するvalue
  if (d === null || d === undefined) {
    return null;
  }
  // toISOString はUTCにおけるYYYY-MM-DDTHH:MM:SSZ形式を返すため、タイムゾーンオフセットを引いてUTCにおける当該時刻に変換する
  return new Date(d.valueOf() - d.getTimezoneOffset() * valuesPerMinute).toISOString().split('T')[0];
}) as NullableFunctionResult<Date, string>;

const MILLISECOND = 1000;
const MINUTE = MILLISECOND * 60;
const HOUR = MINUTE * 60;
const PER_MINUTES = 60 * MILLISECOND;
const PER_HOURS = 60 * PER_MINUTES;

// 相対日時を取得する
export function getRelativeDate(
  d: number | string | undefined | null,
  format?: DateFormatTemplateType | undefined,
): string {
  if (d == null) return '';
  const date = typeof d === 'number' ? d : new Date(d).getTime();

  // 評価不能な場合は空文字列を返却
  if (Number.isNaN(date)) return '';

  const relativeTimeFormat = new Intl.RelativeTimeFormat(TEST_ENV_LOCALE);
  const timeDifference = new Date().getTime() - date;

  const MINUTES_DISPLAY_MAX_TIME = 50 * MINUTE; // 50分
  const HOURS_DISPLAY_MAX_TIME = HOUR; // 60分
  const RELATIVE_DISPLAY_MAX_TIME = 24 * HOUR; // 24時

  // MINUTES_DISPLAY_MAX_TIME以内はminutesによる相対表示
  if (timeDifference < MINUTES_DISPLAY_MAX_TIME) {
    return relativeTimeFormat.format(-(timeDifference / PER_MINUTES).toFixed(0), 'minutes');
  }

  // HOURS_DISPLAY_MAX_TIME以上HOURS_DISPLAY_MAX_TIME未満はhoursによる相対表示
  if (timeDifference > MINUTES_DISPLAY_MAX_TIME && timeDifference < RELATIVE_DISPLAY_MAX_TIME) {
    return relativeTimeFormat.format(timeDifference < HOURS_DISPLAY_MAX_TIME
      ? -(Math.ceil(timeDifference / PER_HOURS))
      : -(Math.floor(timeDifference / PER_HOURS)), 'hours');
  }

  // それ以外は絶対表示
  return toDateString(new Date(date), format);
}

// 年月日を取得する
export function getYearMonthDay(
  d: number | string | undefined | null,
): string {
  if (d == null) return '';
  const date = typeof d === 'number' ? d : new Date(d).getTime();

  // 評価不能な場合は空文字列を返却
  if (Number.isNaN(date)) return '';

  // それ以外は絶対表示
  return toDateString(new Date(date), DateFormatTemplate.YearMonthDay);
}

/**
 * 2つの日付が同値がどうかを判定する
 * @param a
 * @param b
 * @param unit
 */
export function isSameDates(
  a: Date | number | string | undefined | null,
  b: Date | number | string | undefined | null,
  unit: UnitType,
): boolean {
  if (a == null || b == null) return false;
  const dayA = dayjs(a);
  const dayB = dayjs(b);
  if (!dayA.isValid() || !dayB.isValid()) return false;
  return dayA.isSame(dayB, unit);
}

/**
 * baseDateにoffset時間を加える
 * @param baseDate 基準日時
 * @param offsetHours オフセット時間(hour)
 */
export function getOffsetDate(baseDate: Date, offsetHours: number): Date;
export function getOffsetDate(baseDate: dayjs.Dayjs, offsetHours: number): dayjs.Dayjs;
export function getOffsetDate(
  baseDate: dayjs.Dayjs | Date, offsetHours: number,
): dayjs.Dayjs | Date {

  const base = dayjs(baseDate);
  const offset = base.add(offsetHours, 'hours');

  if (baseDate instanceof Date) {
    return offset.toDate();
  }

  return offset;
}

/**
 * 今日の00:00丁度の日付を取得する
 */
export function getZeroToday(): Date {
  return dayjs()
    .set('hours', 0)
    .set('minutes', 0)
    .set('seconds', 0)
    .set('milliseconds', 0)
    .toDate();
}

/**
 * to check the date or date-string is valid
 * @param date
 */
export function isInvalidDate(date: Date | string | null | undefined): boolean {
  if (!date) return true;
  if (typeof date === 'string') {
    const d = new Date(date);
    return Number.isNaN(d.getTime());
  }
  return Number.isNaN(date.getTime());
}

/**
 * normalize date string can be UTC or not UTC format
 * @param date
 */
export function normalizeUtcOrNotUtcDate(date: string | undefined | null): string {
  if (isInvalidDate(date)) return '';
  return dayjs(date).toISOString();
}
