import parse from 'html-react-parser';
import innerText from 'react-innertext';
import { ISharePointListItemResponseWithAttachment } from '../types/ISharePointListItemResponseWithAttachment';
import { ISharePointListsSingleResponse } from '../types/ISharePointListsSingleResponse';
import { ISplitViewDetail } from '../components/domains/split-view/types/ISplitViewDetail';
import { ISplitViewListSingle } from '../components/domains/split-view/types/ISplitViewListSingle';
import { normalizeUtcOrNotUtcDate } from './date';
import { ISharePointListsResponse } from '../mocks/useSharePointApiMock';
import { IApiDataSource, Operand, SearchConditionTree } from '../types/ISearchRequestResult';
import {
  IChatProperties, IMailProperties, ISearchResult, ISpoProperties, SearchResultProperties,
} from '../types/ISearchResult';
import { IRemoteContext } from '../types/IRemoteContext';
import { IContext, ISortOrder } from '../types/IContext';
import { DataSourceKind } from '../types/DataSourceKind';
import { IMailResponse } from '../types/IMailResponse';
import { IChatResponse } from '../types/IChatResponse';
import { IChatAttachment } from '../components/domains/split-view/types/IChatAttachment';

function convertToNonOptional<T>(obj: Partial<T>): T {
  const convertedObj: T = {} as T;
  const keys = Object.keys(obj) as (keyof T)[];

  keys.forEach((key) => {
    const value = obj[key];
    convertedObj[key] = value !== undefined ? value as T[keyof T] : '' as unknown as T[keyof T];
  });

  return convertedObj;
}

/**
 * 渡されたPropertiesからSPOのPropertiesか判別する(型ガード)
 *
 * @export
 * @param {(SearchResultProperties | undefined)} source
 * @return {*}  {source is ISpoProperties}
 */
export function isSpoProperties(
  source: SearchResultProperties | undefined,
): source is ISpoProperties {
  if (!source) return false;
  return 'listUrl' in source;
}

/**
 * 渡されたPropertiesからメールのPropertiesか判別する
 *
 * @export
 * @param {(SearchResultProperties | undefined)} source
 * @return {*}  {source is IMailProperties}
 */
export function isMailProperties(
  source: SearchResultProperties | undefined,
): source is IMailProperties {
  if (!source) return false;
  return 'receivedDateTime' in source;
}

export function isChatProperties(
  source: SearchResultProperties | undefined,
): source is IChatProperties {
  return source !== undefined && 'messageType' in source;
}

/**
 * リプライカード等を除いたチャットに紐づく添付ファイルのみを取得する
 *
 * microsoft.graph.chatMessageAttachment参照
 * contentとcontentUrlは排他関係にある
 * contentTypeが'reference'のものがURL参照するコンテンツ(=添付ファイル)
 */
export function filterChatAttachments(
  chatAttachments: IChatAttachment[],
) {
  return chatAttachments.filter(
    (attachment) => attachment.contentType === 'reference' && !!attachment.contentUrl,
  );
}

/**
 * SharePoint APIから取得したレスポンスを一覧画面に表示できる型に変換する
 * @param source
 */
export function convertSPListSingleToSplitViewListSingle(
  source: ISharePointListsSingleResponse[],
  dataSource: IApiDataSource,
): ISplitViewListSingle[] {

  return source.map((row) => ({
    id: row.GUID ?? '',
    title: row.Title ?? '',
    kind: dataSource.kind ?? DataSourceKind.Other,
    displayDate: normalizeUtcOrNotUtcDate(row.Modified),
    note: row.category ?? '',
    properties: {
      siteUrl: dataSource.properties?.site,
      listUrl: dataSource.properties?.listUrl,
      listId: dataSource.properties?.list,
      listName: dataSource.properties?.listName,
      editLink: row['odata.editLink'] ?? '',
      categoryKeyName: dataSource.properties?.category,
      hasAttachments: row.Attachments ?? false,
      createdDate: normalizeUtcOrNotUtcDate(row.Created),
      updatedDate: normalizeUtcOrNotUtcDate(row.Modified),
    } as ISpoProperties,
  }));
}

/**
 * Graph APIから取得したメールレスポンスを一覧画面に表示できる型に変換する
 */
export function convertMailResponseToSplitViewListSingle(
  source: IMailResponse[],
  dataSource: IApiDataSource,
): ISplitViewListSingle[] {
  return source.map((row) => ({
    id: row.id ?? '',
    title: row.subject ?? '',
    kind: dataSource.kind ?? DataSourceKind.Mail,
    displayDate: normalizeUtcOrNotUtcDate(row.receivedDateTime),
    note: row.from?.emailAddress?.name ?? row.from?.emailAddress?.address ?? '',
    properties: {
      from: row.from,
      sender: row.sender,
      toRecipients: row.toRecipients,
      ccRecipients: row.ccRecipients,
      bccRecipients: row.bccRecipients,
      hasAttachments: row.hasAttachments ?? false,
      sentDateTime: normalizeUtcOrNotUtcDate(row.sentDateTime),
      receivedDateTime: normalizeUtcOrNotUtcDate(row.receivedDateTime),
      updatedDate: normalizeUtcOrNotUtcDate(row.lastModifiedDateTime),
      isDraft: row.isDraft,
    } as IMailProperties,
  }));
}

/**
 * APIから返ってくるSearchConditionTree型からoperatorを除いた検索文字列の配列を得る
 */
export function extractSearchKeywordsArray(tree: string | SearchConditionTree): string[] {
  if (typeof tree === 'string') return [tree];
  return tree.operand.flatMap((node: Operand) => extractSearchKeywordsArray(node));
}

/**
 * 本文から与えられたキーワードに一致する箇所の前後を含む部分文字列を取得する
 * マッチしたキーワードの前8文字(8文字に満たない場合は全量)と後ろすべてを取得
 * キーワードの前が文章の途中だった場合は文頭に3点リーダを付与
 */
export function getContentSubstring(
  content: string,
  keywords: string[],
): string {
  let result = '';

  keywords.forEach((keyword) => {
    if (!result) {
      const index = content.indexOf(keyword);
      if (index !== -1) {
        const startIndex = Math.max(index - 8, 0);
        const endIndex = content.length;
        result = `${startIndex !== 0 ? '…' : ''}${content.slice(startIndex, endIndex)}`;
      }
    }
  });

  return result;
}

export function extractInnerText(bodyContent: string): string {
  const parsed = parse(bodyContent);
  const contentText = innerText(parsed)
    // 連続するスペースは1個に置換
    .replace(/\s+/g, ' ');
  return contentText;
}

/**
 * Chatのbody.contentから検索にヒットしたメッセージの一部を抽出する
 */
export function extractChatContentPreview(
  bodyContent: string,
  keywords: string[],
) {
  const contentText = extractInnerText(bodyContent);
  // ヒットしたメッセージ周辺の文字列の取得
  const substring = getContentSubstring(contentText, keywords);
  return substring !== '' ? substring : contentText;
}
/**
 * Chatのbody.contentから検索にヒットしたメッセージの一部を抽出する
 */
export function extractChatContentPreviewForAI(
  bodyContent: string,
) {
  const contentText = extractInnerText(bodyContent);
  return contentText;
}

/**
 * Graph APIから取得したチャットレスポンスを一覧画面に表示できる型に変換する
 */
export function convertChatResponseToSplitViewSingle(
  source: IChatResponse[],
  dataSource: IApiDataSource,
  keywords: string[],
): ISplitViewListSingle[] {
  if (dataSource.kind !== 'Chat') throw new Error('異なるkindです');
  return source
    .filter((item) => item.from && (item.from.user || item.from.application))
    .filter((item) => item.body && (item.body.content))
    .map((item) => ({
      id: item.id,
      title: item.from.user?.displayName ?? item.from.application?.displayName ?? '',
      kind: dataSource.kind ?? 'Other',
      body: item.body.content,
      displayDate: normalizeUtcOrNotUtcDate(item.createdDateTime),
      note: extractChatContentPreview(item.body.content, keywords),
      properties: {
        chatAttachments: item.attachments,
        from: item.from.user ?? item.from.application,
        updatedDate: item.lastModifiedDateTime,
        lastModifiedDateTime: item.lastModifiedDateTime,
        hasAttachments: filterChatAttachments(item.attachments).length > 0,
        chatId: item.chatId,
        teamId: item.teamId,
        contentType: item.body.contentType,
        messageType: item.messageType,
        bookmarkNote: extractInnerText(item.body.content),
      } as IChatProperties,
    }));
}

/**
 * Graph APIから取得したメールレスポンスを詳細画面に表示できる型に変換する
 */
export function convertMailListItemToSplitViewDetail(
  source: IMailResponse,
  activeItem: ISplitViewListSingle,
): ISplitViewDetail {
  return {
    id: source.id ?? '',
    title: source.subject ?? '',
    kind: activeItem.kind,
    body: source.body?.content,
    displayDate: normalizeUtcOrNotUtcDate(source.receivedDateTime),
    note: source.from?.emailAddress?.name ?? source.from?.emailAddress?.address,
    reposCreatedDate: activeItem.reposCreatedDate,
    reposUpdatedDate: activeItem.reposUpdatedDate,
    properties: {
      mailAttachments: source.attachments,
      from: source.from,
      sender: source.sender,
      toRecipients: source.toRecipients,
      ccRecipients: source.ccRecipients,
      bccRecipients: source.bccRecipients,
      hasAttachments: source.hasAttachments ?? false,
      contentType: source.body?.contentType,
      webLink: source.webLink ?? '',
      sentDateTime: normalizeUtcOrNotUtcDate(source.sentDateTime),
      receivedDateTime: normalizeUtcOrNotUtcDate(source.receivedDateTime),
      updatedDate: normalizeUtcOrNotUtcDate(source.lastModifiedDateTime),
      isDraft: source.isDraft,
    } as IMailProperties,
  };
}

/**
 * SharePoint APIから取得したレスポンスを詳細画面に表示できる型に変換する
 * @param source
 * @param editLink
 */
export function convertSPListItemToSplitViewDetail(
  source: ISharePointListItemResponseWithAttachment,
  activeItem: ISplitViewListSingle,
): ISplitViewDetail {
  return {
    id: source?.GUID ?? '',
    kind: activeItem.kind,
    title: source.Title ?? '',
    note: source.category ?? activeItem.note ?? '',
    displayDate: source.Modified ?? '',
    body: source.docBody ?? '',
    reposCreatedDate: activeItem.reposCreatedDate,
    reposUpdatedDate: activeItem.reposUpdatedDate,
    properties: {
      ...activeItem.properties,
      ...(isSpoProperties(activeItem.properties) ? {
        editLink: source['odata.editLink'] ?? '',
        createdDate: normalizeUtcOrNotUtcDate(source.Created),
      } : {}),
      hasAttachments: (source.AttachmentFiles ?? []).length > 0,
      updatedDate: normalizeUtcOrNotUtcDate(source.Modified),
    },
    // SPOデータ固有の型
    itemId: source?.Id ?? null,
    expiredDate: source.presentPeriod ?? '',
  };
}

/**
 * メールの詳細情報から一覧アイテムに変換する。
 */
export function convertMailDetailToMailSingle(detail: ISplitViewDetail): ISplitViewListSingle {
  if (!detail?.properties) throw new Error('properties is required');

  const convertedDetail: Required<Omit<ISplitViewDetail, 'reposCreatedDate' | 'reposUpdatedDate'>> & {
    reposCreatedDate?: string, reposUpdatedDate?: string
  } = convertToNonOptional(detail);

  const props = detail.properties as IMailProperties;
  return {
    ...convertedDetail,
    note: props.from?.emailAddress?.name ?? props.from?.emailAddress?.address ?? '',
    properties: {
      from: props.from,
      sender: props.sender,
      toRecipients: props.toRecipients,
      ccRecipients: props.ccRecipients,
      bccRecipients: props.bccRecipients,
      hasAttachments: props.hasAttachments ?? false,
      sentDateTime: props.sentDateTime,
      receivedDateTime: props.receivedDateTime,
      updatedDate: props.updatedDate,
    } as IMailProperties,
  };
}

/**
 * チャットの詳細情報から一覧アイテムに変換する。
 */
export function convertChatDetailToChatSingle(detail: ISplitViewDetail): ISplitViewListSingle {
  if (!detail?.properties) throw new Error('properties is required');

  const convertedDetail: Required<Omit<ISplitViewDetail, 'reposCreatedDate' | 'reposUpdatedDate'>> & {
    reposCreatedDate?: string, reposUpdatedDate?: string
  } = convertToNonOptional(detail);

  const props = detail.properties as IChatProperties;
  return {
    ...convertedDetail,
    note: extractInnerText(detail.body ?? ''),
    properties: {
      chatAttachments: props.chatAttachments,
      from: props.from,
      updatedDate: props.updatedDate,
      lastModifiedDateTime: props.lastModifiedDateTime,
      hasAttachments: props.hasAttachments,
      chatId: props.chatId,
      teamId: props.teamId,
      contentType: props.contentType,
      messageType: props.messageType,
    },
  };
}

/**
 * SPOの詳細情報から一覧アイテムに変換する。
 * @param detail SPOの詳細情報。
 */
export function convertSPODetailToSPOSingle(detail: ISplitViewDetail): ISplitViewListSingle {
  if (!detail?.properties) throw new Error('properties is required');

  const convertedDetail: Required<Omit<ISplitViewDetail, 'reposCreatedDate' | 'reposUpdatedDate'>> & {
    reposCreatedDate?: string, reposUpdatedDate?: string
  } = convertToNonOptional(detail);

  const props = detail.properties as ISpoProperties;
  return {
    ...convertedDetail,
    note: detail.note ?? '',
    properties: {
      siteUrl: props.siteUrl,
      listUrl: props.listUrl,
      listId: props.listId,
      listName: props.listName,
      editLink: props.editLink,
      hasAttachments: props.hasAttachments,
      createdDate: props.createdDate,
      updatedDate: props.updatedDate,
      categoryKeyName: props.categoryKeyName,
    } as ISpoProperties,
  };
}

export function convertChatItemToSplitViewDetail(
  source: IChatResponse,
  activeItem: ISplitViewDetail,
  detailedTitle: string,
): ISplitViewDetail {
  return {
    id: activeItem.id,
    kind: activeItem.kind,
    title: activeItem.title,
    note: extractInnerText(source.body.content),
    displayDate: activeItem.displayDate,
    body: source.body.content,
    reposCreatedDate: activeItem.reposCreatedDate,
    reposUpdatedDate: activeItem.reposUpdatedDate,
    properties: {
      ...(activeItem.properties as IChatResponse),
      chatAttachments: source.attachments,
      from: source.from.user ?? source.from.application,
      contentType: source.body.contentType,
      subject: source.subject,
      detailedTitle,
      mentions: source.mentions,
      reactions: source.reactions,
      replyToId: source.replyToId,
      teamChatType: source.teamChatType,
    },
  };
}

function appendDefaultCategory(properties?: ISpoProperties): ISpoProperties {
  return {
    ...properties,
    categoryKeyName: properties?.categoryKeyName ?? 'category1',
  };
}

/**
 * お気に入りデータに不足情報があったときに補完する
 * @param source
 */
export function fillUpLackedData(
  source: ISplitViewListSingle,
): ISplitViewListSingle {
  const now = normalizeUtcOrNotUtcDate(new Date().toISOString());
  return {
    id: source.id,
    kind: source.kind,
    title: source.title,
    note: source.note,
    displayDate: source.displayDate,
    properties:
      isSpoProperties(source.properties)
        ? appendDefaultCategory(source.properties) : source.properties,
    reposCreatedDate: source.reposCreatedDate ?? now,
    reposUpdatedDate: source.reposUpdatedDate ?? now,
  };
}

function getBookmarkNote(item: ISplitViewListSingle | ISplitViewDetail | undefined): string {
  if (!item) return '';
  switch (item.kind) {
    case 'Chat':
      return (item.properties as IChatProperties)?.bookmarkNote ?? item.note ?? '';
    case 'Mail':
    case 'SPO':
    default:
      return item.note ?? '';
  }
}

/**
 * 画面表示用データ型からお気に入り登録に必要なデータのみを抽出する
 * @param source
 */
export function convertSplitViewItemToBookmarkItem(
  source: ISplitViewListSingle | ISplitViewDetail | undefined,
): ISplitViewListSingle {
  return {
    id: source?.id ?? '',
    kind: source?.kind ?? DataSourceKind.Other,
    title: source?.title ?? '',
    note: getBookmarkNote(source),
    displayDate: source?.displayDate ?? '',
    reposCreatedDate: source?.reposCreatedDate,
    reposUpdatedDate: source?.reposUpdatedDate,
    properties: {
      ...(isSpoProperties(source?.properties) ? {
        siteUrl: source?.properties.siteUrl,
        listUrl: source?.properties.listUrl,
        listId: source?.properties.listId,
        listName: source?.properties.listName,
        editLink: source?.properties?.editLink,
        createdDate: normalizeUtcOrNotUtcDate(source?.properties?.createdDate),
        categoryKeyName: source?.properties?.categoryKeyName ?? 'category1',
      } : {}),
      ...(isMailProperties(source?.properties) ? {
        from: source?.properties.from,
        sender: source?.properties.sender,
        toRecipients: source?.properties.toRecipients,
        ccRecipients: source?.properties.ccRecipients,
        bccRecipients: source?.properties.bccRecipients,
        sentDateTime: source?.properties.sentDateTime,
        receivedDateTime: source?.properties.receivedDateTime,
        webLink: source?.properties.webLink,
      } : {}),
      ...(isChatProperties(source?.properties) ? {
        teamId: source?.properties.teamId,
        chatId: source?.properties.chatId,
        from: source?.properties.from,
        createdDateTime: source?.properties.createdDateTime,
        lastModifiedDateTime: source?.properties.lastModifiedDateTime,
        contentType: source?.properties.contentType,
        messageType: source?.properties.messageType,
        subject: source?.properties.subject,
        detailedTitle: source?.properties.detailedTitle,
        ...source?.properties,
      } : {}),
      hasAttachments: source?.properties?.hasAttachments ?? false,
      updatedDate: normalizeUtcOrNotUtcDate(source?.properties?.updatedDate),
    },
  };
}

/**
 * 一覧表示データ型をキャッシュ保存するための型に変換する
 * @param source
 */
export function convertSplitViewListSingleToSearchResult(
  source: ISplitViewListSingle[],
): ISearchResult[] {

  return source.map((row) => ({
    id: row.id ?? '',
    kind: row.kind ?? DataSourceKind.Other,
    title: row.title ?? '',
    note: row.note ?? '',
    displayDate: row.displayDate ?? '',
    properties: {
      ...(isSpoProperties(row.properties) ? {
        siteUrl: row.properties.siteUrl,
        listUrl: row.properties.listUrl,
        listId: row.properties.listId,
        listName: row.properties.listName,
        editLink: row.properties?.editLink,
        createdDate: row.properties?.createdDate,
        categoryKeyName: row.properties?.categoryKeyName,
      } : {}),
      ...(isMailProperties(row.properties) ? {
        from: row.properties.from,
        sender: row.properties.sender,
        toRecipients: row.properties.toRecipients,
        ccRecipients: row.properties.ccRecipients,
        bccRecipients: row.properties.bccRecipients,
        sentDateTime: row.properties.sentDateTime,
        receivedDateTime: row.properties.receivedDateTime,
        webLink: row.properties.webLink,
      } : {}),
      ...(isChatProperties(row.properties) ? {
        ...row?.properties,
      } : {}),
      hasAttachments: row.properties?.hasAttachments ?? false,
      updatedDate: row.properties?.updatedDate,
    },
  }));
}

/**
 * IRemoteContextをISortOrder[]に整形する
 * @param source
 */
export function convertContextToSortContext(
  source: IRemoteContext,
): ISortOrder[] {
  return source?.sort ? JSON.parse(source.sort) : [];
}

/**
 * IRemoteContextをパースしてIContextにして返す
 * @param source
 */
export function parseContext(
  source: IRemoteContext,
): IContext {
  return Object.fromEntries(
    Object.entries(source).map(([key, value]) => {
      try {
        return [key, JSON.parse(value)];
      } catch (e) {
        return [key, value];
      }
    }),
  ) as IContext;
}

/**
 * ISplitViewListSingleにお気に入り作成/更新日付をセットする
 * @param source
 */
export function attachReposDateToISplitViewListSingle(
  source: ISplitViewListSingle,
): ISplitViewListSingle {

  const date = new Date().toISOString();

  return {
    ...source,
    reposCreatedDate: source.reposCreatedDate ?? date,
    reposUpdatedDate: source.reposUpdatedDate ?? date,
  };
}

/**
 * make a new response whose category is remapped with the other column
 * that specified with the environment variable
 * @param post a SharePoint single list item response
 */
export function remapPostCategorySingle(
  post: ISharePointListsSingleResponse,
  category: string,
): ISharePointListsSingleResponse {
  const categoryName = category;
  if (!categoryName) {
    return post;
  }

  const postCategory = post[categoryName];
  if (typeof postCategory !== 'string') {
    return {
      ...post,
      category: '',
    };
  }

  return {
    ...post,
    category: postCategory ?? '',
  };
}

/**
 * make a new response whose category is remapped with the other column
 * that specified with the environment variable
 * @param res a SharePoint list response
 */
export function remapPostCategory(
  res: ISharePointListsResponse,
  category: string,
): ISharePointListsResponse {
  if (!Array.isArray(res.value)) {
    return res;
  }

  return {
    ...res,
    value: res.value.map((single) => remapPostCategorySingle(single, category)),
  };
}
