import { isPC, isSP } from './mediaQuery';
import mockMatchMedia from '../mocks/match-media';

const matchMediaMock = mockMatchMedia();

describe('utilities/mediaQuery', () => {
  describe('isPC', () => {
    describe('when the window.matchMedia() returns false ', () => {
      beforeAll(() => {
        matchMediaMock.mockReturnValue({ matches: false });
      });
      it('should return false', () => {
        expect(isPC()).toBe(false);
      });
    });

    describe('when the window.matchMedia() returns true ', () => {
      beforeAll(() => {
        matchMediaMock.mockReturnValue({ matches: true });
      });
      it('should return true', () => {
        expect(isPC()).toBe(true);
      });
    });
  });

  describe('isSP', () => {
    describe('when the window.matchMedia() returns false ', () => {
      beforeAll(() => {
        matchMediaMock.mockReturnValue({ matches: false });
      });
      it('should return false', () => {
        expect(isSP()).toBe(false);
      });
    });

    describe('when the window.matchMedia() returns true ', () => {
      beforeAll(() => {
        matchMediaMock.mockReturnValue({ matches: true });
      });
      it('should return true', () => {
        expect(isSP()).toBe(true);
      });
    });
  });
});
