import { IAppInfoMessages } from '../types/IAppInfoMessages';
import { handleUnknownAppInfoMessages, handleUnknownNumber, handleUnknownString } from './type';

// define the global type to pick values from window.Geranium
// the values inside the Geranium object are set in public/variables.js
declare global {
  interface Window {
    Geranium: {
      routePrefix: unknown | string,
      connectionString: unknown | string,
      spoHostName: unknown | string,
      companyName: unknown | string,
      defaultRetryCounts: unknown | number,
      retryRegulationTime: unknown | number,
      appInfoMessages: unknown | IAppInfoMessages,
      appAIInfoMessages: unknown | IAppInfoMessages,
      retriableTime: unknown | number,
      additionalWaitingMsec: unknown | number,
      batchRequestChunkSize: number,
      bookmarkListWaitingTime: number,
      companyRegulationSiteUrl: string,
      company: string,
      aiSearchEnabled:boolean
    }
  }
}

interface IEnvironment {
  REACT_APP_TITLE: string;
  REACT_APP_API_URL: string;
  REACT_APP_ROUTE_PREFIX: string;
  REACT_APP_CONNECTION_STRING: string;
  REACT_APP_SHAREPOINT_HOST_NAME: string;
  REACT_APP_SHAREPOINT_REQ_TZ_OFFSET: number;
  REACT_APP_INFO_MESSAGES: IAppInfoMessages | null;
  REACT_APP_AI_INFO_MESSAGES: IAppInfoMessages | null;
  REACT_APP_RETRY_COUNTS: number
  REACT_APP_RETRY_DEAFULT_TIME: number
  REACT_APP_RETRIABLE_TIME: number
  REACT_APP_ADDITIONAL_WAITING_MSEC: number
  REACT_APP_BATCH_REQUEST_CHUNK_SIZE: number
  REACT_APP_BOOKMARK_LIST_WAITING_TIME: number
  REACT_APP_SUBHEAD: string,
  REACT_APP_COMPANY_REGULATION_SITE_URL: string,
  REACT_APP_COMPANY: string,
  REACT_APP_AI_SEARCH_ENABLED: boolean,
}

const environment: IEnvironment = {
  REACT_APP_TITLE: process?.env?.REACT_APP_TITLE ?? import.meta.env.REACT_APP_TITLE,
  REACT_APP_API_URL: process?.env?.REACT_APP_API_URL ?? import.meta.env.REACT_APP_API_URL,
  REACT_APP_ROUTE_PREFIX: handleUnknownString(window.Geranium.routePrefix),
  REACT_APP_CONNECTION_STRING: handleUnknownString(window.Geranium.connectionString),
  REACT_APP_SHAREPOINT_HOST_NAME: handleUnknownString(window.Geranium.spoHostName),
  REACT_APP_SHAREPOINT_REQ_TZ_OFFSET: process?.env?.REACT_APP_SHAREPOINT_REQ_TZ_OFFSET
    ?? import.meta.env.REACT_APP_SHAREPOINT_REQ_TZ_OFFSET,
  REACT_APP_INFO_MESSAGES: handleUnknownAppInfoMessages(window.Geranium.appInfoMessages),
  REACT_APP_AI_INFO_MESSAGES: handleUnknownAppInfoMessages(window.Geranium.appAIInfoMessages),
  REACT_APP_RETRY_COUNTS: handleUnknownNumber(window.Geranium.defaultRetryCounts),
  REACT_APP_RETRY_DEAFULT_TIME: handleUnknownNumber(window.Geranium.retryRegulationTime),
  REACT_APP_RETRIABLE_TIME: handleUnknownNumber(window.Geranium.retriableTime),
  REACT_APP_ADDITIONAL_WAITING_MSEC: handleUnknownNumber(window.Geranium.additionalWaitingMsec),
  REACT_APP_BATCH_REQUEST_CHUNK_SIZE: handleUnknownNumber(window.Geranium.batchRequestChunkSize),
  REACT_APP_BOOKMARK_LIST_WAITING_TIME: handleUnknownNumber(
    window.Geranium.bookmarkListWaitingTime,
  ),
  REACT_APP_SUBHEAD: handleUnknownString(window.Geranium.companyName),
  REACT_APP_COMPANY_REGULATION_SITE_URL:
    handleUnknownString(window.Geranium.companyRegulationSiteUrl),
  REACT_APP_COMPANY: handleUnknownString(window.Geranium.company),
  REACT_APP_AI_SEARCH_ENABLED: window.Geranium.aiSearchEnabled,
};

export default environment;
