import React from 'react';

/**
 * ReactのDOMイベント
 */
export type ReactEvent =
  React.KeyboardEvent<HTMLElement>
  | React.MouseEvent<HTMLElement>
  | React.FocusEvent<HTMLElement>
  | React.FormEvent<HTMLElement>;

/**
 * Reactのタッチイベント
 */
export type TouchOrMouseEvent = React.SyntheticEvent<HTMLElement, TouchEvent | MouseEvent>;

/**
 * イベントがMouseEventであることをtypeから型判定する
 * @param e 種類を判定していないReactEvent
 * @param [type] デフォルト値は 'click'
 * @return e.typeがtypeと一致していればtrue
 */
export function isMouseEvent(e: ReactEvent, type = 'click'): e is React.MouseEvent<HTMLElement> {
  return e.type === type;
}

/**
 * イベントがKeyboardEventであることをtypeから型判定する
 * @param e 種類を判定していないReactEvent
 * @param [type] デフォルト値は 'keydown'
 * @return e.typeがtypeと一致していればtrue
 */
export function isKeyboardEvent(e: ReactEvent, type = 'keydown'): e is React.KeyboardEvent<HTMLElement> {
  return e.type === type;
}

/**
 * イベントがKeyboardEventであるときにキーがEnterであることを判定する
 * さらに、日本語入力中のenterでは発火させない
 * @param e 種類を判定していないReactEvent
 * @param [type] デフォルト値は 'keydown'
 * @return e.keyが'Enter'のときにtrue。それ以外はfalse
 */
export function isEnterKey(e: ReactEvent, type = 'keydown'): boolean {
  if (!isKeyboardEvent(e, type)) return false;
  if (e.key !== 'Enter') return false;
  // 日本語入力中は発火させない
  return !e.nativeEvent.isComposing;
}

/**
 * イベントがEnterキー押し下げかMouseEventであるかを判定する
 * @param e 種類を判定していないReactEvent
 * @return eがクリックイベントまたはEnterのkeydownイベントのときにtrue
 */
export function isEnterKeydownOrClick(e: ReactEvent): boolean {
  return isMouseEvent(e) || isEnterKey(e);
}
