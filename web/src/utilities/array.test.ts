import {
  createDictByKey, mergeCollectionsByKey, removeDupMembers,
  removeDupMembersCaseNotSensitive, roughlyIncludes, sortArrayByKey,
} from './array';

describe('removeDupMembers', () => {
  describe('when the list has the same values', () => {
    const list = ['a', 'a', 'b', 'c', 'c'];

    it('should return a duplication removed array', () => {
      expect(removeDupMembers(list)).toStrictEqual(['a', 'b', 'c']);
    });
  });

  describe('when the list does not have the same values', () => {
    const list = ['a', 'b', 'c'];

    it('should return the list as is', () => {
      expect(removeDupMembers(list)).toStrictEqual(['a', 'b', 'c']);
    });
  });
});

describe('removeDupMembersCaseNotSensitive', () => {
  it('should return ["AbC", "ａｂｃ", "123"]', () => {
    const input = ['AbC', 'ABC', 'abc', 'aBc', 'ａｂｃ', 'ＡＢＣ', '123', 'ＡｂＣ', 'ａＢｃ'];
    const expected = ['AbC', 'ａｂｃ', '123'];
    expect(removeDupMembersCaseNotSensitive(input)).toStrictEqual(expected);
  });
});

describe('roughlyIncludes', () => {
  describe('when the list does not include the word', () => {
    const list = ['a', 'b', 'c'];
    const word = 'd';

    it('should return false', () => {
      expect(roughlyIncludes(list, word)).toBe(false);
    });
  });

  function roughlyIncludesCase(list: string[], word: string) {
    it('should return true', () => {
      expect(roughlyIncludes(list, word)).toBe(true);
    });
  }

  describe('when the list includes the word', () => {
    const cases = [
      { list: ['1'], word: '1' },
      { list: ['１'], word: '1' },
      { list: ['a'], word: 'A' },
      { list: ['A'], word: 'a' },
      { list: ['Ａ'], word: 'a' },
      { list: ['a'], word: 'Ａ' },
      { list: ['ａ'], word: 'a' },
      { list: ['ａ'], word: 'A' },
      { list: ['ａ'], word: 'ａ' },
      { list: ['ａ'], word: 'Ａ' },
      { list: ['あ'], word: 'あ' },
      { list: ['ア'], word: 'ア' },
      { list: ['ア'], word: 'ｱ' },
    ];

    cases.forEach(({ list, word }, i) => {
      describe(`case ${i}`, () => {
        roughlyIncludesCase(list, word);
      });
    });
  });
});

describe('createDictByKey', () => {
  it('should return a data converted as a dictionary', () => {
    const array = [
      { a: 'abc', b: '123' },
      { a: 'efg', b: '456' },
    ];
    const key = 'a';
    const expected = {
      abc: { a: 'abc', b: '123' },
      efg: { a: 'efg', b: '456' },
    };
    expect(createDictByKey(array, key)).toStrictEqual(expected);
  });
});

describe('mergeArraysByKey', () => {
  type TestEntry = string | null | undefined;
  type TestCollection = { a: TestEntry, b: TestEntry, c: TestEntry, d?: TestEntry };

  const key = 'a';
  const origin: TestCollection[] = [
    { a: '123', b: '456', c: '789' },
    { a: '456', b: '789', c: '012' },
    {
      a: '789', b: '012', c: '345', d: 'EFG',
    },
  ];
  const override: TestCollection[] = [
    {
      a: '123', b: null, c: '', d: 'ABC',
    },
    { a: '789', b: 'ghi', c: 'jkl' },
    { a: 'ABC', b: 'EFG', c: 'HIJ' },
  ];

  describe('when skipBlankString is true', () => {
    it('should make a shallow merged array skipping blank overrides', () => {
      const expected: TestCollection[] = [
        {
          // c should be '789'
          a: '123', b: '456', c: '789', d: 'ABC',
        },
        { a: '456', b: '789', c: '012' },
        {
          a: '789', b: 'ghi', c: 'jkl', d: 'EFG',
        },
        { a: 'ABC', b: 'EFG', c: 'HIJ' },
      ];
      expect(mergeCollectionsByKey(key, origin, override, true)).toStrictEqual(expected);
      expect(mergeCollectionsByKey(key, origin, override)).toStrictEqual(expected);
    });
  });

  describe('when skipBlankString is false', () => {
    it('should make a shallow merged array', () => {
      const expected: TestCollection[] = [
        {
          // c should be ''
          a: '123', b: '456', c: '', d: 'ABC',
        },
        { a: '456', b: '789', c: '012' },
        {
          a: '789', b: 'ghi', c: 'jkl', d: 'EFG',
        },
        { a: 'ABC', b: 'EFG', c: 'HIJ' },
      ];
      expect(mergeCollectionsByKey(key, origin, override, false)).toStrictEqual(expected);
    });
  });

});

describe('sortArrayByKey', () => {
  describe('when the comparing field is number', () => {
    const array = [
      { a: 2 },
      { a: 3 },
      { a: 1 },
    ];

    it('should sort array', () => {
      const expected = [
        { a: 1 },
        { a: 2 },
        { a: 3 },
      ];
      expect(sortArrayByKey(array, 'a')).toStrictEqual(expected);
    });

    it('should sort array order by desc', () => {
      const expected = [
        { a: 3 },
        { a: 2 },
        { a: 1 },
      ];
      expect(sortArrayByKey(array, 'a', 'desc')).toStrictEqual(expected);
    });
  });

  describe('when the comparing field is string', () => {
    const array = [
      { a: '2020-01-01T14:00:00.000Z' },
      { a: '2020-01-01T12:00:00.000Z' },
      { a: '2020-01-01T13:00:00.000Z' },
    ];

    it('should sort array', () => {
      const expected = [
        { a: '2020-01-01T12:00:00.000Z' },
        { a: '2020-01-01T13:00:00.000Z' },
        { a: '2020-01-01T14:00:00.000Z' },
      ];
      expect(sortArrayByKey(array, 'a')).toStrictEqual(expected);
    });

    it('should sort array order by desc', () => {
      const expected = [
        { a: '2020-01-01T14:00:00.000Z' },
        { a: '2020-01-01T13:00:00.000Z' },
        { a: '2020-01-01T12:00:00.000Z' },
      ];
      expect(sortArrayByKey(array, 'a', 'desc')).toStrictEqual(expected);
    });
  });

  describe('when the comparing field is not string or number', () => {
    const array = [
      { a: null, id: 3 },
      { a: undefined, id: 1 },
      { a: [], id: 2 },
    ];

    it('should not change the order', () => {
      const expected = [
        { a: null, id: 3 },
        { a: undefined, id: 1 },
        { a: [], id: 2 },
      ];
      expect(sortArrayByKey(array, 'a')).toStrictEqual(expected);
    });

    it('should not change the order', () => {
      const expected = [
        { a: null, id: 3 },
        { a: undefined, id: 1 },
        { a: [], id: 2 },
      ];
      expect(sortArrayByKey(array, 'a', 'desc')).toStrictEqual(expected);
    });
  });
});
