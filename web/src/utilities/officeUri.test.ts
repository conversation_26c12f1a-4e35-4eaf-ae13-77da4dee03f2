// eslint-disable-next-line import/no-named-as-default
import getUriScheme from './officeUri';

describe('getUriScheme', () => {

  const fileExtension = {
    doc: 'doc',
    ppt: 'ppt',
    xls: 'xls',
    docs: 'docs',
    xlsm: 'xlsm',
    xlsx: 'xlsx',
  };
  const NofileExtension = {
    empty: '',
    null: '',
    undefined: '',
  };

  it('空の文字列が渡された時に空の文字列で返すこと', () => {
    expect(getUriScheme(NofileExtension.empty)).toEqual('');
  });

  it('nullが渡された時に空の文字列で返すこと', () => {
    expect(getUriScheme(NofileExtension.null)).toEqual('');
  });

  it('undefinedが渡された時に空の文字列で返すこと', () => {
    expect(getUriScheme(NofileExtension.undefined)).toEqual('');
  });

  it('Docを渡してDoCを返す', () => {
    expect(getUriScheme(fileExtension.doc)).toEqual('ms-word:ofv|u|');
  });

  it('pptを渡してpptを返す', () => {
    expect(getUriScheme(fileExtension.ppt)).toEqual('ms-powerpoint:ofv|u|');
  });

  it('xlsを渡してxlsを返す', () => {
    expect(getUriScheme(fileExtension.xls)).toEqual('ms-excel:ofv|u|');
  });

  it('Docsを渡してDoCsを返す', () => {
    expect(getUriScheme(fileExtension.docs)).toEqual('ms-word:ofv|u|');
  });

  it('xlsmを渡してxlsmを返す', () => {
    expect(getUriScheme(fileExtension.xlsm)).toEqual('ms-excel:ofv|u|');
  });

  it('xlsxを渡してxlsxを返す', () => {
    expect(getUriScheme(fileExtension.xlsx)).toEqual('ms-excel:ofv|u|');
  });

});
