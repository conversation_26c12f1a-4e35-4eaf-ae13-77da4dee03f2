import {
  blankToDash,
  fixedEncodeURIComponent,
  fullCaseToHalfCase,
  fullKataToHalfKata,
  getGuidFromSubEntityId,
  halfCaseToFullCaseAsIs,
  halfKataToFullKata,
  printTemplate,
  trimPreSufSlash, trimRestrictedPathPrefixWithParam,
} from './text';

describe('blankToDash', () => {
  it('should return dash if parameter is null', () => {
    expect(
      blankToDash(null),
    ).toBe('–');
  });
  it('should return dash if parameter is undefined', () => {
    expect(
      blankToDash(undefined),
    ).toBe('–');
  });
  it('should return dash if parameter is blank', () => {
    expect(
      blankToDash(''),
    ).toBe('–');
  });
  it('should return string as it is if parameter is not blank', () => {
    expect(
      blankToDash('test'),
    ).toBe('test');
  });
});

describe('trimPreSufSlash', () => {
  it('should return blank if parameter is null', () => {
    expect(
      trimPreSufSlash(null),
    ).toBe('');
  });
  it('should return blank if parameter is undefined', () => {
    expect(
      trimPreSufSlash(undefined),
    ).toBe('');
  });
  it('should return blank if parameter is blank', () => {
    expect(
      trimPreSufSlash(''),
    ).toBe('');
  });
  it('should return the input as is if if it does not have slashes at the head or end', () => {
    expect(
      trimPreSufSlash('no/head/or/end/slash'),
    ).toBe('no/head/or/end/slash');
  });
  it('should trim the slash at the head and end', () => {
    expect(
      trimPreSufSlash('//slash/at/the/head/should/be/removed'),
    ).toBe('slash/at/the/head/should/be/removed');
    expect(
      trimPreSufSlash('slash/at/the/end/should/be/removed//'),
    ).toBe('slash/at/the/end/should/be/removed');
    expect(
      trimPreSufSlash('/both/head/and/end/slash/should/be/removed/'),
    ).toBe('both/head/and/end/slash/should/be/removed');
  });
});

describe('printTemplate', () => {
  it('should return joined text', () => {
    const text = '123';
    const text2 = '456';
    expect(printTemplate`ABCD${text}EFGH${text2}`).toBe('ABCD123EFGH456');
  });
});

describe('fixedEncodeURIComponent', () => {
  it('should return replaced text', () => {
    const text = '\'';
    const text2 = '!"#$%&\'()*+,-./01234567890:;<=>?@ABCDHIJKLMNOPQRSTUVWXYZ[]\\^_`abcdefghijklmnopqrstuvwxyz{|}~';
    expect(fixedEncodeURIComponent(text)).toBe('%27%27');
    expect(fixedEncodeURIComponent(text2)).toBe('!%22%23%24%25%26%27%27()*%2B%2C-.%2F01234567890%3A%3B%3C%3D%3E%3F%40ABCDHIJKLMNOPQRSTUVWXYZ%5B%5D%5C%5E_%60abcdefghijklmnopqrstuvwxyz%7B%7C%7D~');
  });
});

describe('fullCaseToHalfCase', () => {
  describe('when str is full case', () => {
    it('should return half case', () => {
      expect(fullCaseToHalfCase('Ａａ１')).toBe('Aa1');
      expect(fullCaseToHalfCase('Aa1')).toBe('Aa1');
    });
  });

  describe('when str is half case', () => {
    it('should return half case', () => {
      expect(fullCaseToHalfCase('Ａａ１')).toBe('Aa1');
      expect(fullCaseToHalfCase('Aa1')).toBe('Aa1');
    });
  });
});

describe('halfCaseToFullCaseAsIs', () => {
  it('should return full cases', () => {
    const text = 'abcABC123!{)';
    const expected = 'ａｂｃＡＢＣ１２３！｛）';
    expect(halfCaseToFullCaseAsIs(text)).toBe(expected);
  });
});

describe('halfKataToFullKata', () => {
  describe('when str is half kata-kana', () => {
    it('should return full kata-kana', () => {
      expect(halfKataToFullKata('ｱｲｳｴｵ')).toBe('アイウエオ');
    });
  });
});

describe('fullKataToHalfKata', () => {
  it('should return half kata-kana', () => {
    const text = 'アイウガギグパピプ';
    const expected = 'ｱｲｳｶﾞｷﾞｸﾞﾊﾟﾋﾟﾌﾟ';
    expect(fullKataToHalfKata(text)).toBe(expected);
  });
});

describe('trimRestrictedPathPrefix', () => {
  it('should trim /:i:/r/ and query params"', () => {
    // 置換されるケース
    expect(trimRestrictedPathPrefixWithParam('/:i:/r/')).toBe('/');
    expect(trimRestrictedPathPrefixWithParam('/:i:/r/?')).toBe('/');
    expect(trimRestrictedPathPrefixWithParam('/:i:/r/abc')).toBe('/abc');
    expect(trimRestrictedPathPrefixWithParam('/:i:/r/abc/')).toBe('/abc/');
    expect(trimRestrictedPathPrefixWithParam('/:i:/r/abc?cfs=123')).toBe('/abc');
    expect(trimRestrictedPathPrefixWithParam('/:i:/r/abc?cfs=123&abc=456')).toBe('/abc');
    expect(trimRestrictedPathPrefixWithParam(`/:i:/r/${encodeURIComponent('today?.png')}?cfs=123&abc=456`)).toBe('/today%3F.png');

    // 置換されないケース
    expect(trimRestrictedPathPrefixWithParam('/:I:/R/')).toBe('/:I:/R/');
    expect(trimRestrictedPathPrefixWithParam(':i:/r')).toBe(':i:/r');
    expect(trimRestrictedPathPrefixWithParam(':i:/r')).toBe(':i:/r');
    expect(trimRestrictedPathPrefixWithParam('/:i:/rabc')).toBe('/:i:/rabc');
    expect(trimRestrictedPathPrefixWithParam('abc/:i:/r/')).toBe('abc/:i:/r/');
    expect(trimRestrictedPathPrefixWithParam('abc')).toBe('abc');
    expect(trimRestrictedPathPrefixWithParam('/abc')).toBe('/abc');
    expect(trimRestrictedPathPrefixWithParam('/abc')).toBe('/abc');
    expect(trimRestrictedPathPrefixWithParam('/abc?cfs=123')).toBe('/abc?cfs=123');
  });
});

describe('getGuidFromSubEntityId', () => {
  describe('when the subEntityId is falsy', () => {
    it('should return blank', () => {
      expect(getGuidFromSubEntityId('')).toBe('');
      expect(getGuidFromSubEntityId(undefined)).toBe('');
      expect(getGuidFromSubEntityId(null)).toBe('');
    });
  });

  describe('when the subEntityId is shared link format', () => {
    it('should return the GUID part', () => {
      const value = 'search:spo:ef8f1072-30bb-4492-a0b9-b0b4edb17ca8';
      expect(getGuidFromSubEntityId(value)).toBe('ef8f1072-30bb-4492-a0b9-b0b4edb17ca8');
    });
  });
});
