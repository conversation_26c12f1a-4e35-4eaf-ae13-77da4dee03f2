/**
 * ログ出力を行うオブジェクト
 */
const logger = {
  /**
   * ログを出力します。
   * @param message 出力する内容
   */
  // なんでもログ出力できるようあえてanyとしている
  log: (message?: any, ...optionalParams: any[]): void => { // eslint-disable-line @typescript-eslint/no-explicit-any,@typescript-eslint/explicit-module-boundary-types, max-len
    // TODO: とりあえずコンソール出力とするが、あとで適切な出力に変更する
    // eslint-disable-next-line no-console
    console.log(message, optionalParams);
  },

  /**
   * エラーログを出力します。
   * @param message エラー内容
   */
  // なんでもログ出力できるようあえてanyとしている
  error: (message?: any, ...optionalParams: any[]): void => { // eslint-disable-line @typescript-eslint/no-explicit-any,@typescript-eslint/explicit-module-boundary-types, max-len
    // TODO: とりあえずコンソール出力とするが、あとで適切な出力に変更する
    // eslint-disable-next-line no-console
    console.error(message, optionalParams);
  },
};

export default logger;
