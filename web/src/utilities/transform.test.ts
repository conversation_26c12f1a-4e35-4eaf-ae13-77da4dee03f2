import { IChatAttachment } from '../components/domains/split-view/types/IChatAttachment';
import { DataSourceKindType } from '../types/DataSourceKind';
import { IMailResponse } from '../types/IMailResponse';
import { IApiDataSource } from '../types/ISearchRequestResult';
import { ISharePointListsSingleResponse } from '../types/ISharePointListsSingleResponse';
import {
  convertSPListSingleToSplitViewListSingle,
  convertSPListItemToSplitViewDetail,
  remapPostCategorySingle,
  remapPostCategory,
  convertMailResponseToSplitViewListSingle,
  convertChatResponseToSplitViewSingle,
  convertChatItemToSplitViewDetail,
  extractSearchKeywordsArray,
  getContentSubstring,
  filterChatAttachments,
  convertSplitViewItemToBookmarkItem,
} from './transform';

jest.mock('./environment');

describe('filterChatAttachments', () => {
  it('should only get chat attachments (contentType === "reference")', () => {
    const target: IChatAttachment[] = [
      {
        id: '1',
        name: 'messageReference',
        contentType: 'messageReference',
        contentUrl: null,
        content: 'content',
      },
      {
        id: '2',
        name: 'codesnippet',
        contentType: 'application/vnd.microsoft.card.codesnippet',
        contentUrl: null,
        content: 'content',
      },
      {
        id: '3',
        name: 'test.xlsx',
        contentType: 'reference',
        contentUrl: 'https://test/$value',
        content: null,
      },
    ];
    const result = filterChatAttachments(target);

    expect(result).toStrictEqual([{
      id: '3',
      name: 'test.xlsx',
      contentType: 'reference',
      contentUrl: 'https://test/$value',
      content: null,
    }]);
  });
});

describe('convertSPListSingleToSplitViewListSingle', () => {
  it('should return formatted data', () => {
    const mockResult: ISharePointListsSingleResponse[] = [
      {
        'odata.editLink': 'abc',
        GUID: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
        Title: '第××期（２０２１年度）決算のご報告_0020',
        category: 'category-1',
        Created: '2021-08-24T03:00:59.000Z',
        Modified: '2021-08-24T03:01:08.000Z',
        Attachments: false,
      },
      {
        'odata.editLink': '123',
        GUID: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
        Title: '2021年度社内通知（第〇回）_0019',
        category: 'category-2',
        Created: '2021-08-24T02:39:48.000Z',
        Modified: '2021-08-24T02:39:48.000Z',
        Attachments: false,
      },
    ];

    const mockDataSource1: IApiDataSource = {
      kind: 'SPO',
      properties: {
        listName: '会社からのお知らせ',
        listUrl: 'aaa',
        site: 'bbb',
        list: 'ccc',
        category: 'category1',
      },
    };

    const mockDataSource2: IApiDataSource = {
      kind: 'SPO',
      properties: {
        listName: 'グループ会社のお知らせ',
        listUrl: 'ccc',
        site: 'ddd',
        list: 'eee',
        category: 'category2',
      },
    };

    expect(convertSPListSingleToSplitViewListSingle(mockResult, mockDataSource1)).toStrictEqual([
      {
        id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
        kind: 'SPO',
        displayDate: '2021-08-24T03:01:08.000Z',
        title: '第××期（２０２１年度）決算のご報告_0020',
        note: 'category-1',
        properties: {
          listName: '会社からのお知らせ',
          editLink: 'abc',
          listUrl: 'aaa',
          siteUrl: 'bbb',
          listId: 'ccc',
          categoryKeyName: 'category1',
          createdDate: '2021-08-24T03:00:59.000Z',
          updatedDate: '2021-08-24T03:01:08.000Z',
          hasAttachments: false,
        },
      },
      {
        id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
        kind: 'SPO',
        displayDate: '2021-08-24T02:39:48.000Z',
        title: '2021年度社内通知（第〇回）_0019',
        note: 'category-2',
        properties: {
          listName: '会社からのお知らせ',
          editLink: '123',
          listUrl: 'aaa',
          siteUrl: 'bbb',
          listId: 'ccc',
          categoryKeyName: 'category1',
          createdDate: '2021-08-24T02:39:48.000Z',
          updatedDate: '2021-08-24T02:39:48.000Z',
          hasAttachments: false,
        },
      },
    ]);

    expect(convertSPListSingleToSplitViewListSingle(mockResult, mockDataSource2)).toStrictEqual([
      {
        id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
        kind: 'SPO',
        displayDate: '2021-08-24T03:01:08.000Z',
        title: '第××期（２０２１年度）決算のご報告_0020',
        note: 'category-1',
        properties: {
          listName: 'グループ会社のお知らせ',
          editLink: 'abc',
          listUrl: 'ccc',
          siteUrl: 'ddd',
          listId: 'eee',
          categoryKeyName: 'category2',
          createdDate: '2021-08-24T03:00:59.000Z',
          updatedDate: '2021-08-24T03:01:08.000Z',
          hasAttachments: false,
        },
      },
      {
        id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
        kind: 'SPO',
        displayDate: '2021-08-24T02:39:48.000Z',
        title: '2021年度社内通知（第〇回）_0019',
        note: 'category-2',
        properties: {
          listName: 'グループ会社のお知らせ',
          editLink: '123',
          listUrl: 'ccc',
          siteUrl: 'ddd',
          listId: 'eee',
          categoryKeyName: 'category2',
          createdDate: '2021-08-24T02:39:48.000Z',
          updatedDate: '2021-08-24T02:39:48.000Z',
          hasAttachments: false,
        },
      },
    ]);

  });
});

describe('convertMailResponseToSplitViewListSingle', () => {
  it('should return formatted data', () => {
    const mockResult: IMailResponse[] = [
      {
        id: 'abc',
        createdDateTime: 'createdDateTime',
        lastModifiedDateTime: '2021-08-22T03:00:59.000Z',
        sentDateTime: '2021-08-23T03:00:59.000Z',
        receivedDateTime: '2021-08-24T03:00:59.000Z',
        hasAttachments: false,
        subject: 'subject1',
        webLink: '',
        body: {
          contentType: 'html',
          content: 'content',
        },
        sender: {
          emailAddress: {
            address: '<EMAIL>',
            name: 'test1',
          },
        },
        from: {
          emailAddress: {
            address: '<EMAIL>',
            name: 'test1',
          },
        },
        toRecipients: [{
          emailAddress: {
            address: '<EMAIL>',
            name: 'test2',
          },
        }],
        ccRecipients: [],
        bccRecipients: [],
        attachments: [],
        isDraft: false,
      },
      {
        id: 'def',
        createdDateTime: 'createdDateTime',
        lastModifiedDateTime: '2021-08-22T03:00:59.000Z',
        sentDateTime: '2021-08-23T03:00:59.000Z',
        receivedDateTime: '2021-08-24T03:00:59.000Z',
        hasAttachments: false,
        subject: 'subject2',
        webLink: '',
        body: {
          contentType: 'html',
          content: 'content',
        },
        sender: {
          emailAddress: {
            address: '<EMAIL>',
            name: 'test1',
          },
        },
        from: {
          emailAddress: {
            address: '<EMAIL>',
            name: 'test1',
          },
        },
        toRecipients: [{
          emailAddress: {
            address: '<EMAIL>',
            name: 'test4',
          },
        }],
        ccRecipients: [],
        bccRecipients: [],
        attachments: [],
        isDraft: false,
      },
    ];

    const mockDataSource: IApiDataSource = {
      kind: 'Mail',
      properties: {
        timezoneOffset: 'timezoneOffset',
      },
    };

    expect(convertMailResponseToSplitViewListSingle(mockResult, mockDataSource)).toStrictEqual(
      [
        {
          id: 'abc',
          kind: 'Mail',
          displayDate: '2021-08-24T03:00:59.000Z',
          title: 'subject1',
          note: 'test1',
          // hasAttachments: false,
          properties: {
            sender: {
              emailAddress: {
                address: '<EMAIL>',
                name: 'test1',
              },
            },
            from: {
              emailAddress: {
                address: '<EMAIL>',
                name: 'test1',
              },
            },
            toRecipients: [{
              emailAddress: {
                address: '<EMAIL>',
                name: 'test2',
              },
            }],
            ccRecipients: [],
            bccRecipients: [],
            hasAttachments: false,
            sentDateTime: '2021-08-23T03:00:59.000Z',
            receivedDateTime: '2021-08-24T03:00:59.000Z',
            updatedDate: '2021-08-22T03:00:59.000Z',
            isDraft: false,
          },
        },
        {
          id: 'def',
          kind: 'Mail',
          displayDate: '2021-08-24T03:00:59.000Z',
          title: 'subject2',
          note: 'test1',
          // hasAttachments: false,
          properties: {
            sender: {
              emailAddress: {
                address: '<EMAIL>',
                name: 'test1',
              },
            },
            from: {
              emailAddress: {
                address: '<EMAIL>',
                name: 'test1',
              },
            },
            toRecipients: [{
              emailAddress: {
                address: '<EMAIL>',
                name: 'test4',
              },
            }],
            ccRecipients: [],
            bccRecipients: [],
            hasAttachments: false,
            sentDateTime: '2021-08-23T03:00:59.000Z',
            receivedDateTime: '2021-08-24T03:00:59.000Z',
            updatedDate: '2021-08-22T03:00:59.000Z',
            isDraft: false,
          },
        },
      ],
    );
  });
});

describe('when IChatResponse is given', () => {
  describe('extractSearchKeywordsArray', () => {
    it('should return 1 string array', () => {
      const param = 'singleKeyword';
      const response = extractSearchKeywordsArray(param);
      expect(response).toStrictEqual(['singleKeyword']);
    });

    it('should return multiple string array', () => {
      const param = {
        operator: 'Or',
        operand: [
          {
            operator: 'And',
            operand: ['keyword1',
              {
                operator: 'Or',
                operand: ['keyword2', 'keyword3'],
              },
            ],
          },
          {
            operator: 'And',
            operand: ['keyword4', 'keyword5'],
          }],
      };
      const response = extractSearchKeywordsArray(param);
      expect(response).toStrictEqual(['keyword1', 'keyword2', 'keyword3', 'keyword4', 'keyword5']);
    });
  });

  describe('getContentSubstring', () => {
    describe('when keyword is not in the content', () => {
      it('should return empty string', () => {
        const param = {
          content: 'test text',
          keywords: ['keyword'],
        };
        const response = getContentSubstring(param.content, param.keywords);
        expect(response).toStrictEqual('');
      });
    });
    describe('when first keyword is in the content', () => {
      it('should return substring, when there are > 8 characters before and after keyword', () => {
        const param = {
          content: 'test text keyword test text test text',
          keywords: ['keyword'],
        };
        const response = getContentSubstring(param.content, param.keywords);
        expect(response).toStrictEqual('…st text keyword test text test text');
      });
      it('should return substring, when there are > 8 characters before keyword', () => {
        const param = {
          content: 'test text keyword end',
          keywords: ['keyword'],
        };
        const response = getContentSubstring(param.content, param.keywords);
        expect(response).toStrictEqual('…st text keyword end');
      });
      it('should return substring, when there are > 8 characters after keyword', () => {
        const param = {
          content: 'start keyword test text test text',
          keywords: ['keyword'],
        };
        const response = getContentSubstring(param.content, param.keywords);
        expect(response).toStrictEqual('start keyword test text test text');
      });
    });
    describe('when second keyword is in the content', () => {
      it('should return substring containing second keyword', () => {
        const param = {
          content: 'string containing the second phrase',
          keywords: ['first', 'second'],
        };
        const response = getContentSubstring(param.content, param.keywords);
        expect(response).toStrictEqual('…ing the second phrase');
      });
    });
  });

  it('should return no items', () => {
    const response = convertChatResponseToSplitViewSingle([], { kind: 'Chat' }, ['keyword']);
    expect(response).toStrictEqual([]);
  });

  it('should map properties correctly', () => {
    const response = convertChatResponseToSplitViewSingle(
      [
        {
          id: 'test1',
          attachments: [],
          reactions: [],
          body: { contentType: 'html', content: '<p>test text</p>' },
          createdDateTime: '2023-05-26T00:00:00+09:00',
          lastModifiedDateTime: '2023-05-26T00:00:00+09:00',
          chatId: 'chatId1',
          messageType: 'chat',
          from: {
            user: {
              id: 'userId1',
              displayName: 'John Doe',
              tenantId: 'tenantId1',
            },
            application: null,
          },
        },
        {
          id: 'test2',
          reactions: [],
          attachments: [
            {
              id: 'attachment1',
              name: 'attachment.jpg',
              contentType: 'reference',
              contentUrl: 'https://examle.com/image.jpg',
              content: null,
            },
          ],
          body: { contentType: 'html', content: '<p>test text |XXXXXXXXkeywordXXXXXXXXXXXXXXXXXXXXXX|</p>' },
          createdDateTime: '2023-05-26T04:00:00+09:00',
          lastModifiedDateTime: '2023-05-26T06:05:00+09:00',
          chatId: 'chatId2',
          messageType: 'chat',
          from: {
            user: null,
            application: {
              id: 'botId',
              displayName: 'botDisplayName',
              applicationIdentityType: 'bot',
            },
          },
        },
      ],
      {
        kind: 'Chat',
      },
      ['keyword'],
    );
    expect(response).toStrictEqual([
      {
        id: 'test1',
        title: 'John Doe',
        kind: 'Chat',
        body: '<p>test text</p>',
        displayDate: '2023-05-25T15:00:00.000Z',
        note: 'test text',
        properties: {
          chatAttachments: [],
          from: {
            id: 'userId1',
            displayName: 'John Doe',
            tenantId: 'tenantId1',
          },
          updatedDate: '2023-05-26T00:00:00+09:00',
          lastModifiedDateTime: '2023-05-26T00:00:00+09:00',
          hasAttachments: false,
          chatId: 'chatId1',
          teamId: undefined,
          contentType: 'html',
          messageType: 'chat',
          bookmarkNote: 'test text',
        },
      },
      {
        id: 'test2',
        title: 'botDisplayName',
        kind: 'Chat',
        body: '<p>test text |XXXXXXXXkeywordXXXXXXXXXXXXXXXXXXXXXX|</p>',
        displayDate: '2023-05-25T19:00:00.000Z',
        note: '…XXXXXXXXkeywordXXXXXXXXXXXXXXXXXXXXXX|',
        properties: {
          bookmarkNote: 'test text |XXXXXXXXkeywordXXXXXXXXXXXXXXXXXXXXXX|',
          chatAttachments: [
            {
              id: 'attachment1',
              name: 'attachment.jpg',
              contentType: 'reference',
              contentUrl: 'https://examle.com/image.jpg',
              content: null,
            },
          ],
          from: {
            id: 'botId',
            displayName: 'botDisplayName',
            applicationIdentityType: 'bot',
          },
          updatedDate: '2023-05-26T06:05:00+09:00',
          lastModifiedDateTime: '2023-05-26T06:05:00+09:00',
          hasAttachments: true,
          chatId: 'chatId2',
          teamId: undefined,
          contentType: 'html',
          messageType: 'chat',
        },
      },
    ]);

  });

  it('should throw error when incorrect kind is given', () => {
    expect(() => convertChatResponseToSplitViewSingle([], { kind: 'Mail' }, [''])).toThrowError();
  });

  it('should map properties correctly', () => {
    const response = convertChatItemToSplitViewDetail(
      {
        id: 'test1',
        attachments: [],
        reactions: [],
        body: { contentType: 'html', content: '<p>test text</p>' },
        createdDateTime: '2023-05-26T00:00:00+09:00',
        lastModifiedDateTime: '2023-05-26T00:00:00+09:00',
        chatId: 'chatId1',
        messageType: 'chat',
        from: {
          user: {
            id: 'userId1',
            displayName: 'John Doe',
            tenantId: 'tenantId1',
          },
          application: null,
        },
        teamId: '45678',
        subject: '件名：１',
        teamChatType: 'Chat',
        mentions: [],
        replyToId: 'replyIDs',
      },
      {
        id: 'test3',
        kind: 'Chat',
        title: 'string',
        reposCreatedDate: '20230401',
        reposUpdatedDate: '20230401',
        note: 'string',
        displayDate: '19911202',
        properties: {},
        body: 'string',
        // TODO: SPO固有の型をPropertiesに移動する？
        itemId: 123,
        expiredDate: '20231202',
      },
      'jio',
    );
    expect(response).toStrictEqual({
      id: 'test3',
      kind: 'Chat',
      title: 'string',
      note: 'test text',
      displayDate: '19911202',
      body: '<p>test text</p>',
      reposCreatedDate: '20230401',
      reposUpdatedDate: '20230401',
      properties: {
        chatAttachments: [],
        from: {
          id: 'userId1',
          displayName: 'John Doe',
          tenantId: 'tenantId1',
        },
        contentType: 'html',
        subject: '件名：１',
        teamChatType: 'Chat',
        detailedTitle: 'jio',
        mentions: [],
        reactions: [],
        replyToId: 'replyIDs',
      },
    });

  });
});

describe('convertSplitViewItemToBookmarkItem', () => {
  it('should use default note', () => {
    const result = convertSplitViewItemToBookmarkItem({
      id: 'def',
      kind: 'Mail',
      displayDate: '2021-08-24T03:00:59.000Z',
      title: 'subject2',
      note: 'test1',
      // hasAttachments: false,
      properties: {
        sender: {
          emailAddress: {
            address: '<EMAIL>',
            name: 'test1',
          },
        },
        from: {
          emailAddress: {
            address: '<EMAIL>',
            name: 'test1',
          },
        },
        toRecipients: [{
          emailAddress: {
            address: '<EMAIL>',
            name: 'test4',
          },
        }],
        ccRecipients: [],
        bccRecipients: [],
        hasAttachments: false,
        sentDateTime: '2021-08-23T03:00:59.000Z',
        receivedDateTime: '2021-08-24T03:00:59.000Z',
        updatedDate: '2021-08-22T03:00:59.000Z',
      },
    });
    expect(result.note).toBe('test1');
  });

  it('should use chat note', () => {
    const result = convertSplitViewItemToBookmarkItem({
      id: 'test2',
      title: 'botDisplayName',
      kind: 'Chat',
      body: '<p>test text |XXXXXXXXkeywordXXXXXXXXXXXXXXXXXXXXXX|</p>',
      displayDate: '2023-05-25T19:00:00.000Z',
      note: 'search result note',
      properties: {
        bookmarkNote: 'bookmark note',
        chatAttachments: [
          {
            id: 'attachment1',
            name: 'attachment.jpg',
            contentType: 'reference',
            contentUrl: 'https://examle.com/image.jpg',
            content: null,
          },
        ],
        from: {
          id: 'botId',
          displayName: 'botDisplayName',
          applicationIdentityType: 'bot',
        },
        updatedDate: '2023-05-26T06:05:00+09:00',
        lastModifiedDateTime: '2023-05-26T06:05:00+09:00',
        hasAttachments: true,
        chatId: 'chatId2',
        teamId: undefined,
        contentType: 'html',
        messageType: 'chat',
      },
    });

    expect(result.note).toBe('bookmark note');
  });
});

describe('convertSPListItemToSplitViewDetail', () => {
  const activeItemMock = {
    id: '',
    title: '',
    displayDate: '',
    kind: 'SPO' as DataSourceKindType,
    note: 'note',
    properties: {
      listName: 'listName',
      listId: 'listId',
      listUrl: 'listUrl',
      siteUrl: 'sharePointUrl',
      editLink: 'abc',
      categoryKeyName: 'category1',
    },
    reposCreatedDate: '2023-04-02T00:00:00Z',
    reposUpdatedDate: '2023-04-02T00:00:00Z',
  };

  it('should return reformatted data', () => {
    expect(
      convertSPListItemToSplitViewDetail({
        GUID: 'abcd',
        Id: 10,
        Title: 'Title-test',
        docBody: 'docBody-test',
        Created: '2021-08-24T03:00:59.000Z',
        Modified: '2021-08-24T03:01:08.000Z',
        presentPeriod: 'presentPeriod-test',
        'odata.editLink': 'abc',
        category: 'sourceCategory1',
      }, activeItemMock),
    ).toStrictEqual({
      id: 'abcd',
      kind: 'SPO',
      itemId: 10,
      title: 'Title-test',
      body: 'docBody-test',
      displayDate: '2021-08-24T03:01:08.000Z',
      expiredDate: 'presentPeriod-test',
      note: 'sourceCategory1',
      reposCreatedDate: '2023-04-02T00:00:00Z',
      reposUpdatedDate: '2023-04-02T00:00:00Z',
      properties: {
        hasAttachments: false,
        listName: 'listName',
        listId: 'listId',
        listUrl: 'listUrl',
        siteUrl: 'sharePointUrl',
        editLink: 'abc',
        categoryKeyName: 'category1',
        createdDate: '2021-08-24T03:00:59.000Z',
        updatedDate: '2021-08-24T03:01:08.000Z',
      },
    });
  });

  describe('when the source data does not have expected values', () => {
    it('should return reformatted data filled with blanks', () => {
      expect(
        convertSPListItemToSplitViewDetail({
          GUID: null,
          Id: null,
          Title: null,
          docBody: null,
          Created: null,
          Modified: null,
          presentPeriod: null,
          category: null,
        }, {
          id: '',
          title: '',
          kind: 'Other',
          note: '',
          displayDate: '',
          reposCreatedDate: '2023-04-02T00:00:00Z',
          reposUpdatedDate: '2023-04-02T00:00:00Z',
          properties: {
            listUrl: 'listUrl',
          },
        }),
      ).toStrictEqual({
        id: '',
        kind: 'Other',
        itemId: null,
        title: '',
        body: '',
        displayDate: '',
        expiredDate: '',
        note: '',
        properties: {
          listUrl: 'listUrl',
          hasAttachments: false,
          editLink: '',
          createdDate: '',
          updatedDate: '',
        },
        reposCreatedDate: '2023-04-02T00:00:00Z',
        reposUpdatedDate: '2023-04-02T00:00:00Z',
      });
    });
  });
});

describe('remapPostCategorySingle', () => {

  describe('when category name is empty or not a string', () => {
    it('should return original data', () => {
      expect(remapPostCategorySingle({}, '')).toEqual({});
    });
  });

  describe('when category is not available in the response', () => {
    it('should return a blank category', () => {
      const mockParam = {
        id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
        createdDate: '2021-08-24T02:39:48.000Z',
        category2: '',
      };
      const category = 'category2';
      expect(remapPostCategorySingle(mockParam, category)).toEqual({
        id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
        createdDate: '2021-08-24T02:39:48.000Z',
        category: '',
        category2: '',
      });
      const mockParams = { abc: 'abc', category1: false } as unknown as ISharePointListsSingleResponse;
      expect(remapPostCategorySingle(mockParams, category)).toEqual({
        abc: 'abc',
        category: '',
        category1: false,
      });
    });
  });

  describe('when category name has a value and is a string', () => {
    it('should return remap the new category name', () => {
      const mockResults = { 'spo category': 'value' };
      const category = 'spo category';
      expect(remapPostCategorySingle(mockResults, category)).toStrictEqual({
        'spo category': 'value',
        category: 'value',
      });
    });
  });
});

describe('remapPostCategory', () => {
  describe('when res.value is not an array', () => {
    it('should return original parameter as is', () => {
      expect(remapPostCategory({ value: undefined }, '')).toEqual({ value: undefined });
      expect(remapPostCategory({ value: null }, '')).toEqual({ value: null });
      expect(remapPostCategory({}, '')).toEqual({});
    });
  });

  describe('when res.value has items with specified category', () => {

    it('should return remapped items', () => {
      expect(remapPostCategory({
        value: [
          {
            id: 'b',
            'spo category': 'abc',
          }, {
            id: 'a',
            'spo category': '123',
          },
        ],
      }, 'spo category')).toEqual({
        value: [
          {
            id: 'b',
            'spo category': 'abc',
            category: 'abc',
          }, {
            id: 'a',
            'spo category': '123',
            category: '123',
          },
        ],
      });
    });
  });
});
