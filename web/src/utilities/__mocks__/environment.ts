const environment = {
  REACT_APP_TITLE: process?.env?.REACT_APP_TITLE,
  REACT_APP_API_URL: process?.env?.REACT_APP_API_URL,
  REACT_APP_ROUTE_PREFIX: '/',
  REACT_APP_CONNECTION_STRING: 'c2d94ddb-49b1-405a-b85f-568bd54af10b',
  // set static value to mock the dynamic environment variables
  REACT_APP_SHAREPOINT_URL: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/sub-project/',
  REACT_APP_SHAREPOINT_LIST: 'A6EEDE04-AE01-4AD6-A934-EA3C74B230E0',
  REACT_APP_SHAREPOINT_LIST_URL: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/sub-project/Lists/List/DispForm.aspx',
  REACT_APP_SHAREPOINT_REQ_TZ_OFFSET: process?.env?.REACT_APP_SHAREPOINT_REQ_TZ_OFFSET,
  REACT_APP_FORCE_READ_DATE: '2022-01-01T00:00:00.000+09:00',
  REACT_APP_MANIFEST_ID: 'becdb796-ce61-4f88-b826-dd8eaf854f6f',
  REACT_APP_MAINHEAD: '会社からのお知らせ',
  REACT_APP_SUBHEAD: '三菱地所',
  REACT_APP_SPO_COLUMNS: { category: 'category1' },
  REACT_APP_BATCH_REQUEST_CHUNK_SIZE: 20,
  REACT_APP_BOOKMARK_LIST_WAITING_TIME: 500,
  REACT_APP_COMPANY_REGULATION_SITE_URL: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/sub-project/',
  REACT_APP_COMPANY: 'mec',
  REACT_APP_AI_SEARCH_ENABLED: true,
};

export default environment;
