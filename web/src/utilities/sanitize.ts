import sanitizeHTML from 'sanitize-html';
import environment from './environment';
import {
  complementRelativeUrl,
  isAbsoluteUrl,
} from './url';

// カスタムデータ属性を追加する場合はSANITIZER_EXTRA_ALLOWED_ATTRIBUTESで許可してあげないとトリムされる
// useSpoImgLoaderで取得するときに使うカスタムdata属性
export const DATA_ATTR_SPO_SRC = 'data-spo-src';
// useMailLoadingFeatureで取得するときに使うカスタムdata属性
export const DATA_ATTR_MAIL_SRC = 'data-mail-src';
// useChatImgLoadingFeatureで取得するときに使うカスタムdata属性
export const DATA_ATTR_CHAT_SRC = 'data-chat-src';
export const DATA_ATTR_CHAT_ATTACHMENT_ID = 'data-chat-attachment-id';
export const DATA_ATTR_CHAT_MENTION_ID = 'data-chat-mention-id';
export const DATA_ATTR_CHAT_EMOJI_ALT = 'data-chat-emoji-alt';

// サニタイザーに許可する追加のHTMLタグ
// https://developer.mozilla.org/ja/docs/Web/HTML/Element#obsolete_and_deprecated_elements
// この設定に加えてデフォルトタグも許可される https://www.npmjs.com/package/sanitize-html
export const SANITIZER_EXTRA_ALLOWED_TAGS = [
  'big', 'blink', 'image', 'nobr', 'strike', 'center', 'font', 'img', 'style', 'meta',
];

// サニタイザーに許可する追加のHTML属性
// https://www.w3.org/TR/html4/index/attributes.html
// この設定に加えてデフォルト属性も許可される https://www.npmjs.com/package/sanitize-html
export const SANITIZER_EXTRA_ALLOWED_ATTRIBUTES = [
  // カスタムデータ属性を許可しないとトリムされてしまう
  DATA_ATTR_SPO_SRC, DATA_ATTR_MAIL_SRC, DATA_ATTR_CHAT_SRC,
  DATA_ATTR_CHAT_ATTACHMENT_ID, DATA_ATTR_CHAT_MENTION_ID, DATA_ATTR_CHAT_EMOJI_ALT,
  // a
  'abbr', 'align', 'alink', 'alt', 'axis',
  // b
  'background', 'bgcolor', 'border',
  // c
  'center', 'cellpadding', 'cellspacing', 'char', 'charoff', 'cite',
  'class', 'clear', 'color', 'colspan', 'compact',
  // d
  'datetime', 'dir',
  // f
  'face', 'frame', 'frameborder',
  // h
  'headers', 'height', 'hreflang', 'hspace',
  // i
  'id', 'ismap', 'lang', 'longdesc',
  // m
  'marginheight', 'marginwidth', 'media',
  // n
  'noresize', 'noshade', 'nowrap',
  // r
  'rel', 'rev', 'rowspan', 'rules',
  // s
  'scope', 'scrolling', 'size', 'span', 'start', 'style', 'summary',
  // t
  'text', 'title', 'type',
  // v
  'valign', 'value',
  // w
  'width',
];

/**
 * sanitizeHTMLに渡すOptionsを返す
 * デフォルトで許可されるタグと属性は下記参照
 * https://www.npmjs.com/package/sanitize-html#default-options
 *
 * @return {*}  {IOptions}
 */
export function createSanitizeOptions(): sanitizeHTML.IOptions {
  return {
    // セキュリティ要求と相関するので変更するときは注意
    allowedTags: sanitizeHTML.defaults.allowedTags.concat(SANITIZER_EXTRA_ALLOWED_TAGS),
    disallowedTagsMode: 'recursiveEscape',
    allowedAttributes: {
      ...sanitizeHTML.defaults.allowedAttributes,
      font: ['*'],
      '*': SANITIZER_EXTRA_ALLOWED_ATTRIBUTES,
    },
    nonTextTags: ['style', 'script', 'meta'],
    /**
     * Outlookから取得した本文埋め込み画像用imgタグのsrc属性には「cid:~」で始まるid情報が設定されているので
     * srcが取り除かれないように'cid'を追加
     */
    allowedSchemes: ['http', 'https', 'cid'],
  };
}

/**
 * useSpoImgLoaderで使用されるカスタムdata属性を付与すべきかどうかを判定する
 * falseを返す例：https://projectgeranium.sharepoint.com/_layouts/15/images/icxlsx.png
 * @param absUrl
 * @param urlString
 * @return {boolean}
 */
export function isAttachDataSpoSrc(absUrl: string, urlString: string): boolean {
  // 相対URLかつパスの先頭が「/_」で始まらなければtrue
  if (
    !isAbsoluteUrl(urlString)
    && !urlString.startsWith('/_')
  ) return true;

  // SharePointドメインかつホスト名を除いたパスの先頭が「_」で始まらなければtrue
  if (
    urlString.startsWith(absUrl)
    && !urlString.replace(absUrl, '').startsWith('/_')
  ) return true;

  return false;
}

/**
 * タグの属性内にあるURLを絶対URLに置き換える
 * @param attrName
 * @param tagName
 * @param currentAttribs
 */
export function transformSpoTagImpl(
  attrName: string, tagName: string, currentAttribs: sanitizeHTML.Attributes,
): sanitizeHTML.Tag {
  const tgtAttr = currentAttribs[attrName];
  const attribs = { ...currentAttribs };
  const absUrl = `https://${environment.REACT_APP_SHAREPOINT_HOST_NAME}`;
  if (tgtAttr) {
    attribs[attrName] = complementRelativeUrl(absUrl, tgtAttr);
  }
  if (tagName === 'img') {
    attribs.alt = attribs.alt ? attribs.alt : ' ';

    // 相対URLか、絶対URLかつSharePointドメインの場合、識別できる属性を付与する
    if (tgtAttr && isAttachDataSpoSrc(absUrl, tgtAttr)) {
      attribs[DATA_ATTR_SPO_SRC] = tgtAttr;
    }
  }
  return { tagName, attribs };
}

/**
 * タグの属性内にあるURLを絶対URLに置き換えるためのオプションを作成する
 */
export function addRelativePathReplacer(): sanitizeHTML.IOptions {
  return {
    transformTags: {
      a: transformSpoTagImpl.bind(transformSpoTagImpl, 'href'),
      img: transformSpoTagImpl.bind(transformSpoTagImpl, 'src'),
    },
  };
}

/**
 * imgタグのsrc属性にcidが設定されている場合カスタムデータ属性を付与する
 * @param dispatch
 * @param attrName
 * @param tagName
 * @param currentAttribs
 */
export function mailImgSrcDispatcherImpl(
  attrName: string,
  tagName: string,
  currentAttribs: sanitizeHTML.Attributes,
): sanitizeHTML.Tag {
  const tgtAttr = currentAttribs[attrName];
  const attribs = { ...currentAttribs };

  if (tagName === 'img') {
    attribs.alt = attribs.alt ? attribs.alt : ' ';

    if (tgtAttr?.startsWith('cid:')) {
      // 画像URL置換対象であることを、識別できる属性を付与する
      attribs[DATA_ATTR_MAIL_SRC] = tgtAttr;
    }
  }
  return { tagName, attribs };
}

/**
 * メール本文内imgタグにカスタムデータ属性を付与するオプションを作成する
 */
export function addInlineMailImgSrc(): sanitizeHTML.IOptions {
  return {
    transformTags: {
      img: mailImgSrcDispatcherImpl.bind(mailImgSrcDispatcherImpl, 'src'),
    },
  };
}

/**
 * imgタグのsrc属性にGraph APIのURLが埋め込まれている場合カスタムデータ属性を付与する
 * @param dispatch
 * @param attrName
 * @param tagName
 * @param currentAttribs
 */
export function chatImgSrcDispatcherImpl(
  attrName: string,
  tagName: string,
  currentAttribs: sanitizeHTML.Attributes,
): sanitizeHTML.Tag {
  const tgtAttr = currentAttribs[attrName];
  const attribs = { ...currentAttribs };

  if (tagName === 'img') {
    attribs.alt = attribs.alt ? attribs.alt : ' ';

    if (tgtAttr?.startsWith('https://graph.microsoft.com/') && tgtAttr?.endsWith('/$value')) {
      // 画像URL置換対象であることを、識別できる属性を付与する
      attribs[DATA_ATTR_CHAT_SRC] = tgtAttr;
    }
  }
  return { tagName, attribs };
}

export function transformAtTagImpl(
  attrName: string,
  tagName: string,
  currentAttribs: sanitizeHTML.Attributes,
) {
  const allAttribs = { ...currentAttribs };
  const { [attrName]: idAttr, ...attribs } = allAttribs;
  if (tagName === 'at') {
    attribs[DATA_ATTR_CHAT_MENTION_ID] = idAttr;
    attribs.class = 'chat-mention';
  }
  return { tagName: 'span', attribs };
}

export function transformAttachmentTagImpl(
  attrName: string,
  tagName: string,
  currentAttribs: sanitizeHTML.Attributes,
) {
  const allAttribs = { ...currentAttribs };
  const { [attrName]: idAttr, ...attribs } = allAttribs;
  if (tagName === 'attachment') {
    attribs[DATA_ATTR_CHAT_ATTACHMENT_ID] = idAttr;
    attribs.class = 'attachment-component';
  }
  return { tagName: 'div', attribs };
}

export function transformEmojiTagImpl(
  attrName: string,
  tagName: string,
  currentAttribs: sanitizeHTML.Attributes,
) {
  const allAttribs = { ...currentAttribs };
  const { [attrName]: altAttr, ...attribs } = allAttribs;
  const textnode = altAttr || '';
  if (tagName === 'emoji') {
    attribs[DATA_ATTR_CHAT_EMOJI_ALT] = altAttr;
    attribs.class = 'chat-emoji';
  }
  return { tagName: 'span', attribs, text: textnode };
}

/**
 * チャット本文内imgタグにGraphAPIのURLが埋め込まれている場合にカスタムデータ属性を付与するオプションを作成する
 */
export function addInlineChatImgSrc(): sanitizeHTML.IOptions {
  return {
    transformTags: {
      img: chatImgSrcDispatcherImpl.bind(chatImgSrcDispatcherImpl, 'src'),
      at: transformAtTagImpl.bind(transformAtTagImpl, 'id'),
      attachment: transformAttachmentTagImpl.bind(transformAttachmentTagImpl, 'id'),
      emoji: transformEmojiTagImpl.bind(transformEmojiTagImpl, 'alt'),
    },
  };
}

/**
 * sourceからサニタイズ済のSPO用innerHTML用データを生成する
 * @param source
 */
export function makeSpoSanitizedInnerHtml(
  source: string | undefined | null,
): { __html: string } {

  if (!source) return { __html: '' };

  return {
    __html: sanitizeHTML(
      source,
      {
        ...createSanitizeOptions(),
        ...addRelativePathReplacer(),
      },
    ),
  };
}

/**
 * sourceからサニタイズ済のMail用innerHTML用データを生成する
 * @param source
 */
export function makeMailSanitizedInnerHtml(
  source: string | undefined | null,
): { __html: string } {

  if (!source) return { __html: '' };

  return {
    __html: sanitizeHTML(
      source,
      {
        ...createSanitizeOptions(),
        ...addInlineMailImgSrc(),
      },
    ),
  };
}

/**
 * sourceからサニタイズ済のChat用innerHTML用データを生成する
 * @param source
 */
export function makeChatSanitizedInnerHtml(
  source: string | undefined | null,
): { __html: string } {

  if (!source) return { __html: '' };

  return {
    __html: sanitizeHTML(
      source,
      {
        ...createSanitizeOptions(),
        ...addInlineChatImgSrc(),
      },
    ),
  };
}
