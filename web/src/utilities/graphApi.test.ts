// 型定義しかないモジュールなのでlinter警告が出るが実害は無い
// eslint-disable-next-line import/no-unresolved
import * as microsoftGraph from '@microsoft/microsoft-graph-types/microsoft-graph';
import { flatSearchRes } from './graphApi';

describe('utilities/graphApi', () => {

  describe('flatSearchRes', () => {
    it('should return flatten data', () => {
      const mockResult = [
        {
          searchTerms: [
            'Item',
          ],
          hitsContainers: [
            {
              total: 20,
              moreResultsAvailable: false,
              hits: [
                {
                  hitId: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
                  rank: 1,
                  summary: '当社第××期（２０２１年度）決算につきましてご報告いたします <ddd/> 内容につきましては、下記よりご覧ください <ddd/> 〈本件に関するお問い合わせ先〉      経営部　小田 <ddd/> ',
                  resource: {
                    id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
                    createdDateTime: '2021-08-24T03:00:59Z',
                    lastModifiedDateTime: '2021-08-24T03:01:08Z',
                    webUrl: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx?ID=20',
                    sharepointIds: {
                      listId: 'ba083c0c-c77c-4c1b-8c6b-f767f453104d',
                      listItemId: '20',
                    },
                    parentReference: {
                      siteId: 'projectgeranium.sharepoint.com,3373ca41-5972-46a5-b93d-ca5fca89a973,fbede796-e258-49e5-b26f-d32753b47fc0',
                    },
                    fields: {
                      title: '第××期（２０２１年度）決算のご報告_0020',
                    },
                  },
                },
                {
                  hitId: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
                  rank: 2,
                  summary: '下記の通り通知致します <ddd/> 異動　2021年△月〇日付     　　 山田　花子（人事部 <ddd/> ',
                  resource: {
                    id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
                    createdDateTime: '2021-08-24T02:39:48Z',
                    lastModifiedDateTime: '2021-08-24T02:39:48Z',
                    webUrl: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx?ID=19',
                    sharepointIds: {
                      listId: 'ba083c0c-c77c-4c1b-8c6b-f767f453104d',
                      listItemId: '19',
                    },
                    parentReference: {
                      siteId: 'projectgeranium.sharepoint.com,3373ca41-5972-46a5-b93d-ca5fca89a973,fbede796-e258-49e5-b26f-d32753b47fc0',
                    },
                    fields: {
                      title: '2021年度社内通知（第〇回）_0019',
                    },
                  },
                },
              ],
            },
          ],
        },
      ] as unknown as microsoftGraph.SearchResponse[];

      expect(flatSearchRes(mockResult, (hit) => hit)).toStrictEqual([
        {
          hitId: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
          rank: 1,
          summary: '当社第××期（２０２１年度）決算につきましてご報告いたします <ddd/> 内容につきましては、下記よりご覧ください <ddd/> 〈本件に関するお問い合わせ先〉      経営部　小田 <ddd/> ',
          resource: {
            id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
            createdDateTime: '2021-08-24T03:00:59Z',
            lastModifiedDateTime: '2021-08-24T03:01:08Z',
            webUrl: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx?ID=20',
            sharepointIds: {
              listId: 'ba083c0c-c77c-4c1b-8c6b-f767f453104d',
              listItemId: '20',
            },
            parentReference: {
              siteId: 'projectgeranium.sharepoint.com,3373ca41-5972-46a5-b93d-ca5fca89a973,fbede796-e258-49e5-b26f-d32753b47fc0',
            },
            fields: {
              title: '第××期（２０２１年度）決算のご報告_0020',
            },
          },
        },
        {
          hitId: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
          rank: 2,
          summary: '下記の通り通知致します <ddd/> 異動　2021年△月〇日付     　　 山田　花子（人事部 <ddd/> ',
          resource: {
            id: '77fb00d8-3af5-41e1-9a20-4f5dc3140002',
            createdDateTime: '2021-08-24T02:39:48Z',
            lastModifiedDateTime: '2021-08-24T02:39:48Z',
            webUrl: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx?ID=19',
            sharepointIds: {
              listId: 'ba083c0c-c77c-4c1b-8c6b-f767f453104d',
              listItemId: '19',
            },
            parentReference: {
              siteId: 'projectgeranium.sharepoint.com,3373ca41-5972-46a5-b93d-ca5fca89a973,fbede796-e258-49e5-b26f-d32753b47fc0',
            },
            fields: {
              title: '2021年度社内通知（第〇回）_0019',
            },
          },
        },
      ]);
    });
  });
});
