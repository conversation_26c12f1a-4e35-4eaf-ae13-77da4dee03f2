import dayjs from 'dayjs';
import {
  DateFormatTemplate,
  DateFormatTemplateType,
  getOffsetDate,
  getRelativeDate,
  getZeroToday, isInvalidDate,
  isSameDates,
  normalizeUtcOrNotUtcDate,
  getYearMonthDay,
} from './date';
import * as dateUtility from './date';

jest.mock('./environment');

const doAsMockedDateTimeFormat = (dateTimeFormat: Intl.DateTimeFormat, body: () => void) => {
  jest.spyOn(Intl, 'DateTimeFormat').mockImplementation(() => dateTimeFormat);
  body();
};

describe('toDateString', () => {
  const d = new Date(2020, 0, 5, 0, 0, 0);

  const doTest = (
    language: string,
    expected: string,
    format?: DateFormatTemplateType | undefined,
  ) => {
    const mockDateTimeFormat = new Intl.DateTimeFormat(language);
    doAsMockedDateTimeFormat(mockDateTimeFormat, () => {
      expect(dateUtility.toDateString(d, format)).toBe(expected);
    });
  };

  it('should be 2020/1/5 for ja-jp', () => doTest('ja-JP', '2020/1/5'));
  it('should be 1/5/2020 for en-us', () => doTest('en-US', '1/5/2020'));
  it('should be blank for null', () => expect(dateUtility.toDateString(null)).toBe(''));
  it('should be blank for undefined', () => expect(dateUtility.toDateString(undefined)).toBe(''));
  it('should be 1/5 for ja-JP', () => doTest('ja-JP', '1/5', DateFormatTemplate.MonthDay));
  it('should be 2020/1/5 for ja-JP', () => doTest('ja-JP', '2020/1/5', DateFormatTemplate.YearMonthDay));
  it('should be 2020/1/5 (日) 00:00 for ja-JP', () => doTest('ja-JP', '2020/1/5（日）00:00', DateFormatTemplate.YearMonthDayTimeWithWeekday));
});

describe('toTimeString', () => {
  const t = new Date(2020, 0, 5, 14, 5, 3);
  const doTest = (language: string, expected: string) => {
    // 実装の一部まで書いてしまっているので、これがmockかというと微妙だけど、置き換える方法が他にないため
    const mockDateTimeFormat = new Intl.DateTimeFormat(language, { timeStyle: 'medium' });
    doAsMockedDateTimeFormat(mockDateTimeFormat, () => {
      expect(dateUtility.toTimeString(t)).toBe(expected);
    });
  };
  it('should be 14:05:03 for ja-jp', () => doTest('ja-JP', '14:05:03'));
  it('should be 2:05:03 PM for en-us', () => doTest('en-US', '2:05:03 PM'));
  it('should be blank for null', () => expect(dateUtility.toTimeString(null)).toBe(''));
  it('should be blank for undefined', () => expect(dateUtility.toTimeString(undefined)).toBe(''));
});

describe('toDatePart', () => {
  const t = new Date(2020, 0, 5, 14, 5, 3);
  it('should be equal to date-only value', () => expect(dateUtility.toDatePart(t)).toEqual(new Date(2020, 0, 5)));
  it('should be null for null', () => expect(dateUtility.toDatePart(null)).toBeNull());
  it('should be null for undefined', () => expect(dateUtility.toDatePart(undefined)).toBeNull());
});

describe('toTimePart', () => {
  const t = new Date(2020, 0, 5, 15, 3, 2);
  const timeValue = ((15 * 60 + 3) * 60 + 2) * 1000;
  it('should be time-only value', () => expect(dateUtility.toTimePart(t).valueOf()).toEqual(timeValue));
  it('should be null for null', () => expect(dateUtility.toTimePart(null)).toBeNull());
  it('should be null for undefined', () => expect(dateUtility.toTimePart(undefined)).toBeNull());
});

describe('toApiDate', () => {
  const t = new Date(2020, 0, 5, 0, 1, 3);
  const t2 = new Date(2020, 0, 5, 23, 41, 3);
  it('should be date even if local timezone is positive', () => expect(dateUtility.toApiDate(t)).toEqual('2020-01-05'));
  it('should be date even if local timezone is negative', () => expect(dateUtility.toApiDate(t2)).toEqual('2020-01-05'));
  it('should be null for null', () => expect(dateUtility.toApiDate(null)).toBeNull());
  it('should be null for undefined', () => expect(dateUtility.toApiDate(undefined)).toBeNull());
});

describe('relativeDate', () => {
  it('should return blank if input is null or undefined', () => {
    expect(getRelativeDate(undefined)).toBe('');
    expect(getRelativeDate(null)).toBe('');
  });

  it('should return blank if the input is NaN or invalid date string', () => {
    expect(getRelativeDate(NaN)).toBe('');
    expect(getRelativeDate('Invalid Date String Format')).toBe('');
  });

  it('should return a relative value in minutes', () => {
    const relativeDate = getRelativeDate(new Date().getTime() - 10 * 60 * 1000);
    expect(relativeDate).toBe('10 分前');

    const dateStr = new Date(new Date().getTime() - 10 * 60 * 1000).toISOString();
    expect(getRelativeDate(dateStr)).toBe('10 分前');
  });

  it('should not return a relative value in minutes', () => {
    const relativeDate = getRelativeDate(new Date().getTime() - 85 * 60 * 1000);
    expect(relativeDate).not.toBe('85 minutes ago');

    const dateStr = new Date(new Date().getTime() - 85 * 60 * 1000).toISOString();
    expect(getRelativeDate(dateStr)).not.toBe('85 minutes ago');
  });

  it('should return a relative value in hours', () => {
    const relativeDate = getRelativeDate(new Date().getTime() - 15 * 60 * 60 * 1000);
    expect(relativeDate).toBe('15 時間前');

    const dateStr = new Date(new Date().getTime() - 15 * 60 * 60 * 1000).toISOString();
    expect(getRelativeDate(dateStr)).toBe('15 時間前');
  });

  it('should not return a relative value in hours', () => {
    const relativeDate = getRelativeDate(new Date().getTime() - 25 * 60 * 60 * 1000);
    expect(relativeDate).not.toBe('25 hours ago');

    const dateStr = new Date(new Date().getTime() - 25 * 60 * 60 * 1000).toISOString();
    expect(getRelativeDate(dateStr)).not.toBe('25 hours ago');
  });

  it('should round the time up', () => {
    const relativeDate = getRelativeDate(new Date().getTime() - 55 * 60 * 1000);
    expect(relativeDate).toBe('1 時間前');

    const dateStr = new Date(new Date().getTime() - 55 * 60 * 1000).toISOString();
    expect(getRelativeDate(dateStr)).toBe('1 時間前');
  });

  it('should round the time down', () => {
    const relativeDate = getRelativeDate(new Date().getTime() - 65 * 60 * 1000);
    expect(relativeDate).toBe('1 時間前');

    const dateStr = new Date(new Date().getTime() - 65 * 60 * 1000).toISOString();
    expect(getRelativeDate(dateStr)).toBe('1 時間前');
  });

  it('should return the absolute date', () => {
    const relativeDate = getRelativeDate(new Date('December 17, 1995 03:24:00').getTime());
    expect(relativeDate).toBe('1995/12/17');

    const displayDate = getRelativeDate(new Date('December 17, 1995 03:24:00').getTime(), DateFormatTemplate.MonthDay);
    expect(displayDate).toBe('12/17');

    const dateStr = new Date(new Date('December 17, 1995 03:24:00').getTime()).toISOString();
    expect(getRelativeDate(dateStr)).toBe('1995/12/17');
  });
});

describe('getYearMonthDay', () => {
  it('should return blank if input is null or undefined', () => {
    expect(getYearMonthDay(undefined)).toBe('');
    expect(getYearMonthDay(null)).toBe('');
  });
  it('should return yearMonthDay', () => {
    const yearMonthDay = getYearMonthDay(new Date(2020, 0, 5, 0, 1, 3).getTime());
    expect(getYearMonthDay(yearMonthDay)).toBe('2020/1/5');
    const dateStr = new Date(new Date('December 17, 1995 03:24:00').getTime()).toISOString();
    expect(getYearMonthDay(dateStr)).toBe('1995/12/17');
    expect(getYearMonthDay('2021-01-01T00:00:00.000Z')).toBe('2021/1/1');
  });
});

describe('isSameDates', () => {
  describe('when one of them is an invalid value', () => {
    it('should return false', () => {
      expect(isSameDates(undefined, '2021/01/01', 'day')).toBe(false);
      expect(isSameDates('2021/01/01', undefined, 'day')).toBe(false);
      expect(isSameDates(null, '2021/01/01', 'day')).toBe(false);
      expect(isSameDates('2021/01/01', null, 'day')).toBe(false);
      expect(isSameDates('abcd', '2021/01/01', 'day')).toBe(false);
      expect(isSameDates('2021/01/01', 'abcd', 'day')).toBe(false);
    });
  });

  describe('when A and B are not the same', () => {
    it('should return false', () => {
      expect(isSameDates('2021-01-01T00:00:00.000Z', '2021-01-02T00:00:00.000Z', 'days')).toBe(false);
      expect(isSameDates('2021-01-01T00:00:00.000Z', '2021-01-01T01:00:00.000Z', 'hours')).toBe(false);
      expect(isSameDates('2021-01-01T00:00:00.000Z', '2021-01-01T00:01:00.000Z', 'minutes')).toBe(false);
    });
  });

  describe('when A and B are the same', () => {
    it('should return true', () => {
      expect(isSameDates('2021-01-02T00:00:00.000Z', '2021-01-02T00:00:00.000Z', 'days')).toBe(true);
      expect(isSameDates('2021-01-01T01:00:00.000Z', '2021-01-01T01:00:00.000Z', 'hours')).toBe(true);
      expect(isSameDates('2021-01-01T00:01:00.000Z', '2021-01-01T00:01:00.000Z', 'minutes')).toBe(true);
    });
  });
});

describe('getOffsetDate', () => {
  const testDateISO = '2021-01-01T00:00:00Z';

  function expectsInBothTypes(offset: number, result1: Date, result2: dayjs.Dayjs) {
    describe('when baseDate is typeof Date', () => {
      const date = new Date(testDateISO);

      it('should return a date instance', () => {
        expect(getOffsetDate(date, offset)).toStrictEqual(result1);
      });
    });

    describe('when baseDate is typeof Dayjs', () => {
      const date = dayjs(testDateISO);

      it('should return a dayjs instance', () => {
        expect(getOffsetDate(date, offset)).toStrictEqual(result2);
      });
    });
  }

  describe('when offsetHours = 0', () => {
    const offsetHours = 0;
    const expectDateISO = '2021-01-01T00:00:00.000Z';
    expectsInBothTypes(offsetHours, new Date(expectDateISO), dayjs(expectDateISO));
  });

  describe('when offsetHours = +1', () => {
    const offsetHours = 1;
    const expectDateISO = '2021-01-01T01:00:00.000Z';
    expectsInBothTypes(offsetHours, new Date(expectDateISO), dayjs(expectDateISO));
  });

  describe('when offsetHours = -1', () => {
    const offsetHours = -1;
    const expectDateISO = '2020-12-31T23:00:00.000Z';
    expectsInBothTypes(offsetHours, new Date(expectDateISO), dayjs(expectDateISO));
  });
});

describe('getZeroToday', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  const fakeTimes = [
    '2020-01-12T00:00:00.000+09:00',
    '2020-01-12T12:05:12.134+09:00',
    '2020-01-12T23:59:59.395+09:00',
  ];

  fakeTimes.forEach((time) => {
    describe(`when the time is ${time}`, () => {
      beforeEach(() => {
        jest.setSystemTime(new Date(time));
      });

      it('should return 2020-01-12T00:00:00+09:00', () => {
        expect(getZeroToday()).toStrictEqual(new Date('2020-01-12T00:00:00+09:00'));
      });
    });
  });
});

describe('isInvalidDate', () => {
  describe('when the date is invalid', () => {
    it('should return true', () => {
      expect(isInvalidDate(undefined)).toBe(true);
      expect(isInvalidDate(null)).toBe(true);
      expect(isInvalidDate('')).toBe(true);
      expect(isInvalidDate(new Date('abc'))).toBe(true);
    });
  });

  describe('when the date is valid', () => {
    it('should return false', () => {
      expect(isInvalidDate('2000-01-01T00:00:00.000+09:00')).toBe(false);
      expect(isInvalidDate(new Date('2000-01-01T00:00:00.000Z'))).toBe(false);
    });
  });
});

describe('normalizeUtcOrNotUtcDate', () => {
  [
    ['2000-01-01T19:00:00', '2000-01-01T10:00:00Z'],
    ['2000-01-01T00:00:00Z', '2000-01-01T00:00:00Z'],
  ].forEach(([input, expected]) => {

    it('should return ISO date string', () => {
      expect(new Date(normalizeUtcOrNotUtcDate(input)).getTime())
        .toEqual(new Date(expected).getTime());
    });
  });

  it('should return blank', () => {
    expect(normalizeUtcOrNotUtcDate(undefined)).toBe('');
    expect(normalizeUtcOrNotUtcDate(null)).toBe('');
    expect(normalizeUtcOrNotUtcDate('a')).toBe('');
  });

});
