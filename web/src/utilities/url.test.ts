import {
  complementRelativeUrl, createDeepLinkOfPost, createSharedLinkWithTitle, isAbsoluteUrl,
} from './url';

describe('utilities/url', () => {

  describe('isAbsoluteUrl', () => {

    describe('when the urlString is absolute url', () => {
      it('should return true', () => {
        expect(isAbsoluteUrl('https://example.com')).toBe(true);
        expect(isAbsoluteUrl('http://example.com')).toBe(true);
        expect(isAbsoluteUrl('ftp://example.com')).toBe(true);
        expect(isAbsoluteUrl('sftp://example.com')).toBe(true);
        expect(isAbsoluteUrl('file://example.com')).toBe(true);
        // テストデータとして使用
        // eslint-disable-next-line no-script-url
        expect(isAbsoluteUrl('javascript:void(0)')).toBe(true);
      });
    });

    describe('when the urlString is not an absolute url', () => {
      expect(isAbsoluteUrl('example.html')).toBe(false);
      expect(isAbsoluteUrl('.example.com')).toBe(false);
      expect(isAbsoluteUrl('./example.html')).toBe(false);
      expect(isAbsoluteUrl('../example/index.html')).toBe(false);
    });
  });

  describe('complementRelativeUrl', () => {

    describe('when the relativeUrl is an absolute url already', () => {
      it('should return the relativeUrl as is', () => {
        expect(complementRelativeUrl(
          'https://google.com/current/path',
          'https://example.com',
        )).toBe('https://example.com');

        expect(complementRelativeUrl(
          'https://google.com/current/path',
          'http://example.com',
        )).toBe('http://example.com');

        expect(complementRelativeUrl(
          'https://example.com/current/path/',
          // テストデータとして使用
          // eslint-disable-next-line no-script-url
          'javascript:void(0)',
          // テストデータとして使用
          // eslint-disable-next-line no-script-url
        )).toBe('javascript:void(0)');
      });
    });

    describe('when the relativeUrl is an in-page link', () => {
      it('should return the relativeUrl as is', () => {
        expect(complementRelativeUrl(
          'https://google.com/current/path',
          '#top',
        )).toBe('#top');
      });
    });

    describe('when the absUrl is not an absolute url', () => {
      it('should return the relativeUrl as is', () => {
        expect(complementRelativeUrl(
          '/not/an/absolute/url',
          './index.html',
        )).toBe('./index.html');
      });
    });

    describe('when the absUrl is "https://example.com/current/path/"', () => {

      describe('when the relativeUrl is "/sites/index.html"', () => {
        it('should return "https://example.com/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path/',
            '/sites/index.html',
          )).toBe('https://example.com/sites/index.html');
        });
      });

      describe('when the relativeUrl is "sites/index.html"', () => {
        it('should return "https://example.com/current/path/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path/',
            'sites/index.html',
          )).toBe('https://example.com/current/path/sites/index.html');
        });
      });

      describe('when the relativeUrl is "./sites/index.html"', () => {
        it('should return "https://example.com/current/path/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path/',
            './sites/index.html',
          )).toBe('https://example.com/current/path/sites/index.html');
        });
      });

      describe('when the relativeUrl is "../sites/index.html"', () => {
        it('should return "https://example.com/current/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path/',
            '../sites/index.html',
          )).toBe('https://example.com/current/sites/index.html');
        });
      });

      describe('when the relativeUrl is "../../sites/index.html"', () => {
        it('should return "https://example.com/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path/',
            '../../sites/index.html',
          )).toBe('https://example.com/sites/index.html');
        });
      });

      describe('when the relativeUrl is "../../../sites/index.html"', () => {
        // 相対パスが遡りきれないときはルート直下のパスに変換される
        it('should return "https://example.com/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path',
            '../../../sites/index.html',
          )).toBe('https://example.com/sites/index.html');
        });
      });
    });

    // basUrlの最後にスラッシュが付くか付かないかで相対パス解決の挙動が違うので注意
    describe('when the absUrl is "https://example.com/current/path"', () => {

      describe('when the relativeUrl is "sites/index.html"', () => {
        it('should return "https://example.com/current/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path',
            'sites/index.html',
          )).toBe('https://example.com/current/sites/index.html');
        });
      });

      describe('when the relativeUrl is "./sites/index.html"', () => {
        it('should return "https://example.com/current/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path',
            './sites/index.html',
          )).toBe('https://example.com/current/sites/index.html');
        });
      });

      describe('when the relativeUrl is "../sites/index.html"', () => {
        it('should return "https://example.com/sites/index.html"', () => {
          expect(complementRelativeUrl(
            'https://example.com/current/path',
            '../sites/index.html',
          )).toBe('https://example.com/sites/index.html');
        });
      });

    });

  });
});

describe('createDeepLinkOfPost', () => {
  describe('when the appCatalogueId is falsy', () => {
    expect(createDeepLinkOfPost('', 'abc')).toBe('');
  });

  describe('when the spoGuid is falsy', () => {
    it('should return blank', () => {
      expect(createDeepLinkOfPost('abc', '')).toBe('');
      expect(createDeepLinkOfPost('abc', null)).toBe('');
      expect(createDeepLinkOfPost('abc', undefined)).toBe('');
    });
  });

  describe('when the both parameters have values', () => {
    it('should return a deep link', () => {
      const expectedContext = encodeURIComponent(JSON.stringify({ subEntityId: 'search:spo:123' }));
      const expectedUrl = `https://teams.microsoft.com/l/entity/abc/geranium_tab?context=${expectedContext}`;
      expect(createDeepLinkOfPost('abc', '123')).toBe(expectedUrl);
    });
  });
});

describe('createSharedLinkWithTitle', () => {
  describe('when the parameters are not enough to generating deep link', () => {
    const appCatalogueId = '';
    const spoGuid = '';

    it('should return blank', () => {
      expect(createSharedLinkWithTitle(appCatalogueId, spoGuid, 'abc')).toBe('');
    });
  });

  describe('when the parameters are correct to generating deep link', () => {
    const appCatalogueId = 'abc';
    const spoGuid = '123';

    describe('when the title is falsy', () => {
      const title = '';

      it('should return only deep link', () => {
        const expectedContext = encodeURIComponent(JSON.stringify({ subEntityId: 'search:spo:123' }));
        const expectedUrl = `https://teams.microsoft.com/l/entity/abc/geranium_tab?context=${expectedContext}`;
        expect(createSharedLinkWithTitle(appCatalogueId, spoGuid, title)).toBe(expectedUrl);
      });
    });

    describe('when the title has value', () => {
      const title = 'DEF';

      it('should return title and deep link', () => {
        const expectedContext = encodeURIComponent(JSON.stringify({ subEntityId: 'search:spo:123' }));
        const expectedUrl = `https://teams.microsoft.com/l/entity/abc/geranium_tab?context=${expectedContext}`;
        expect(createSharedLinkWithTitle(appCatalogueId, spoGuid, title)).toBe(`DEF ${expectedUrl}`);
      });
    });
  });
});
