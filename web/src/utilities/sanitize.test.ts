import sanitizeHTML from 'sanitize-html';
import { HTML_CHAT_CONTENT_MOCK, HTML_CONTENT_MOCK } from '../mocks/innner-html-example';
import {
  addRelativePathReplacer,
  chatImgSrcDispatcherImpl,
  createSanitizeOptions, DATA_ATTR_MAIL_SRC, DATA_ATTR_SPO_SRC,
  mailImgSrcDispatcherImpl,
  isAttachDataSpoSrc,
  makeMailSanitizedInnerHtml,
  makeSpoSanitizedInnerHtml,
  SANITIZER_EXTRA_ALLOWED_ATTRIBUTES,
  SANITIZER_EXTRA_ALLOWED_TAGS,
  transformSpoTagImpl,
  makeChatSanitizedInnerHtml,
  DATA_ATTR_CHAT_SRC,
  DATA_ATTR_CHAT_MENTION_ID,
  DATA_ATTR_CHAT_ATTACHMENT_ID,
  DATA_ATTR_CHAT_EMOJI_ALT,
} from './sanitize';
import environment from './environment';

jest.mock('./environment');

describe('utilities/sanitize', () => {
  beforeEach(() => {
    environment.REACT_APP_SHAREPOINT_HOST_NAME = 'projectgeranium.sharepoint.com';
  });
  describe('SANITIZER_EXTRA_ALLOWED', () => {
    it('should include specific tags', () => {
      expect(SANITIZER_EXTRA_ALLOWED_TAGS).toStrictEqual(expect.arrayContaining(
        ['big', 'blink', 'image', 'nobr', 'strike', 'center', 'font', 'img'],
      ));
    });
  });

  describe('SANITIZER_EXTRA_ALLOWED_ATTRIBUTES', () => {
    it('should include specific attributes', () => {
      expect(SANITIZER_EXTRA_ALLOWED_ATTRIBUTES).toStrictEqual(expect.arrayContaining([
        // data-spo-srcを許可しないとトリムされてしまう
        DATA_ATTR_SPO_SRC, DATA_ATTR_MAIL_SRC, DATA_ATTR_CHAT_SRC,
        DATA_ATTR_CHAT_ATTACHMENT_ID, DATA_ATTR_CHAT_MENTION_ID, DATA_ATTR_CHAT_EMOJI_ALT,
        // a
        'abbr', 'align', 'alink', 'alt', 'axis',
        // b
        'background', 'bgcolor', 'border',
        // c
        'center', 'cellpadding', 'cellspacing', 'char', 'charoff', 'cite',
        'class', 'clear', 'color', 'colspan', 'compact',
        // d
        'datetime', 'dir',
        // f
        'face', 'frame', 'frameborder',
        // h
        'headers', 'height', 'hreflang', 'hspace',
        // i
        'id', 'ismap', 'lang', 'longdesc',
        // m
        'marginheight', 'marginwidth', 'media',
        // n
        'noresize', 'noshade', 'nowrap',
        // r
        'rel', 'rev', 'rowspan', 'rules',
        // s
        'scope', 'scrolling', 'size', 'span', 'start', 'style', 'summary',
        // t
        'text', 'title', 'type',
        // v
        'valign', 'value',
        // w
        'width',
      ]));
    });
  });

  describe('createSanitizeOptions', () => {
    it('should return IOptions type', () => {
      const expected = {
        allowedTags: sanitizeHTML.defaults.allowedTags.concat(SANITIZER_EXTRA_ALLOWED_TAGS),
        disallowedTagsMode: 'recursiveEscape',
        allowedAttributes: {
          ...sanitizeHTML.defaults.allowedAttributes,
          font: ['*'],
          '*': SANITIZER_EXTRA_ALLOWED_ATTRIBUTES,
        },
        nonTextTags: [
          'style',
          'script',
          'meta',
        ],
        allowedSchemes: ['http', 'https', 'cid'],
      };
      expect(
        createSanitizeOptions(),
      ).toStrictEqual(expected);
    });
  });

  describe('isAttachDataSpoSrc', () => {
    const absUrl = 'https://projectgeranium.sharepoint.com';
    describe('when Absolute URLs that are not SharePoint domains, or the site path begins with "/_"', () => {
      it('should return false', () => {
        expect(
          isAttachDataSpoSrc(absUrl, '/_layouts/sample.jpg'),
        ).toBe(false);
        expect(
          isAttachDataSpoSrc(absUrl, 'https://sample.com/sites/sample.jpg'),
        ).toBe(false);
        expect(
          isAttachDataSpoSrc(absUrl, 'https://sample.com/_sites/sample.jpg'),
        ).toBe(false);
        expect(
          isAttachDataSpoSrc(absUrl, 'https://sample.com/_layouts/sample.jpg'),
        ).toBe(false);
      });
    });

    describe('when relative URL or a SharePoint domain', () => {
      it('should return true', () => {
        expect(
          isAttachDataSpoSrc(absUrl, '/sites/sample.jpg'),
        ).toBe(true);
        expect(
          isAttachDataSpoSrc(absUrl, 'https://projectgeranium.sharepoint.com/Shared%20Documents/Test.png'),
        ).toBe(true);
      });
    });
  });

  describe('transformSpoTagImpl', () => {
    describe('When the attribute specified in attrName does not exist', () => {
      it('should not add the attribute specified by attrName', () => {
        const attributes = {
          alt: 'alt-string',
        };
        expect(
          transformSpoTagImpl('attrName', 'img', attributes),
        ).toStrictEqual({
          attribs: {
            alt: 'alt-string',
          },
          tagName: 'img',
        });
      });
    });

    describe('When tagName is not "img"', () => {
      it('should not add "alt" attribute', () => {
        const attributes = {
          href: '/sites/projectgeranium/SitePages/Home.aspx',
        };
        expect(
          transformSpoTagImpl('href', 'a', attributes),
        ).toStrictEqual({
          attribs: {
            href: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/SitePages/Home.aspx',
          },
          tagName: 'a',
        });
      });
    });

    describe('When tagName is "img"', () => {
      it('should return false', () => {
        const attributes1 = {
          alt: 'alt-string',
          src: '/sites/サンプル.png',
        };
        expect(
          transformSpoTagImpl('src', 'img', attributes1),
        ).toStrictEqual({
          attribs: {
            alt: 'alt-string',
            src: 'https://projectgeranium.sharepoint.com/sites/%E3%82%B5%E3%83%B3%E3%83%97%E3%83%AB.png',
            'data-spo-src': '/sites/サンプル.png',
          },
          tagName: 'img',
        });

        const attributes2 = {
          src: 'https://sample.com/sites/サンプル.png',
        };
        expect(
          transformSpoTagImpl('src', 'img', attributes2),
        ).toStrictEqual({
          attribs: {
            alt: ' ',
            src: 'https://sample.com/sites/サンプル.png',
          },
          tagName: 'img',
        });
      });
    });
  });

  describe('addRelativePathReplacer', () => {
    const options = addRelativePathReplacer();

    it('should add a transformer to replace href of anchor tags', () => {
      expect(options.transformTags?.a).not.toBeUndefined();
      if (!options.transformTags) return;
      if (typeof options.transformTags.a === 'string') return;

      expect(
        options.transformTags.a('a', { href: '/item/url.html', title: 'abc' }),
      ).toStrictEqual({
        tagName: 'a',
        attribs: {
          title: 'abc',
          href: 'https://projectgeranium.sharepoint.com/item/url.html',
        },
      });
    });

    it('should add a transformer to replace src of img tags', () => {
      expect(options.transformTags?.img).not.toBeUndefined();
      if (!options.transformTags) return;
      if (typeof options.transformTags.img === 'string') return;

      expect(
        options.transformTags.img('img', { src: '/item/url.jpg', width: '100' }),
      ).toStrictEqual({
        tagName: 'img',
        attribs: {
          width: '100',
          alt: ' ',
          'data-spo-src': '/item/url.jpg',
          src: 'https://projectgeranium.sharepoint.com/item/url.jpg',
        },
      });

      expect(
        options.transformTags.img('img', { src: 'https://example.com/url.jpg', width: '100', alt: 'efg' }),
      ).toStrictEqual({
        tagName: 'img',
        attribs: {
          width: '100',
          alt: 'efg',
          src: 'https://example.com/url.jpg',
        },
      });
    });
  });

  describe('mailImgSrcDispatcherImpl', () => {
    describe('When the attribute specified in attrName does not exist', () => {
      it('should not add the attribute specified by attrName', () => {
        const attributes = {
          alt: 'alt-string',
        };
        expect(
          mailImgSrcDispatcherImpl('attrName', 'img', attributes),
        ).toStrictEqual({
          attribs: {
            alt: 'alt-string',
          },
          tagName: 'img',
        });
      });
    });

    describe('When tagName is not "img"', () => {
      it('should not do nothing', () => {
        const attributes = {
          href: '/sites/projectgeranium/SitePages/Home.aspx',
        };
        expect(
          mailImgSrcDispatcherImpl('href', 'a', attributes),
        ).toStrictEqual({
          attribs: {
            href: '/sites/projectgeranium/SitePages/Home.aspx',
          },
          tagName: 'a',
        });
      });
    });

    describe('When tagName is "img"', () => {
      it('should not set custom attribute', () => {
        const attributes = {
          src: 'https://sample.com/sites/サンプル.png',
        };
        expect(
          mailImgSrcDispatcherImpl('src', 'img', attributes),
        ).toStrictEqual({
          attribs: {
            alt: ' ',
            src: 'https://sample.com/sites/サンプル.png',
          },
          tagName: 'img',
        });
      });

      it('should set custom Attribute', () => {
        const attributes1 = {
          src: 'cid:aaaa',
        };
        const attributes2 = {
          src: 'cid:bbbb',
        };
        expect(
          mailImgSrcDispatcherImpl('src', 'img', attributes1),
        ).toStrictEqual({
          attribs: {
            alt: ' ',
            src: 'cid:aaaa',
            'data-mail-src': 'cid:aaaa',
          },
          tagName: 'img',
        });

        expect(
          mailImgSrcDispatcherImpl('src', 'img', attributes2),
        ).toStrictEqual({
          attribs: {
            alt: ' ',
            src: 'cid:bbbb',
            'data-mail-src': 'cid:bbbb',
          },
          tagName: 'img',
        });
      });
    });
  });

  describe('chatImgSrcDispatcherImpl', () => {
    describe('When the attribute specified in attrName does not exist', () => {
      it('should not add the attribute specified by attrName', () => {
        const attributes = {
          alt: 'alt-string',
        };
        expect(
          chatImgSrcDispatcherImpl('attrName', 'img', attributes),
        ).toStrictEqual({
          attribs: {
            alt: 'alt-string',
          },
          tagName: 'img',
        });
      });
    });

    describe('When tagName is not "img"', () => {
      it('should not do nothing', () => {
        const attributes = {
          href: 'https://graph.microsoft.com/v1.0/test/$value',
        };
        expect(
          chatImgSrcDispatcherImpl('href', 'a', attributes),
        ).toStrictEqual({
          attribs: {
            href: 'https://graph.microsoft.com/v1.0/test/$value',
          },
          tagName: 'a',
        });
      });
    });

    describe('When tagName is "img"', () => {
      it('should not set custom attribute', () => {
        const attributes = {
          src: 'https://sample.com/sites/サンプル.png',
        };
        expect(
          chatImgSrcDispatcherImpl('src', 'img', attributes),
        ).toStrictEqual({
          attribs: {
            alt: ' ',
            src: 'https://sample.com/sites/サンプル.png',
          },
          tagName: 'img',
        });
      });

      it('should set custom Attribute', () => {
        const attributes1 = {
          src: 'https://graph.microsoft.com/v1.0/test1/$value',
        };
        const attributes2 = {
          src: 'https://graph.microsoft.com/v1.0/test2/$value',
        };
        expect(
          chatImgSrcDispatcherImpl('src', 'img', attributes1),
        ).toStrictEqual({
          attribs: {
            alt: ' ',
            src: 'https://graph.microsoft.com/v1.0/test1/$value',
            'data-chat-src': 'https://graph.microsoft.com/v1.0/test1/$value',
          },
          tagName: 'img',
        });

        expect(
          chatImgSrcDispatcherImpl('src', 'img', attributes2),
        ).toStrictEqual({
          attribs: {
            alt: ' ',
            src: 'https://graph.microsoft.com/v1.0/test2/$value',
            'data-chat-src': 'https://graph.microsoft.com/v1.0/test2/$value',
          },
          tagName: 'img',
        });
      });
    });
  });

  describe('makeSpoSanitizedInnerHtml', () => {

    it('should return sanitized content', () => {
      // "__"を使用する戻り値のため必要
      // eslint-disable-next-line no-underscore-dangle
      const result = makeSpoSanitizedInnerHtml(HTML_CONTENT_MOCK)?.__html;
      expect(result).not.toBe(undefined);
      if (!result) return;

      // サニタイズされていることを確認
      expect(result).not.toMatch('<html');
      expect(result).not.toMatch('<body');
      expect(result).not.toMatch('<iframe');
      expect(result).not.toMatch('<link');
      expect(result).not.toMatch('<script');

      // サニタイズされていないことを確認
      expect(result).toMatch('<style');
      expect(result).toMatch('<table border="1px">');
      expect(result).toMatch('<tbody>');
      expect(result).toMatch('<tr height="100px">');
      expect(result).toMatch('<td colspan="2" rowspan="2">');
      expect(result).toMatch('<div width="100">');
      expect(result).toMatch('<ul>');
      expect(result).toMatch('<li>');
      expect(result).toMatch('<ol>');
      expect(result).toMatch('<p>');
      expect(result).toMatch('<a href="https://example.com/">');
      expect(result).toMatch('<font color="red">');
      expect(result).toMatch('<span style="height:10em">abcd</span>');
      expect(result).toMatch('</font>');
      expect(result).toMatch('</a>');
      expect(result).toMatch('</p>');
      expect(result).toMatch('</li>');
      expect(result).toMatch('</ol>');
      expect(result).toMatch('</ul>');
      expect(result).toMatch('</div>');
      expect(result).toMatch('</td>');
      expect(result).toMatch('</tr>');
      expect(result).toMatch('</tbody>');
      expect(result).toMatch('</table>');

      // URLの変換結果を確認
      // 絶対URLは維持される 画像タグにはaltが付く
      expect(result).toMatch('<img src="https://example.com/image.png" height="200" alt=" " />');
      expect(result).toMatch('<a href="https://example.com/">');

      // 相対URLは絶対URL化される 画像タグにはaltが付く
      expect(result).toMatch('<img src="https://projectgeranium.sharepoint.com/img/image.png" height="200" alt=" " data-spo-src="/img/image.png" />');
      expect(result).toMatch('<a href="https://projectgeranium.sharepoint.com/123/site.html">site</a>');

      // アンカーリンクはそのまま維持される
      expect(result).toMatch('<a href="#abc">abc</a>');

      // 無関係の形式のURLは変換されない 画像タグにはaltが付く
      expect(result).toMatch('<img src="cid:aaaa" height="200" alt=" " />');
      expect(result).toMatch('<img src="https://graph.microsoft.com/info.png" height="200" alt=" " />');
      expect(result).toMatch('<img src="https://graph.microsoft.com/v1.0/test/$value" height="200" alt=" " />');
    });
  });

  describe('makeMailSanitizedInnerHtml', () => {
    it('should return sanitized content', () => {
      // "__"を使用する戻り値のため必要
      // eslint-disable-next-line no-underscore-dangle
      const result = makeMailSanitizedInnerHtml(HTML_CONTENT_MOCK)?.__html;
      expect(result).not.toBe('');
      if (!result) return;

      // サニタイズされていることを確認
      expect(result).not.toMatch('<html');
      expect(result).not.toMatch('<body');
      expect(result).not.toMatch('<iframe');
      expect(result).not.toMatch('<link');
      expect(result).not.toMatch('<script');

      // サニタイズされていないことを確認
      expect(result).toMatch('<style');
      expect(result).toMatch('<table border="1px">');
      expect(result).toMatch('<tbody>');
      expect(result).toMatch('<tr height="100px">');
      expect(result).toMatch('<td colspan="2" rowspan="2">');
      expect(result).toMatch('<div width="100">');
      expect(result).toMatch('<ul>');
      expect(result).toMatch('<li>');
      expect(result).toMatch('<ol>');
      expect(result).toMatch('<p>');
      expect(result).toMatch('<a href="https://example.com/">');
      expect(result).toMatch('<font color="red">');
      expect(result).toMatch('<span style="height:10em">abcd</span>');
      expect(result).toMatch('</font>');
      expect(result).toMatch('</a>');
      expect(result).toMatch('</p>');
      expect(result).toMatch('</li>');
      expect(result).toMatch('</ol>');
      expect(result).toMatch('</ul>');
      expect(result).toMatch('</div>');
      expect(result).toMatch('</td>');
      expect(result).toMatch('</tr>');
      expect(result).toMatch('</tbody>');
      expect(result).toMatch('</table>');

      // URLが変換されないことを確認。画像タグにはaltが付く
      expect(result).toMatch('<img src="https://example.com/image.png" height="200" alt=" " />');
      expect(result).toMatch('<img src="/img/image.png" height="200" alt=" " />');
      expect(result).toMatch('<img src="https://graph.microsoft.com/info.png" height="200" alt=" " />');
      expect(result).toMatch('<img src="https://graph.microsoft.com/v1.0/test/$value" height="200" alt=" " />');
      // srcがcid:で始まるものにはカスタムデータ属性を付与する。画像タグにはaltが付く
      expect(result).toMatch('<img src="cid:aaaa" height="200" alt=" " data-mail-src="cid:aaaa" />');

      // URLは変換されない
      // アンカーリンクはそのまま維持される
      expect(result).toMatch('<a href="https://example.com/">');
      expect(result).toMatch('<a href="/123/site.html">site</a>');
      expect(result).toMatch('<a href="#abc">abc</a>');

    });
  });

  describe('makeChatSanitizedInnerHtml', () => {
    it('should return sanitized content', () => {
      // "__"を使用する戻り値のため必要
      // eslint-disable-next-line no-underscore-dangle
      const result = makeChatSanitizedInnerHtml(HTML_CONTENT_MOCK)?.__html;
      expect(result).not.toBe('');
      if (!result) return;

      // サニタイズされていることを確認
      expect(result).not.toMatch('<html');
      expect(result).not.toMatch('<body');
      expect(result).not.toMatch('<iframe');
      expect(result).not.toMatch('<link');
      expect(result).not.toMatch('<script');

      // サニタイズされていないことを確認
      expect(result).toMatch('<style');
      expect(result).toMatch('<table border="1px">');
      expect(result).toMatch('<tbody>');
      expect(result).toMatch('<tr height="100px">');
      expect(result).toMatch('<td colspan="2" rowspan="2">');
      expect(result).toMatch('<div width="100">');
      expect(result).toMatch('<ul>');
      expect(result).toMatch('<li>');
      expect(result).toMatch('<ol>');
      expect(result).toMatch('<p>');
      expect(result).toMatch('<a href="https://example.com/">');
      expect(result).toMatch('<font color="red">');
      expect(result).toMatch('<span style="height:10em">abcd</span>');
      expect(result).toMatch('</font>');
      expect(result).toMatch('</a>');
      expect(result).toMatch('</p>');
      expect(result).toMatch('</li>');
      expect(result).toMatch('</ol>');
      expect(result).toMatch('</ul>');
      expect(result).toMatch('</div>');
      expect(result).toMatch('</td>');
      expect(result).toMatch('</tr>');
      expect(result).toMatch('</tbody>');
      expect(result).toMatch('</table>');

      // URLが変換されないことを確認。画像タグにはaltが付く
      expect(result).toMatch('<img src="https://example.com/image.png" height="200" alt=" " />');
      expect(result).toMatch('<img src="/img/image.png" height="200" alt=" " />');
      expect(result).toMatch('<img src="cid:aaaa" height="200" alt=" " />');
      expect(result).toMatch('<img src="https://graph.microsoft.com/info.png" height="200" alt=" " />');
      // srcがGraph APIのURLであるものにはカスタムデータ属性を付与する。画像タグにはaltが付く
      expect(result).toMatch('<img src="https://graph.microsoft.com/v1.0/test/$value" height="200" alt=" " data-chat-src="https://graph.microsoft.com/v1.0/test/$value" />');

      // URLは変換されない
      // アンカーリンクはそのまま維持される
      expect(result).toMatch('<a href="https://example.com/">');
      expect(result).toMatch('<a href="/123/site.html">site</a>');
      expect(result).toMatch('<a href="#abc">abc</a>');

    });

    it('should transform chat component elements', () => {
      // eslint-disable-next-line no-underscore-dangle
      const result = makeChatSanitizedInnerHtml(HTML_CHAT_CONTENT_MOCK)?.__html;
      expect(result).not.toBe('');

      expect(result).toMatch('<span data-chat-mention-id="0" class="chat-mention">原 悠那</span>');
      expect(result).toMatch('<div data-chat-attachment-id="123456789" class="attachment-component"></div>');
      expect(result).toMatch('<span title="bow" data-chat-emoji-alt="🙇‍♀️" class="chat-emoji">🙇‍♀️</span>');
    });
  });
});
