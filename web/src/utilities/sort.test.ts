import { compareMultiple<PERSON>eys } from './sort';

const original = [
  {
    id: 1,
    numberField: 100,
    stringField: 'abc',
    boolField: true,
    dateField: new Date(2020, 1, 15),
    eqField: 1,
  },
  {
    id: 2,
    numberField: 50,
    stringField: 'bcd',
    boolField: false,
    dateField: new Date(2020, 2, 1),
    eqField: 2,
  },
  {
    id: 3,
    numberField: 130,
    stringField: 'cde',
    boolField: false,
    dateField: new Date(2019, 6, 3),
    eqField: null,
  },
  {
    id: 4,
    numberField: 80,
    stringField: null,
    boolField: true,
    dateField: null,
    eqField: null,
  },
];
describe('compareMultipleKeys', () => {
  it('should sort the array as [\'abc\', \'bcd\', \'cde\', null] using stringField', () => {
    const sorted = original
      .sort((a, b) => compareMultipleKeys(a, b, ['stringField']))
      .map((x) => x.id);
    expect(sorted).toEqual([1, 2, 3, 4]);
  });

  it('should sort the array as [50, 80, 100, 130] using numberField', () => {
    const sorted = original
      .sort((a, b) => compareMultipleKeys(a, b, ['numberField']))
      .map((x) => x.id);
    expect(sorted).toEqual([2, 4, 1, 3]);
  });

  it('should sort the array as [2019/7/3, 2020/2/15, 2020/3/1, null] using dateField', () => {
    const sorted = original
      .sort((a, b) => compareMultipleKeys(a, b, ['dateField']))
      .map((x) => x.id);
    expect(sorted).toEqual([3, 1, 2, 4]);
  });

  it('should sort the array as [(false, 2019/7/3), (false, 2020/3/1), (true, 2020/2/15), (null, null)] using (boolField, dateField)', () => {
    const sorted = original
      .sort((a, b) => compareMultipleKeys(a, b, ['boolField', 'dateField']))
      .map((x) => x.id);
    expect(sorted).toEqual([3, 2, 1, 4]);
  });

  it('should sort the array as [(1, 100), (2, 50), (null, 80), (null, 130)] using (eqField, numberField)', () => {
    const sorted = original
      .sort((a, b) => compareMultipleKeys(a, b, ['eqField', 'numberField']))
      .map((x) => x.id);
    expect(sorted).toEqual([1, 2, 4, 3]);
  });

  it('should sort the array as [130, 100, 80, 50] using (numberField descending)', () => {
    const sorted = original
      // 項目名の先頭に!をつけると降順となる
      .sort((a, b) => compareMultipleKeys(a, b, ['!numberField']))
      .map((x) => x.id);
    expect(sorted).toEqual([3, 1, 4, 2]);
  });

  it('should sort the array as [\'100\', \'130\', \'50\', \'80\'] using mapping function', () => {
    const sorted = original
      .sort((a, b) => compareMultipleKeys(a, b, [(x) => x.numberField.toString()]))
      .map((x) => x.id);
    expect(sorted).toEqual([1, 3, 2, 4]);
  });

  it('should sort the array using full option', () => {
    const sorted = original
      .sort((a, b) => compareMultipleKeys(a, b, [
        {
          // わかりづらいけど、文字列を逆順にする
          converter: (x) => x.boolField.toString().substring(0, 4)
            .split('').reverse()
            .join(''),
          direction: 1,
          nullsFirst: 1,
        },
        {
          converter: 'stringField',
          direction: 1,
          nullsFirst: -1,
        },
      ]))
      .map((x) => x.id);
    expect(sorted).toEqual([4, 1, 2, 3]);
  });
});
