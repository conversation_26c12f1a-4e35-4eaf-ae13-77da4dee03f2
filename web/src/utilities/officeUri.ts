import { ValueOf } from './type';

export const UriSchemes = {
  word: 'ms-word:ofv|u|',
  powerpoint: 'ms-powerpoint:ofv|u|',
  excel: 'ms-excel:ofv|u|',
};
export type UriSchemesType = ValueOf<typeof UriSchemes>;

/**
 * 渡された拡張子から対応するOfficeアプリのスキーム名を返却する
 *
 * @param {(string | null | undefined)} fileExtension 空文字列|null|undefinedの場合は何もしない
 * @return {*}  {(UriSchemesType | string)} 対応するOfficeアプリのスキーム名かnullを返却する
 */
export function getUriScheme(fileExtension: string | null | undefined): UriSchemesType | string {
  if (!fileExtension) return '';

  if (/doc|DOC/.test(fileExtension)) {
    return UriSchemes.word;
  }
  if (/xls|XLS/.test(fileExtension)) {
    return UriSchemes.excel;
  }
  if (/ppt|PPT/.test(fileExtension)) {
    return UriSchemes.powerpoint;
  }
  return '';
}

export default getUriScheme;
