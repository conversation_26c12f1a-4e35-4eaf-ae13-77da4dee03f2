// MGtGetで使うscope
const GraphScope = {
  USER: [
    'User.Read',
    'User.ReadBasic.All',
    'User.Read.All',
    'User.ReadWrite',
    'User.ReadWrite.All',
  ],
  GROUP_MEMBER: [
    'GroupMember.Read.All',
    'Group.Read.All',
    'GroupMember.ReadWrite.All',
    'Group.REadWrite.All',
    'Directory.Read.All',
  ],
  DRIVE: [
    'Files.Read',
    'Files.ReadWrite',
    'Files.Read.All',
    'Files.ReadWrite.All',
    'Sites.Read.All',
    'Sites.ReadWrite.All',
  ],
  DRIVE_ITEM: [
    'Files.Read',
    'Files.ReadWrite',
    'Files.Read.All',
    'Files.ReadWrite.All',
    'Sites.Read.All',
    'Sites.ReadWrite.All',
  ],
};
export default GraphScope;
