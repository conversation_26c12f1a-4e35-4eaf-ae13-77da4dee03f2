import { ValueOf } from '../type';

const ErrorMessage = {
  // 一般的な通信エラー
  API_REQUEST_FAIL: 'Error communicating with the server. Please try again later.',
  // リンクが無効
  UNAVAILABLE_LINK: 'The link is not available. Please check the link and try again.',
  // リクエスト過多
  TOO_MANY_REQUEST: 'Too many requests being sent at the moment. Please try again later.',
};

export type ErrorMessageType = ValueOf<typeof ErrorMessage>;
export default ErrorMessage;
