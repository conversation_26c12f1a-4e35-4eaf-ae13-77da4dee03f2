import { MetricReporter } from '@avanade-teams/app-insights-reporter';
import { GraphError } from '@microsoft/microsoft-graph-client';
import * as React from 'react';
import { isDecimal } from './number';

export interface ICustomProperties {
  [key: string]: unknown;
}

type State = {
  view: string;
}

type ViewStatus = {
  LOADING: string;
  ERROR: string;
}

export class AtTaneGraphError extends GraphError {

  constructor(
    public errorResponse: Response,
    statusCode?: number,
    message?: string,
    baseError?: Error,
  ) {
    super(statusCode, message, baseError);
  }

}

/**
 * パフォーマンスログの計測時期を判定する
 * 初期のローディング状態から別の状態に変化したタイミングを
 * 初期表示完了とみなす
 * @param view ReducerのState
 * @param viewCache DOMのRefオブジェクト
 * @param viewStatus ComponentのViewStatusオブジェクト
 */
export function isLogInitTiming<T extends State, U extends Pick<ViewStatus, 'LOADING'>>(
  view: T['view'],
  viewCache: React.MutableRefObject<T['view'] | ''>,
  viewStatus: U,
): boolean {
  return !(view === viewStatus.LOADING && viewCache.current === viewStatus.LOADING);
}

export function mergedClassName(
  baseClassName: string, className: string | undefined | null,
): string {
  if (!className) return baseClassName;
  return [baseClassName, className].join(' ');
}

function formatNumber(value: unknown): string {
  if (isDecimal(value)) {
    return value.toFixed(1);
  }
  return value as string;
}

/**
 * call MetricReporter with a base format
 * @param report
 * @param data
 */
export function sendMetric(
  report: MetricReporter,
  data: {
    baseName: string,
    fnName: string,
    start: number,
    end?: number,
    customProperties?: { [key: string]: unknown },
  },
): void {
  report({
    name: `${data.baseName}: ${data.fnName}`,
    average: data.end ?? performance.now() - data.start,
    customProperties: Object.fromEntries(
      Object.entries(data.customProperties ?? {})
        .map(([key, val]) => [key, formatNumber(val).toString()]),
    ),
  });
}

/**
 * パフォーマンス計測結果をロギングする
 * @param reportMetric
 * @param fnName
 * @param start
 * @param note
 */
export function logPerformanceMetric(
  reportMetric: MetricReporter,
  METRIC_NAME: string,
  fnName: string,
  start: number,
  customProperties?: { [key: string]: unknown },
): void {
  sendMetric(reportMetric, {
    baseName: METRIC_NAME,
    fnName,
    start,
    customProperties,
  });
}

/**
 * common function to call Geranium REST API
 * @param token
 * @param method
 * @param url
 * @param body
 */
export async function fetchUrlRes(
  token: string, method: string, url: string, body?: string,
): Promise<Response> {

  if (!url) {
    return Promise.reject(new Error('MISSING_URL'));
  }

  const res = await fetch(
    url,
    {
      method,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body,
    },
  );

  if (!res.ok) {
    return Promise.reject(
      new AtTaneGraphError(res, res.status, res.statusText),
    );
  }

  return res;
}

/**
 * copy the value to clipboard
 * @param value
 */
export function copyToClipboard(value: string | undefined | null): void {
  // don't copy if the value is falsy
  if (!value) return;

  // traditional copy method
  // making textarea element and select values then remove it
  const textArea = document.createElement('textarea') as HTMLTextAreaElement;
  textArea.innerHTML = `${value}`;
  document.body.appendChild(textArea);

  try {
    textArea.select();
    // deprecated API but we use it for the requirement
    // we can not use Clipboard API inside of the iframe
    document.execCommand('copy');
  } finally {
    if (textArea.parentNode) {
      textArea.parentNode.removeChild(textArea);
    }
  }
}
