import React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import { TeamsTheme, useTeamsInfo } from '@avanade-teams/teams-info';
import useComponentInitUtility from '../../hooks/utilities/useComponentInitUtility';
import Home from './Home';

jest.mock('@avanade-teams/teams-info');
const useTeamsInfoMock = (useTeamsInfo as jest.Mock).mockReturnValue({
  currentTheme: TeamsTheme.DEFAULT,
});

jest.mock('../../hooks/utilities/useComponentInitUtility');
const useComponentInitMock = useComponentInitUtility as jest.Mock;
useComponentInitMock.mockReturnValue([
  undefined,
  [],
  undefined,
  undefined,
  jest.fn(),
]);

jest.mock('../domains/split-view/split-view-container/SplitViewContainer', () => () => <p>container</p>);

describe('Home.tsx', () => {

  beforeEach(() => {
    useTeamsInfoMock.mockClear();
    useComponentInitMock.mockClear();
  });

  function renderComponent() {
    return render(<Home />);
  }

  describe('className', () => {
    describe('when currentTheme is "default"', () => {
      beforeEach(() => {
        useTeamsInfoMock.mockReturnValue({
          currentTheme: TeamsTheme.DEFAULT,
        });
      });

      it('should be "theme-light"', async () => {
        const { container } = renderComponent();
        expect(container.children[0]).toHaveClass('theme-light');
      });
    });

    describe('when currentTheme is "dark"', () => {
      beforeEach(() => {
        useTeamsInfoMock.mockReturnValue({
          currentTheme: TeamsTheme.DARK,
        });
      });

      it('should be "theme-dark"', () => {
        const { container } = renderComponent();
        expect(container.children[0]).toHaveClass('theme-dark');
      });
    });

    describe('when currentTheme is "contrast"', () => {
      beforeEach(() => {
        useTeamsInfoMock.mockReturnValue({
          currentTheme: TeamsTheme.CONTRAST,
        });
      });

      it('should be "theme-dark"', () => {
        const { container } = renderComponent();
        expect(container.children[0]).toHaveClass('theme-dark');
      });
    });
  });
});
