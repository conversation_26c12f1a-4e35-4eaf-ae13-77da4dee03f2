import { TeamsTheme, useTeamsInfo } from '@avanade-teams/teams-info';
import * as React from 'react';
import useComponentInitUtility from '../../hooks/utilities/useComponentInitUtility';
import SplitViewContainer from '../domains/split-view/split-view-container/SplitViewContainer';

// CSS
import './Home.scss';

const Home: React.FC = () => {
  const [, , , , onInit] = useComponentInitUtility({
    componentName: 'Home',
  });

  React.useEffect(() => {
    onInit();
  }, [onInit]);

  const { currentTheme } = useTeamsInfo();
  const themeClass = React.useMemo(() => {
    if (currentTheme === TeamsTheme.DEFAULT) return 'theme-light';
    return 'theme-dark';
  }, [currentTheme]);

  return (
    <div className={`home ${themeClass}`}>
      <div className="home-layout">
        <SplitViewContainer />
      </div>
    </div>
  );
};

export default Home;
