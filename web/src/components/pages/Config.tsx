import React from 'react';
import * as microsoftTeams from '@microsoft/teams-js';
import environment from '../../utilities/environment';

const Config: React.FC = () => {
  React.useEffect(() => {
    microsoftTeams.settings.registerOnSaveHandler((saveEvent) => {
      const baseUrl = `https://${window.location.hostname}:${window.location.port}`;
      microsoftTeams.settings.setSettings({
        suggestedDisplayName: environment.REACT_APP_TITLE,
        entityId: 'Index',
        contentUrl: `${baseUrl}`,
        websiteUrl: `${baseUrl}`,
      });
      saveEvent.notifySuccess();
    });
    microsoftTeams.settings.setValidityState(true);
  }, []);

  return (
    <div>
      <h1>Tab Configuration</h1>
      <div>
        This is where you will add your tab configuration options the user can
        choose when the tab is added to your team/group chat.
      </div>
    </div>
  );
};

export default Config;
