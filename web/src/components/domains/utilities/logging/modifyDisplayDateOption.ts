import { FilterOption } from '../../../../types/IContext';

function convertOptionValue(option: number) {
  const options = [
    '期間指定なし',
    '24時間以内',
    '1週間以内',
    '1ヶ月以内',
    '半年以内',
    '1年以内',
    '期間を指定',
  ];
  return options[option] ?? option.toString();
}

function modifyDisplayDateOption(filter: FilterOption[]) {
  return filter.map(({ key, option, ...rest }) => {
    if (key === 'displayDate' && typeof option === 'number') {
      return { key, option: convertOptionValue(option), ...rest };
    }
    return { key, option };
  });
}

export default modifyDisplayDateOption;
