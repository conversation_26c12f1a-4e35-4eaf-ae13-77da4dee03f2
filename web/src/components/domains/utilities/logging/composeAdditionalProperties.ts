import { isSpoProperties, isChatProperties } from '../../../../utilities/transform';
import { ISplitViewListSingle } from '../../split-view/types/ISplitViewListSingle';

/**
 * カスタムでログに出力するプロパティを作成する。
 */
export function composeAdditionalProperties(item: ISplitViewListSingle) {
  // TODO: 情報源の種別が増えたとき、それを一意に特定できて、かつ個人情報を含まないものを選定する
  if (isSpoProperties(item.properties)) {
    return {
      siteUrl: item.properties.siteUrl,
      listUrl: item.properties.listUrl,
      listId: item.properties.listId,
    };
  }
  if (isChatProperties(item.properties)) {
    const messageId = item.id;
    const { messageType: type, teamId, chatId } = item.properties;
    const appendProperties = type === 'team' ? { teamId, chatId } : { chatId };
    return {
      type,
      messageId,
      ...appendProperties,
    };
  }
  return {};
}

export default composeAdditionalProperties;
