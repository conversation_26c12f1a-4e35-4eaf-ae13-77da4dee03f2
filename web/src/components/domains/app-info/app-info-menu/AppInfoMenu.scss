@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.app-info-menu {
  display: flex;
  flex-flow: column;
  position: relative;

  &.is-self-height {
    height: 100%;
  }

  @include media-pc {
    background-color: var(--color-guide-background-1);
    border-radius: 4px;
    box-shadow: $box-shadow-2;
  }
}

.app-info-menu-edge {
  @include media-pc {
    display: none;
  }
}

.app-info-menu-close-pc {
  position: absolute;
  top: 18px;
  right: 18px;
  z-index: 1;

  &:hover {
    .ui-icon__filled {
      fill: var(--color-guide-brand-icon-hover);
    }
  }

  @include media-sp {
    display: none;
  }
}

.app-info-menu-scroll-wrapper {
  height: 100%;
  display: flex;
  flex-flow: column;
  box-shadow: $box-shadow-2;

  @include media-sp {
    background-color: var(--color-guide-background-1);
  }

  @include media-pc {
    border-radius: 4px;
    // 記事ヘッダー固定時にカゲが外へ漏れないようにしている
    overflow: hidden;
    padding-top: 40px;
  }
}

.app-info-menu-scroll {
  @include scrollbars-common;
}

.app-info-menu-scroll-inner {
  display: flex;
  flex-flow: column;
  height: 100%;

  padding-left: 120px;
  padding-right: 120px;

  @include media-sp {
    padding-top: 12px;
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
  }
}

.app-info-menu-header {
  display: flex;
  align-items: center;
  column-gap: 0.5rem;
}

.app-info-menu-title {
  font-size: 24px;
  margin: 0;

  @include media-sp {
    font-size: 16px;
  }
}

.app-info-menu-desc {
  font-size: 12px;

  .app-info-menu-top-link {
    padding: 0;
    vertical-align: baseline;
    margin-left: -1px;
    margin-right: -3px;

    font-weight: normal;

    &:hover {
      @include media-pc {
        text-decoration: underline;
      }

      .ui-icon__filled {
        display: none;
      }

      .ui-icon__outline {
        display: block;
      }
    }

    .ui-box {
      margin-left: 0;
    }
  }
}

.app-info-menu-header-skeleton {
  .app-info-menu-desc {
    width: 20em;
  }
}

.app-info-menu-message {
  height: 100%;
  display: flex;
  flex-flow: column;
  justify-content: center;
  align-items: center;
}
