import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Header,
} from '@fluentui/react-northstar';
import * as React from 'react';
import PropTypes from 'prop-types';
import Scrollbars from 'react-custom-scrollbars-2';
import useSwipeBackBehavior from '../../../../hooks/behaviors/useSwipeBackBehavior';
import { mergedClassName } from '../../../../utilities/commonFunction';

// CSS
import './AppInfoMenu.scss';
import { ValueOf } from '../../../../utilities/type';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import { IAppInfoMessages } from '../../../../types/IAppInfoMessages';
import NoticeBox from '../../../commons/molecules/notice-box/NoticeBox';
import AppInfoContent from '../app-info-content/AppInfoContent';
import environment from '../../../../utilities/environment';

/**
 * アプリ画像URL
 */
const appIconUrl = `${environment.REACT_APP_ROUTE_PREFIX}assets/atTane_logo.png`;

/**
 * 画面モード
 */
export const AppInfoMenuView = {
  LOADING: 'loading',
  DEFAULT: 'default',
  ERROR: 'error',
};
export type AppInfoMenuViewType = ValueOf<typeof AppInfoMenuView>;

/**
 * ラベル
 */
const AppInfoMenuLabel = {
  TITLE: '横断検索アプリ(atTane)',
};

/**
 * エラーメッセージ
 */
export const AppInfoMenuMessage = {
  BLANK: '',
  FAILED_TO_FETCH: 'コンテンツの取得に失敗しました',
  NO_CONTENTS: '登録されているコンテンツが0件のため表示できません',
};
export type AppInfoMenuMessageType = ValueOf<typeof AppInfoMenuMessage>;

export interface IAppInfoMenuProps {
  view?: AppInfoMenuViewType;
  className?: string;
  open?: boolean;
  appInfoMessages?: IAppInfoMessages | null;
  errorMessage?: AppInfoMenuMessageType;
  onClose?: () => void,
  onSwipingBack?: (n: number) => void,
}

/**
 * AppInfoMenu
 * @param props
 */
const AppInfoMenu: React.FC<IAppInfoMenuProps> = (props) => {
  const {
    className,
    view,
    open,
    appInfoMessages,
    errorMessage,
    onClose,
    onSwipingBack,
  } = props;

  // 画面のモード判定
  const isLoading = React.useMemo(() => view === AppInfoMenuView.LOADING, [view]);
  const isError = React.useMemo(() => view === AppInfoMenuView.ERROR, [view]);

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('app-info-menu', className);
    const isOpen = open ? 'is-open' : '';
    return mergedClassName(isOpen, step1);
  }, [className, open]);

  const displayMessages = React.useMemo(() => appInfoMessages ?? {}, [appInfoMessages]);

  const handleClose = React.useCallback(() => {
    if (onClose) onClose();
  }, [onClose]);

  // スクロールバーの参照
  const scrollbarsRef = React.useRef<Scrollbars>(null);

  // スクロールエリアのクラス名
  const scrollbarClassNames = React.useMemo(() => {
    const isLoadingClass = mergedClassName('app-info-menu-scroll', isLoading ? 'is-loading' : undefined);
    return mergedClassName(isLoadingClass, isError ? 'is-error' : undefined);
  }, [isError, isLoading]);

  // スワイプバック機能
  const [
    touchLength,
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    touchInfo,
  ] = useSwipeBackBehavior(10, handleClose, 'y');
  React.useEffect(() => {
    if (onSwipingBack) onSwipingBack(touchLength);
  }, [touchLength, onSwipingBack]);

  // スクロール量が条件を満たしたらスワイプ開始する
  const handleOnTouchStartScrollbarWrapper = React.useCallback(
    (e: React.SyntheticEvent<HTMLElement, TouchEvent | MouseEvent>) => {
      if (!scrollbarsRef.current) return;
      if (scrollbarsRef.current.getValues().scrollTop <= 0) {
        onTouchStart(e);
      }
    }, [onTouchStart],
  );

  // スクロール量が条件を満たしたらスワイプ開始する
  const handleOnTouchMoveScrollbarWrapper = React.useCallback(
    (e: React.SyntheticEvent<HTMLElement, TouchEvent | MouseEvent>) => {
      const isNotCapturingAndNoScrollLength = !touchInfo.current.isCapturing
        && scrollbarsRef.current
        && scrollbarsRef.current.getValues().scrollTop <= 0;

      if (isNotCapturingAndNoScrollLength) {
        onTouchStart(e);
      } else {
        onTouchMove(e);
      }
    }, [onTouchMove, touchInfo, onTouchStart],
  );

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="app-info-menu-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
          onTouchCancel={onTouchEnd}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="app-info-menu-close-pc">
        <Button
          className="app-info-menu-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div
        className="app-info-menu-scroll-wrapper"
        onTouchStart={handleOnTouchStartScrollbarWrapper}
        onTouchMove={handleOnTouchMoveScrollbarWrapper}
        onTouchEnd={onTouchEnd}
        onTouchCancel={onTouchEnd}
      >
        <Scrollbars
          ref={scrollbarsRef}
          className={scrollbarClassNames}
        >
          <div className="app-info-menu-scroll-inner">
            <div className="app-info-menu-header">
              <img
                alt="atTaneロゴアイコン"
                src={appIconUrl}
                width="32"
              />
              <Header content={AppInfoMenuLabel.TITLE} as="h3" className="app-info-menu-title" />
            </div>
            {!isError && (
              <AppInfoContent
                displayMessages={displayMessages}
              />
            )}
            {/* エラー表示 */}
            {isError && (
              <div className="app-info-menu-message">
                <NoticeBox message={errorMessage} />
              </div>
            )}
          </div>
        </Scrollbars>

      </div>
    </div>
  );

};

AppInfoMenu.propTypes = {
  className: PropTypes.string,
  view: PropTypes.string,
  // eslint-disable-next-line react/forbid-prop-types
  appInfoMessages: PropTypes.object,
  errorMessage: PropTypes.string,
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

AppInfoMenu.defaultProps = {
  className: undefined,
  view: AppInfoMenuView.DEFAULT,
  appInfoMessages: {},
  errorMessage: '',
  open: false,
  onClose: undefined,
  onSwipingBack: undefined,
};

export default React.memo(AppInfoMenu);
