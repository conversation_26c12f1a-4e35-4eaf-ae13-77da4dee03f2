import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import * as React from 'react';
import { IAppInfoMessages } from '../../../../types/IAppInfoMessages';
import environment from '../../../../utilities/environment';
import {
  AppInfoMenuMessage,
  AppInfoMenuMessageType,
  AppInfoMenuView,
  AppInfoMenuViewType,
} from '../app-info-menu/AppInfoMenu';

type useAppInfoReturnType = [
  props: [
    open: boolean,
    view: AppInfoMenuViewType,
    appInfoMessages: IAppInfoMessages | null,
    appAIInfoMessages: IAppInfoMessages | null,
    errorMessage: AppInfoMenuMessageType,
  ],
  showAppInfo: () => void,
  hideAppInfo: () => void,
];

/**
 * useAppInfo機能
 * @param reportEvent ログ送信関数
 */
const useAppInfo = (
  reportEvent: EventReporter,
): useAppInfoReturnType => {

  // atTaneの構成ファイルからアプリInfoメッセージを取得する
  const appInfoMessages = environment.REACT_APP_INFO_MESSAGES;
  const appAIInfoMessages = environment.REACT_APP_AI_INFO_MESSAGES;

  // モーダルの開閉フラグ
  const [open, setOpen] = React.useState(false);

  // appInfoMessagesで欠けている情報を取得する
  const getMissingItems = React.useCallback(() => {
    const keyNames = [];
    if (
      !appInfoMessages?.overview
      || appInfoMessages.overview.length === 0
    ) keyNames.push('overview');
    if (
      !appInfoMessages?.searchCategory
      || appInfoMessages?.searchCategory?.length === 0
    ) keyNames.push('searchCategory');
    if (
      !appInfoMessages?.searchableItems
      || appInfoMessages?.searchableItems?.length === 0
    ) keyNames.push('searchableItems');
    if (
      !appInfoMessages?.searchCriteria
      || appInfoMessages?.searchCriteria?.length === 0
    ) keyNames.push('searchCriteria');
    if (
      !appInfoMessages?.sortOrder
      || appInfoMessages?.sortOrder?.length === 0
    ) keyNames.push('sortOrder');
    if (
      !appInfoMessages?.contactUs
      || appInfoMessages?.contactUs?.length === 0
    ) keyNames.push('contactUs');
    const results: [number, string] = [keyNames.length, keyNames.join(', ')];
    return results;
  }, [appInfoMessages]);

  // 0件判定
  const isEmptyMessages = React.useMemo(
    () => !appInfoMessages
      || Object.values(appInfoMessages).every((value: string[]) => value.length === 0),
    [appInfoMessages],
  );

  // モーダルを開く
  const showAppInfo = React.useCallback(() => {
    setOpen(true);

    // 項目が一部欠けていたり、丸ごとなかったりする場合は、エラーログを出力
    const [missingCount, missingItemKeyNames] = getMissingItems();
    if (
      isEmptyMessages
      || missingCount !== 0
    ) {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'MISSING_APP_INFO_MESSAGES',
        customProperties: {
          missingItems: missingItemKeyNames,
        },
      });
    }
    reportEvent({
      type: EventReportType.USER_EVENT,
      name: 'SHOW_APP_INFO',
    });
  }, [getMissingItems, isEmptyMessages, reportEvent]);

  // モーダルを閉じる
  const hideAppInfo = React.useCallback(() => {
    setOpen(false);
  }, []);

  // 画面モード判定
  const view = React.useMemo(() => {
    // appInfoMessagesがnullのときはエラー表示
    if (isEmptyMessages) {
      return AppInfoMenuView.ERROR;
    }
    // デフォルト値
    return AppInfoMenuView.DEFAULT;
  }, [isEmptyMessages]);

  // エラーメッセージ判定
  const errorMessage = React.useMemo(() => {
    // エラー時以外は空値とする
    if (view !== AppInfoMenuView.ERROR) return AppInfoMenuMessage.BLANK;
    // 0件メッセージ
    if (isEmptyMessages) {
      return AppInfoMenuMessage.NO_CONTENTS;
    }
    // 取得失敗メッセージ
    return AppInfoMenuMessage.FAILED_TO_FETCH;
  }, [view, isEmptyMessages]);

  return [
    [
      open,
      view,
      appInfoMessages,
      appAIInfoMessages,
      errorMessage,
    ],
    showAppInfo,
    hideAppInfo,
  ];
};

export default useAppInfo;
