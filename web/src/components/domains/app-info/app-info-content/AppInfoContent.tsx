import * as React from 'react';
import {
  Label, Text,
} from '@fluentui/react-northstar';
import PropTypes from 'prop-types';
import { IAppInfoMessages } from '../../../../types/IAppInfoMessages';

// CSS
import './AppInfoContent.scss';

export interface IAppInfoContentProps {
  displayMessages: IAppInfoMessages;
}

const AppInfoContent: React.FC<IAppInfoContentProps> = ((props) => {
  const {
    displayMessages,
  } = props;

  // 有効なメッセージが存在するかどうか
  const isMessageExists = (
    value: string[],
  ) => (!!(Array.isArray(value) && value.join() !== ''));

  return (
    <div className="app-info-menu-main">
      <ul className="app-info-menu-items">
        {displayMessages.overview
          && isMessageExists(displayMessages.overview)
          && (
            <div className="app-info-menu-item-overview">
              {displayMessages.overview.map((message) => (
                <Text content={message} />
              ))}
            </div>
          )}
        {displayMessages.searchCategory
          && isMessageExists(displayMessages.searchCategory)
          && (
            <div className="app-info-menu-item">
              <div className="app-info-menu-item-label">
                <Label content="検索対象" />
              </div>
              <div className="app-info-menu-item-text">
                {displayMessages.searchCategory.map((message) => (
                  <Text content={message} />
                ))}
              </div>
            </div>
          )}
        {displayMessages.searchableItems
          && isMessageExists(displayMessages.searchableItems)
          && (
            <div className="app-info-menu-item">
              <div className="app-info-menu-item-label">
                <Label content="検索対象項目" />
              </div>
              <div className="app-info-menu-item-text">
                {displayMessages.searchableItems.map((message) => (
                  <Text content={message} />
                ))}
              </div>
            </div>
          )}
        {displayMessages.searchCriteria
          && isMessageExists(displayMessages.searchCriteria)
          && (
            <div className="app-info-menu-item">
              <div className="app-info-menu-item-label">
                <Label content="検索条件" />
              </div>
              <div className="app-info-menu-item-text">
                {displayMessages.searchCriteria.map((message) => (
                  <Text content={message} />
                ))}
              </div>
            </div>
          )}
        {displayMessages.sortOrder
          && isMessageExists(displayMessages.sortOrder)
          && (
            <div className="app-info-menu-item">
              <div className="app-info-menu-item-label">
                <Label content="検索結果の表示順" />
              </div>
              <div className="app-info-menu-item-text">
                {displayMessages.sortOrder.map((message) => (
                  <Text content={message} />
                ))}
              </div>
            </div>
          )}
        {displayMessages.contactUs
          && isMessageExists(displayMessages.contactUs)
          && (
            <div className="app-info-menu-item">
              <div className="app-info-menu-item-label">
                <Label content="本アプリへのフィードバック" />
              </div>
              <div className="app-info-menu-item-text">
                {displayMessages.contactUs.map((message) => (
                  <Text content={message} />
                ))}
              </div>
            </div>
          )}
      </ul>
    </div>
  );
});

AppInfoContent.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  displayMessages: PropTypes.any,
};

AppInfoContent.defaultProps = {
  displayMessages: {},
};

export default AppInfoContent;
