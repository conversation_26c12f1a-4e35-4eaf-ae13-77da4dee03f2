@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.app-info-menu-main {
  display: flex;
  flex-direction: column;
  height: calc(100% + 1px);
  flex-grow: 1;
}

.app-info-menu-items {
  list-style: none;
  padding: 0;

  display: flex;
  flex-direction: column; // row wrap から column に変更
  justify-content: flex-start;

  @include media-sp {
    padding-bottom: 24px;
  }
}

.app-info-menu-item-overview {
  display: flex;
  flex-flow: column wrap;
  padding-bottom: 1.5rem;
}

.app-info-menu-item {
  display: flex;
  flex-flow: row wrap;
  row-gap: 0.4rem;
  padding-bottom: 1.8rem;
  width: 100%; // 幅を100%に設定して確実に縦並びにする
}

.app-info-menu-item-label {
  width: 100%;

  * {
    font-size: 1rem;
  }

  .ui-label {
    background-color: var(--color-guide-app-info-label-background);
    padding-left: 1rem;
    padding-right: 1rem;

    .ui-box {
      color: var(--color-guide-brand-main-foreground);
    }
  }
}

.app-info-menu-item-text {
  display: flex;
  flex-flow: column wrap;
}