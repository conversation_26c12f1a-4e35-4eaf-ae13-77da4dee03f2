import * as React from 'react';
import { Story, Meta } from '@storybook/react';
import {
  cloneForMobileView,
  setStoryMockCommonArgTypes,
  setStoryWidthHeight,
  StoryMockProps,
} from '../../../../../.storybook/utils';
import SplitViewListTabs, { ISplitViewListTabsProps } from './SplitViewListTabs';
import { SearchListMode } from '../types/SearchListMode';

export default {
  title: 'split-view/SplitViewListTabs.tsx',
  component: SplitViewListTabs,
  argTypes: {
    ...setStoryMockCommonArgTypes(1000, 500),
    onClickListTab: {
      action: 'clicked',
    },
  },
} as Meta;

const Template: Story<StoryMockProps<ISplitViewListTabsProps>> = (args) => (
  <div {...setStoryWidthHeight(args)}>
    <div style={{ overflow: 'hidden', height: '100%' }}>
      <SplitViewListTabs {...args} />
    </div>
  </div>
);

export const Default = Template.bind({});
Default.args = {
  mode: SearchListMode.DEFAULT,
};

export const Unread = Template.bind({});
Unread.args = {
  mode: SearchListMode.Chat,
};

export const DefaultOnMobile = cloneForMobileView(Template, Default);
export const UnreadOnMobile = cloneForMobileView(Template, Unread);
