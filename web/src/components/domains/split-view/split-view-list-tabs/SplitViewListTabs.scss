@import '../../../../styles/variables';

.split-view-list-tabs-container {
  // border-bottom: 1px solid var(--color-guide-foreground-6);
}

// TODO:ここのUIについては見当の余地あり
.split-view-list-tabs-content {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: flex-end;
  height: 25px;

  /* Fluent UIのMenuコンポーネントの余白を打ち消す */
  .ui-menu {
    margin: 0;
    padding: 0;
  }

  .ui-menu__itemwrapper {
    margin: 0;
    padding: 0;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;

    .tab-button {
      cursor: not-allowed;
    }
  }

  a.ui-menu__item {
    color: var(--color-guide-foreground-2);
    cursor: pointer;
    transition: border-color 0.2s, color 0.2s, box-shadow 0.2s, background-color 0.2s;
    border-radius: 4px 4px 0 0;
    padding: 6px 12px 8px;
    margin: 0;

    /* マージンを0にしてタブをくっつける */
    border-bottom: 1px solid var(--color-guide-foreground-6);
    position: relative;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
    background-color: rgba(0, 0, 0, 0.05);

    /* 非アクティブタブを暗く */
    &[aria-selected='true'] {
      font-weight: bold;
      color: var(--color-guide-brand-main-foreground);
      box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.15);
      background-color: var(--color-guide-background);
      /* アクティブタブは背景色と同じに */
      z-index: 1;
      /* 下線を削除 */
      border-bottom: none;
    }

    &[aria-selected='false']:hover {
      color: var(--color-guide-default-foreground);
      box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.12);
      background-color: rgba(0, 0, 0, 0.03);
      /* ホバー時は少し明るく */
      /* 下線を維持（非選択時のデフォルト） */
    }
  }

}
