import * as React from 'react';
import { Menu, tabList<PERSON>ehavior } from '@fluentui/react-northstar';
import PropTypes from 'prop-types';
import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import { SearchListMode, SearchListModePropTypes, SearchModeType } from '../types/SearchListMode';

// CSS
import './SplitViewListTabs.scss';

export interface ISplitViewListTabsProps {
  mode?: SearchModeType | null;
  isActive?: boolean;
  onClick?: (toBe: boolean, e: React.SyntheticEvent<HTMLElement>) => void;
  disabled?: boolean;
  reportEvent: EventReporter;
  isAISearchEnabled: boolean,
}

/**
 * モード切り替え時のログを送信する
 * @param fromMode 切り替え前のモード
 * @param toMode 切り替え後のモード
 * @param reportEvent ログ送信用のコールバック
 */
function sendModeSwitchLog(
  fromMode: string,
  toMode: string,
  reportEvent: EventReporter,
): void {
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: 'SWITCH_SEARCH_MODE',
    customProperties: {
      fromMode,
      toMode,
      executionDate: new Date().toISOString(),
    },
  });
}

/**
 * SplitViewListTabs
 * @param props
 * @param isActive // デフォルト or チャットなのか確認
 * @param disabled // 検索中は押せない
 */
const SplitViewListTabs: React.FC<ISplitViewListTabsProps> = (props) => {
  const {
    mode,
    isActive,
    onClick,
    disabled,
    reportEvent,
    isAISearchEnabled,
  } = props;

  // searchMode check
  const isDefaultMode = React.useMemo(() => (mode === SearchListMode.DEFAULT), [mode]);

  // デフォルトタブ用のクリックハンドラー
  const handleDefaultTabClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (disabled || !onClick) return;
    // 現在ChatモードであればDEFAULTモードに切り替える (falseを渡す)
    if (isActive) {
      onClick(false, e);
      sendModeSwitchLog('Chat', 'Default', reportEvent);
    }
    // すでにDEFAULTモードなら何もしない
  }, [disabled, onClick, isActive, reportEvent]);

  // Chatタブ用のクリックハンドラー
  const handleChatTabClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (disabled || !onClick) return;
    // 現在DEFAULTモードであればChatモードに切り替える (trueを渡す)
    if (!isActive) {
      onClick(true, e);
      sendModeSwitchLog('Default', 'Chat', reportEvent);
    }
    // すでにChatモードなら何もしない
  }, [disabled, onClick, isActive, reportEvent]);
  // AI検索が無効の場合、タブ自体を表示しない
  if (!isAISearchEnabled) {
    return null;
  }

  return (
    <div className={`split-view-list-tabs-container ${disabled ? 'disabled' : ''}`}>
      <Menu
        className="split-view-list-tabs-content"
        underlined
        primary
        accessibility={tabListBehavior}
        defaultActiveIndex={isDefaultMode ? 0 : 1}
        variables={{
          underlinedBorderColor: 'transparent',
        }}
      >
        <Menu.Item
          className="split-view-list-tabs-default"
          index={0}
          onClick={handleDefaultTabClick}
          disabled={disabled}
        >
          <Menu.ItemContent>Default</Menu.ItemContent>
        </Menu.Item>
        <Menu.Item
          className="split-view-list-tabs-chat"
          index={1}
          onClick={handleChatTabClick}
          disabled={disabled}
        >
          <Menu.ItemContent>AISearch</Menu.ItemContent>
        </Menu.Item>
      </Menu>
    </div>
  );
};

SplitViewListTabs.propTypes = {
  mode: SearchListModePropTypes,
  onClick: PropTypes.func,
  isActive: PropTypes.bool,
  disabled: PropTypes.bool,
};

SplitViewListTabs.defaultProps = {
  mode: SearchListMode.DEFAULT,
  onClick: undefined,
  isActive: false,
  disabled: false,
};
export default SplitViewListTabs;
