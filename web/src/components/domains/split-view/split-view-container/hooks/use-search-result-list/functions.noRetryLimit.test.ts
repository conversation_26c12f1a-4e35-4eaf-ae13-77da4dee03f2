import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { SplitViewListView } from '../../../split-view-list/SplitViewList';
import {
  onIntervalImpl,
} from './functions';
import mockMatchMedia from '../../../../../../mocks/match-media';
import { UseSearchResultRepositoryReturnType } from '../../../../../../hooks/accessors/useSearchResultRepositoryAccessor';
import { PerformanceMetrics, initialPerformanceMetrics } from '../../../../../../types/PerformanceMetrics';

jest.mock('../../../../../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 2,
    REACT_APP_RETRIABLE_TIME: -1,
  },
}));

mockMatchMedia();

describe('use-search-result-list/functions.noRetryLimit', () => {
  const performanceMetricsMock = { current: initialPerformanceMetrics };
  const setPerformanceMetricsMock = jest.fn();
  const metricsMock: [
    performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
    setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  ] = [performanceMetricsMock, setPerformanceMetricsMock];

  beforeEach(() => {
    setPerformanceMetricsMock.mockClear();
  });
  describe('onIntervalImpl', () => {
    describe('when REACT_APP_RETRIABLE_TIME is -1', () => {
      const getSearchRequestApiMock = jest.fn();
      const fetchListMock = jest.fn();
      const processMock = jest.fn();
      const dispatchMock = jest.fn();
      const reportEventMock = jest.fn();
      const reportMetricMock = jest.fn();
      const setAllListDataRefMock = jest.fn();
      const setRequestManagerRefMock = jest.fn();
      const setSearchRequestMock = jest.fn();
      const setIntervalMock = jest.fn();
      const onSuccessMock = jest.fn();
      const clearCacheMock = jest.fn();
      const useSearchResultRepositoryReturn = {
        clearCacheMock,
      } as unknown as UseSearchResultRepositoryReturnType;
      const cancellationRefMock = { current: false };

      it('should complete the search without calling TOO_MANY_RETRY', () => {
        onIntervalImpl(
          {
            reqId: 'reqId',
            state: 'Completed',
            results: [
              {
                ids: ['aa', 'aa'],
                reqId: 'aa',
                pid: 'aa',
                dataSource: {
                  kind: 'SPO',
                  properties: {
                    list: 'list',
                    listUrl: 'listUrl',
                    site: 'site',
                    listName: 'listName',
                    category: 'category',
                  },
                },
              },
            ],
          },
          getSearchRequestApiMock,
          fetchListMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: false },
          metricsMock,
          {
            current: [
              {
                id: 'aaa',
                note: '',
                displayDate: '',
                properties: {},
                kind: 'SPO',
                title: 'qwerty',
              },
            ],
          },
          setAllListDataRefMock,
          {
            current: {
              reqId: 'reqId',
              retryCount: 10,
              processes: [{ pid: 'aa', status: 'Done', hasRetryError: false }],
              hasError: false,
              loaded: false,
              hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          setSearchRequestMock,
          setIntervalMock,
          useSearchResultRepositoryReturn,
          cancellationRefMock,
          onSuccessMock,
        );

        expect(dispatchMock).toBeCalledWith({
          type: 'SET_LISTVIEW',
          payload: {
            listView: SplitViewListView.SEARCH_COMPLETED,
          },
        });
        expect(reportEventMock).not.toBeCalledWith({
          type: EventReportType.SYS_ERROR,
          name: 'TOO_MANY_RETRY',
        });
        expect(reportMetricMock).toBeCalledTimes(1);
        expect(setIntervalMock).toBeCalledTimes(1);
      });
    });

  });

});
