import { EventReportType } from '@avanade-teams/app-insights-reporter';
// import { ISharePointListsResponse } from '../../../../../../types/ISharePointListsResponse';
import { createListApiSingleEntry, createSearchWordsMock, createSplitViewListSingle } from '../../../../../../utilities/test';
import { SplitViewListMessage, SplitViewListView } from '../../../split-view-list/SplitViewList';
import { ListMode } from '../../../types/ListMode';
import {
  abortInterval,
  checkSameRequestCacheRequestAndAnyResultsCache,
  fetchRetrievedListItems,
  fetchSearchRequest,
  handleBatchRequestBadResponse,
  isSearchRequestFulfilled,
  isCacheStatusCompleted,
  onBlurSearchInputImpl,
  onChangeSearchInputImpl,
  onClearSearchInputImpl,
  onIntervalImpl,
  onSubmitSearchInputImpl,
  onSubmitChatSearchImpl,
  onSwitchLastSearchResultImpl,
  onUpdateLastSubmittedWords,
  registerSearchRequest,
  retrieveUnprocessedResults,
  sendSearchLog,
  sendAISearchLog,
  setPartialSearchResultCache,
  splitSearchInput,
  indicateSearchStateError,
  setAllSearchResultCache,
  getUniqueIds,
} from './functions';
import mockMatchMedia from '../../../../../../mocks/match-media';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../../split-view-detail/SplitViewDetail';
import { UseSearchResultRepositoryReturnType } from '../../../../../../hooks/accessors/useSearchResultRepositoryAccessor';
import { IProcess, IRequestManager } from '../useSearchResultList';
import { DataSourceKindType } from '../../../../../../types/DataSourceKind';
import { PerformanceMetrics, initialPerformanceMetrics } from '../../../../../../types/PerformanceMetrics';
import { ISplitViewListSingle } from '../../../types/ISplitViewListSingle';
import { CacheStatus, ISearchRequestCache } from '../../../../../../types/IGeraniumAttaneDB';
import { ISearchRequestResult, SearchRequestState } from '../../../../../../types/ISearchRequestResult';

jest.mock('../../../../../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 2,
    REACT_APP_RETRIABLE_TIME: 60,
  },
}));

mockMatchMedia();

describe('use-search-result-list/fuctions', () => {
  const performanceMetricsMock = { current: initialPerformanceMetrics };
  const setPerformanceMetricsMock = jest.fn();
  const metricsMock: [
    performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
    setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  ] = [performanceMetricsMock, setPerformanceMetricsMock];

  beforeEach(() => {
    setPerformanceMetricsMock.mockClear();
  });

  describe('splitSearchInput', () => {
    describe('when the value is "a b c"', () => {
      it('should return ["a", "b", "c"]', () => {
        expect(splitSearchInput('a b c')).toStrictEqual(['a', 'b', 'c']);
      });
    });

    describe('when the value is "a　b　c"', () => {
      it('should return ["a", "b", "c"]', () => {
        expect(splitSearchInput('a　b　c')).toStrictEqual(['a', 'b', 'c']);
      });
    });

    describe('when the value is "  abc  123　 efg"', () => {
      it('should return ["abc", "123", "efg"]', () => {
        expect(splitSearchInput('  abc  123　 efg')).toStrictEqual(['abc', '123', 'efg']);
      });
    });

    describe('when the value is "  　  　"', () => {
      it('should return []', () => {
        expect(splitSearchInput('  　  　')).toStrictEqual([]);
      });
    });
  });

  describe('onChangeSearchInputImpl', () => {
    describe('when the ListViewType is LOADING', () => {
      it('should not do anything', () => {
        const callbackMock = jest.fn();
        onChangeSearchInputImpl(
          ListMode.SEARCH,
          SplitViewListView.LOADING,
          'a',
          callbackMock,
        );
        expect(callbackMock).toBeCalledTimes(0);
      });
    });

    describe('when the inputs has value', () => {
      it('should call callback', () => {
        const callbackMock = jest.fn();
        onChangeSearchInputImpl(
          ListMode.SEARCH,
          SplitViewListView.DEFAULT,
          'a',
          callbackMock,
        );
        expect(callbackMock).toBeCalledTimes(1);
        expect(callbackMock).toBeCalledWith('a');
      });
    });
  });

  describe('onClearSearchInputImpl', () => {
    const setListModeCallbackMock = jest.fn();
    const setInputValueCallbackMock = jest.fn();
    const setSearchWordsCallbackMock = jest.fn();
    const setRequestManagerRefMock = jest.fn();
    const setAllListDataRefMock = jest.fn();
    const dispatchMock = jest.fn();

    beforeEach(() => {
      setListModeCallbackMock.mockClear();
      setInputValueCallbackMock.mockClear();
      setRequestManagerRefMock.mockClear();
      setAllListDataRefMock.mockClear();
      dispatchMock.mockClear();
    });

    describe('when the ListMode is SEARCH', () => {
      it('should set the ListMode to INITIAL_DISPLAY', () => {
        onClearSearchInputImpl(
          ListMode.SEARCH,
          setListModeCallbackMock,
          setInputValueCallbackMock,
          setSearchWordsCallbackMock,
          setRequestManagerRefMock,
          setAllListDataRefMock,
          dispatchMock,

        );
        expect(setListModeCallbackMock).toBeCalledTimes(1);
        expect(setInputValueCallbackMock).toBeCalledTimes(1);
        expect(setSearchWordsCallbackMock).toBeCalledTimes(1);
        expect(setRequestManagerRefMock).toBeCalledTimes(1);
        expect(setAllListDataRefMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_DATA',
          payload: {
            listView: SplitViewListView.DEFAULT,
          },
        });
      });
    });
  });

  describe('onBlurSearchInputImpl', () => {
    describe('when the listMode is not SEARCH', () => {
      it('should not call setInputValue', () => {
        const setInputValueMock = jest.fn();
        onBlurSearchInputImpl(ListMode.INITIAL_DISPLAY, '', setInputValueMock);
        expect(setInputValueMock).toBeCalledTimes(0);
      });

      describe('when the listMode is SEARCH', () => {
        describe('when the inputValue is not blank', () => {
          it('should not call setInputValue', () => {
            const setInputValueMock = jest.fn();
            onBlurSearchInputImpl(ListMode.SEARCH, 'abc', setInputValueMock);
            expect(setInputValueMock).toBeCalledTimes(0);
          });
        });

        describe('when the inputValue is blank', () => {
          it('should call setInputValue with lastSubmittedOriginValue.current', () => {
            const setInputValueMock = jest.fn();
            onBlurSearchInputImpl(ListMode.SEARCH, '', setInputValueMock);
            expect(setInputValueMock).toBeCalledTimes(1);
            expect(setInputValueMock).toBeCalledWith('');
          });
        });
      });
    });
  });

  describe('onSubmitSearchInputImpl', () => {
    const setListModeMock = jest.fn();
    const setSubmittedValueMock = jest.fn();
    const setSearchRequestMock = jest.fn();
    const setRequestManagerRefMock = jest.fn();
    const setAllListDataRef = jest.fn();
    const setSyncBookmark = jest.fn();
    const setSearchRequestCacheMock = jest.fn();
    const setSearchResultsCacheMock = jest.fn();
    const resetCancelRefMock = jest.fn();
    const dispatchMock = jest.fn();

    beforeEach(() => {
      setListModeMock.mockClear();
      setSubmittedValueMock.mockClear();
      setSearchRequestMock.mockClear();
      setRequestManagerRefMock.mockClear();
      setAllListDataRef.mockClear();
      setSyncBookmark.mockClear();
      setSearchRequestCacheMock.mockClear();
      setSearchResultsCacheMock.mockClear();
      resetCancelRefMock.mockClear();
    });

    describe('when the input is blank', () => {
      it('should return blank and should not do anything', () => {
        expect(onSubmitSearchInputImpl(
          setListModeMock,
          '',
          setSubmittedValueMock,
          setSearchRequestMock,
          setRequestManagerRefMock,
          setAllListDataRef,
          setSyncBookmark,
          setSearchRequestCacheMock,
          setSearchResultsCacheMock,
          resetCancelRefMock,
          dispatchMock,
        )).toBe('');
        expect(setListModeMock).toBeCalledTimes(0);
        expect(setSubmittedValueMock).toBeCalledTimes(0);
        expect(setSearchRequestMock).toBeCalledTimes(0);
        expect(setRequestManagerRefMock).toBeCalledTimes(0);
        expect(setAllListDataRef).toBeCalledTimes(0);
        expect(setSyncBookmark).toBeCalledTimes(0);
        expect(setSearchRequestCacheMock).toBeCalledTimes(0);
        expect(setSearchResultsCacheMock).toBeCalledTimes(0);
        expect(resetCancelRefMock).toBeCalledTimes(0);
      });
    });

    describe('when the input has value', () => {
      describe('when the input only contains spaces', () => {
        it('should return blank and should not do anything', () => {
          expect(onSubmitSearchInputImpl(
            setListModeMock,
            ' 　  　　',
            setSubmittedValueMock,
            setSearchRequestMock,
            setRequestManagerRefMock,
            setAllListDataRef,
            setSyncBookmark,
            setSearchRequestCacheMock,
            setSearchResultsCacheMock,
            resetCancelRefMock,
            dispatchMock,
          )).toBe('');
          expect(setListModeMock).toBeCalledTimes(0);
          expect(setSubmittedValueMock).toBeCalledTimes(0);
          expect(setSearchRequestMock).toBeCalledTimes(0);
          expect(setRequestManagerRefMock).toBeCalledTimes(0);
          expect(setAllListDataRef).toBeCalledTimes(0);
          expect(setSyncBookmark).toBeCalledTimes(0);
          expect(setSearchRequestCacheMock).toBeCalledTimes(0);
          expect(setSearchResultsCacheMock).toBeCalledTimes(0);
          expect(resetCancelRefMock).toBeCalledTimes(0);
          expect(dispatchMock).toBeCalledTimes(0);
        });
      });

      describe('when the input has meaningful words', () => {
        it('should return the input and should call setListMode and setSubmittedValue', () => {
          expect(onSubmitSearchInputImpl(
            setListModeMock,
            'a b c',
            setSubmittedValueMock,
            setSearchRequestMock,
            setRequestManagerRefMock,
            setAllListDataRef,
            setSyncBookmark,
            setSearchRequestCacheMock,
            setSearchResultsCacheMock,
            resetCancelRefMock,
            dispatchMock,
          )).toBe('a b c');
          expect(setListModeMock).toBeCalledTimes(1);
          expect(setListModeMock).toBeCalledWith(ListMode.SEARCH);
          expect(setSearchRequestMock).toBeCalledTimes(1);
          expect(setRequestManagerRefMock).toBeCalledTimes(1);
          expect(setAllListDataRef).toBeCalledTimes(1);
          expect(setSyncBookmark).toBeCalledTimes(1);
          expect(setSubmittedValueMock).toBeCalledTimes(1);
          expect(setSubmittedValueMock).toBeCalledWith('a b c');
          expect(setSearchRequestCacheMock).toBeCalledTimes(1);
          expect(setSearchResultsCacheMock).toBeCalledTimes(1);
          expect(dispatchMock).toBeCalledWith({
            type: 'SET_DATA',
            payload: {
              detailView: SplitViewDetailView.ERROR,
              detailMessage: SplitViewDetailMessage.NOT_SELECTED,
              detail: undefined,
              context: {
                sort: [],
                filter: [],
                timestamp: new Date(),
              },
            },
          });
        });
      });

    });
  });

  describe('onSwitchLastSearchResultImpl', () => {
    const setListModeMock = jest.fn();
    const callbackMock = jest.fn();

    beforeEach(() => {
      setListModeMock.mockClear();
      callbackMock.mockClear();
    });

    describe('when the input is blank', () => {
      it('should return blank and should not do anything', () => {
        expect(onSwitchLastSearchResultImpl(setListModeMock, '', callbackMock)).toBe('');
        expect(setListModeMock).toBeCalledTimes(0);
        expect(callbackMock).toBeCalledTimes(0);
      });
    });

    describe('when the input has value', () => {
      describe('when the input only contains spaces', () => {
        it('should return blank and should not do anything', () => {
          expect(onSwitchLastSearchResultImpl(setListModeMock, ' 　  　　', callbackMock)).toBe('');
          expect(setListModeMock).toBeCalledTimes(0);
          expect(callbackMock).toBeCalledTimes(0);
        });
      });

      describe('when the input has meaningful words', () => {
        it('should return the input and should call setListMode and setSubmittedValue', () => {
          expect(onSwitchLastSearchResultImpl(setListModeMock, 'a b c', callbackMock)).toBe('a b c');
          expect(setListModeMock).toBeCalledTimes(1);
          expect(setListModeMock).toBeCalledWith(ListMode.SEARCH);
          expect(callbackMock).toBeCalledTimes(1);
          // TODO:エラー
          expect(callbackMock).toBeCalledWith('a b c');
        });
      });

    });
  });

  describe('sendSearchLog', () => {
    jest.useFakeTimers('modern');
    const reportEventMock = jest.fn();

    beforeEach(() => {
      reportEventMock.mockClear();
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    it('should send a search log', () => {
      const words = '会社 お知らせ';
      sendSearchLog(createSearchWordsMock(words), reportEventMock);

      expect(reportEventMock).toBeCalledTimes(1);
      expect(reportEventMock).toBeCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_SEARCH',
        customProperties: expect.objectContaining({
          searchWords: '["会社","お知らせ"]',
          executionDate: new Date().toISOString(),
        }),
      });
    });
  });

  describe('fetchSearchRequest', () => {
    const getSearchRequestApiMock = jest.fn();
    const setSearchRequestMock = jest.fn();
    const dispatchMock = jest.fn();
    const reportEventMock = jest.fn();

    beforeEach(() => {
      getSearchRequestApiMock.mockClear();
      setSearchRequestMock.mockClear();
      dispatchMock.mockClear();
      reportEventMock.mockClear();
    });

    describe('when getSearchRequestApi is undefined', () => {
      it('should return resolve', async () => {
        await fetchSearchRequest(
          undefined,
          setSearchRequestMock,
          dispatchMock,
          reportEventMock,
          { current: false },
        );
        expect(setSearchRequestMock).toBeCalledTimes(0);
        expect(dispatchMock).toBeCalledTimes(0);
        expect(reportEventMock).toBeCalledTimes(0);
      });
    });

    describe('when get search request resolves', () => {

      describe('when the isUnmounted.current is true', () => {
        it('should cancel dispatch', async () => {
          getSearchRequestApiMock.mockResolvedValueOnce([]);
          await fetchSearchRequest(
            getSearchRequestApiMock,
            setSearchRequestMock,
            dispatchMock,
            reportEventMock,
            { current: true },
          );
          expect(setSearchRequestMock).toBeCalledTimes(1);
          expect(dispatchMock).toBeCalledTimes(0);
          expect(reportEventMock).toBeCalledTimes(0);
        });
      });

      describe('when result is void', () => {
        it('should return resolve', async () => {
          getSearchRequestApiMock.mockResolvedValueOnce('');
          await fetchSearchRequest(
            getSearchRequestApiMock,
            setSearchRequestMock,
            dispatchMock,
            reportEventMock,
            { current: true },
          );
          expect(setSearchRequestMock).toBeCalledTimes(0);
          expect(dispatchMock).toBeCalledTimes(0);
          expect(reportEventMock).toBeCalledTimes(0);
        });
      });

      describe('when the isUnmounted.current is false', () => {
        it('should get Search Request', async () => {
          getSearchRequestApiMock.mockResolvedValueOnce([]);
          await fetchSearchRequest(
            getSearchRequestApiMock,
            setSearchRequestMock,
            dispatchMock,
            reportEventMock,
            { current: false },
          );
          expect(setSearchRequestMock).toBeCalledTimes(1);
          expect(dispatchMock).toBeCalledTimes(0);
          expect(reportEventMock).toBeCalledTimes(0);
        });
      });
    });

    describe('when get search request rejects', () => {
      it('should dispatch SET_ERROR', async () => {
        getSearchRequestApiMock.mockRejectedValueOnce('');
        await fetchSearchRequest(
          getSearchRequestApiMock,
          setSearchRequestMock,
          dispatchMock,
          reportEventMock,
          { current: false },
        );
        expect(reportEventMock).toBeCalledTimes(1);
        expect(reportEventMock).toHaveBeenCalledWith({
          type: EventReportType.SYS_ERROR,
          name: 'API_REQUEST_FAIL',
          error: '',
        });
        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_ERROR',
          payload: SplitViewListMessage.API_REQUEST_FAIL,
        });
      });
    });
  });

  describe('registerSearchRequest', () => {
    const postSearchRequestApiMock = jest.fn();
    const dispatchMock = jest.fn();
    const reportEventMock = jest.fn();
    const onSuccessMock = jest.fn();

    beforeEach(() => {
      postSearchRequestApiMock.mockClear();
      dispatchMock.mockClear();
      reportEventMock.mockClear();
      onSuccessMock.mockClear();
    });

    describe('when postSearchRequestApi is undefined', () => {
      it('should return resolve', async () => {
        await registerSearchRequest(
          undefined,
          {
            userInputs: '',
            synonyms: {
              input: ['aa'],
            },
            combinedWords: '',
          },
          dispatchMock,
          reportEventMock,
          { current: false },
          onSuccessMock,
        );
        expect(dispatchMock).toBeCalledTimes(0);
        expect(reportEventMock).toBeCalledTimes(0);
        expect(onSuccessMock).toBeCalledTimes(0);
      });
    });

    describe('when post search request resolves', () => {
      postSearchRequestApiMock.mockResolvedValueOnce(['aaa']);
      it('should called onSuccess', async () => {
        await registerSearchRequest(
          postSearchRequestApiMock,
          {
            userInputs: '',
            synonyms: {
              input: ['aa'],
            },
            combinedWords: 'aaa',
          },
          dispatchMock,
          reportEventMock,
          { current: false },
          onSuccessMock,
        );
        expect(dispatchMock).toBeCalledTimes(0);
        expect(reportEventMock).toBeCalledTimes(1);
        expect(onSuccessMock).toBeCalledTimes(0);
      });
    });

    describe('when post search request rejects', () => {
      postSearchRequestApiMock.mockRejectedValueOnce('');
      it('should called dispatch & reportEvent', async () => {
        await registerSearchRequest(
          postSearchRequestApiMock,
          {
            userInputs: '',
            synonyms: {
              input: ['aa'],
            },
            combinedWords: 'aaa',
          },
          dispatchMock,
          reportEventMock,
          { current: false },
          onSuccessMock,
        );
        expect(reportEventMock).toBeCalledTimes(2);
        expect(reportEventMock).toHaveBeenCalledWith({
          type: EventReportType.SYS_ERROR,
          name: 'API_REQUEST_FAIL',
          error: '',
        });
        expect(dispatchMock).toBeCalledTimes(2);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_ERROR',
          payload: SplitViewListMessage.API_REQUEST_FAIL,
        });
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_DETAIL_ERROR',
          payload: SplitViewDetailMessage.API_REQUEST_FAIL,
        });
        expect(onSuccessMock).toBeCalledTimes(1);
      });
    });
  });

  describe('onUpdateLastSubmittedWords', () => {
    const getSearchRequestApiMock = jest.fn();
    const postSearchRequestApiMock = jest.fn();
    const setSearchRequestMock = jest.fn();
    const dispatchMock = jest.fn();
    const reportEventMock = jest.fn();
    const setLastSubmittedRef = jest.fn();
    const clearCacheMock = jest.fn();
    const useSearchResultRepositoryReturn = {
      clearCacheMock,
    } as unknown as UseSearchResultRepositoryReturnType;
    const setRequestManagerRefMock = jest.fn();

    beforeEach(() => {
      getSearchRequestApiMock.mockClear();
      postSearchRequestApiMock.mockClear();
      setSearchRequestMock.mockClear();
      clearCacheMock.mockClear();
      dispatchMock.mockClear();
      reportEventMock.mockClear();
      setLastSubmittedRef.mockClear();
      setRequestManagerRefMock.mockClear();
    });

    describe('when the search function is unavailable', () => {
      it('should do nothing', async () => {
        await onUpdateLastSubmittedWords(
          '',
          getSearchRequestApiMock,
          postSearchRequestApiMock,
          setSearchRequestMock,
          useSearchResultRepositoryReturn,
          dispatchMock,
          reportEventMock,
          { current: true },
          { current: '' },
          { current: 'search' },
          setLastSubmittedRef,
          setRequestManagerRefMock,
          metricsMock,
        );
        await onUpdateLastSubmittedWords(
          '',
          getSearchRequestApiMock,
          postSearchRequestApiMock,
          setSearchRequestMock,
          useSearchResultRepositoryReturn,
          dispatchMock,
          reportEventMock,
          { current: false },
          { current: '' },
          { current: 'search' },
          setLastSubmittedRef,
          setRequestManagerRefMock,
          metricsMock,
        );
        await onUpdateLastSubmittedWords(
          '',
          undefined,
          postSearchRequestApiMock,
          setSearchRequestMock,
          useSearchResultRepositoryReturn,
          dispatchMock,
          reportEventMock,
          { current: false },
          { current: 'a' },
          { current: 'search' },
          setLastSubmittedRef,
          setRequestManagerRefMock,
          metricsMock,
        );
        await onUpdateLastSubmittedWords(
          '',
          getSearchRequestApiMock,
          undefined,
          setSearchRequestMock,
          useSearchResultRepositoryReturn,
          dispatchMock,
          reportEventMock,
          { current: false },
          { current: 'aa' },
          { current: 'search' },
          setLastSubmittedRef,
          setRequestManagerRefMock,
          metricsMock,
        );
        expect(dispatchMock).toBeCalledTimes(0);
        expect(getSearchRequestApiMock).toBeCalledTimes(0);
        expect(postSearchRequestApiMock).toBeCalledTimes(0);
      });
    });

    describe('when there are 101 or more keywords', () => {
      it('should show SET_ERROR', async () => {
        const words = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100'].join(' ');
        await onUpdateLastSubmittedWords(
          words,
          getSearchRequestApiMock,
          postSearchRequestApiMock,
          setSearchRequestMock,
          useSearchResultRepositoryReturn,
          dispatchMock,
          reportEventMock,
          { current: false },
          { current: '' },
          { current: 'search' },
          setLastSubmittedRef,
          setRequestManagerRefMock,
          metricsMock,
        );
        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_ERROR',
          payload: SplitViewListMessage.TOO_MANY_SEARCH_KEYWORDS,
        });
      });
    });

    describe('when the number of characters is 256 or more', () => {
      it('should show SET_ERROR', async () => {
        const words = '【文字数チェック】親譲りの無鉄砲で小供の時から損ばかりしてい【約35】【約40】る。小学校に居る時分学校の二階から飛び降りて一週間ほど腰を抜かした事がある。なぜそんな無闇をしたと聞く人があるかも知れぬ。別段深い理由でもない。新築の二階から首を出していたら、同級生の一人が冗談に、いくら威張っても、そこから飛び降りる事は出来まい。弱虫やーい。と囃したからである。小使に負ぶさって帰って来た時、おやじが大きな眼をして二階ぐらいから飛び降りて腰を抜かす奴があるかと云ったから、この次は抜かさずに飛んで見せま【255】a';
        await onUpdateLastSubmittedWords(
          words,
          getSearchRequestApiMock,
          postSearchRequestApiMock,
          setSearchRequestMock,
          useSearchResultRepositoryReturn,
          dispatchMock,
          reportEventMock,
          { current: false },
          { current: '' },
          { current: 'search' },
          setLastSubmittedRef,
          setRequestManagerRefMock,
          metricsMock,
        );
        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_ERROR',
          payload: SplitViewListMessage.TOO_MANY_SEARCH_CHARACTERS,
        });
      });
    });

    describe('when the search function is available', () => {
      /* 内部でセットしたgetSearchRequestApiMockがundefinedで返ってしまうので、一時除外。ブラウザテストで担保可能。リファクタリングで実施。
      it('should dispatch SET_DATA', async () => {
        const words = 'a';
        getSearchRequestApiMock.mockResolvedValueOnce({
          value: [
            {
              'odata.editLink': 'abcd',
              Title: 'aaaのご報告',
              GUID: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
              Modified: new Date().toISOString(),
              Created: '2021-08-24T03:00:59Z',
              category: 'カテゴリ名1',
              Attachments: true,
            },
          ],
        } as ISharePointListsResponse);
        await onUpdateLastSubmittedWords(
          words,
          getSearchRequestApiMock,
          postSearchRequestApiMock,
          setSearchRequestMock,
          useSearchResultRepositoryReturn,
          dispatchMock,
          reportEventMock,
          { current: false },
          { current: '' },
          { current: 'search' },
          setLastSubmittedRef,
          setRequestManagerRefMock,
          metricsMock,
        );
        expect(dispatchMock).toBeCalledTimes(1);
        expect(getSearchRequestApiMock).toBeCalledTimes(1);
        expect(postSearchRequestApiMock).toBeCalledTimes(1);
      });
      */

      describe('when the listModeCache is ListMode.INITIAL_DISPLAY', () => {
        it('should not do anything', async () => {
          const words = 'a';

          await onUpdateLastSubmittedWords(
            words,
            getSearchRequestApiMock,
            postSearchRequestApiMock,
            setSearchRequestMock,
            useSearchResultRepositoryReturn,
            dispatchMock,
            reportEventMock,
            { current: false },
            { current: words },
            { current: ListMode.INITIAL_DISPLAY },
            setLastSubmittedRef,
            setRequestManagerRefMock,
            metricsMock,
          );
          expect(dispatchMock).toBeCalledTimes(0);
          expect(getSearchRequestApiMock).toBeCalledTimes(0);
          expect(postSearchRequestApiMock).toBeCalledTimes(0);
        });
      });

      describe('when the ref has a same value', () => {
        it('should not do anything', async () => {
          const words = 'a';

          await onUpdateLastSubmittedWords(
            words,
            undefined,
            postSearchRequestApiMock,
            setSearchRequestMock,
            useSearchResultRepositoryReturn,
            dispatchMock,
            reportEventMock,
            { current: false },
            { current: words },
            { current: 'initialDisplay' },
            setLastSubmittedRef,
            setRequestManagerRefMock,
            metricsMock,
          );
          expect(dispatchMock).toBeCalledTimes(0);
          expect(getSearchRequestApiMock).toBeCalledTimes(0);
          expect(postSearchRequestApiMock).toBeCalledTimes(0);
        });
      });
    });
  });

  describe('retrieveUnprocessedResults', () => {
    const setRequestManagerRefMock = jest.fn();
    const resultsMock = [
      {
        ids: ['111', '1111'],
        reqId: 'abc',
        pid: 'pid1',
        dataSource: {
          kind: 'SPO' as DataSourceKindType,
          properties: {
            site: 'siteUrl',
            list: 'listGUID',
            category: 'category1',
          },
        },
      },
      {
        ids: ['222', '2222'],
        reqId: 'abc',
        pid: 'pid2',
        dataSource: {
          kind: 'SPO' as DataSourceKindType,
          properties: {
            site: 'siteUrl',
            list: 'listGUID',
            category: 'category1',
          },
        },
      },
      {
        ids: ['333', '3333'],
        reqId: 'abc',
        pid: 'pid3',
        dataSource: {
          kind: 'SPO' as DataSourceKindType,
          properties: {
            site: 'siteUrl',
            list: 'listGUID',
            category: 'category1',
          },
        },
      },
    ];

    beforeEach(() => {
      setRequestManagerRefMock.mockClear();
    });

    describe('requestManagerRef.current.processes is 0', () => {
      const reqManagerMock = {
        current: {
          reqId: 'abc', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
        },
      };
      it('all results are set by InProgress', () => {
        expect(retrieveUnprocessedResults(
          true,
          resultsMock,
          reqManagerMock,
          setRequestManagerRefMock,
        )).toStrictEqual(resultsMock);
        expect(setRequestManagerRefMock).toBeCalledWith({
          processes: [
            {
              pid: 'pid1',
              status: 'InProgress',
            },
            {
              pid: 'pid2',
              status: 'InProgress',
            },
            {
              pid: 'pid3',
              status: 'InProgress',
            },
          ],
        });
      });
    });

    describe('requestManagerRef.current.processes is greater than 0', () => {
      const reqManagerMock = {
        current: {
          reqId: 'abc',
          retryCount: 10,
          processes: [{
            pid: 'pid1',
            status: 'Done',
          },
          {
            pid: 'pid2',
            status: 'Done',
          }],
          hasError: false,
          loaded: false,
        } as unknown as IRequestManager,
      };
      it('should be set merged with the previous process', () => {
        expect(retrieveUnprocessedResults(
          false,
          resultsMock,
          reqManagerMock,
          setRequestManagerRefMock,
        )).toStrictEqual([
          {
            ids: ['333', '3333'],
            reqId: 'abc',
            pid: 'pid3',
            dataSource: {
              kind: 'SPO',
              properties: {
                site: 'siteUrl',
                list: 'listGUID',
                category: 'category1',
              },
            },
          },
        ]);
        expect(setRequestManagerRefMock).toBeCalledWith({
          processes: [
            {
              pid: 'pid1',
              status: 'Done',
            },
            {
              pid: 'pid2',
              status: 'Done',
            },
            {
              pid: 'pid3',
              status: 'InProgress',
            },
          ],
        });
      });

    });
  });

  describe('abortInterval', () => {

    const searchRequestMock = {
      reqId: 'abc',
      results: [
        {
          ids: ['aa', 'aa'],
          reqId: 'aa',
          pid: 'aa',
          dataSource: {
            kind: 'SPO' as DataSourceKindType,
            properties: {
              site: 'siteUrl',
              list: 'listGUID',
              category: 'category1',
            },
          },
        },
      ],
    };

    describe('when RequestManager hasError is true', () => {
      it('should return true', () => {
        const requestManagerMock = {
          current: {
            reqId: 'abc', retryCount: 10, processes: [], hasError: true, loaded: false, hasIncompleteRequests: false,
          },
        };
        expect(abortInterval(requestManagerMock, searchRequestMock)).toBe(true);
      });
    });

    describe('when RequestManager hasError is false', () => {
      const requestManagerMock = {
        current: {
          reqId: 'abc', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
        },
      };
      it('should return false', () => {
        expect(abortInterval(requestManagerMock, searchRequestMock)).toBe(false);
      });
      describe('when reqId is different from the latest', () => {
        it('should return true', () => {
          expect(abortInterval(requestManagerMock, {
            reqId: 'new reqId',
            results: [
              {
                ids: ['aa', 'aa'],
                reqId: 'aa',
                pid: 'aa',
                dataSource: {
                  kind: 'SPO',
                  properties: {
                    site: 'siteUrl',
                    list: 'listGUID',
                    category: 'category1',
                  },
                },
              },
            ],
          })).toBe(true);
        });
      });
    });
  });

  describe('fetchRetrievedListItems', () => {
    const fetchSPOListMock = jest.fn();
    const fetchMailsMock = jest.fn();
    const fetchChatMock = jest.fn();
    const addResultsMock = jest.fn();
    const updateResultsMock = jest.fn();
    const reportEventMock = jest.fn();
    const reportMetricMock = jest.fn();
    const setAllListDataRefMock = jest.fn();
    const setRequestManagerRefMock = jest.fn();
    const processMock = jest.fn();
    const dispatchMock = jest.fn();
    const onSuccessMock = jest.fn();
    const cancellationRefMock = { current: false };

    beforeEach(() => {
      fetchSPOListMock.mockClear();
      fetchMailsMock.mockClear();
      fetchChatMock.mockClear();
      addResultsMock.mockClear();
      updateResultsMock.mockClear();
      reportEventMock.mockClear();
      reportMetricMock.mockClear();
      setAllListDataRefMock.mockClear();
      setRequestManagerRefMock.mockClear();
      processMock.mockClear();
      dispatchMock.mockClear();
      onSuccessMock.mockClear();
      cancellationRefMock.current = false;
    });

    describe('when fetchRetrievedListItems is unavailable', () => {
      it('should do nothing', async () => {
        // 検索要求が取れていない
        await fetchRetrievedListItems(
          null,
          fetchSPOListMock,
          addResultsMock,
          updateResultsMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          processMock,
          { current: true },
          metricsMock,
          { current: [] },
          cancellationRefMock,
          setAllListDataRefMock,
          {
            current: {
              reqId: 'aa', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          onSuccessMock,
        );
        // fetch関数がundefiend
        await fetchRetrievedListItems(
          {
            reqId: 'aa',
            results: [
              {
                ids: ['aa', 'aa'],
                reqId: 'aa',
                pid: 'aa',
                dataSource: {},
              },
              {
                ids: ['bb', 'bb'],
                reqId: 'bb',
                pid: 'bb',
                dataSource: {},
              },
            ],
          },
          undefined,
          addResultsMock,
          updateResultsMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: true },
          metricsMock,
          { current: [] },
          cancellationRefMock,
          setAllListDataRefMock,
          {
            current: {
              reqId: 'aa', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          onSuccessMock,
        );
        // reqIdが存在しない
        await fetchRetrievedListItems(
          {
            results: [
              {
                ids: ['aa', 'aa'],
                reqId: 'aa',
                pid: 'aa',
                dataSource: {},
              },
              {
                ids: ['bb', 'bb'],
                reqId: 'bb',
                pid: 'bb',
                dataSource: {},
              },
            ],
          },
          fetchSPOListMock,
          addResultsMock,
          updateResultsMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: true },
          metricsMock,
          { current: [] },
          cancellationRefMock,
          setAllListDataRefMock,
          {
            current: {
              reqId: 'aa', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          onSuccessMock,
        );
        // resultsが存在しない
        await fetchRetrievedListItems(
          {
            reqId: 'aa',
          },
          fetchSPOListMock,
          addResultsMock,
          updateResultsMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: true },
          metricsMock,
          { current: [] },
          cancellationRefMock,
          setAllListDataRefMock,
          {
            current: {
              reqId: 'aa', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          onSuccessMock,
        );

        expect(fetchSPOListMock).toBeCalledTimes(0);
        expect(dispatchMock).toBeCalledTimes(0);
      });
    });

    describe('when fetchRetrievedListItems is available', () => {
      it('should called fetchSPOList', async () => {
        fetchSPOListMock.mockResolvedValue({
          value: [
            createListApiSingleEntry({ GUID: 'aa', Modified: '2050-01-01T00:00:00.000Z' }),
            createListApiSingleEntry({ GUID: 'bb', Modified: '2050-01-01T00:00:00.000Z' }),
          ],
        });

        const searchRequestMock = {
          reqId: 'aa',
          results: [
            {
              ids: ['aa', 'aa'],
              reqId: 'aa',
              pid: 'aa',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
            {
              ids: ['bb', 'bb'],
              reqId: 'bb',
              pid: 'bb',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
          ],
        };
        await fetchRetrievedListItems(
          searchRequestMock,
          fetchSPOListMock,
          addResultsMock,
          updateResultsMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: false },
          metricsMock,
          { current: [] },
          cancellationRefMock,
          setAllListDataRefMock,
          {
            current: {
              reqId: 'aa', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          onSuccessMock,
        );
        expect(fetchSPOListMock).toBeCalled();
      });

      it('fetchRetrievedListItemsがChat検索結果を正しく処理する', async () => {
        // Chat検索結果のモック設定
        const mockChatResult = {
          responses: [
            {
              id: 'chat-1',
              status: 200,
              body: {
                id: 'chat-1',
                subject: 'テストチャット',
                from: { user: { displayName: 'テストユーザー' } },
                createdDateTime: '2024-01-01T00:00:00Z',
              },
            },
          ],
        };

        processMock.mockResolvedValue({
          responses: [mockChatResult],
          errors: [],
          recoverable: [],
          tooManyRequests: [],
          totalTooManyRequests: 0,
        });

        const searchRequestMock = {
          reqId: 'test-req-id',
          results: [
            {
              ids: ['chat-1'],
              reqId: 'test-req-id',
              pid: 'chat-pid',
              dataSource: {
                kind: 'Chat' as DataSourceKindType,
                properties: {},
              },
            },
          ],
        };

        await fetchRetrievedListItems(
          searchRequestMock,
          fetchSPOListMock,
          addResultsMock,
          updateResultsMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: false },
          metricsMock,
          { current: [] },
          cancellationRefMock,
          setAllListDataRefMock,
          {
            current: {
              reqId: 'test-req-id',
              retryCount: 0,
              processes: [],
              hasError: false,
              loaded: false,
              hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          onSuccessMock,
        );

        // Chat検索処理が実行されることを確認
        expect(processMock).toHaveBeenCalled();
        expect(addResultsMock).toHaveBeenCalledWith('chat-pid', ['chat-1']);
      });

      it('fetchRetrievedListItemsがMail検索結果を正しく処理する', async () => {
        // Mail検索結果のモック設定
        const mockMailResult = {
          responses: [
            {
              id: 'mail-1',
              status: 200,
              body: {
                id: 'mail-1',
                subject: 'テストメール',
                from: { emailAddress: { name: 'テストユーザー' } },
                receivedDateTime: '2024-01-01T00:00:00Z',
              },
            },
          ],
        };

        processMock.mockResolvedValue({
          responses: [mockMailResult],
          errors: [],
          recoverable: [],
          tooManyRequests: [],
          totalTooManyRequests: 0,
        });

        const searchRequestMock = {
          reqId: 'test-req-id',
          results: [
            {
              ids: ['mail-1'],
              reqId: 'test-req-id',
              pid: 'mail-pid',
              dataSource: {
                kind: 'Mail' as DataSourceKindType,
                properties: {},
              },
            },
          ],
        };

        await fetchRetrievedListItems(
          searchRequestMock,
          fetchSPOListMock,
          addResultsMock,
          updateResultsMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: false },
          metricsMock,
          { current: [] },
          cancellationRefMock,
          setAllListDataRefMock,
          {
            current: {
              reqId: 'test-req-id',
              retryCount: 0,
              processes: [],
              hasError: false,
              loaded: false,
              hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          onSuccessMock,
        );

        // Mail検索処理が実行されることを確認
        expect(processMock).toHaveBeenCalled();
        expect(addResultsMock).toHaveBeenCalledWith('mail-pid', ['mail-1']);
      });
    });
  });

  describe('isSearchRequestFulfilled', () => {
    it.each([
      {
        when: 'state is not Completed or Cancelled',
        searchRequestMock: {
          reqId: 'aa',
          state: SearchRequestState.IN_PROGRESS,
          results: [
            {
              ids: ['aa', 'aa'],
              reqId: 'aa',
              pid: 'aa',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
            {
              ids: ['bb', 'bb'],
              reqId: 'bb',
              pid: 'bb',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
          ],
        },
        requestManagerMock: {
          current: {
            reqId: 'abc',
            retryCount: 10,
            processes: [
              { status: 'Done', pid: 'aa', hasRetryError: false },
              { status: 'Done', pid: 'bb', hasRetryError: false },
            ] as IProcess[],
            hasError: true,
            loaded: false,
            hasIncompleteRequests: false,
          },
        },
        expected: false,
      },
      {
        when: 'state is Completed',
        searchRequestMock: {
          reqId: 'aa',
          state: SearchRequestState.COMPLETED,
          results: [
            {
              ids: ['aa', 'aa'],
              reqId: 'aa',
              pid: 'aa',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
            {
              ids: ['bb', 'bb'],
              reqId: 'bb',
              pid: 'bb',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
          ],
        },
        requestManagerMock: {
          current: {
            reqId: 'abc',
            retryCount: 10,
            processes: [
              { status: 'Done', pid: 'aa', hasRetryError: false },
              { status: 'Done', pid: 'bb', hasRetryError: false },
            ] as IProcess[],
            hasError: true,
            loaded: false,
            hasIncompleteRequests: false,
          },
        },
        expected: true,
      },
      {
        when: 'state is cancelled',
        searchRequestMock: {
          reqId: 'aa',
          state: SearchRequestState.CANCELLED,
          results: [
            {
              ids: ['aa', 'aa'],
              reqId: 'aa',
              pid: 'aa',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
            {
              ids: ['bb', 'bb'],
              reqId: 'bb',
              pid: 'bb',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
          ],
        },
        requestManagerMock: {
          current: {
            reqId: 'abc',
            retryCount: 10,
            processes: [
              { status: 'Done', pid: 'aa', hasRetryError: false },
              { status: 'Done', pid: 'bb', hasRetryError: false },
            ] as IProcess[],
            hasError: true,
            loaded: false,
            hasIncompleteRequests: false,
          },
        },
        expected: true,
      },
      {
        when: 'results.length and completed processes count do not match',
        searchRequestMock: {
          reqId: 'aa',
          state: SearchRequestState.COMPLETED,
          results: [
            {
              ids: ['aa', 'aa'],
              reqId: 'aa',
              pid: 'aa',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
            {
              ids: ['bb', 'bb'],
              reqId: 'bb',
              pid: 'bb',
              dataSource: {
                kind: 'SPO' as DataSourceKindType,
                properties: {
                  site: 'siteUrl',
                  list: 'listGUID',
                  category: 'category1',
                },
              },
            },
          ],
        },
        requestManagerMock: {
          current: {
            reqId: 'abc',
            retryCount: 10,
            processes: [
              { status: 'Done', pid: 'aa', hasRetryError: false },
              { status: 'InProgress', pid: 'bb', hasRetryError: false },
            ] as IProcess[],
            hasError: true,
            loaded: false,
            hasIncompleteRequests: false,
          },
        },
        expected: false,
      },
    ])('returns $expected when $when', ({
      searchRequestMock, requestManagerMock, expected,
    }) => {
      const result = isSearchRequestFulfilled(
        searchRequestMock,
        requestManagerMock,
      );
      expect(result).toBe(expected);
    });
  });

  describe('indicate message when retries exceed the specified number of times', () => {
    const dispatchMock = jest.fn();
    const reportEventMock = jest.fn();
    it('should return TOO_MANY_RETRY', () => {
      indicateSearchStateError(
        dispatchMock,
        reportEventMock,
      );
      expect(dispatchMock).toBeCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'SEARCH_STATE_ERROR',
      });
    });
  });

  describe('onIntervalImpl', () => {
    const getSearchRequestApiMock = jest.fn();
    const fetchListMock = jest.fn();
    const fetchMailsMock = jest.fn();
    const fetchChatMock = jest.fn();
    const processMock = jest.fn();
    const dispatchMock = jest.fn();
    const reportEventMock = jest.fn();
    const reportMetricMock = jest.fn();
    const setAllListDataRefMock = jest.fn();
    const setRequestManagerRefMock = jest.fn();
    const setSearchRequestMock = jest.fn();
    const setIntervalMock = jest.fn();
    const onSuccessMock = jest.fn();
    const clearCacheMock = jest.fn();
    const useSearchResultRepositoryReturn = {
      clearCacheMock,
    } as unknown as UseSearchResultRepositoryReturnType;
    const cancellationRefMock = { current: false };

    beforeEach(() => {
      getSearchRequestApiMock.mockClear();
      fetchListMock.mockClear();
      fetchMailsMock.mockClear();
      fetchChatMock.mockClear();
      processMock.mockClear();
      dispatchMock.mockClear();
      reportEventMock.mockClear();
      reportMetricMock.mockClear();
      setAllListDataRefMock.mockClear();
      setRequestManagerRefMock.mockClear();
      setSearchRequestMock.mockClear();
      setIntervalMock.mockClear();
      clearCacheMock.mockClear();
      onSuccessMock.mockClear();
      cancellationRefMock.current = false;
    });

    describe('when onIntervalImpl is unavailable', () => {
      it('should do nothing', () => {
        onIntervalImpl(
          {},
          getSearchRequestApiMock,
          fetchChatMock,
          processMock,
          dispatchMock,
          reportEventMock,
          reportMetricMock,
          { current: true },
          metricsMock,
          { current: [] },
          setAllListDataRefMock,
          {
            current: {
              reqId: 'aa', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
            },
          },
          setRequestManagerRefMock,
          setSearchRequestMock,
          setIntervalMock,
          useSearchResultRepositoryReturn,
          cancellationRefMock,
          onSuccessMock,
        );

        expect(dispatchMock).toBeCalledTimes(0);
        expect(reportEventMock).toBeCalledTimes(0);
        expect(reportMetricMock).toBeCalledTimes(0);
      });
    });

    describe('when onIntervalImpl is available', () => {
      describe('when the first search result is obtained', () => {
        it('should be called setRequestManager and setInterval', () => {
          const IN_PROGRESS_INTERVAL = 3 * 1000;
          onIntervalImpl(
            {
              reqId: 'aa',
              results: [
                {
                  ids: ['aa', 'aa'],
                  reqId: 'aa',
                  pid: 'aa',
                  dataSource: {},
                },
                {
                  ids: ['bb', 'bb'],
                  reqId: 'bb',
                  pid: 'bb',
                  dataSource: {},
                },
              ],
            },
            getSearchRequestApiMock,
            fetchListMock,
            processMock,
            dispatchMock,
            reportEventMock,
            reportMetricMock,
            { current: true },
            metricsMock,
            { current: [] },
            setAllListDataRefMock,
            {
              current: {
                reqId: 'aa', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
              },
            },
            setRequestManagerRefMock,
            setSearchRequestMock,
            setIntervalMock,
            useSearchResultRepositoryReturn,
            cancellationRefMock,
            onSuccessMock,
          );

          expect(setRequestManagerRefMock).toBeCalledWith({
            retryCount: 0,
          });
          expect(setIntervalMock).toBeCalledTimes(1);
          expect(setIntervalMock).toBeCalledWith(IN_PROGRESS_INTERVAL);
        });
      });

      describe('when retries exceed the specified number of times', () => {
        it('should be called TOO_MANY_RETRY', () => {
          onIntervalImpl(
            {
              reqId: 'reqId',
              results: [
                {
                  ids: ['aa', 'aa'],
                  reqId: 'aa',
                  pid: 'aa',
                  dataSource: {},
                },
                {
                  ids: ['bb', 'bb'],
                  reqId: 'bb',
                  pid: 'bb',
                  dataSource: {},
                },
              ],
            },
            getSearchRequestApiMock,
            fetchListMock,
            processMock,
            dispatchMock,
            reportEventMock,
            reportMetricMock,
            { current: true },
            metricsMock,
            { current: [] },
            setAllListDataRefMock,
            {
              current: {
                reqId: 'reqId', retryCount: 70, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
              },
            },
            setRequestManagerRefMock,
            setSearchRequestMock,
            setIntervalMock,
            useSearchResultRepositoryReturn,
            cancellationRefMock,
            onSuccessMock,
          );
          expect(reportEventMock).toBeCalledTimes(1);
          expect(reportEventMock).toBeCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'TOO_MANY_RETRY',
          });
        });

        describe('when one or more data is available', () => {
          it('should be called dispatch SET_DATA', () => {
            onIntervalImpl(
              {
                reqId: 'reqId',
                results: [
                  {
                    ids: ['aa', 'aa'],
                    reqId: 'aa',
                    pid: 'aa',
                    dataSource: {},
                  },
                  {
                    ids: ['bb', 'bb'],
                    reqId: 'bb',
                    pid: 'bb',
                    dataSource: {},
                  },
                ],
              },
              getSearchRequestApiMock,
              fetchListMock,
              processMock,
              dispatchMock,
              reportEventMock,
              reportMetricMock,
              { current: true },
              metricsMock,
              {
                current: [{
                  id: 'aa',
                  note: '',
                  displayDate: '2023-04-01T00:00:00.00Z',
                  properties: {
                  },
                  kind: 'SPO',
                  title: 'qwerty',
                }],
              },
              setAllListDataRefMock,
              {
                current: {
                  reqId: 'reqId', retryCount: 70, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
                },
              },
              setRequestManagerRefMock,
              setSearchRequestMock,
              setIntervalMock,
              useSearchResultRepositoryReturn,
              cancellationRefMock,
              onSuccessMock,
            );
            expect(dispatchMock).toBeCalledWith({
              type: 'SET_DETAILVIEW',
              payload: {
                detailView: SplitViewDetailView.DEFAULT,
              },
            });
            expect(dispatchMock).toBeCalledWith({
              type: 'SET_DATA',
              payload: {
                listView: SplitViewListView.SEARCH_COMPLETED,
                listMessage: SplitViewListMessage.TOO_MANY_RETRY,
              },
            });
          });
        });

        describe('when no data is unavailable', () => {
          it('should be called dispatch SET_DATA', () => {
            onIntervalImpl(
              {
                reqId: 'reqId',
                results: [
                  {
                    ids: ['aa', 'aa'],
                    reqId: 'aa',
                    pid: 'aa',
                    dataSource: {},
                  },
                  {
                    ids: ['bb', 'bb'],
                    reqId: 'bb',
                    pid: 'bb',
                    dataSource: {},
                  },
                ],
              },
              getSearchRequestApiMock,
              fetchListMock,
              processMock,
              dispatchMock,
              reportEventMock,
              reportMetricMock,
              { current: true },
              metricsMock,
              { current: [] },
              setAllListDataRefMock,
              {
                current: {
                  reqId: 'reqId', retryCount: 70, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
                },
              },
              setRequestManagerRefMock,
              setSearchRequestMock,
              setIntervalMock,
              useSearchResultRepositoryReturn,
              cancellationRefMock,
              onSuccessMock,
            );
            expect(dispatchMock).toBeCalledTimes(3);
            expect(dispatchMock).toBeCalledWith({
              type: 'SET_DATA',
              payload: {
                listView: SplitViewListView.SEARCH_COMPLETED,
                listMessage: SplitViewListMessage.TOO_MANY_RETRY,
                detailView: SplitViewDetailView.ERROR,
                detailMessage: SplitViewDetailMessage.NO_ITEMS,
              },
            });
          });
        });
      });

      describe('when the state of the search request is error', () => {
        it('should be terminated interval', () => {
          onIntervalImpl(
            {
              reqId: 'reqId',
              state: 'Error',
              results: [
                {
                  ids: ['aa', 'aa'],
                  reqId: 'aa',
                  pid: 'aa',
                  dataSource: {},
                },
                {
                  ids: ['bb', 'bb'],
                  reqId: 'bb',
                  pid: 'bb',
                  dataSource: {},
                },
              ],
            },
            getSearchRequestApiMock,
            fetchListMock,
            processMock,
            dispatchMock,
            reportEventMock,
            reportMetricMock,
            { current: true },
            metricsMock,
            { current: [] },
            setAllListDataRefMock,
            {
              current: {
                reqId: 'reqId', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
              },
            },
            setRequestManagerRefMock,
            setSearchRequestMock,
            setIntervalMock,
            useSearchResultRepositoryReturn,
            cancellationRefMock,
            onSuccessMock,
          );

          expect(reportEventMock).toBeCalledTimes(1);
          expect(reportEventMock).toBeCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'SEARCH_STATE_ERROR',
          });
          expect(dispatchMock).toBeCalledTimes(1);
          expect(setIntervalMock).toBeCalledTimes(2);
        });
      });

      describe('when the state of the search request is inProgress', () => {
        const getSearchRequestApiUndefiedMock = jest.fn().mockResolvedValue(undefined);
        it('should be go get a new search request', () => {
          onIntervalImpl(
            {
              reqId: 'reqId',
              state: 'InProgress',
              results: [
                {
                  ids: ['aa', 'aa'],
                  reqId: 'aa',
                  pid: 'aa',
                  dataSource: {
                    kind: 'SPO',
                    properties: {
                      list: 'list',
                      listUrl: 'listUrl',
                      site: 'site',
                      listName: 'listName',
                      category: 'category',
                    },
                  },
                },
              ],
            },
            getSearchRequestApiUndefiedMock,
            fetchListMock,
            processMock,
            dispatchMock,
            reportEventMock,
            reportMetricMock,
            { current: false },
            metricsMock,
            {
              current: [{
                id: 'aa',
                note: '',
                displayDate: '2023-04-01T00:00:00.00Z',
                properties: {
                },
                kind: 'SPO',
                title: 'qwerty',
              }],
            },
            setAllListDataRefMock,
            {
              current: {
                reqId: 'reqId', retryCount: 10, processes: [], hasError: false, loaded: false, hasIncompleteRequests: false,
              },
            },
            setRequestManagerRefMock,
            setSearchRequestMock,
            setIntervalMock,
            useSearchResultRepositoryReturn,
            cancellationRefMock,
            onSuccessMock,
          );
          expect(setRequestManagerRefMock).toBeCalledTimes(2);
          expect(getSearchRequestApiUndefiedMock).toBeCalledTimes(1);
          expect(dispatchMock).toBeCalledTimes(0);
          expect(reportEventMock).toBeCalledTimes(0);
        });
      });

      describe('when the State of the search request is Completed', () => {
        describe('when there is no data in the list', () => {
          it('should set dispatch "SET_NO_ITEMS" ', () => {
            onIntervalImpl(
              {
                reqId: 'reqId',
                state: 'Completed',
                results: [
                  {
                    ids: ['aa', 'aa'],
                    reqId: 'aa',
                    pid: 'aa',
                    dataSource: {
                      kind: 'SPO',
                      properties: {
                        list: 'list',
                        listUrl: 'listUrl',
                        site: 'site',
                        listName: 'listName',
                        category: 'category',
                      },
                    },
                  },
                ],
              },
              getSearchRequestApiMock,
              fetchListMock,
              processMock,
              dispatchMock,
              reportEventMock,
              reportMetricMock,
              { current: false },
              metricsMock,
              { current: [] },
              setAllListDataRefMock,
              {
                current: {
                  reqId: 'reqId',
                  retryCount: 10,
                  processes: [{ pid: 'aa', status: 'Done', hasRetryError: false }],
                  hasError: false,
                  loaded: false,
                  hasIncompleteRequests: false,
                },
              },
              setRequestManagerRefMock,
              setSearchRequestMock,
              setIntervalMock,
              useSearchResultRepositoryReturn,
              cancellationRefMock,
              onSuccessMock,
            );
            expect(reportEventMock).toBeCalledTimes(1);
            expect(reportEventMock).toBeCalledWith({
              type: EventReportType.SYS_EVENT,
              name: 'NO_SEARCH_RESULT',
            });
            expect(dispatchMock).toBeCalledTimes(1);
            expect(dispatchMock).toBeCalledWith({
              type: 'SET_NO_ITEMS',
              payload: {
                listMessage: SplitViewListMessage.NO_SEARCH_RESULT,
                detailMessage: SplitViewDetailMessage.NO_ITEMS,
              },
            });
            expect(reportMetricMock).toBeCalledTimes(1);
            expect(setIntervalMock).toBeCalledTimes(1);
          });
        });

        describe('when there is data in the list', () => {
          it('should set dispatch "SET_LISTVIEW" ', () => {
            onIntervalImpl(
              {
                reqId: 'reqId',
                state: 'Completed',
                results: [
                  {
                    ids: ['aa', 'aa'],
                    reqId: 'aa',
                    pid: 'aa',
                    dataSource: {
                      kind: 'SPO',
                      properties: {
                        list: 'list',
                        listUrl: 'listUrl',
                        site: 'site',
                        listName: 'listName',
                        category: 'category',
                      },
                    },
                  },
                ],
              },
              getSearchRequestApiMock,
              fetchListMock,
              processMock,
              dispatchMock,
              reportEventMock,
              reportMetricMock,
              { current: false },
              metricsMock,
              {
                current: [
                  {
                    id: 'aaa',
                    note: '',
                    displayDate: '',
                    properties: {},
                    kind: 'SPO',
                    title: 'qwerty',
                  },
                ],
              },
              setAllListDataRefMock,
              {
                current: {
                  reqId: 'reqId',
                  retryCount: 10,
                  processes: [{ pid: 'aa', status: 'Done', hasRetryError: false }],
                  hasError: false,
                  loaded: false,
                  hasIncompleteRequests: false,
                },
              },
              setRequestManagerRefMock,
              setSearchRequestMock,
              setIntervalMock,
              useSearchResultRepositoryReturn,
              cancellationRefMock,
              onSuccessMock,
            );
            expect(dispatchMock).toBeCalledTimes(1);
            expect(dispatchMock).toBeCalledWith({
              type: 'SET_LISTVIEW',
              payload: {
                listView: SplitViewListView.SEARCH_COMPLETED,
              },
            });
            expect(reportEventMock).not.toBeCalled();
            expect(reportMetricMock).toBeCalledTimes(1);
            expect(setIntervalMock).toBeCalledTimes(1);
          });
        });
        describe('when cancellation is true', () => {
          it('should report CLICK_CANCEL" ', () => {
            onIntervalImpl(
              {
                reqId: 'reqId',
                state: 'Completed',
                condition: 'test keyword',
                results: [
                  {
                    ids: ['aa', 'aa'],
                    reqId: 'aa',
                    pid: 'aa',
                    dataSource: {
                      kind: 'SPO',
                      properties: {
                        list: 'list',
                        listUrl: 'listUrl',
                        site: 'site',
                        listName: 'listName',
                        category: 'category',
                      },
                    },
                  },
                ],
              },
              getSearchRequestApiMock,
              fetchListMock,
              processMock,
              dispatchMock,
              reportEventMock,
              reportMetricMock,
              { current: false },
              metricsMock,
              {
                current: [
                  {
                    id: 'aaa',
                    note: '',
                    displayDate: '',
                    properties: {},
                    kind: 'SPO',
                    title: 'qwerty',
                  },
                ],
              },
              setAllListDataRefMock,
              {
                current: {
                  reqId: 'reqId',
                  retryCount: 10,
                  processes: [{ pid: 'aa', status: 'Done', hasRetryError: false }],
                  hasError: false,
                  loaded: false,
                  hasIncompleteRequests: false,
                },
              },
              setRequestManagerRefMock,
              setSearchRequestMock,
              setIntervalMock,
              useSearchResultRepositoryReturn,
              { current: true },
              onSuccessMock,
            );
            expect(dispatchMock).toBeCalledTimes(1);
            expect(dispatchMock).toBeCalledWith({
              type: 'SET_LISTVIEW',
              payload: {
                listView: SplitViewListView.SEARCH_COMPLETED,
              },
            });
            expect(reportEventMock).toBeCalledWith({
              type: EventReportType.USER_EVENT,
              name: 'CLICK_CANCEL',
              customProperties: {
                searchWords: 'test keyword',
                elapsedFromStart: 0,
              },
            });
            expect(reportMetricMock).toBeCalledTimes(1);
            expect(setIntervalMock).toBeCalledTimes(1);
          });
        });
      });
    });
  });

  describe('send retry error', () => {
    const requestManagementRef = {
      current: {
        reqId: '', retryCount: 0, hasError: false, loaded: false, processes: [], hasIncompleteRequests: false,
      },
    };
    const setRequestManagementRef = jest.fn();
    const dispatchMock = jest.fn();
    const eventReporter = jest.fn();

    beforeEach(() => {
      dispatchMock.mockClear();
      eventReporter.mockClear();
      setRequestManagementRef.mockClear();
    });
    it('should report no errors', () => {
      handleBatchRequestBadResponse<unknown>('',
        'Mail',
        {
          responses: [],
          recoverable: [],
          errors: [],
          totalTooManyRequests: 0,
          tooManyRequests: [],
        },
        requestManagementRef,
        setRequestManagementRef,
        dispatchMock,
        eventReporter);

      expect(eventReporter).not.toHaveBeenCalled();
      expect(setRequestManagementRef).not.toHaveBeenCalled();
    });

    it('should report exceeded recoverable and errors', () => {
      handleBatchRequestBadResponse<unknown>('',
        'Mail',
        {
          responses: [],
          recoverable: [
            {
              body: { code: '429', message: 'retry after' },
              id: '1',
              status: 429,
              headers: { 'Retry-After': '3' },
            },
            {
              body: { code: '409', message: 'conflict' },
              id: '2',
              status: 409,
              headers: {},
            },
          ],
          errors: [
            {
              body: { code: '400', message: 'bad request' },
              id: '3',
              status: 400,
              headers: {},
            },
            {
              body: { code: '404', message: 'not found' },
              id: '4',
              status: 404,
              headers: {},
            },
          ],
          totalTooManyRequests: 10,
          tooManyRequests: [
            { maxRetryAfter: 2, count: 2 },
            { maxRetryAfter: 4, count: 2 },
            { maxRetryAfter: 6, count: 2 },
            { maxRetryAfter: 1, count: 2 },
            { maxRetryAfter: 8, count: 2 },
          ],
        },
        requestManagementRef,
        setRequestManagementRef,
        dispatchMock,
        eventReporter);

      expect(eventReporter).toHaveBeenCalledWith(
        {
          type: EventReportType.SYS_ERROR,
          name: 'BATCH_REQUEST_RETRY_EXCEEDED',
          customProperties: {
            kind: 'Mail',
            ids: [
              { id: '1', status: 429 },
              { id: '2', status: 409 },
            ],
            count: 2,
          },
        },
      );
      expect(eventReporter).toHaveBeenCalledWith(
        {
          type: EventReportType.SYS_ERROR,
          name: 'BATCH_REQUEST_FAILED',
          customProperties: {
            kind: 'Mail',
            ids: [
              { id: '3', status: 400 },
              { id: '4', status: 404 },
            ],
            count: 2,
          },
        },
      );
      expect(eventReporter).toHaveBeenCalledWith(
        {
          type: EventReportType.SYS_EVENT,
          name: 'BATCH_REQUEST_RETRY_OCCURRED',
          customProperties: {
            kind: 'Mail',
            totalTooManyRequests: 10,
            tooManyRequests: [
              { maxRetryAfter: 2, count: 2 },
              { maxRetryAfter: 4, count: 2 },
              { maxRetryAfter: 6, count: 2 },
              { maxRetryAfter: 1, count: 2 },
              { maxRetryAfter: 8, count: 2 },
            ],
          },
        },
      );
      expect(setRequestManagementRef).toHaveBeenCalled();
    });
  });

  describe('isCacheStatusCompleted', () => {
    const resultsCacheMock = [
      {
        ids: ['111', '1111'],
        reqId: 'abc',
        pid: 'pid1',
        dataSource: {
          kind: 'SPO' as DataSourceKindType,
          properties: {
            site: 'siteUrl',
            list: 'listGUID',
            category: 'category1',
          },
        },
      },
    ] as unknown as ISplitViewListSingle[];
    describe('when searchResultsCache is undefiend', () => {
      it('should return false', () => {
        expect(isCacheStatusCompleted(
          undefined,
          resultsCacheMock,
          'reqId',
        )).toBeFalsy();
      });
    });

    describe('when SearchRequest and searchRequestCache reqId are different', () => {
      const requestCacheMock = {
        status: 'Completed',
        result: {
          reqId: '111',
          state: 'Error',
          results: [],
        },
      };
      it('should return false', () => {
        expect(isCacheStatusCompleted(
          requestCacheMock,
          resultsCacheMock,
          '222',
        )).toBeFalsy();
      });
    });

    describe('when searchRequestCache.status is not CacheStatus.COMPLETED', () => {

      describe.each([
        {
          status: 'RequestInProgress',
          result: {
            reqId: 'reqId',
            state: 'Error',
            results: [],
          },
        },
        {
          status: 'SearchOnInterval',
          result: {
            reqId: 'reqId',
            state: 'Error',
            results: [],
          },
        },
        {
          status: 'Error',
          result: {
            reqId: 'reqId',
            state: 'Error',
            results: [],
          },
        },
      ])('when searchRequestCache.status is $status', (requestCache) => {

        it('should return false', () => {
          expect(isCacheStatusCompleted(
            requestCache,
            resultsCacheMock,
            'reqId',
          )).toBeFalsy();
        });
      });
    });

    describe('when all cache fetche have been completed', () => {
      const requestCacheMock = {
        status: 'Completed',
        result: {
          reqId: '111',
          state: 'Error',
          results: [],
        },
      };

      it('should return true', () => {
        expect(isCacheStatusCompleted(
          requestCacheMock,
          resultsCacheMock,
          '111',
        )).toBeTruthy();
      });
    });
  });

  describe('setPartialSearchResultCache', () => {
    const cachedPidsRef = { current: ['pid1'] };
    const dispatchMock = jest.fn();
    const setRequestManagerRefMock = jest.fn();
    const setAllDataListRefMock = jest.fn();
    const setMetricsMock = jest.fn();

    beforeEach(() => {
      dispatchMock.mockClear();
      setRequestManagerRefMock.mockClear();
      setAllDataListRefMock.mockClear();
      setMetricsMock.mockClear();
    });

    describe('when list any items', () => {
      const searchResultCache: ISplitViewListSingle[] = Array.from(Array(200), (v, k) => ({
        id: `${k}`, note: 'note', title: 'title', properties: {}, kind: 'SPO', displayDate: new Date().toISOString(),
      }));
      it('should set all items', () => {
        setPartialSearchResultCache(
          searchResultCache,
          cachedPidsRef,
          dispatchMock,
          setRequestManagerRefMock,
          setAllDataListRefMock,
          setMetricsMock,
        );
        expect(setMetricsMock).toHaveBeenCalledTimes(1);
        expect(dispatchMock).toHaveBeenCalledWith({
          type: 'SET_LIST_WITH_DEFAULT',
          payload: {
            list: searchResultCache,
          },
        });
        expect(dispatchMock).toHaveBeenCalledWith(
          {
            type: 'SET_LISTVIEW',
            payload: {
              listView: SplitViewListView.ON_INTERVAL,
            },
          },
        );
        expect(setAllDataListRefMock).toHaveBeenCalledWith(searchResultCache);
        expect(setRequestManagerRefMock).toHaveBeenCalledWith({
          processes: [{ pid: 'pid1', status: 'Done' }] as IProcess[],
        });
      });
    });
  });

  describe('checkSameRequestCacheRequestAndAnyResultsCache', () => {
    it.each([
      {
        when: 'cache status is InProgress',
        allListDataRef: { current: [] as ISplitViewListSingle[] },
        searchResultsCache: [{}] as ISplitViewListSingle[],
        searchRequestCache: { status: CacheStatus.REQUEST_IN_PROGRESS, result: { reqId: 'reqId1' } } as ISearchRequestCache,
        searchRequest: { reqId: 'reqId1' } as ISearchRequestResult,
        expected: true,
      },
      {
        when: 'cache status is SearchOnInterval',
        allListDataRef: { current: [] as ISplitViewListSingle[] },
        searchResultsCache: [{}] as ISplitViewListSingle[],
        searchRequestCache: { status: CacheStatus.SEARCH_ON_INTERVAL, result: { reqId: 'reqId1' } } as ISearchRequestCache,
        searchRequest: { reqId: 'reqId1' } as ISearchRequestResult,
        expected: true,
      },
      {
        when: 'cache status is Completed',
        allListDataRef: { current: [] as ISplitViewListSingle[] },
        searchResultsCache: [{}] as ISplitViewListSingle[],
        searchRequestCache: { status: CacheStatus.COMPLETED, result: { reqId: 'reqId1' } } as ISearchRequestCache,
        searchRequest: { reqId: 'reqId1' } as ISearchRequestResult,
        expected: false,
      },
      {
        when: 'requestId has been changed',
        allListDataRef: { current: [] as ISplitViewListSingle[] },
        searchResultsCache: [{}] as ISplitViewListSingle[],
        searchRequestCache: { status: CacheStatus.SEARCH_ON_INTERVAL, result: { reqId: 'reqId2' } } as ISearchRequestCache,
        searchRequest: { reqId: 'reqId1' } as ISearchRequestResult,
        expected: false,
      },
      {
        when: 'search results cache has no items',
        allListDataRef: { current: [] as ISplitViewListSingle[] },
        searchResultsCache: [] as ISplitViewListSingle[],
        searchRequestCache: { status: CacheStatus.SEARCH_ON_INTERVAL, result: { reqId: 'reqId1' } } as ISearchRequestCache,
        searchRequest: { reqId: 'reqId1' } as ISearchRequestResult,
        expected: false,
      },
      {
        when: 'allListDataRef has any items',
        allListDataRef: { current: [{}] as ISplitViewListSingle[] },
        searchResultsCache: [] as ISplitViewListSingle[],
        searchRequestCache: { status: CacheStatus.SEARCH_ON_INTERVAL, result: { reqId: 'reqId1' } } as ISearchRequestCache,
        searchRequest: { reqId: 'reqId1' } as ISearchRequestResult,
        expected: false,
      },
    ])('returns $expected when $when', ({
      allListDataRef, searchResultsCache, searchRequestCache, searchRequest, expected,
    }) => {
      const result = checkSameRequestCacheRequestAndAnyResultsCache(
        allListDataRef,
        searchResultsCache,
        searchRequestCache,
        searchRequest,
      );
      expect(result).toBe(expected);
    });
  });

  describe('search interval', () => {
    const setintervalMock = jest.fn();
    const dispatchMock = jest.fn();
    const setAllListDataRefMock = jest.fn();
    beforeEach(() => {
      setintervalMock.mockClear();
      dispatchMock.mockClear();
      setAllListDataRefMock.mockClear();
    });

    describe('setAllSearchResultCache', () => {
      it('should store full items', () => {
        const items = Array(201).map((_) => ({} as unknown as ISplitViewListSingle));
        setAllSearchResultCache(
          items,
          undefined,
          { current: [] },
          setAllListDataRefMock,
          dispatchMock,
          setintervalMock,
          performanceMetricsMock,
          setPerformanceMetricsMock,
        );
        expect(dispatchMock).toHaveBeenCalledWith({
          type: 'SET_LIST_WITH_DEFAULT',
          payload: {
            list: items,
          },
        });
      });
    });

    describe('getUniqueIds', () => {
      it('should distinct in the given chunk', () => {
        const result = getUniqueIds(['1', '1'], { current: [] });
        expect(result).toStrictEqual(['1']);
      });

      it('should filter in already retrieved items', () => {
        const result = getUniqueIds(['id-1', 'id-3'], { current: [createSplitViewListSingle({ id: 'id-1' }), createSplitViewListSingle({ id: 'id-2' })] });
        expect(result).toStrictEqual(['id-3']);
      });
    });
  });

  // AI機能のテスト
  describe('sendAISearchLog', () => {
    const reportEventMock = jest.fn();

    beforeEach(() => {
      reportEventMock.mockClear();
    });

    it('AI検索実行時のログが正しく送信される', () => {
      // AI検索ログ送信のテスト
      const inputValue = 'テスト検索クエリ';
      const dateFilter = '2024-01-01';
      const sourceFilter = 'Mail';
      const resultCount = 10;

      sendAISearchLog(
        inputValue,
        dateFilter,
        sourceFilter,
        resultCount,
        reportEventMock,
      );

      expect(reportEventMock).toHaveBeenCalledTimes(1);
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_AI_SEARCH',
        customProperties: {
          searchQuery: inputValue,
          dateFilter,
          sourceFilter,
          resultCount,
          executionDate: expect.any(String),
        },
      });
    });

    it('フィルターが空の場合にnoneが設定される', () => {
      // フィルターが空の場合のテスト
      const inputValue = 'テスト検索クエリ';
      const dateFilter = '';
      const sourceFilter = '';
      const resultCount = 5;

      sendAISearchLog(
        inputValue,
        dateFilter,
        sourceFilter,
        resultCount,
        reportEventMock,
      );

      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_AI_SEARCH',
        customProperties: {
          searchQuery: inputValue,
          dateFilter: 'none',
          sourceFilter: 'none',
          resultCount,
          executionDate: expect.any(String),
        },
      });
    });
  });

  describe('onSubmitChatSearchImpl', () => {
    const fetchResultFromAIMock = jest.fn();
    const fetchGroupIdMock = jest.fn();
    const setListModeMock = jest.fn();
    const setInputValueMock = jest.fn();
    const setDateFilterMock = jest.fn();
    const setSourceFilterMock = jest.fn();
    const setChatLastSubmittedWordsMock = jest.fn();
    const setChatListDataRefMock = jest.fn();
    const dispatchMock = jest.fn();
    const setReplyMock = jest.fn();
    const convertResultToSplitViewListSingleMock = jest.fn();
    const reportEventMock = jest.fn();

    beforeEach(() => {
      fetchResultFromAIMock.mockClear();
      fetchGroupIdMock.mockClear();
      setListModeMock.mockClear();
      setInputValueMock.mockClear();
      setDateFilterMock.mockClear();
      setSourceFilterMock.mockClear();
      setChatLastSubmittedWordsMock.mockClear();
      setChatListDataRefMock.mockClear();
      dispatchMock.mockClear();
      setReplyMock.mockClear();
      convertResultToSplitViewListSingleMock.mockClear();
      reportEventMock.mockClear();
    });

    it('fetchResultFromAIまたはfetchGroupIdが未定義の場合は何もしない', async () => {
      // fetchResultFromAIが未定義の場合
      await onSubmitChatSearchImpl(
        'テスト検索',
        'test-oid',
        undefined as any,
        fetchGroupIdMock,
        setListModeMock,
        setInputValueMock,
        '2024-01-01',
        '2024-12-31',
        setDateFilterMock,
        'Mail',
        setSourceFilterMock,
        setChatLastSubmittedWordsMock,
        setChatListDataRefMock,
        dispatchMock,
        setReplyMock,
        convertResultToSplitViewListSingleMock,
        reportEventMock,
      );

      expect(setListModeMock).not.toHaveBeenCalled();
      expect(fetchGroupIdMock).not.toHaveBeenCalled();

      // fetchGroupIdが未定義の場合
      await onSubmitChatSearchImpl(
        'テスト検索',
        'test-oid',
        fetchResultFromAIMock,
        undefined,
        setListModeMock,
        setInputValueMock,
        '2024-01-01',
        '2024-12-31',
        setDateFilterMock,
        'Mail',
        setSourceFilterMock,
        setChatLastSubmittedWordsMock,
        setChatListDataRefMock,
        dispatchMock,
        setReplyMock,
        convertResultToSplitViewListSingleMock,
        reportEventMock,
      );

      expect(setListModeMock).not.toHaveBeenCalled();
      expect(fetchResultFromAIMock).not.toHaveBeenCalled();
    });

    it('AI検索が正常に実行される', async () => {
      // モックの設定
      const mockGroupIds = ['group1', 'group2'];
      const mockAIResult = {
        reply: 'AI検索結果のレスポンス',
        list: [
          { id: '1', title: 'テスト結果1' },
          { id: '2', title: 'テスト結果2' },
        ],
      };
      const mockConvertedList = [
        createSplitViewListSingle({ id: '1', title: 'テスト結果1' }),
        createSplitViewListSingle({ id: '2', title: 'テスト結果2' }),
      ];

      fetchGroupIdMock.mockResolvedValue(mockGroupIds);
      fetchResultFromAIMock.mockResolvedValue(mockAIResult);
      convertResultToSplitViewListSingleMock.mockReturnValue(mockConvertedList);

      await onSubmitChatSearchImpl(
        'テスト検索クエリ',
        'test-oid',
        fetchResultFromAIMock,
        fetchGroupIdMock,
        setListModeMock,
        setInputValueMock,
        '2024-01-01',
        '2024-12-31',
        setDateFilterMock,
        'Mail',
        setSourceFilterMock,
        setChatLastSubmittedWordsMock,
        setChatListDataRefMock,
        dispatchMock,
        setReplyMock,
        convertResultToSplitViewListSingleMock,
        reportEventMock,
      );

      // 検索モードに切り替わることを確認
      expect(setListModeMock).toHaveBeenCalledWith(ListMode.SEARCH);
      // 入力値がクリアされることを確認
      expect(setInputValueMock).toHaveBeenCalledWith('');
      // リストデータが初期化されることを確認
      expect(setChatListDataRefMock).toHaveBeenCalledWith([]);
      // グループIDが取得されることを確認
      expect(fetchGroupIdMock).toHaveBeenCalled();
      // AI検索が実行されることを確認
      expect(fetchResultFromAIMock).toHaveBeenCalledWith(
        'テスト検索クエリ',
        'test-oid',
        mockGroupIds,
        '2024-01-01',
        '2024-12-31',
        'Mail',
      );

      // dispatchが適切な回数呼ばれることを確認
      expect(dispatchMock).toHaveBeenCalledTimes(3); // 初期化1回 + 結果表示2回
    });
    // TODO:AI検索が失敗した場合のテストケース必要

    it('AI検索結果が0件の場合の処理', async () => {
      // モックの設定
      const mockGroupIds = ['group1', 'group2'];
      const mockAIResult = {
        reply: 'AI検索結果のレスポンス',
        list: [],
      };
      const mockConvertedList: ISplitViewListSingle[] = [];

      fetchGroupIdMock.mockResolvedValue(mockGroupIds);
      fetchResultFromAIMock.mockResolvedValue(mockAIResult);
      convertResultToSplitViewListSingleMock.mockReturnValue(mockConvertedList);

      await onSubmitChatSearchImpl(
        'テスト検索クエリ',
        'test-oid',
        fetchResultFromAIMock,
        fetchGroupIdMock,
        setListModeMock,
        setInputValueMock,
        '2024-01-01',
        '2024-12-31',
        setDateFilterMock,
        'Mail',
        setSourceFilterMock,
        setChatLastSubmittedWordsMock,
        setChatListDataRefMock,
        dispatchMock,
        setReplyMock,
        convertResultToSplitViewListSingleMock,
        reportEventMock,
      );

      // 0件表示の状態がdispatchされることを確認
      // dispatchが3回呼ばれることを確認（初期化1回 + 0件表示1回）
      expect(dispatchMock).toHaveBeenCalledTimes(2);

      // 最後の呼び出しが0件表示状態であることを確認
      const lastCall = dispatchMock.mock.calls[dispatchMock.mock.calls.length - 1];
      expect(lastCall[0]).toEqual({
        type: 'SET_NO_ITEMS',
        payload: {
          listMessage: SplitViewListMessage.NO_SEARCH_RESULT,
          detailMessage: SplitViewDetailMessage.NO_ITEMS,
        },
      });

      // 0件結果のログが送信されることを確認
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_EVENT,
        name: 'NO_AI_SEARCH_RESULT',
      });
    });
  });

});
