import { GraphError } from '@microsoft/microsoft-graph-client';
import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter/dist/useEventReporter';
import mockMatchMedia from '../../../../../../mocks/match-media';
import { openAnchor, openWindow } from '../../../../../../utilities/navigate';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../../split-view-detail/SplitViewDetail';
import { ISharePointListItemResponseWithAttachment } from '../../../../../../types/ISharePointListItemResponseWithAttachment';
import { ReleaseState } from '../../../../../../types/ISharePointListsSingleResponse';
import { ISplitViewDetail } from '../../../types/ISplitViewDetail';
import {
  createListItemLink,
  fetchChatDisplayCategory,
  fetchSpoItemDetail,
  getActiveItem,
  getInnerHtmlUpdateTrigger,
  isArticleValidToShow,
  onChangeDetail,
  onClickAltLinkImpl,
  onClickItemImpl,
  onCloseImpl,
  openAttachmentFile,
  openBodyInnerLink,
  reformatAttachments,
} from './functions';
import { createSplitViewDetail, createSplitViewListSingle } from '../../../../../../utilities/test';
import { ISplitViewListSingle } from '../../../types/ISplitViewListSingle';
import { ITeamworkUserIdentity } from '../../../../../../types/IChatResponse';
import { IDetailAttachment } from '../../../types/IDetailAttachment';
import { DataSourceKind } from '../../../../../../types/DataSourceKind';
import { SearchListMode } from '../../../types/SearchListMode';

jest.mock('../../../../../../utilities/environment');

// navigateのmock
jest.mock('../../../../../../utilities/navigate');

// matchMediaのmock
const matchMediaMock = mockMatchMedia();

describe('use-split-view-detail/functions', () => {

  describe('reformatAttachments', () => {

    it('should return zero length array', () => {
      // result.AttachmentFilesがnullのパターン
      expect(
        reformatAttachments('https://example.com/sites/test', {
          AttachmentFiles: null,
        } as unknown as ISharePointListItemResponseWithAttachment),
      ).toStrictEqual([]);

      // hostNameにhttp:〜.com/sites/ が含まれないパターン
      expect(
        reformatAttachments('', {
          AttachmentFiles: [
            {
              FileName: 'file-name.pptx',
              ServerRelativeUrl: 'web/file-name.pptx',
            },
            {
              FileName: 'null',
              ServerRelativeUrl: null,
            },
          ],
        } as ISharePointListItemResponseWithAttachment),
      ).toStrictEqual([]);
    });

    it('should return reformatted attachments array', () => {
      expect(
        reformatAttachments('https://example.com/sites/test', {
          AttachmentFiles: [
            {
              FileName: 'file-name.xls',
              ServerRelativeUrl: 'web/file-name.xls',
            },
            {
              FileName: 'file-name.png',
              ServerRelativeUrl: 'web/file-name.png',
            },
            {
              FileName: 'file-name.pptx',
              ServerRelativeUrl: 'web/file-name.pptx',
            },
            {
              FileName: 'file-name.doc',
              ServerRelativeUrl: 'web/file-name.doc',
            },
            {
              FileName: 'file-name.pdf',
              ServerRelativeUrl: 'web/file-name.pdf',
            },
          ],
        } as ISharePointListItemResponseWithAttachment),
      ).toStrictEqual([
        {
          id: '-',
          extension: 'xls',
          title: 'file-name.xls',
          url: 'ms-excel:ofv|u|https://example.com/web/file-name.xls?web=1',
          spUrl: 'https://example.com/web/file-name.xls?web=1',
          icon: 'excel',
        },
        {
          id: '-',
          extension: 'png',
          title: 'file-name.png',
          url: 'https://example.com/web/file-name.png?web=1',
          spUrl: 'https://example.com/web/file-name.png?web=1',
          icon: '',
        },
        {
          id: '-',
          extension: 'pptx',
          title: 'file-name.pptx',
          url: 'ms-powerpoint:ofv|u|https://example.com/web/file-name.pptx?web=1',
          spUrl: 'https://example.com/web/file-name.pptx?web=1',
          icon: 'ppt',
        },
        {
          id: '-',
          extension: 'doc',
          title: 'file-name.doc',
          url: 'ms-word:ofv|u|https://example.com/web/file-name.doc?web=1',
          spUrl: 'https://example.com/web/file-name.doc?web=1',
          icon: 'word',
        },
        {
          id: '-',
          extension: 'pdf',
          title: 'file-name.pdf',
          url: 'https://example.com/web/file-name.pdf?web=1',
          spUrl: 'https://example.com/web/file-name.pdf?web=1',
          icon: 'pdf',
        },
      ]);
    });

  });

  describe('isArticleValidToShow', () => {
    beforeAll(() => {
      jest.useFakeTimers('legacy');
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    describe('when releaseState is not PUBLISHED', () => {
      const detail = {
        releaseState: ReleaseState.DRAFT,
        presentPeriod: '',
      } as ISharePointListItemResponseWithAttachment;
      it('should return false', () => {
        expect(isArticleValidToShow(detail)).toBe(false);
      });
    });

    describe('When PresentPeriod is in the past rather than the present.', () => {
      it('should return false', () => {
        const detail = {
          releaseState: ReleaseState.PUBLISHED,
          presentPeriod: '2021-08-24T03:00:59Z',
        } as ISharePointListItemResponseWithAttachment;
        expect(isArticleValidToShow(detail)).toBe(false);
      });
    });

    describe('When releaseState is PUBLISHED and presentPeriod does not exist', () => {
      it('should return true', () => {
        const detail = {
          releaseState: ReleaseState.PUBLISHED,
          presentPeriod: '',
        } as ISharePointListItemResponseWithAttachment;
        expect(isArticleValidToShow(detail)).toBe(true);
      });
    });

    describe('When releaseState is PUBLISHED and presentPeriod is valid', () => {
      it('should return true', () => {
        const graeterThanCurrent = {
          releaseState: ReleaseState.PUBLISHED,
          presentPeriod: '2030-08-24T03:00:59Z',
        } as ISharePointListItemResponseWithAttachment;
        expect(isArticleValidToShow(graeterThanCurrent)).toBe(true);

        const equalCurrent = {
          releaseState: ReleaseState.PUBLISHED,
          presentPeriod: new Date().toISOString(),
        } as ISharePointListItemResponseWithAttachment;
        expect(isArticleValidToShow(equalCurrent)).toBe(true);
      });
    });
  });

  describe('fetchSpoItemDetail', () => {
    const updateBookmarkMock = jest.fn();
    const dispatchMock = jest.fn();
    const reportEventMock = jest.fn();
    const fetchDetailMock = jest.fn();
    const activeItemMock = {
      kind: 'SPO',
      note: 'category1',
      title: 'Title1',
      displayDate: '2023-06-12T22:22:22.00Z',
      properties: {
        categoryKeyName: 'category1',
        listId: 'listId',
        listUrl: 'listUrl',
        siteUrl: 'sharePointUrl',
        listName: 'listName',
        editLink: 'editLink',
      },
    } as ISplitViewListSingle;
    const setApiDetailPreRequestMetricsMock = jest.fn();
    const setApiDetailRequestMetricsMock = jest.fn();
    const setApiDetailPostRequestMetricsMock = jest.fn();

    beforeEach(() => {
      updateBookmarkMock.mockClear();
      dispatchMock.mockClear();
      reportEventMock.mockClear();
      fetchDetailMock.mockClear();
      setApiDetailPreRequestMetricsMock.mockClear();
      setApiDetailRequestMetricsMock.mockClear();
      setApiDetailPostRequestMetricsMock.mockClear();
    });

    describe('when the required params are unavailable', () => {
      it('should do nothing', async () => {
        // editLinkが不正なパターン
        await fetchSpoItemDetail(
          fetchDetailMock,
          {
            ...activeItemMock,
            properties: {
              ...activeItemMock.properties,
              editLink: '',
            },
          },
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          false,
          false,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        // sharePointUrlが不正なパターン
        await fetchSpoItemDetail(
          fetchDetailMock,
          {
            ...activeItemMock,
            properties: {
              ...activeItemMock.properties,
              siteUrl: '',
            },
          },
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          false,
          false,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        // listNameが不正なパターン
        await fetchSpoItemDetail(
          fetchDetailMock,
          {
            ...activeItemMock,
            properties: {
              ...activeItemMock.properties,
              listName: '',
            },
          },
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          false,
          false,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        // fetchDetailがundefinedなパターン
        await fetchSpoItemDetail(
          undefined,
          activeItemMock,
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          false,
          false,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        expect(dispatchMock).toBeCalledTimes(0);
      });
    });

    describe('when the fetchDetail rejects', () => {
      beforeEach(() => {
        updateBookmarkMock.mockClear();
      });
      describe('when the statusCode is 403', () => {
        it('should dispatch SET_DETAIL_ERROR action with UNAVAILABLE message', async () => {
          fetchDetailMock.mockRejectedValue(new GraphError(403));

          await fetchSpoItemDetail(
            fetchDetailMock,
            activeItemMock,
            dispatchMock,
            reportEventMock as EventReporter,
            { current: false },
            false,
            false,
            updateBookmarkMock,
            setApiDetailPreRequestMetricsMock,
            setApiDetailRequestMetricsMock,
            setApiDetailPostRequestMetricsMock,
          );

          expect(reportEventMock).toBeCalledTimes(1);
          expect(reportEventMock).toBeCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'API_REQUEST_FAIL',
            error: new GraphError(403),
          });

          expect(dispatchMock).toBeCalledTimes(1);
          expect(dispatchMock).toBeCalledWith({
            type: 'SET_DETAIL_ERROR',
            payload: SplitViewDetailMessage.UNAVAILABLE,
          });
        });
      });
      describe('when the statusCode is 404', () => {
        it('should dispatch SET_DETAIL_ERROR action with UNAVAILABLE message', async () => {
          fetchDetailMock.mockRejectedValue(new GraphError(404));

          await fetchSpoItemDetail(
            fetchDetailMock,
            activeItemMock,
            dispatchMock,
            reportEventMock as EventReporter,
            { current: false },
            false,
            false,
            updateBookmarkMock,
            setApiDetailPreRequestMetricsMock,
            setApiDetailRequestMetricsMock,
            setApiDetailPostRequestMetricsMock,
          );

          expect(reportEventMock).toBeCalledTimes(1);
          expect(reportEventMock).toBeCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'API_REQUEST_FAIL',
            error: new GraphError(404),
          });

          expect(dispatchMock).toBeCalledTimes(1);
          expect(dispatchMock).toBeCalledWith({
            type: 'SET_DETAIL_ERROR',
            payload: SplitViewDetailMessage.UNAVAILABLE,
          });
        });
      });

      describe('when the statusCode is not 403/404', () => {
        it('should dispatch SET_DETAIL_ERROR action with API_REQUEST_FAIL message', async () => {
          fetchDetailMock.mockRejectedValue(new GraphError(401));

          await fetchSpoItemDetail(
            fetchDetailMock,
            activeItemMock,
            dispatchMock,
            reportEventMock as EventReporter,
            { current: false },
            false,
            false,
            updateBookmarkMock,
            setApiDetailPreRequestMetricsMock,
            setApiDetailRequestMetricsMock,
            setApiDetailPostRequestMetricsMock,
          );

          expect(reportEventMock).toBeCalledTimes(1);
          expect(reportEventMock).toBeCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'API_REQUEST_FAIL',
            error: new GraphError(401),
          });

          expect(dispatchMock).toBeCalledTimes(1);
          expect(dispatchMock).toBeCalledWith({
            type: 'SET_DETAIL_ERROR',
            payload: SplitViewDetailMessage.API_REQUEST_FAIL,
          });
        });
      });

      describe('when the isUnmounted.current is true', () => {
        it('should cancel dispatch', async () => {
          fetchDetailMock.mockRejectedValue(new GraphError(403));
          await fetchSpoItemDetail(
            fetchDetailMock,
            activeItemMock,
            dispatchMock,
            reportEventMock as EventReporter,
            // isUnmounted.currentがtrueの場合はdispatchしない
            { current: true },
            false,
            true,
            updateBookmarkMock,
            setApiDetailPreRequestMetricsMock,
            setApiDetailRequestMetricsMock,
            setApiDetailPostRequestMetricsMock,
          );

          expect(reportEventMock).toBeCalledTimes(1);
          expect(reportEventMock).toBeCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'API_REQUEST_FAIL',
            error: new GraphError(403),
          });

          expect(dispatchMock).toBeCalledTimes(0);
        });
      });
    });

    describe('when the fetchDetail resolves', () => {
      const reposDates = {
        reposCreatedDate: new Date('2023-06-19T01:23:45.67Z').toISOString(),
        reposUpdatedDate: new Date('2023-06-19T12:34:56.78Z').toISOString(),
      };

      it('should dispatch SET_DETAIL action', async () => {
        fetchDetailMock.mockResolvedValue({
          releaseState: ReleaseState.PUBLISHED,
          category1: 'fetchedCategory1',
        });

        await fetchSpoItemDetail(
          fetchDetailMock,
          activeItemMock,
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          false,
          true,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_DETAIL',
          payload: {
            detail: {
              id: '',
              kind: 'SPO',
              title: '',
              note: 'category1',
              displayDate: '',
              body: '',
              expiredDate: '',
              itemId: null,
              properties: {
                spoAttachments: [],
                hasAttachments: false,
                categoryKeyName: 'category1',
                listId: 'listId',
                listUrl: 'listUrl',
                siteUrl: 'sharePointUrl',
                listName: 'listName',
                editLink: '',
                createdDate: '',
                updatedDate: '',
              },
            },
          },
        });
        expect(updateBookmarkMock).not.toHaveBeenCalled();
      });

      it('should dispatch and update bookmark item action', async () => {
        fetchDetailMock.mockResolvedValue({
          Title: 'Title-modified',
          Modified: '2023-06-15T23:23:23.00Z',
          category: 'category2',
          releaseState: ReleaseState.PUBLISHED,
          category1: 'fetchedCategory1',
        });

        await fetchSpoItemDetail(
          fetchDetailMock,
          { ...activeItemMock, ...reposDates },
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          true,
          true,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        expect(dispatchMock).toBeCalledTimes(2);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_DETAIL',
          payload: {
            detail: {
              id: '',
              kind: 'SPO',
              title: 'Title-modified',
              note: 'category2',
              displayDate: '2023-06-15T23:23:23.00Z',
              body: '',
              expiredDate: '',
              itemId: null,
              reposCreatedDate: '2023-06-19T01:23:45.670Z',
              reposUpdatedDate: '2023-06-19T12:34:56.780Z',
              properties: {
                spoAttachments: [],
                hasAttachments: false,
                categoryKeyName: 'category1',
                listId: 'listId',
                listUrl: 'listUrl',
                siteUrl: 'sharePointUrl',
                listName: 'listName',
                editLink: '',
                createdDate: '',
                updatedDate: '2023-06-15T23:23:23.000Z',
              },
            },
          },
        });
        expect(dispatchMock).toBeCalledWith({
          type: 'UPDATE_ITEM',
          payload: {
            id: '',
            item: {
              id: '',
              kind: 'SPO',
              title: 'Title-modified',
              note: 'category2',
              displayDate: '2023-06-15T23:23:23.00Z',
              body: '',
              expiredDate: '',
              itemId: null,
              reposCreatedDate: '2023-06-19T01:23:45.670Z',
              reposUpdatedDate: '2023-06-19T12:34:56.780Z',
              properties: {
                hasAttachments: false,
                categoryKeyName: 'category1',
                listId: 'listId',
                listUrl: 'listUrl',
                siteUrl: 'sharePointUrl',
                listName: 'listName',
                editLink: '',
                createdDate: '',
                updatedDate: '2023-06-15T23:23:23.000Z',
              },
            },
          },
        });
        expect(updateBookmarkMock).toHaveBeenCalledWith({
          id: '',
          kind: 'SPO',
          title: 'Title-modified',
          note: 'category2',
          displayDate: '2023-06-15T23:23:23.00Z',
          reposCreatedDate: '2023-06-19T01:23:45.670Z',
          reposUpdatedDate: '2023-06-19T12:34:56.780Z',
          properties: {
            hasAttachments: false,
            categoryKeyName: 'category1',
            listId: 'listId',
            listUrl: 'listUrl',
            siteUrl: 'sharePointUrl',
            listName: 'listName',
            editLink: '',
            createdDate: '',
            updatedDate: '2023-06-15T23:23:23.000Z',
          },
        });
      });

      it('should not update bookmark item by same title', async () => {
        fetchDetailMock.mockResolvedValue({
          category: 'category1',
          Title: 'Title1',
          Modified: '2023-06-12T22:22:22.00Z',
          releaseState: ReleaseState.PUBLISHED,
          category1: 'fetchedCategory1',
        });

        await fetchSpoItemDetail(
          fetchDetailMock,
          { ...activeItemMock, ...reposDates },
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          true,
          true,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_DETAIL',
          payload: {
            detail: {
              id: '',
              kind: 'SPO',
              title: 'Title1',
              note: 'category1',
              displayDate: '2023-06-12T22:22:22.00Z',
              body: '',
              expiredDate: '',
              itemId: null,
              reposCreatedDate: '2023-06-19T01:23:45.670Z',
              reposUpdatedDate: '2023-06-19T12:34:56.780Z',
              properties: {
                spoAttachments: [],
                hasAttachments: false,
                categoryKeyName: 'category1',
                listId: 'listId',
                listUrl: 'listUrl',
                siteUrl: 'sharePointUrl',
                listName: 'listName',
                editLink: '',
                createdDate: '',
                updatedDate: '2023-06-12T22:22:22.000Z',
              },
            },
          },
        });

        expect(updateBookmarkMock).not.toHaveBeenCalled();
      });

      it('should not enqueue bookmark update', async () => {
        fetchDetailMock.mockResolvedValue({
          releaseState: ReleaseState.PUBLISHED,
          category1: 'fetchedCategory1',
        });

        await fetchSpoItemDetail(
          fetchDetailMock,
          { ...activeItemMock, ...reposDates },
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          true,
          false,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );
        expect(updateBookmarkMock).not.toHaveBeenCalled();
      });

      it('should not enqueue bookmark update 2', async () => {
        fetchDetailMock.mockResolvedValue({
          releaseState: ReleaseState.PUBLISHED,
          category1: 'fetchedCategory1',
        });

        await fetchSpoItemDetail(
          fetchDetailMock,
          activeItemMock,
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          true,
          false,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );
        expect(updateBookmarkMock).not.toHaveBeenCalled();
      });

      it('should dispatch SET_DETAIL_ERROR action', async () => {
        fetchDetailMock.mockResolvedValue({
          releaseState: ReleaseState.DRAFT,
        });

        await fetchSpoItemDetail(
          fetchDetailMock,
          activeItemMock,
          dispatchMock,
          reportEventMock as EventReporter,
          { current: false },
          false,
          false,
          updateBookmarkMock,
          setApiDetailPreRequestMetricsMock,
          setApiDetailRequestMetricsMock,
          setApiDetailPostRequestMetricsMock,
        );

        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_DETAIL_ERROR',
          payload: SplitViewDetailMessage.UNAVAILABLE,
        });
      });

      describe('when the isUnmounted.current is true', () => {
        it('should cancel dispatch', async () => {
          fetchDetailMock.mockResolvedValue({ detail: null, attachments: null });
          await fetchSpoItemDetail(
            fetchDetailMock,
            activeItemMock,
            dispatchMock,
            reportEventMock as EventReporter,
            // isUnmounted.currentがtrueの場合はdispatchしない
            { current: true },
            false,
            false,
            updateBookmarkMock,
            setApiDetailPreRequestMetricsMock,
            setApiDetailRequestMetricsMock,
            setApiDetailPostRequestMetricsMock,
          );
          expect(dispatchMock).toBeCalledTimes(0);
        });

        it('should not update item', async () => {
          fetchDetailMock.mockResolvedValue({ detail: null, attachments: null });
          await fetchSpoItemDetail(
            fetchDetailMock,
            activeItemMock,
            dispatchMock,
            reportEventMock as EventReporter,
            // isUnmounted.currentがtrueの場合はdispatchしない
            { current: true },
            true,
            true,
            updateBookmarkMock,
            setApiDetailPreRequestMetricsMock,
            setApiDetailRequestMetricsMock,
            setApiDetailPostRequestMetricsMock,
          );
          expect(dispatchMock).toBeCalledTimes(0);
        });
      });
    });
  });

  describe('openAttachmentFile', () => {
    const spoActiveItemMock = {
      id: 'id1',
      kind: 'SPO',
      note: 'category1',
      title: 'Title1',
      displayDate: '2023-06-12T22:22:22.00Z',
      properties: {
        categoryKeyName: 'category1',
        listId: 'listId',
        listUrl: 'listUrl',
        siteUrl: 'sharePointUrl',
        listName: 'listName',
        editLink: 'editLink',
      },
    } as ISplitViewListSingle;
    const openWindowMock = openWindow as jest.Mock;
    const reportEventMock = jest.fn();

    beforeEach(() => {
      openWindowMock.mockClear();
      reportEventMock.mockClear();
    });

    describe('when the attachment is available', () => {
      const attachment: IDetailAttachment = {
        id: 'attachmentId1',
        extension: 'xlsx',
        title: 'abcd',
        url: '1234',
        spUrl: '5678',
      };

      describe('when attachment is SPO', () => {
        describe('when isPC = true', () => {
          const isPC = true;

          it('should call openWindow with attachment.url', () => {
            openAttachmentFile(attachment, spoActiveItemMock, reportEventMock, isPC);
            expect(openWindowMock).toBeCalledWith(
              '1234',
              [reportEventMock,
                {
                  type: EventReportType.USER_EVENT,
                  name: 'CLICK_ATTACHMENT',
                  customProperties: expect.objectContaining({
                    detail: {
                      id: 'id1',
                      kind: DataSourceKind.SPO,
                      attachmentId: 'attachmentId1',
                      extension: 'xlsx',
                      properties: {
                        listUrl: 'listUrl',
                        siteUrl: 'sharePointUrl',
                        listId: 'listId',
                      },
                    },
                  }),
                },
              ],
            );
          });
        });

        describe('when isPC = false', () => {
          const isPC = false;

          it('should call openWindow with attachment.spUrl', () => {
            openAttachmentFile(attachment, spoActiveItemMock, reportEventMock, isPC);
            expect(openWindowMock).toBeCalledWith(
              '5678',
              [reportEventMock,
                {
                  type: EventReportType.USER_EVENT,
                  name: 'CLICK_ATTACHMENT',
                  customProperties: expect.objectContaining({
                    detail: {
                      id: 'id1',
                      kind: DataSourceKind.SPO,
                      attachmentId: 'attachmentId1',
                      extension: 'xlsx',
                      properties: {
                        listUrl: 'listUrl',
                        siteUrl: 'sharePointUrl',
                        listId: 'listId',
                      },
                    },
                  }),
                },
              ],
            );
          });
        });

      });

      describe('when attachment is Mail', () => {
        const mailActiveItemMock = {
          id: 'id2',
          kind: 'Mail',
          note: 'note2',
          title: 'Title2',
          displayDate: '2023-06-12T22:22:22.00Z',
          properties: {
            categoryKeyName: 'category1',
          },
        } as ISplitViewListSingle;
        it('should call openWindow with attachment.url', () => {
          // isPCがfalseであってもattachment.urlが呼び出される
          openAttachmentFile(attachment, mailActiveItemMock, reportEventMock, false);
          expect(openWindowMock).toBeCalledWith(
            '1234',
            [reportEventMock,
              {
                type: EventReportType.USER_EVENT,
                name: 'CLICK_ATTACHMENT',
                customProperties: expect.objectContaining({
                  detail: {
                    id: 'id2',
                    kind: DataSourceKind.Mail,
                    attachmentId: 'attachmentId1',
                    extension: 'xlsx',
                    properties: {},
                  },
                }),
              },
            ],
          );
        });
      });

      describe('when attachment is Chat', () => {
        const chatActiveItemMock = {
          id: 'id3',
          title: 'Title3',
          kind: 'Chat',
          displayDate: '2023-05-25T19:00:00.000Z',
          note: 'note3',
          properties: {
            chatAttachments: [
              {
                id: 'attachment1',
                name: 'attachment.jpg',
                contentType: 'messageReference',
                contentUrl: 'https://examle.com/image.jpg',
                content: 'test content',
              },
            ],
            from: {
              id: 'botId',
              displayName: 'botDisplayName',
              applicationIdentityType: 'bot',
            },
            updatedDate: '2023-05-26T06:05:00+09:00',
            lastModifiedDateTime: '2023-05-26T06:05:00+09:00',
            hasAttachments: true,
            chatId: 'chatId',
            teamId: undefined,
            messageType: 'chat',
          },
        } as ISplitViewListSingle;
        it('should call openWindow with attachment.url', () => {
          // isPCがfalseであってもattachment.urlが呼び出される
          openAttachmentFile(attachment, chatActiveItemMock, reportEventMock, false);
          expect(openWindowMock).toBeCalledWith(
            '1234',
            [reportEventMock,
              {
                type: EventReportType.USER_EVENT,
                name: 'CLICK_ATTACHMENT',
                customProperties: expect.objectContaining({
                  detail: {
                    id: 'id3',
                    kind: DataSourceKind.Chat,
                    attachmentId: 'attachmentId1',
                    extension: 'xlsx',
                    properties: {
                      chatId: 'chatId',
                      messageId: 'id3',
                      type: 'chat',
                    },
                  },
                }),
              },
            ],
          );
        });
      });

    });

    describe('when the attachment is undefined', () => {
      const attachment = undefined;

      describe('when isPC = true', () => {
        const isPC = true;

        it('should not call openWindow', () => {
          openAttachmentFile(attachment, spoActiveItemMock, reportEventMock, isPC);
          expect(openWindowMock).not.toBeCalled();
        });
      });

      describe('when isPC = false', () => {
        const isPC = false;

        it('should not call openWindow', () => {
          openAttachmentFile(attachment, spoActiveItemMock, reportEventMock, isPC);
          expect(openWindowMock).not.toBeCalled();
        });
      });
    });
  });

  describe('openBodyInnerLink', () => {
    const openWindowMock = openWindow as jest.Mock;
    const preventDefaultMock = jest.fn();
    const openAnchorMock = openAnchor as jest.Mock;
    const reportEventMock = jest.fn();

    beforeEach(() => {
      openWindowMock.mockClear();
      preventDefaultMock.mockClear();
      openAnchorMock.mockClear();
      reportEventMock.mockClear();
    });

    describe('when the clicked element has a url', () => {
      it('should call preventDefault and openWindow', () => {
        openBodyInnerLink(
          { preventDefault: preventDefaultMock } as unknown as MouseEvent,
          { href: 'abcd', getAttribute: () => 'abcd' } as unknown as HTMLLinkElement,
          reportEventMock,
        );

        expect(preventDefaultMock).toBeCalledTimes(1);
        expect(openWindowMock).toBeCalledTimes(1);
        expect(openWindowMock).toBeCalledWith(
          'abcd',
          [reportEventMock, { type: EventReportType.USER_EVENT, name: 'CLICK_DETAIL_BODY_LINK' }],
        );
        expect(openAnchorMock).toBeCalledTimes(0);
      });
    });

    describe('when the clicked has an in-page anchor', () => {
      it('should call preventDefault openAnchor', () => {
        openBodyInnerLink(
          { preventDefault: preventDefaultMock } as unknown as MouseEvent,
          { href: '#abcd', getAttribute: () => '#abcd' } as unknown as HTMLLinkElement,
          reportEventMock,
        );

        expect(preventDefaultMock).toBeCalledTimes(1);
        expect(openWindowMock).toBeCalledTimes(0);
        expect(openAnchorMock).toBeCalledTimes(1);
      });
    });

    describe('when the $link.href is unavailable', () => {
      it('should call preventDefault', () => {
        openBodyInnerLink(
          { preventDefault: preventDefaultMock } as unknown as MouseEvent,
          { href: null, getAttribute: () => null } as unknown as HTMLLinkElement,
          reportEventMock,
        );

        expect(preventDefaultMock).toBeCalledTimes(1);
        expect(openWindowMock).toBeCalledTimes(0);
        expect(openAnchorMock).toBeCalledTimes(0);
      });
    });
  });

  describe('getInnerHtmlUpdateTrigger', () => {
    it('should return true', () => {
      expect(getInnerHtmlUpdateTrigger('a', SplitViewDetailView.DEFAULT, createSplitViewDetail({ body: 'a' }))).toBe(true);
    });

    it('should return false', () => {
      expect(getInnerHtmlUpdateTrigger('', SplitViewDetailView.DEFAULT, createSplitViewDetail({ body: 'a' }))).toBe(false);
      expect(getInnerHtmlUpdateTrigger('a', SplitViewDetailView.LOADING, createSplitViewDetail({ body: 'a' }))).toBe(false);
      expect(getInnerHtmlUpdateTrigger('a', SplitViewDetailView.DEFAULT, createSplitViewDetail({ body: undefined }))).toBe(false);
    });
  });

  describe('getActiveItem', () => {
    it('should return undefined', () => {
      expect(getActiveItem('', [createSplitViewListSingle({ id: 'aaa' }), createSplitViewListSingle({ id: 'bbb' })])).toBeUndefined();
    });

    it('should return list', () => {
      expect(getActiveItem('1', [createSplitViewListSingle({ id: '1' })])).toStrictEqual(createSplitViewListSingle({ id: '1' }));
    });
  });

  describe('onChangeDetail', () => {
    const reportEvent = jest.fn();
    const onChanged = jest.fn();
    const updateBookmark = jest.fn();
    const isFirstItemSelected = false;

    beforeEach(() => {
      reportEvent.mockClear();
      onChanged.mockClear();
      updateBookmark.mockClear();
    });

    describe('when detail is undefined', () => {
      const detail = undefined;

      it('should not call anything', () => {
        const detailView = SplitViewDetailView.DEFAULT;
        const reportName = 'abc';

        onChangeDetail(
          detail,
          detailView,
          reportEvent,
          reportName,
          isFirstItemSelected,
          SearchListMode.DEFAULT,
          updateBookmark,
          onChanged,
        );

        expect(reportEvent).not.toBeCalled();
        expect(updateBookmark).not.toBeCalled();
        expect(onChanged).not.toBeCalled();
      });
    });

    describe('when detail is available', () => {
      const detail: ISplitViewDetail = createSplitViewDetail({});

      describe('when detailView is not DEFAULT', () => {
        const detailView = SplitViewDetailView.LOADING;

        it('should not call anything', () => {
          const reportName = 'abc';

          onChangeDetail(
            detail,
            detailView,
            reportEvent,
            reportName,
            false,
            SearchListMode.DEFAULT,
            updateBookmark,
            onChanged,
          );

          expect(reportEvent).not.toBeCalled();
          expect(updateBookmark).not.toBeCalled();
          expect(onChanged).not.toBeCalled();
        });
      });

      describe('when detailView is DEFAULT', () => {
        const detailView = SplitViewDetailView.DEFAULT;

        describe('when reportName is "ABC"', () => {
          const reportName = 'ABC';

          it('should call reportEvent', () => {
            onChangeDetail(
              detail,
              detailView,
              reportEvent,
              reportName,
              isFirstItemSelected,
              SearchListMode.DEFAULT,
              updateBookmark,
              onChanged,
            );

            expect(reportEvent).toBeCalledTimes(1);
            expect(reportEvent).toBeCalledWith({
              type: EventReportType.USER_EVENT,
              name: 'SHOW_DETAIL_ABC',
              customProperties: expect.objectContaining({
                isFirstItemSelected,
                detail: expect.objectContaining({
                  id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
                  kind: 'SPO',
                  properties: {
                    listUrl: '',
                    siteUrl: '',
                    listId: '',
                  },
                }),
              }),
            });
          });

          it('should call onChanged', () => {
            onChangeDetail(
              detail,
              detailView,
              reportEvent,
              reportName,
              isFirstItemSelected,
              SearchListMode.DEFAULT,
              updateBookmark,
              onChanged,
            );
            expect(onChanged).toBeCalledTimes(1);
          });

          it('should call updateBookmark', () => {
            onChangeDetail(
              detail,
              detailView,
              reportEvent,
              reportName,
              isFirstItemSelected,
              SearchListMode.DEFAULT,
              updateBookmark,
              onChanged,
            );
            expect(updateBookmark).toBeCalledTimes(1);
            expect(updateBookmark).toBeCalledWith(createSplitViewDetail({}));
          });
        });
      });
    });
  });

  describe('onClickItemImpl', () => {
    const dispatchMock = jest.fn();
    const reportEventMock = jest.fn();

    beforeEach(() => {
      dispatchMock.mockClear();
      reportEventMock.mockClear();
    });

    describe('when the item has id and title', () => {
      const mockData = createSplitViewListSingle({
        id: 'abcd',
        title: '1234',
        kind: 'Mail',
        properties: {
          listUrl: 'aaa',
          siteUrl: 'bbb',
        },
      });

      it('should dispatch SET_ACTIVE with activeId and title', () => {
        onClickItemImpl(mockData, dispatchMock);
        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_ACTIVE',
          payload: {
            activeId: 'abcd',
            title: '1234',
            kind: 'Mail',
          },
        });
      });
    });

    describe('when the item does not have id and title', () => {
      const mockData = createSplitViewListSingle({
        id: undefined,
        title: undefined,
        properties: {
          listUrl: undefined,
          siteUrl: undefined,
        },
      });

      it('should dispatch SET_ACTIVE with blanks', () => {
        onClickItemImpl(mockData, dispatchMock);
        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'SET_ACTIVE',
          payload: {
            activeId: '',
            title: '',
            kind: 'SPO',
          },
        });
      });
    });
  });

  describe('onCloseImpl', () => {
    const dispatchMock = jest.fn();

    beforeEach(() => {
      dispatchMock.mockClear();
    });

    describe('when the isSP() returns false', () => {
      beforeEach(() => {
        matchMediaMock.mockClear().mockReturnValue({ matches: false });
      });

      it('should not dispatch UNSELECT', () => {
        onCloseImpl(dispatchMock);
        expect(dispatchMock).toBeCalledTimes(0);
      });
    });

    describe('when the iSP() returns true', () => {
      beforeEach(() => {
        matchMediaMock.mockClear().mockReturnValue({ matches: true });
      });

      it('should dispatch UNSELECT', () => {
        onCloseImpl(dispatchMock);
        expect(dispatchMock).toBeCalledTimes(1);
        expect(dispatchMock).toBeCalledWith({
          type: 'UNSELECT',
          payload: undefined,
        });
      });
    });
  });

  describe('createListItemLink', () => {
    describe('when itemId or listUrl is unavailable', () => {
      it('should return blank', () => {
        expect(createListItemLink(null, 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx')).toBe('');
        expect(createListItemLink(1, undefined)).toBe('');
      });
    });

    describe('When the required parameters are available', () => {
      it('should return listItemUrl', () => {
        expect(createListItemLink(123, 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx'))
          .toStrictEqual('https://projectgeranium.sharepoint.com/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx?ID=123');
      });
    });
  });

  describe('onClickAltLinkImpl', () => {
    const openWindowMock = openWindow as jest.Mock;
    const reportEventMock = jest.fn();
    const executeDeepLinkMock = jest.fn();
    const listUrl = 'aaa';

    beforeEach(() => {
      reportEventMock.mockClear();
      openWindowMock.mockClear();
      executeDeepLinkMock.mockClear();
    });

    describe('when the detail is undefined or its itemId is unavailable', () => {
      it('should not do anything', () => {
        onClickAltLinkImpl(undefined, reportEventMock, listUrl, executeDeepLinkMock);
        onClickAltLinkImpl(
          { itemId: null } as unknown as ISplitViewDetail,
          reportEventMock,
          listUrl,
          executeDeepLinkMock,
        );
        onClickAltLinkImpl(
          { itemId: undefined } as unknown as ISplitViewDetail,
          reportEventMock,
          listUrl,
          executeDeepLinkMock,
        );
        expect(openWindowMock).not.toBeCalled();
      });
    });

    describe('when the detail has itemId', () => {
      describe('when the spoListUrl is not found', () => {
        it('should not do anything', () => {
          onClickAltLinkImpl(
            { itemId: 1, kind: 'SPO' } as unknown as ISplitViewDetail,
            reportEventMock,
            '',
            executeDeepLinkMock,
          );
          expect(openWindowMock).not.toBeCalled();
        });
      });

      describe('when the spoListUrl is available', () => {
        it('should call openWindow', () => {
          onClickAltLinkImpl(
            { itemId: 1, kind: 'SPO' } as unknown as ISplitViewDetail,
            reportEventMock,
            listUrl,
            executeDeepLinkMock,
          );
          expect(openWindowMock).toBeCalledTimes(1);
          expect(openWindowMock).toBeCalledWith(
            'aaa?ID=1',
            [reportEventMock, { type: EventReportType.USER_EVENT, name: 'CLICK_SPO_ALT_LINK' }],
          );
        });
      });

      describe('when the MailLink is available', () => {
        it('should call openWindow', () => {
          onClickAltLinkImpl(
            {
              itemId: 1, id: '1', kind: 'Mail', properties: { webLink: 'https://example.com' },
            } as unknown as ISplitViewDetail,
            reportEventMock,
            listUrl,
            executeDeepLinkMock,
          );
          expect(openWindowMock).toBeCalledTimes(1);
          // TODO:下記判定はgetMailLinkで行われているスマートフォン判定の挙動が正常に動かないためコメントアウトする。
          /* 該当関数の試験は別途リファクタリングで行う。
           expect(openWindowMock).toBeCalledWith(
            'https://example.com',
            [reportEventMock, { type: EventReportType.USER_EVENT, name: 'CLICK_MAIL_ALT_LINK' }],
          );
          */
        });
      });

      describe('when the ChatLink is available', () => {
        it('should call openWindow in the case of "messageType type is teams"', () => {
          onClickAltLinkImpl(
            {
              id: 'test3',
              kind: 'Chat',
              title: 'string',
              reposCreatedDate: '20230401',
              reposUpdatedDate: '20230401',
              note: 'string',
              displayDate: '20001202',
              properties: {
                teamId: 'teamId123',
                chatId: 'chatId123',
                messageType: 'team',
                replyToId: 'replyIds123',
              },
              body: 'string',
              // TODO: SPO固有の型をPropertiesに移動する？
              itemId: 123,
              expiredDate: '20231202',
            } as unknown as ISplitViewDetail,
            reportEventMock,
            listUrl,
            executeDeepLinkMock,
          );
          expect(executeDeepLinkMock).toBeCalledTimes(1);
          expect(executeDeepLinkMock).toBeCalledWith(
            'https://teams.microsoft.com/l/message/chatId123/test3?parentMessageId=replyIds123&messageId=test3&allowXTenantAccess=false',
            { type: EventReportType.USER_EVENT, name: 'CLICK_CHAT_ALT_LINK' },
          );
        });

        it('should call openWindow in the case of "messageType type is chat"', () => {
          onClickAltLinkImpl(
            {
              id: 'test56',
              kind: 'Chat',
              title: 'string',
              reposCreatedDate: '20230401',
              reposUpdatedDate: '20230401',
              note: 'string',
              displayDate: '20001202',
              properties: {
                teamId: 'teamId123',
                chatId: 'chatId456',
                messageType: 'chat',
                replyToId: 'replyIds123',
              },
              body: 'string',
              // TODO: SPO固有の型をPropertiesに移動する？
              itemId: 123,
              expiredDate: '20231202',
            } as unknown as ISplitViewDetail,
            reportEventMock,
            listUrl,
            executeDeepLinkMock,
          );
          expect(executeDeepLinkMock).toBeCalledTimes(1);
          expect(executeDeepLinkMock).toBeCalledWith(
            'https://teams.microsoft.com/l/message/chatId456/test56?context={"contextType":"chat"}',
            { type: EventReportType.USER_EVENT, name: 'CLICK_CHAT_ALT_LINK' },
          );
        });
      });
    });
  });

  describe('fetchChatDisplayCategory', () => {
    const fetchChatMemberMock = jest.fn();
    const fetchChatTeamNameMock = jest.fn();
    const fetchChatChannelNameMock = jest.fn();
    beforeEach(() => {
      fetchChatMemberMock.mockClear();
      fetchChatTeamNameMock.mockClear();
      fetchChatChannelNameMock.mockClear();
    });

    it('should display topic name', async () => {
      fetchChatMemberMock.mockReturnValue(Promise.resolve({
        topic: 'test1',
        members: [
          { displayName: 'userName1', userId: 'user1' },
          { displayName: 'userName2', userId: 'user2' },
        ],
      }));

      const result = await fetchChatDisplayCategory(
        createSplitViewListSingle({
          kind: 'Chat',
          properties: {
            chatId: '1',
            messageType: 'chat',
            from: {
              id: 'user1',
            } as ITeamworkUserIdentity,
          },
        }),
        fetchChatMemberMock,
        fetchChatTeamNameMock,
        fetchChatChannelNameMock,
      );

      expect(result).toBe('test1');
    });

    it('should display group members', async () => {
      fetchChatMemberMock.mockReturnValue(Promise.resolve({
        topic: null,
        members: [
          { displayName: 'userName1', userId: 'user1' },
          { displayName: 'userName2', userId: 'user2' },
        ],
      }));

      const result = await fetchChatDisplayCategory(
        createSplitViewListSingle({
          kind: 'Chat',
          properties: {
            chatId: '1@thread.v2',
            messageType: 'chat',
            from: {
              id: 'user1',
            } as ITeamworkUserIdentity,
          },
        }),
        fetchChatMemberMock,
        fetchChatTeamNameMock,
        fetchChatChannelNameMock,
      );

      expect(result).toBe('userName1, userName2');
    });

    it('should display only recipient', async () => {
      fetchChatMemberMock.mockReturnValue(Promise.resolve({
        topic: null,
        members: [
          { displayName: 'userName1', userId: 'user1' },
          { displayName: 'userName2', userId: 'user2' },
        ],
      }));

      const result = await fetchChatDisplayCategory(
        createSplitViewListSingle({
          kind: 'Chat',
          properties: {
            chatId: '<EMAIL>',
            messageType: 'chat',
            from: {
              id: 'user1',
            } as ITeamworkUserIdentity,
          },
        }),
        fetchChatMemberMock,
        fetchChatTeamNameMock,
        fetchChatChannelNameMock,
      );

      expect(result).toBe('userName2');
    });

    it('should display chat message title', async () => {
      fetchChatTeamNameMock.mockReturnValue(Promise.resolve({
        displayName: 'TeamName',
      }));
      fetchChatChannelNameMock.mockReturnValueOnce(Promise.resolve({
        displayName: 'ChannelName',
      }));
      fetchChatChannelNameMock.mockReturnValueOnce(Promise.resolve({
        displayName: 'General2',
      }));

      const result = await fetchChatDisplayCategory(
        createSplitViewListSingle({
          kind: 'Chat',
          properties: {
            teamId: '1',
            chatId: '1',
            messageType: 'team',
          },
        }),
        fetchChatMemberMock,
        fetchChatChannelNameMock,
        fetchChatTeamNameMock,
      );
      expect(result).toBe('TeamName - ChannelName');

      const result2 = await fetchChatDisplayCategory(
        createSplitViewListSingle({
          kind: 'Chat',
          properties: {
            teamId: '1',
            chatId: '1',
            messageType: 'team',
          },
        }),
        fetchChatMemberMock,
        fetchChatChannelNameMock,
        fetchChatTeamNameMock,
      );
      expect(result2).toBe('TeamName - General2');
    });

    it('should display chat message title with "一般"', async () => {
      fetchChatTeamNameMock.mockReturnValue(Promise.resolve({
        displayName: 'TeamName',
      }));
      fetchChatChannelNameMock.mockReturnValueOnce(Promise.resolve({
        displayName: 'General',
      }));
      fetchChatChannelNameMock.mockReturnValueOnce(Promise.resolve({
        displayName: 'general',
      }));

      const result = await fetchChatDisplayCategory(
        createSplitViewListSingle({
          kind: 'Chat',
          properties: {
            teamId: '1',
            chatId: '1',
            messageType: 'team',
          },
        }),
        fetchChatMemberMock,
        fetchChatChannelNameMock,
        fetchChatTeamNameMock,
      );
      expect(result).toBe('TeamName - 一般');

      const result2 = await fetchChatDisplayCategory(
        createSplitViewListSingle({
          kind: 'Chat',
          properties: {
            teamId: '1',
            chatId: '1',
            messageType: 'team',
          },
        }),
        fetchChatMemberMock,
        fetchChatChannelNameMock,
        fetchChatTeamNameMock,
      );

      expect(result2).toBe('TeamName - 一般');
    });
  });

});
