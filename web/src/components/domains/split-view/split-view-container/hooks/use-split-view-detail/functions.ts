import { EventReporter, EventReportType, IEventReporterParam } from '@avanade-teams/app-insights-reporter';
import { GraphError } from '@microsoft/microsoft-graph-client';
import * as React from 'react';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import {
  FetchSPODetail,
} from '../../../../../../hooks/accessors/useSharePointApiAccessor';
import { isSP } from '../../../../../../utilities/mediaQuery';
import {
  convertChatDetailToChatSingle,
  convertChatItemToSplitViewDetail,
  convertMailDetailToMailSingle,
  convertMailListItemToSplitViewDetail,
  convertSPListItemToSplitViewDetail,
  convertSplitViewItemToBookmarkItem,
  convertSPODetailToSPOSingle,
  isSpoProperties,
} from '../../../../../../utilities/transform';
import { ISplitViewDetail } from '../../../types/ISplitViewDetail';
import {
  SplitViewDetailMessage,
  SplitViewDetailView, SplitViewDetailViewType,
} from '../../../split-view-detail/SplitViewDetail';
import { ISharePointListItemResponseWithAttachment } from '../../../../../../types/ISharePointListItemResponseWithAttachment';
import { ReleaseState } from '../../../../../../types/ISharePointListsSingleResponse';
import { ISplitViewListSingle } from '../../../types/ISplitViewListSingle';
import { SetDetailActionPayload, SplitViewDispatch } from '../../reducers/splitViewReducer';
import { downloadFile, openAnchor, openWindow } from '../../../../../../utilities/navigate';
import { ISpoAttachment } from '../../../types/ISpoAttachment';
import { IDetailAttachment } from '../../../types/IDetailAttachment';
import { getIconTypeByExtension } from '../../../../../../utilities/icons';
import { splitExtensionFromFileName } from '../../../../../../utilities/regexp';
import { getUriScheme } from '../../../../../../utilities/officeUri';
import { UpdateBookmark } from '../../../../../../hooks/accessors/useBookmarkRepositoryAccessor';
import { FetchMailDetail, FetchMailDetailAttachments } from '../../../../../../hooks/accessors/useMailApiAccessor';
import { IChatProperties, IMailProperties } from '../../../../../../types/ISearchResult';
import { composeAdditionalProperties } from '../../../../utilities/logging/composeAdditionalProperties';
import {
  FetchChatRepliesInBackground, FetchChatChannelName, FetchChatDetail,
  FetchChatMember, FetchChatTeamName,
} from '../../../../../../hooks/accessors/useChatApiAccessor';
import { AddRemoteBookmark } from '../../../../../../hooks/features/useRemoteBookmarkFeature';
import { normalizeUtcOrNotUtcDate } from '../../../../../../utilities/date';
import { DataSourceKind } from '../../../../../../types/DataSourceKind';
import { SearchModeType } from '../../../types/SearchListMode';

// dayjsのisSameOrBeforeプラグインを追加
// https://day.js.org/docs/en/plugin/plugin
dayjs.extend(isSameOrBefore);

interface ReplyOptionsInterface {
  replyToId: string | null;
  teamChatType: string | null;
}

/**
 * SharePoint項目の情報を画面用に整形する
 * @param baseUrl
 * @param result
 * @returns
 */
export function reformatAttachments(
  baseUrl: string, result: ISharePointListItemResponseWithAttachment,
): ISpoAttachment[] {

  if (!Array.isArray(result.AttachmentFiles)) return [];

  const hostName = baseUrl.match(/^(http.*?)\/sites/);
  // ホスト名が取得できない場合は空配列を返却
  if (!hostName?.[1]) return [];

  return result.AttachmentFiles.map((attachment) => {
    const nameAndExtension = splitExtensionFromFileName(attachment.FileName);
    const extension = nameAndExtension?.[2];
    const uriScheme = getUriScheme(extension);
    const fileUrl = `${hostName[1]}/${attachment.ServerRelativeUrl}?web=1`;

    return {
      // SharePoint APIから取得する添付ファイルデータにはidにあたる情報がなく
      // 個人情報を含む値はログ出力しないルールによりファイル名を代わりに充てることもできないため空欄とする
      id: '-',
      extension,
      title: attachment.FileName ?? '',
      url: `${uriScheme}${fileUrl}`,
      spUrl: fileUrl,
      icon: getIconTypeByExtension(extension),
    };
  });
}

/**
 * SharePoint項目の情報をPayloadとしてSETする
 * @param result
 * @param baseUrl
 * @param editLink
 * @param listName
 */
export function convertResultAsPayload(
  result: ISharePointListItemResponseWithAttachment,
  activeItem: ISplitViewListSingle,
): SetDetailActionPayload {
  const detail = convertSPListItemToSplitViewDetail(result, activeItem);

  return {
    detail: {
      ...detail,
      properties: {
        ...detail.properties,
        spoAttachments: reformatAttachments(
          isSpoProperties(activeItem.properties) ? activeItem.properties.listUrl ?? '' : '',
          result,
        ),
      },
    },
  };
}

/**
 * 記事詳細の公開ステータス、公開期限、削除済みかどうかを確認して、表示可能な記事かどうかを判定
 * 公開ステータスが"公開"である AND (掲示期限が未設定 OR 掲示期限が現在日時よりも未来 = greater than or equal)
 * 以上の時trueを返す
 * @param detail 記事詳細データ
 * @return boolean trueで閲覧可能, falseで閲覧禁止
 */
export function isArticleValidToShow(detail: ISharePointListItemResponseWithAttachment): boolean {
  const isPublished = detail.releaseState === ReleaseState.PUBLISHED;
  const isPeriodNotPresented = !detail.presentPeriod;
  if (isPublished && isPeriodNotPresented) {
    return true;
  }

  const isValidDate = dayjs().isSameOrBefore(dayjs(detail.presentPeriod), 'date');
  return isPublished && isValidDate;
}

function checkDisplayUpdated(list: ISplitViewListSingle, detail: ISplitViewDetail) {
  return list.note !== detail.note
    || list.title !== detail.title
    || normalizeUtcOrNotUtcDate(list.displayDate) !== normalizeUtcOrNotUtcDate(detail.displayDate);
}

/**
 * SPO項目を取得する
 * @param editLink
 * @param fetchDetail
 * @param dispatch
 * @param reportEvent
 * @param isUnmounted
 */
export function fetchSpoItemDetail(
  fetchDetail: FetchSPODetail | undefined,
  activeItem: ISplitViewListSingle | undefined,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  needUpdateList: boolean,
  bookmarked: boolean,
  addRemoteBookmark: AddRemoteBookmark | undefined,
  setApiDetailPreRequestMetrics: () => void,
  setApiDetailRequestMetrics: () => void,
  setApiDetailPostRequestMetrics: () => void,
): Promise<void> {

  if (
    !activeItem
    || !activeItem.properties
    // listURLを確認
    || !isSpoProperties(activeItem.properties)
    || !activeItem.properties?.siteUrl
    || !activeItem.properties?.editLink
    || !activeItem.properties?.listName
    || !fetchDetail
    || !addRemoteBookmark
  ) return Promise.resolve();

  setApiDetailPreRequestMetrics();

  return fetchDetail(activeItem.properties.siteUrl,
    activeItem.properties.editLink,
    activeItem.properties.categoryKeyName)
    // 異常系処理
    // graph-clientのrejectはGraphError型になる
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: reason,
      });

      const isUnavailable = (reason?.statusCode === 403 || reason?.statusCode === 404);
      if (isUnmounted.current) return;
      dispatch({
        type: 'SET_DETAIL_ERROR',
        payload: isUnavailable
          // 403/404のときはメッセージの種類を変える
          ? SplitViewDetailMessage.UNAVAILABLE
          : SplitViewDetailMessage.API_REQUEST_FAIL,
      });

      //  正常系処理
    }).then(async (result) => {
      if (isUnmounted.current || !result) return;

      setApiDetailRequestMetrics();

      if (isArticleValidToShow(result)) {
        const payload = convertResultAsPayload(result, activeItem);
        // 投稿が閲覧可能な場合
        dispatch({
          type: 'SET_DETAIL',
          payload,
        });
        const displayUpdated = payload.detail && checkDisplayUpdated(activeItem, payload.detail);
        if (needUpdateList && payload.detail && displayUpdated) {
          dispatch({
            type: 'UPDATE_ITEM',
            payload: {
              id: activeItem.id ?? '',
              item: convertSPODetailToSPOSingle(payload.detail),
            },
          });

          if (bookmarked) {
            await addRemoteBookmark(convertSplitViewItemToBookmarkItem(payload.detail));
          }
        }
      } else {
        // 投稿が閲覧不可な場合
        dispatch({
          type: 'SET_DETAIL_ERROR',
          payload: SplitViewDetailMessage.UNAVAILABLE,
        });
      }

      setApiDetailPostRequestMetrics();
    });
}

/**
 * 添付ファイルを対応するOfficeアプリ、もしくはブラウザで開く
 *
 * @export
 * @param {IDetailAttachment} attachment
 * @param {EventReporter} reportEvent
 * @param isPC boolean trueのときにはPC用リンク、falseのときはSP用リンクを開く
 */
export function openAttachmentFile(
  attachment: IDetailAttachment | undefined,
  activeItem: ISplitViewListSingle | undefined,
  reportEvent: EventReporter,
  isPC: boolean,
): void {
  if (!attachment || !activeItem) return;

  // SPOの場合のみPCとiPhoneで呼び出すurlが変わる
  const url = (
    (activeItem.kind === DataSourceKind.SPO && isPC)
    || activeItem.kind !== DataSourceKind.SPO
  ) ? attachment.url : attachment.spUrl;

  openWindow(url, [reportEvent, {
    type: EventReportType.USER_EVENT,
    name: 'CLICK_ATTACHMENT',
    customProperties: {
      detail: {
        id: activeItem.id,
        kind: activeItem.kind,
        attachmentId: attachment.id,
        extension: attachment.extension,
        properties: composeAdditionalProperties(activeItem),
      },
    },
  }]);
}

/**
 * Mail添付ファイルをダウンロード
 *
 * @export
 * @param {IDetailAttachment} attachment
 * @param {EventReporter} reportEvent
 * @param isPC boolean falseであれば何もしない
 */
export function downloadAttachmentFile(
  attachment: IDetailAttachment | undefined,
  activeItem: ISplitViewListSingle | undefined,
  reportEvent: EventReporter,
  isPC: boolean,
  fetchMailDetailAttachments: FetchMailDetailAttachments | undefined,
): Promise<void> {
  if (
    !attachment
    || !activeItem
    || !isPC
    || !fetchMailDetailAttachments
  ) return Promise.resolve();

  return fetchMailDetailAttachments(activeItem.id, attachment.id)
    // 異常系処理
    // graph-clientのrejectはGraphError型になる
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: reason,
      });
    })
    //  正常系処理
    .then(async (result) => {
      if (!result) return;

      const base64 = result.contentBytes;
      const fileName = result.name;

      downloadFile(base64, fileName, [reportEvent, {
        type: EventReportType.USER_EVENT,
        name: 'CLICK_MAIL_ATTACHMENT',
        customProperties: {
          detail: {
            id: activeItem.id,
            kind: activeItem.kind,
            attachmentId: attachment.id,
            extension: attachment.extension,
            properties: composeAdditionalProperties(activeItem),
          },
        },
      }]);
    });
}

/**
 * 本文内部のリンクをクリックしたときのイベントリスナ
 * @param e イベント
 * @param $el クリックされた要素
 * @param report EventReporter
 */
export function openBodyInnerLink(
  e: MouseEvent,
  $el: HTMLElement,
  reportEvent: EventReporter,
): void {
  e.preventDefault();
  const $link = $el as HTMLLinkElement;
  if (!$link.href) return;

  // アンカーリンクの場合
  const rawHref = $link.getAttribute('href');
  if (rawHref?.startsWith('#')) {
    openAnchor(rawHref);
    return;
  }

  // それ以外
  openWindow($link.href, [reportEvent, { type: EventReportType.USER_EVENT, name: 'CLICK_DETAIL_BODY_LINK' }]);
}

/**
 * innerHTML内のリンク要素を検索するタイミングを判定
 * 選択中の記事がある && ローディングやエラー状態ではない && 記事本文が存在する ときにtrue
 * @param activeId
 * @param detailView
 * @param detail
 */
export function getInnerHtmlUpdateTrigger(
  activeId: string,
  detailView: SplitViewDetailViewType,
  detail: ISplitViewDetail | undefined,
): boolean {
  return (
    // 選択中の記事がある
    !!activeId
    // AND ローディングやエラー状態ではない
    && detailView === SplitViewDetailView.DEFAULT
    // AND 記事本文が存在する
    && !!detail?.body
  );
}

/**
 * listからactiveIdと一致するレコードを返却する
 * @param activeId
 * @param list
 */
export function getActiveItem(
  activeId: string, list: ISplitViewListSingle[],
): ISplitViewListSingle | undefined {
  if (!activeId || !Array.isArray(list)) return undefined;
  return list.find((item) => item.id === activeId);
}

/**
 * detailが変化したらログを送信する
 * @param detail
 * @param detailView
 * @param reportEvent
 * @param reportName
 * @param isFirstItemSelected
 * @param updateBookmark
 * @param onChanged ログ送信をした場合に実行される
 */
export function onChangeDetail(
  detail: ISplitViewDetail | undefined,
  detailView: SplitViewDetailViewType,
  reportEvent: EventReporter,
  reportName: string,
  isFirstItemSelected: boolean,
  searchMode: SearchModeType,
  updateBookmark: UpdateBookmark | undefined,
  onChanged: () => void,
): void {
  if (!detail || detailView !== SplitViewDetailView.DEFAULT) return;

  const detailWithoutBody = convertSplitViewItemToBookmarkItem(detail);
  reportEvent({
    type: EventReportType.USER_EVENT,
    name: `SHOW_DETAIL_${reportName}`,
    customProperties: {
      // 初期選択かどうかを判別
      isFirstItemSelected,
      // どちらのsearchModeか判別
      searchMode,
      // 記事の本文以外の情報を送信
      detail: {
        id: detailWithoutBody.id,
        kind: detailWithoutBody.kind,
        properties: composeAdditionalProperties(detailWithoutBody),
      },
    },
  });

  onChanged();

  // お気に入り済の記事だった場合は最新化
  if (updateBookmark) {
    updateBookmark(detailWithoutBody);
  }
}

/**
 * 一覧項目クリック時の処理
 *
 * @export
 * @param {ISplitViewListSingle} item
 * @param {SplitViewDispatch} dispatch
 */
export function onClickItemImpl(
  item: ISplitViewListSingle,
  dispatch: SplitViewDispatch,
): void {
  dispatch({
    type: 'SET_ACTIVE',
    payload: {
      activeId: item?.id ?? '',
      title: item?.title ?? '',
      kind: item?.kind ?? 'Other',
    },
  });
}

export function onCloseImpl(dispatch: SplitViewDispatch): void {
  if (!isSP()) return;
  dispatch({
    type: 'UNSELECT',
    payload: undefined,
  });
}

/**
 * SPOリストアイテム詳細フォームのURLを作成する
 *
 * @param {(number | null | undefined)} itemId
 * @param {(string | undefined)} listUrl
 * @return {*}  {(string)}
 */
export function createListItemLink(
  itemId: number | null | undefined,
  listUrl: string | undefined,
): string {
  if (!itemId || !listUrl) return '';
  return `${listUrl}?ID=${itemId}`;
}

function openSPOLink(detail: ISplitViewDetail, reportEvent: EventReporter, listUrl: string) {
  const link = createListItemLink(detail.itemId, listUrl);
  if (!link) return;

  openWindow(link, [reportEvent, {
    type: EventReportType.USER_EVENT,
    name: 'CLICK_SPO_ALT_LINK',
  }]);
}

function getMailLink(detail: ISplitViewDetail) {
  if (isSP()) {
    return detail.id && `ms-outlook://emails/message/${detail.id}`;
  }
  const properties = detail.properties as IMailProperties;
  return properties.webLink;
}

function openMailLink(detail: ISplitViewDetail, reportEvent: EventReporter) {
  const link = getMailLink(detail);
  if (!link) return;

  openWindow(link, [reportEvent, {
    type: EventReportType.USER_EVENT,
    name: 'CLICK_MAIL_ALT_LINK',
  }]);
}
function getChatLink(detail: ISplitViewDetail) {
  const prefix = 'https://teams.microsoft.com';
  const messageId = detail.id;
  const properties = (detail.properties as IChatProperties);
  const chatId = properties?.chatId;
  const messageType = properties?.messageType;
  const replyToId = properties?.replyToId;

  if (!messageId || !chatId || !messageType) {
    throw new Error('Required property is null or undefined');
  }
  if (messageType === 'team') {
    if (replyToId) {
      return `${prefix}/l/message/${chatId}/${messageId}?parentMessageId=${replyToId}&messageId=${messageId}&allowXTenantAccess=false`;
    }
    // 親メッセージ（スレッド）の場合
    return `${prefix}/l/message/${chatId}/${messageId}?allowXTenantAccess=false`;
  }
  if (messageType === 'chat') {
    return `${prefix}/l/message/${chatId}/${messageId}?context={"contextType":"chat"}`;
  }
  throw new Error('unknown chat type');
}

function openChatLink(
  detail: ISplitViewDetail,
  executeDeepLink: (link: string, reporterParam: IEventReporterParam) => void,
) {
  const link = getChatLink(detail);
  executeDeepLink(link, {
    type: EventReportType.USER_EVENT,
    name: 'CLICK_CHAT_ALT_LINK',
  });
}

/**
 * 選択したアイテムから詳細ページへのリンクを作成して開く
 * @param detail
 * @param reportEvent
 */
export function onClickAltLinkImpl(
  detail: ISplitViewDetail | undefined,
  reportEvent: EventReporter,
  listUrl: string,
  executeDeepLink: (link: string, reporterParam: IEventReporterParam) => void,
): void {
  if (!detail) return;
  switch (detail.kind) {
    case 'SPO':
      openSPOLink(detail, reportEvent, listUrl);
      break;
    case 'Mail':
      openMailLink(detail, reportEvent);
      break;
    case 'Chat':
      openChatLink(detail, executeDeepLink);
      break;
    case 'Other':
    default:
      break;
  }
}

/**
 * activeItemのidからMailの詳細開く
 * @param detail
 * @param reportEvent
 */
export function fetchMailItemDetail(
  fetchMailDetail: FetchMailDetail | undefined,
  activeItem: ISplitViewListSingle | undefined,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  needUpdateList: boolean,
  bookmarked: boolean,
  addRemoteBookmark: AddRemoteBookmark | undefined,
  setApiDetailPreRequestMetrics: () => void,
  setApiDetailRequestMetrics: () => void,
  setApiDetailPostRequestMetrics: () => void,
): Promise<void> {
  if (
    !activeItem
    || !activeItem.id
    || !activeItem.properties
    || isSpoProperties(activeItem.properties)
    || !fetchMailDetail
    || !addRemoteBookmark
  ) return Promise.resolve();

  setApiDetailPreRequestMetrics();
  // chatmodeの場合もactiveItem.idを渡せば取得できるようにする
  if (!fetchMailDetail) return Promise.resolve();
  // 通常のfetchMailDetail処理
  return fetchMailDetail(activeItem.id)
    // 異常系処理
    // graph-clientのrejectはGraphError型になる
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: reason,
      });

      const isUnavailable = (reason?.statusCode === 403 || reason?.statusCode === 404);
      if (isUnmounted.current) return;
      dispatch({
        type: 'SET_DETAIL_ERROR',
        payload: isUnavailable
          // 403/404のときはメッセージの種類を変える
          ? SplitViewDetailMessage.UNAVAILABLE
          : SplitViewDetailMessage.API_REQUEST_FAIL,
      });

      //  正常系処理
    }).then(async (result) => {
      if (isUnmounted.current || !result) return;

      setApiDetailRequestMetrics();

      const inlineMailAttachments = result.attachments.filter(
        (attachment) => attachment.isInline === true,
      );

      dispatch({
        type: 'SET_INLINE_MAIL_ATTACHMENTS',
        payload: {
          inlineMailAttachments,
        },
      });

      const detail = convertMailListItemToSplitViewDetail(result, activeItem);
      dispatch({
        type: 'SET_DETAIL',
        payload: { detail },
      });

      const displayUpdated = checkDisplayUpdated(activeItem, detail);
      if (needUpdateList && displayUpdated) {
        dispatch({
          type: 'UPDATE_ITEM',
          payload: {
            id: activeItem.id ?? '',
            item: convertMailDetailToMailSingle(detail),
          },
        });
        if (bookmarked) {
          await addRemoteBookmark(convertSplitViewItemToBookmarkItem(detail));
        }
      }

      setApiDetailPostRequestMetrics();
    });
}

function getMemberNames(
  members: { userId: string, displayName: string, email: string, }[],
  chatId: string,
  authorId: string,
): string {
  const groupChatSuffixes = '@thread.v2';
  return chatId.endsWith(groupChatSuffixes)
    ? members.map((member) => member.displayName ?? member.email).join(', ')
    : (() => {
      const recipient = members.find((member) => member.userId !== authorId);
      return recipient?.displayName ?? recipient?.email ?? '';
    })();
}
/**
 * チャットの表示カテゴリを取得する関数
 *
 * @param activeItem - 選択された記事（`ISplitViewListSingle` 型）
 * @param fetchChatMember - チャットのメンバー情報を取得する関数（オプション）
 * @param fetchChatTeamName - チーム名を取得する関数（オプション）
 * @param fetchChatChannelName - チャンネル名を取得する関数（オプション）
 * @returns - チャットの表示カテゴリ（チーム名やチャンネル名など）を表す文字列
 */
export async function fetchChatDisplayCategory(
  activeItem: ISplitViewListSingle,
  fetchChatMember?: FetchChatMember,
  fetchChatTeamName?: FetchChatTeamName,
  fetchChatChannelName?: FetchChatChannelName,
): Promise<string> {
  // activeItem から `properties` を取得し、`IChatProperties` 型としてキャスト
  const properties = activeItem.properties as IChatProperties;

  // `messageType` によって処理を分岐
  switch (properties.messageType) {
    case 'chat':
      // チャット ID がない、または fetchChatMember がない場合は空文字を返す
      if (!properties?.chatId || !fetchChatMember) return Promise.resolve('');
      // チャット ID を使ってメンバー情報を取得し、トピック名またはメンバー名を返す
      return fetchChatMember(properties?.chatId).then(
        (response) => (
          response.topic ?? getMemberNames(response.members, properties.chatId, properties.from?.id ?? '')
        ),
      );
    case 'team':
      // 必須プロパティが欠けている場合は空文字を返す
      if (
        !properties?.teamId || !properties?.chatId
        || !fetchChatTeamName || !fetchChatChannelName
      ) {
        return Promise.resolve('');
      }
      // チーム名とチャンネル名を非同期で取得し、それらを組み合わせてフォーマットする
      return Promise.all([
        // チャンネル名を取得
        fetchChatChannelName(properties.teamId).then(
          (response) => ({ teamName: response.displayName }),
        ),
        // チーム名を取得
        fetchChatTeamName(properties?.teamId, properties?.chatId).then(
          (response) => ({ channelName: response.displayName }),
        ),
      ]).then(
        (results) => {
          // `general` というチャンネル名は `一般` に変換
          const convertedChannelName = results[1].channelName.toLowerCase() === 'general' ? '一般' : results[1].channelName;
          return `${results[0].teamName} - ${convertedChannelName}`;
        },
      );
    default:
      // `messageType` が `chat` や `team` 以外の場合は空文字を返す
      return Promise.resolve('');
  }
}

export async function fetchChatItemDetail(
  fetchChatDetail: FetchChatDetail | undefined,
  fetchChatMember: FetchChatMember | undefined,
  fetchChatTeamName: FetchChatTeamName | undefined,
  fetchChatChannelName: FetchChatChannelName | undefined,
  activeItem: ISplitViewListSingle | undefined,
  dispatch: SplitViewDispatch,
  reportEvent: EventReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  needUpdateList: boolean,
  bookmarked: boolean,
  addRemoteBookmark: AddRemoteBookmark | undefined,
  setApiDetailPreRequestMetrics: () => void,
  setApiDetailRequestMetrics: () => void,
  setApiDetailPostRequestMetrics: () => void,
): Promise<void> {
  if (
    !activeItem
    || !activeItem.id
    || !activeItem.properties
    || isSpoProperties(activeItem.properties)
    || !fetchChatDetail
    || !addRemoteBookmark
  ) return Promise.resolve();

  setApiDetailPreRequestMetrics();

  const detailedTitle = await fetchChatDisplayCategory(
    activeItem,
    fetchChatMember,
    fetchChatTeamName,
    fetchChatChannelName,
  ).catch((reason: GraphError) => {
    reportEvent({
      type: EventReportType.SYS_ERROR,
      name: 'API_REQUEST_FAIL',
      error: reason,
    });

    const isUnavailable = (reason?.statusCode === 403 || reason?.statusCode === 404);
    if (isUnmounted.current) return;
    dispatch({
      type: 'SET_DETAIL_ERROR',
      payload: isUnavailable
        // 403/404のときはメッセージの種類を変える
        ? SplitViewDetailMessage.UNAVAILABLE
        : SplitViewDetailMessage.API_REQUEST_FAIL,
    });
  });

  return fetchChatDetail(
    (activeItem.properties as IChatProperties).chatId,
    activeItem.id,
  )
    // 異常系処理
    // graph-clientのrejectはGraphError型になる
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: reason,
      });

      const isUnavailable = (reason?.statusCode === 403 || reason?.statusCode === 404);
      if (isUnmounted.current) return;
      dispatch({
        type: 'SET_DETAIL_ERROR',
        payload: isUnavailable
          // 403/404のときはメッセージの種類を変える
          ? SplitViewDetailMessage.UNAVAILABLE
          : SplitViewDetailMessage.API_REQUEST_FAIL,
      });

      //  正常系処理
      // ここで取得しているmessageTypeはmessage
    }).then(async (result) => {
      if (isUnmounted.current || !result) return;
      setApiDetailRequestMetrics();

      if (result.attachments.length !== 0) {
        dispatch({
          type: 'SET_CHAT_ATTACHMENTS',
          payload: {
            chatAttachments: result.attachments,
          },
        });
      }

      const detail = convertChatItemToSplitViewDetail(result, activeItem, detailedTitle ?? '');

      dispatch({
        type: 'SET_DETAIL',
        payload: { detail },
      });

      const displayUpdated = checkDisplayUpdated(activeItem, detail);
      if (needUpdateList && displayUpdated) {
        dispatch({
          type: 'UPDATE_ITEM',
          payload: {
            id: activeItem.id ?? '',
            item: convertChatDetailToChatSingle(detail),
          },
        });

        if (bookmarked) {
          await addRemoteBookmark(convertSplitViewItemToBookmarkItem(detail));
        }
      }

      setApiDetailPostRequestMetrics();
    });
}
/**
 * チャットのdetailに裏で取得したデータを反映する関数。
 *
 * @param fetchChatRepliesInBackground - バックグラウンドでチャットの返信を取得する関数
 * @param activeItem - 現在アクティブなアイテム
 * @param reportEvent - イベントレポーター
 * @param setReplyToId - ReplyToIdを更新するための関数
 * @returns void
 */
export async function fetchChatItemRepliesDetail(
  fetchChatRepliesInBackground: FetchChatRepliesInBackground | undefined,
  activeItem: ISplitViewListSingle | undefined,
  reportEvent: EventReporter,
  setReplyOptions: React.Dispatch<React.SetStateAction<ReplyOptionsInterface>>,
): Promise<void> {

  if (
    !activeItem
    || !activeItem.id
    || !activeItem.properties
    || !fetchChatRepliesInBackground
  ) return Promise.resolve();

  return fetchChatRepliesInBackground(
    (activeItem.properties as IChatProperties).chatId,
    activeItem.id,
    (activeItem.properties as IChatProperties).teamId ?? '',
    (activeItem.properties as IChatProperties).messageType,
  )
    .then(async (replyRes) => {
      if (replyRes) {
        const detail = convertChatItemToSplitViewDetail(replyRes, activeItem, '');
        const replyToId = (detail.properties as IChatProperties).replyToId ?? null;
        const teamChatType = (detail.properties as IChatProperties).teamChatType ?? null;
        setReplyOptions({ replyToId, teamChatType });
      } else {
        // TODO:ログ出力で終了は望ましくないので既存の機能との統合をする
        console.log('messageTypeはteamではありませんでした。');
      }
    })
    .catch((reason: GraphError) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: reason,
      });
    });
}
