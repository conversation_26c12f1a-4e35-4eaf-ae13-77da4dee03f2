import * as React from 'react';
import useAsync, { AsyncState } from 'react-use/lib/useAsync';
import { ISearchRequestResult } from '../../../../../types/ISearchRequestResult';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../split-view-detail/SplitViewDetail';
import { ListMode, ListModeType } from '../../types/ListMode';
import {
  initSplitViewState, initSplitViewStateForAI, ISplitViewState, SplitViewDispatch, splitViewReducer,
} from '../reducers/splitViewReducer';
import { ISplitViewListSingle } from '../../types/ISplitViewListSingle';
import { isPC } from '../../../../../utilities/mediaQuery';
import {
  FilterLeftOptions, formatList, getEmptyFilterOptions,
} from '../../../../../utilities/filter/filterSettings';
import { SplitViewListView } from '../../split-view-list/SplitViewList';
import { SearchListMode, SearchModeType } from '../../types/SearchListMode';

export type UseSplitViewReducersReturnType = {
  initialDisplayReducerReturn: [ISplitViewState, SplitViewDispatch],
  bookmarksListReducerReturn: [ISplitViewState, SplitViewDispatch],
  searchResultListReducerReturn: [ISplitViewState, SplitViewDispatch],
  chatModeListReducerReturn: [ISplitViewState, SplitViewDispatch], // チャットモード用のリデューサー
  formedBookmarksList: ISplitViewListSingle[],
  formedSearchResultsList: ISplitViewListSingle[],
  formedChatModeList: ISplitViewListSingle[], // チャットモードのフォーマット済みリスト
  bookmarksEmptyFilterOptions: AsyncState<FilterLeftOptions>,
  searchResultEmptyFilterOptions: AsyncState<FilterLeftOptions>,
  chatModeEmptyFilterOptions: AsyncState<FilterLeftOptions>, // チャットモード用のフィルターオプション
  activeBookmarkItemId: string | undefined,
  activeSearchResultItemId: string | undefined,
  activeChatModeItemId: string | undefined, // チャットモードのアクティブアイテムID
  listModeStateReturn: [listMode: ListModeType, setListMode: (next: ListModeType) => void],
  searchModeStateReturn:
   [searchMode: SearchModeType,
    setSearchMode: (next: SearchModeType) => void],
  searchRequestStateReturn: [
    searchRequest: ISearchRequestResult | null,
    setSearchRequest: (result: ISearchRequestResult | null) => void
  ],
  syncBookmarkStateReturn: [
    syncBookmark: boolean,
    setSyncBookmark: (start: boolean) => void,
  ]
  isInitialDisplayMode: boolean,
  isBookmarksListMode: boolean,
  isSearchListMode: boolean,
  isChatSearchMode:boolean,
  isDefaultSearchMode:boolean,
  isDetailOpen: boolean,
};

export function getActiveId(
  state: ISplitViewState,
  formedList: ISplitViewListSingle[],
  isPc: boolean,
) {
  if (isPc && (state.activeId ?? '').length === 0) {
    return formedList[0]?.id;
  }
  // activeIdが設定されていてもフィルターで非表示になっている場合はformedListの1行目をActiveIdとする
  if (isPc && (!formedList.some((formedListItem) => formedListItem.id === state.activeId))) {
    return formedList[0]?.id;
  }
  return state.activeId;
}

/**
 * provide states for SplitViewContainer
 */
const useSplitViewReducers = (): UseSplitViewReducersReturnType => {
  // create list states
  const initialDisplayReducerResults = React.useReducer(splitViewReducer, initSplitViewState());
  const bookmarksReducerResults = React.useReducer(splitViewReducer, initSplitViewState());
  // searchResultReducerResultsは[state,dispatch]を持っている
  // useSearchResultListのuseReducerReturnが空の配列を持つ
  const searchResultReducerResults = React.useReducer(splitViewReducer, {
    ...initSplitViewState(),
    detailView: SplitViewDetailView.ERROR,
    detailMessage: SplitViewDetailMessage.BLANK,
  });

  // チャットモード用のリデューサー
  const chatModeReducerResults = React.useReducer(splitViewReducer, {
    ...initSplitViewStateForAI(),
    detailView: SplitViewDetailView.ERROR,
    detailMessage: SplitViewDetailMessage.BLANK,
  });

  // create the list mode state
  const listModeStateReturn = React.useState<ListModeType>(ListMode.INITIAL_DISPLAY);
  const [listMode] = listModeStateReturn;
  // 検索モードの管理
  const searchModeStateReturn = React.useState<SearchModeType>(SearchListMode.DEFAULT);
  const [searchMode] = searchModeStateReturn;
  // 検索要求Stateを作成
  const searchRequestStateReturn = React.useState<ISearchRequestResult | null>(null);

  // お気に入り同期開始Stateを作成
  const syncBookmarkStateReturn = React.useState<boolean>(false);

  // list mode flags
  const isInitialDisplayMode = React.useMemo(
    () => listMode === ListMode.INITIAL_DISPLAY, [listMode],
  );
  const isBookmarksListMode = React.useMemo(() => listMode === ListMode.BOOKMARKS, [listMode]);
  const isSearchListMode = React.useMemo(() => listMode === ListMode.SEARCH, [listMode]);
  const isChatSearchMode = React.useMemo(() => searchMode === SearchListMode.Chat, [searchMode]);
  const isDefaultSearchMode = React.useMemo(() => searchMode
  === SearchListMode.DEFAULT, [searchMode]);
  const [initialDisplayState] = initialDisplayReducerResults;
  const [bookmarksState] = bookmarksReducerResults;
  // searchResultStateのdetailが詳細表示される
  const [searchResultState] = searchResultReducerResults;
  // チャットモードのステート
  const [chatModeState] = chatModeReducerResults;

  // return true when any of the states has activeId
  const isDetailOpen = React.useMemo(
    () => !!initialDisplayState.activeId
      || !!bookmarksState.activeId
      || !!searchResultState.activeId
      || !!chatModeState.activeId, // チャットモードのアクティブID
    [
      initialDisplayState.activeId,
      bookmarksState.activeId,
      searchResultState.activeId,
      chatModeState.activeId, // チャットモードのアクティブID
    ],
  );

  const formedBookmarksList = React.useMemo(() => formatList(bookmarksState),
  // eslint-disable-next-line react-hooks/exhaustive-deps
    [bookmarksState.list, bookmarksState.context]);

  const isLoading = (state: ISplitViewState) => state.listView === SplitViewListView.LOADING
  || state.listView === SplitViewListView.ON_INTERVAL
  || state.listView === 'IN_PROGRESS';

  const formedSearchResultsList = React.useMemo(
    // 検索中であればリストのキャッシュを、完了していればフォーマット化して返す
    () => (isLoading(searchResultState) ? searchResultState.list : formatList(searchResultState)),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [searchResultState.list, searchResultState.context, searchResultState.listView],
  );

  // チャットモードのフォーマット済みリスト
  const formedChatModeList = React.useMemo(
    // 検索中であればリストのキャッシュを、完了していればフォーマット化して返す
    () => (isLoading(chatModeState) ? chatModeState.list : formatList(chatModeState)),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chatModeState.list, chatModeState.context, chatModeState.listView],
  );

  const dummyEmptyFilterOptions: FilterLeftOptions = {
    displayDate: [0, 0, 0, 0, 0, 0, 0],
    kind: [0, 0, 0],
  };

  const bookmarksEmptyFilterOptions = useAsync(
    () => new Promise((resolve) => {
      setTimeout(() => resolve(getEmptyFilterOptions(bookmarksState)), 0);
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [bookmarksState.list, bookmarksState.context?.filter],
  );

  const searchResultEmptyFilterOptions = useAsync(
    () => new Promise((resolve) => {
      if (isLoading(searchResultState)) {
        resolve(dummyEmptyFilterOptions);
      } else {
        setTimeout(() => resolve(getEmptyFilterOptions(searchResultState)), 0);
      }
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [searchResultState.list, searchResultState.context?.filter, searchResultState.listView],
  );

  // チャットモード用のフィルターオプション
  const chatModeEmptyFilterOptions = useAsync(
    () => new Promise((resolve) => {
      if (isLoading(chatModeState)) {
        resolve(dummyEmptyFilterOptions);
      } else {
        setTimeout(() => resolve(getEmptyFilterOptions(chatModeState)), 0);
      }
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chatModeState.list, chatModeState.context?.filter, chatModeState.listView],
  );

  const activeBookmarkItemId = getActiveId(bookmarksState, formedBookmarksList, isPC());
  const activeSearchResultItemId = getActiveId(searchResultState, formedSearchResultsList, isPC());
  // チャットモードのアクティブアイテムID
  const activeChatModeItemId = getActiveId(chatModeState, formedChatModeList, isPC());

  return {
    initialDisplayReducerReturn: initialDisplayReducerResults,
    bookmarksListReducerReturn: bookmarksReducerResults,
    // searchResultReducerResultsのdetailが詳細表示
    searchResultListReducerReturn: searchResultReducerResults,
    // チャットモード用のリデューサー
    chatModeListReducerReturn: chatModeReducerResults,
    formedBookmarksList,
    formedSearchResultsList,
    formedChatModeList, // チャットモードのフォーマット済みリスト
    bookmarksEmptyFilterOptions,
    searchResultEmptyFilterOptions,
    chatModeEmptyFilterOptions, // チャットモード用のフィルターオプション
    activeBookmarkItemId,
    activeSearchResultItemId,
    activeChatModeItemId, // チャットモードのアクティブアイテムID
    listModeStateReturn,
    searchModeStateReturn,
    searchRequestStateReturn,
    syncBookmarkStateReturn,
    isInitialDisplayMode,
    isBookmarksListMode,
    isSearchListMode,
    isChatSearchMode,
    isDefaultSearchMode,
    isDetailOpen,
  };
};

export default useSplitViewReducers;
