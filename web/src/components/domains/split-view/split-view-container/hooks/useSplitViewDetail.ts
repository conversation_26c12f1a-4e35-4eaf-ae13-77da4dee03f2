import * as React from 'react';
import { useState } from 'react';
import { useMedia } from 'react-use';
import { useDeepLink } from '@avanade-teams/deeplink';
import useComponentInitUtility from '../../../../../hooks/utilities/useComponentInitUtility';
import useSharePointApiAccessor from '../../../../../hooks/accessors/useSharePointApiAccessor';
import { ISplitViewDetail } from '../../types/ISplitViewDetail';
import { ISplitViewListSingle } from '../../types/ISplitViewListSingle';
import {
  downloadAttachmentFile,
  fetchChatItemDetail,
  fetchChatItemRepliesDetail,
  fetchMailItemDetail,
  fetchSpoItemDetail,
  getActiveItem,
  getInnerHtmlUpdateTrigger,
  onChangeDetail,
  onClickAltLinkImpl,
  onClickItemImpl,
  onCloseImpl,
  openAttachmentFile,
  openBodyInnerLink,
} from './use-split-view-detail/functions';
import { ISplitViewState, SplitViewDispatch } from '../reducers/splitViewReducer';
import { MediaQueryStrings } from '../../../../../utilities/mediaQuery';
import useInnerHtmlClickBehavior from '../../../../../hooks/behaviors/useInnerHtmlClickBehavior';
import useSpoImgLoaderFeature from '../../../../../hooks/behaviors/useSpoImgLoaderFeature';
import {
  DATA_ATTR_CHAT_ATTACHMENT_ID, DATA_ATTR_CHAT_SRC, DATA_ATTR_MAIL_SRC, DATA_ATTR_SPO_SRC,
} from '../../../../../utilities/sanitize';
import { ListMode, ListModeType } from '../../types/ListMode';
import useBookmarkRepositoryAccessor from '../../../../../hooks/accessors/useBookmarkRepositoryAccessor';
import { DataSourceKind } from '../../../../../types/DataSourceKind';
import useMailApiAccessor from '../../../../../hooks/accessors/useMailApiAccessor';
import useChatApiAccessor from '../../../../../hooks/accessors/useChatApiAccessor';
import { isSpoProperties } from '../../../../../utilities/transform';
import useMailLoadingFeature from '../../../../../hooks/features/useMailLoadingFeature';
import useChatImgLoadingFeature from '../../../../../hooks/features/useChatImgLoadingFeature';
import useRemoteBookmarkFeature from '../../../../../hooks/features/useRemoteBookmarkFeature';
import { logPerformanceMetric } from '../../../../../utilities/commonFunction';
import useChatMentionFeature from '../../../../../hooks/features/useChatMentionFeature';
import useChatAttachmentsLoadingFeature from '../../../../../hooks/features/useChatAttachmentsLoadingFeature';
import { IDetailAttachment } from '../../types/IDetailAttachment';
import { SearchModeType } from '../../types/SearchListMode';

const METRIC_NAME = 'useSplitViewDetail:';

type UseSplitViewDetailReturnType = {
  onClickAttachment: (attachment?: IDetailAttachment) => void,
  onClickItem: (item: ISplitViewListSingle) => void,
  onClickAltLink: (detail: ISplitViewDetail | undefined) => void,
  onCloseDetail: () => void,
  $bodyRef: React.RefObject<HTMLDivElement>,
  replyOptions: { replyToId: string | null; teamChatType: string | null };
};

const useSplitViewDetail = (
  name: string,
  // searchResultListReducerReturnが渡ってきている
  useReducerReturn: ReturnType<typeof React.useReducer>,
  useComponentInitReturn: ReturnType<typeof useComponentInitUtility>,
  useSpoApiReturn: ReturnType<typeof useSharePointApiAccessor>,
  useMailApiReturn: ReturnType<typeof useMailApiAccessor>,
  useChatApiReturn: ReturnType<typeof useChatApiAccessor>,
  useRepositoryReturn: ReturnType<typeof useBookmarkRepositoryAccessor>,
  useRemoteBookmarkResults: ReturnType<typeof useRemoteBookmarkFeature>,
  listMode: ListModeType,
  bookmarkDict: { [key: string]: boolean },
  hideHighlight?: () => Promise<void>,
  searchListMode?: SearchModeType,
): UseSplitViewDetailReturnType => {

  // リスト名・イベントリポートで使用
  const listName = React.useRef(name);
  const [replyOptions, setReplyOptions] = useState<{
    replyToId: string | null;
    teamChatType: string | null;
  }>({
    replyToId: null,
    teamChatType: null,
  });
  const [{
    // detailを取り出す
    list, activeId, detailView, detail, inlineMailAttachments, chatAttachments,
  }, dispatch] = useReducerReturn as [ISplitViewState, SplitViewDispatch];

  // コンポーネント共通機能
  const [isUnmounted, [reportEvent, reportMetric], , , , oid, , metrics] = useComponentInitReturn;
  const [performanceMetrics, setPerformanceMetrics] = metrics;
  const { executeDeepLink } = useDeepLink({ reporter: reportEvent });

  // 永続化データの読み書きフック
  const { updateBookmark } = useRepositoryReturn;

  const { addRemoteBookmark } = useRemoteBookmarkResults;

  // SharePoint REST API
  const [fetchDetail, , fetchFileBlob] = useSpoApiReturn;

  // Graph API
  const {
    fetchMailDetail,
    fetchMailDetailAttachments,
  } = useMailApiReturn;

  const isBookmarked = React.useCallback(
    (id: string) => !!bookmarkDict && bookmarkDict[id], [bookmarkDict],
  );

  const {
    fetchChatDetail,
    fetchChatRepliesInBackground,
    fetchChatMember,
    fetchChatTeamName,
    fetchChatChannelName,
  } = useChatApiReturn;

  // 一覧初期表示直後の自動選択なのか、ユーザークリックによる変更なのかを保持するフラグ
  const isFirstItemSelected = React.useRef(true);

  // 一覧項目クリック時の処理
  const onClickItem = React.useCallback((item) => {
    // ユーザーの意図的なクリックであることを反映する(ログに使用)
    // このonClickItemを経由しない場合isFirstItemSelected.currentはtrueになっている
    isFirstItemSelected.current = false;
    if (!hideHighlight) {
      onClickItemImpl(item, dispatch);
      return;
    }
    // dispatchする前にmark.jsが改竄したDOMの状態を戻す
    // dispatch前に戻さないとDOMの状態がおかしくなる
    hideHighlight().then(() => {
      onClickItemImpl(item, dispatch);
    });
  }, [dispatch, hideHighlight]);

  // 現在アクティブな項目をメモ化
  const activeItem: ISplitViewListSingle | undefined = React.useMemo(
    () => getActiveItem(activeId, list),
    [list, activeId],
  );

  // 詳細取得前処理(Pre Request)完了時刻をメトリックのプロパティに設定する
  const setApiDetailPreRequestMetrics = () => {
    if (listMode !== ListMode.SEARCH) return;
    if (!performanceMetrics.current.viewDetailsFromCache.isAvailable) return;
    if (performanceMetrics.current.viewDetailsFromCache.isLogged) return;
    // 詳細取得前処理にかかった時間をメトリックのプロパティに設定する
    setPerformanceMetrics({
      viewDetailsFromCache: {
        ...performanceMetrics.current.viewDetailsFromCache,
        customProperties: {
          ...performanceMetrics.current.viewDetailsFromCache.customProperties,
          apiDetailPreRequestTimeElapsed:
            performance.now()
            - performanceMetrics.current
              .viewDetailsFromCache.references.apiDetailPreRequestStartTime,
        },
      },
    });
    // 詳細取得処理の開始時刻を記録
    setPerformanceMetrics({
      viewDetailsFromCache: {
        ...performanceMetrics.current.viewDetailsFromCache,
        references: {
          ...performanceMetrics.current.viewDetailsFromCache.references,
          apiDetailRequestStartTime: performance.now(),
        },
      },
    });
  };
  // 詳細取得処理(Request)完了時刻をメトリックのプロパティに設定する
  const setApiDetailRequestMetrics = () => {
    if (listMode !== ListMode.SEARCH) return;
    if (!performanceMetrics.current.viewDetailsFromCache.isAvailable) return;
    if (performanceMetrics.current.viewDetailsFromCache.isLogged) return;
    // 詳細取得処理にかかった時間をメトリックのプロパティに設定する
    setPerformanceMetrics({
      viewDetailsFromCache: {
        ...performanceMetrics.current.viewDetailsFromCache,
        customProperties: {
          ...performanceMetrics.current.viewDetailsFromCache.customProperties,
          apiDetailRequestTimeElapsed:
            performance.now()
            - performanceMetrics.current.viewDetailsFromCache.references.apiDetailRequestStartTime,
        },
      },
    });
    // 詳細取得後処理の開始時刻を記録
    setPerformanceMetrics({
      viewDetailsFromCache: {
        ...performanceMetrics.current.viewDetailsFromCache,
        references: {
          ...performanceMetrics.current.viewDetailsFromCache.references,
          apiDetailPostRequestStartTime: performance.now(),
        },
      },
    });
  };
  // 詳細取得後処理(Post Request)完了時刻をメトリックのプロパティに設定する
  const setApiDetailPostRequestMetrics = () => {
    if (listMode !== ListMode.SEARCH) return;
    if (!performanceMetrics.current.viewDetailsFromCache.isAvailable) return;
    if (performanceMetrics.current.viewDetailsFromCache.isLogged) return;
    // 詳細取得後処理にかかった時間をメトリックのプロパティに設定する
    setPerformanceMetrics({
      viewDetailsFromCache: {
        ...performanceMetrics.current.viewDetailsFromCache,
        customProperties: {
          ...performanceMetrics.current.viewDetailsFromCache.customProperties,
          apiDetailRequestTimeElapsed:
            performance.now()
            - performanceMetrics.current.viewDetailsFromCache.references.apiDetailRequestStartTime,
        },
      },
    });
  };
  // activeItemのIDが変化したら詳細情報を取得する
  React.useEffect(
    () => {
      const bookmarked = isBookmarked(activeItem?.id ?? '');
      switch (activeItem?.kind) {
        case DataSourceKind.SPO:
          fetchSpoItemDetail(
            fetchDetail,
            activeItem,
            dispatch,
            reportEvent,
            isUnmounted,
            listMode === ListMode.BOOKMARKS,
            bookmarked,
            addRemoteBookmark,
            setApiDetailPreRequestMetrics,
            setApiDetailRequestMetrics,
            setApiDetailPostRequestMetrics,
          );
          break;
        case DataSourceKind.Mail:
          fetchMailItemDetail(
            fetchMailDetail,
            activeItem,
            dispatch,
            reportEvent,
            isUnmounted,
            listMode === ListMode.BOOKMARKS,
            bookmarked,
            addRemoteBookmark,
            setApiDetailPreRequestMetrics,
            setApiDetailRequestMetrics,
            setApiDetailPostRequestMetrics,
          );
          break;
        case DataSourceKind.Chat:
          fetchChatItemDetail(
            fetchChatDetail,
            fetchChatMember,
            fetchChatTeamName,
            fetchChatChannelName,
            activeItem,
            dispatch,
            reportEvent,
            isUnmounted,
            listMode === ListMode.BOOKMARKS,
            bookmarked,
            addRemoteBookmark,
            setApiDetailPreRequestMetrics,
            setApiDetailRequestMetrics,
            setApiDetailPostRequestMetrics,
          );
          fetchChatItemRepliesDetail(
            fetchChatRepliesInBackground,
            activeItem,
            reportEvent,
            setReplyOptions,
          );
          break;
        default:
          break;
      }
    },
    // お気に入り情報を更新した後に起きる詳細情報の再取得を抑止するためにdepsを調整しています
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      fetchDetail,
      fetchMailDetail,
      fetchChatDetail,
      fetchChatRepliesInBackground,
      fetchChatMember,
      fetchChatTeamName,
      fetchChatChannelName,
      activeItem?.id,
      isUnmounted,
    ],
  );

  // detailが変化したらログを送信する。オンラインデータとの差分があればお気に入りを更新する
  React.useEffect(() => {
    onChangeDetail(
      detail,
      detailView,
      reportEvent,
      listName.current,
      isFirstItemSelected.current,
      searchListMode ?? '', // searchListModeがundefinedの場合は空文字を渡す
      updateBookmark,
      // フラグを元に戻す
      () => {
        isFirstItemSelected.current = true;

        // 検索結果キャッシュによって一覧が表示されてから詳細アイテムが画面に表示されるまでの時間をログ出力する
        if (listMode !== ListMode.SEARCH) return;
        if (!performanceMetrics.current.viewDetailsFromCache.isAvailable) return;
        if (performanceMetrics.current.viewDetailsFromCache.isLogged) return;
        performanceMetrics.current.viewDetailsFromCache.isLogged = true;
        logPerformanceMetric(
          reportMetric,
          `${METRIC_NAME}("${activeItem?.kind}",${activeItem?.id})`,
          'ViewDetailsFromCache',
          performanceMetrics.current.viewDetailsFromCache.start,
          performanceMetrics.current.viewDetailsFromCache.customProperties,
        );
      },
    );
    // detail変化以外の理由による発火を抑止するためにdepsを調整
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    detail,
    detailView,
    isFirstItemSelected,
    reportEvent,
    updateBookmark,
  ]);

  // 添付ファイルクリック時のアクション
  const isPC = useMedia(MediaQueryStrings.PC);
  const onClickAttachment = React.useCallback((attachment?: IDetailAttachment) => {
    // SPOとチャットは開く
    if (attachment?.url) {
      openAttachmentFile(
        attachment,
        activeItem,
        reportEvent,
        isPC,
      );
      // メールはダウンロード
    } else {
      downloadAttachmentFile(
        attachment,
        activeItem,
        reportEvent,
        isPC,
        fetchMailDetailAttachments,
      );
    }
  }, [
    fetchMailDetailAttachments,
    activeItem,
    reportEvent,
    isPC,
  ]);

  // 本文HTML内のリンククリック時の処理
  const onClickBodyLink = React.useCallback((e: MouseEvent, $el: HTMLElement) => {
    openBodyInnerLink(e, $el, reportEvent);
  }, [reportEvent]);

  // 本文HTML内の代替リンク(SPO遷移)クリック時の処理
  const onClickAltLink = React.useCallback((clickedDetail: ISplitViewDetail | undefined) => {
    const listUrl = isSpoProperties(activeItem?.properties) ? activeItem?.properties.listUrl : '';
    onClickAltLinkImpl(clickedDetail, reportEvent, listUrl ?? '', executeDeepLink);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reportEvent, activeItem]);

  // 本文HTML内のリンクを更新するための条件
  const innerHtmlUpdateTrigger = React.useMemo(
    () => getInnerHtmlUpdateTrigger(activeId, detailView, detail),
    [activeId, detailView, detail],
  );

  // 本文HTML要素の監視・イベントリスナの登録
  const [$bodyRef] = useInnerHtmlClickBehavior<HTMLDivElement>('a', innerHtmlUpdateTrigger, listMode, onClickBodyLink);
  // 本文HTML内のSharePoint画像を追加取得する機能
  useSpoImgLoaderFeature<HTMLDivElement>(
    $bodyRef,
    `img[${DATA_ATTR_SPO_SRC}]`,
    fetchFileBlob,
    reportEvent,
    innerHtmlUpdateTrigger,
    activeItem,
    isSpoProperties(activeItem?.properties) ? activeItem?.properties?.siteUrl ?? '' : '',
    listMode,
  );

  // 本文HTML内のMail画像を追加取得する機能
  useMailLoadingFeature<HTMLDivElement>(
    useMailApiReturn,
    $bodyRef,
    `img[${DATA_ATTR_MAIL_SRC}]`,
    innerHtmlUpdateTrigger,
    activeItem,
    inlineMailAttachments,
    listMode,
    reportEvent,
  );

  // 本文HTML内のChat画像を追加取得する機能
  useChatImgLoadingFeature<HTMLDivElement>(
    useChatApiReturn,
    $bodyRef,
    `img[${DATA_ATTR_CHAT_SRC}]`,
    innerHtmlUpdateTrigger,
    activeItem,
    listMode,
    reportEvent,
  );

  useChatMentionFeature<HTMLDivElement>(
    $bodyRef,
    'span.chat-mention',
    oid,
    detail,
    listMode,
  );

  useChatAttachmentsLoadingFeature<HTMLDivElement>(
    $bodyRef,
    `div[${DATA_ATTR_CHAT_ATTACHMENT_ID}]`,
    innerHtmlUpdateTrigger,
    activeItem,
    chatAttachments,
    listMode,
  );

  // 詳細クローズ時のアクション
  const onCloseDetail = React.useCallback(() => {
    onCloseImpl(dispatch);
  }, [dispatch]);

  return {
    onClickAttachment,
    onClickItem,
    onClickAltLink,
    onCloseDetail,
    $bodyRef,
    replyOptions,
  };
};

export default useSplitViewDetail;
