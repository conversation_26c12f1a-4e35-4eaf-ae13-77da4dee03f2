import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { SplitViewDetailMessage } from '../../split-view-detail/SplitViewDetail';
import { SplitViewListMessage } from '../../split-view-list/SplitViewList';
import { fetchSearchRequestResult } from './useInitialDisplay';
import mockMatchMedia from '../../../../../mocks/match-media';
import { ISplitViewState } from '../reducers/splitViewReducer';

jest.mock('../../../../../utilities/environment');

const matchMediaMock = mockMatchMedia();
const current = new Date();

describe('fetchSearchRequestResult', () => {
  const getSearchRequstApiMock = jest.fn();
  const setSearchRequestMock = jest.fn();
  const setAlreadyFetchedMock = jest.fn();
  const onClearSearchInputMock = jest.fn();
  const onInitializeRefsMock = jest.fn();
  const onSwitchLastSearchResultMock = jest.fn();
  const replaceContextsMock = jest.fn();
  const clearCacheMock = jest.fn();
  const dispatchInitialDisplayMock = jest.fn();
  const dispatchSearchResultMock = jest.fn();
  const reportEventMock = jest.fn();
  const onSuccessMock = jest.fn();
  const searchResultsState: ISplitViewState = {
    listView: 'default',
    listMessage: 'INITIAL DISPLAY TEXT',
    list: [{
      id: '23467',
      title: '会社からのお知らせ',
      kind: 'SPO',
      displayDate: '2022-04-12',
      note: '総務',
      properties: {
        listName: '会社からのお知らせ',
        createdDate: '2022-04-12',
        editLink: '9u90j3f9ghjp-jgr3q',
        listId: '9u90j3f9ghjp-jgr3q',
        listUrl: 'http.hogehoge',
        siteUrl: 'http.hogehoge',
        hasAttachments: true,
        spoAttachments: [{
          id: '',
          extension: '',
          title: 'hogehoge',
          url: 'hogehoge',
          icon: 'pdf',
        }],
      },
    }],
    activeId: 'aas122',
    detailView: '',
    detailMessage: '',
    detail: undefined,
    context: {
      sort: [{
        key: 'displayDate',
        order: 'asc',
        priority: 123,
      }],
      filter: [{
        key: 'displayDate',
        option: 78,
      }],
      timestamp: current,
    },
    inlineMailAttachments: [],
    chatAttachments: [],
  };

  beforeEach(() => {
    getSearchRequstApiMock.mockClear();
    setSearchRequestMock.mockClear();
    setAlreadyFetchedMock.mockClear();
    onClearSearchInputMock.mockClear();
    onInitializeRefsMock.mockClear();
    onSwitchLastSearchResultMock.mockClear();
    replaceContextsMock.mockClear();
    clearCacheMock.mockClear();
    dispatchInitialDisplayMock.mockClear();
    dispatchSearchResultMock.mockClear();
    reportEventMock.mockClear();
    onSuccessMock.mockClear();
    matchMediaMock.mockClear().mockReturnValue({ matches: true });
  });

  describe('when the search function is unavailable', () => {
    it('should do nothing', async () => {
      await fetchSearchRequestResult(
        undefined,
        setSearchRequestMock,
        setAlreadyFetchedMock,
        onClearSearchInputMock,
        onInitializeRefsMock,
        onSwitchLastSearchResultMock,
        '',
        clearCacheMock,
        dispatchInitialDisplayMock,
        dispatchSearchResultMock,
        reportEventMock,
        { current: false },
        jest.fn(),
        searchResultsState,
      );
      expect(dispatchInitialDisplayMock).toBeCalledTimes(0);
      expect(reportEventMock).toBeCalledTimes(0);
    });
  });

  describe('when the search function resolves', () => {

    describe('when the isUnmounted.current is true', () => {
      it('should cancel dispatch', async () => {
        getSearchRequstApiMock.mockResolvedValueOnce([]);
        await fetchSearchRequestResult(
          getSearchRequstApiMock,
          setSearchRequestMock,
          setAlreadyFetchedMock,
          onClearSearchInputMock,
          onInitializeRefsMock,
          onSwitchLastSearchResultMock,
          '',
          clearCacheMock,
          dispatchInitialDisplayMock,
          dispatchSearchResultMock,
          reportEventMock,
          { current: true },
          jest.fn(),
          searchResultsState,
        );
        expect(reportEventMock).toBeCalledTimes(0);
        expect(dispatchInitialDisplayMock).toBeCalledTimes(0);
      });
    });
  });

  describe('when the search function rejects', () => {
    it('should dispatch SET_ERROR', async () => {
      getSearchRequstApiMock.mockRejectedValueOnce('');
      await fetchSearchRequestResult(
        getSearchRequstApiMock,
        setSearchRequestMock,
        setAlreadyFetchedMock,
        onClearSearchInputMock,
        onInitializeRefsMock,
        onSwitchLastSearchResultMock,
        '',
        clearCacheMock,
        dispatchInitialDisplayMock,
        dispatchSearchResultMock,
        reportEventMock,
        { current: false },
        jest.fn(),
        searchResultsState,
      );
      expect(reportEventMock).toBeCalledTimes(1);
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: new Error(''),
      });
      expect(dispatchInitialDisplayMock).toBeCalledTimes(2);
      expect(dispatchInitialDisplayMock).toBeCalledWith({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
      expect(dispatchInitialDisplayMock).toBeCalledWith({
        type: 'SET_NO_ITEMS',
        payload: {
          listMessage: SplitViewListMessage.INITIAL_DISPLAY,
          detailMessage: SplitViewDetailMessage.BLANK,
        },
      });
    });
  });
});
