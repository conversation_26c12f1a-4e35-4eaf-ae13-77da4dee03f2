import React, { <PERSON>spatch, SetStateAction } from 'react';
import { renderHook } from '@testing-library/react-hooks';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../split-view-detail/SplitViewDetail';
import { SplitViewListMessage, SplitViewListView } from '../../split-view-list/SplitViewList';
import { UseComponentInitReturnType } from '../../../../../hooks/utilities/useComponentInitUtility';
import useSearchResultList, { restraintToSubmitWhileLoading, checkListViewLoadingState } from './useSearchResultList';
import useSplitViewReducers from './useSplitViewReducers';
import { ISearchRequestResult } from '../../../../../types/ISearchRequestResult';
import {
  ISplitViewState, SplitViewAction,
} from '../reducers/splitViewReducer';
import { UseSharePointApiReturnType } from '../../../../../hooks/accessors/useSharePointApiAccessor';
import { createListApiSingleEntry, createSearchRequestResultReturnValue } from '../../../../../utilities/test';
import { UseSearchRequestApiReturnType } from '../../../../../hooks/accessors/useSearchRequestApiAccessor';
import { ListModeType } from '../../types/ListMode';
import { UseSearchResultRepositoryReturnType } from '../../../../../hooks/accessors/useSearchResultRepositoryAccessor';
import mockMatchMedia from '../../../../../mocks/match-media';
import useGraphApiRequestQueue from '../../../../../hooks/behaviors/useGraphApiRequestQueue';
import useSplitViewHighlighter from './useSplitViewHighlighter';
import { SearchModeType } from '../../types/SearchListMode';
import useChatList from './useChatList';
import { IUserApiAccessorReturn } from '../../../../../hooks/accessors/useUserApiAccessor';

jest.mock('../../../../../utilities/environment');

const onSubmitSearchInputImplMock = jest.fn();
jest.mock('../hooks/use-search-result-list/functions', () => ({
  __esModule: true,
  onSubmitSearchInputImpl: onSubmitSearchInputImplMock,
}));

const matchMediaMock = mockMatchMedia();

const processMock = jest.fn();

// TODO: useSearchResultListのUTを無理に書こうとするのではなく、Functionsに実装を分割していき細かな処理毎にUTを書いて全体の動作を担保する
describe.skip('useSearchResultList', () => {
  beforeAll(() => {
    matchMediaMock.mockReturnValue({ matches: true });
  });
  const searchInputValue = '';
  const conditionKeywords: string[] = [];

  // フックに渡す変数をMock
  const { result: splitViewReducersResult } = renderHook(() => useSplitViewReducers());
  const { result: useChatListRecuderResult } = renderHook(() => useChatList());
  const { result: useSplitViewHighlighterResult } = renderHook(() => useSplitViewHighlighter());
  const {
    setSearchWords,
    hideHighlight,
  } = useSplitViewHighlighterResult.current;

  const {
    listModeStateReturn,
    searchRequestStateReturn,
    chatModeListReducerReturn,
    syncBookmarkStateReturn,
  } = splitViewReducersResult.current;

  const { listSearchModeStateReturn } = useChatListRecuderResult.current;
  const oid = 'test';

  // mock useReducer
  const state: ISplitViewState = {
    list: [],
    listView: SplitViewListView.LOADING,
    listMessage: SplitViewListMessage.BLANK,
    activeId: '',
    detail: undefined,
    detailView: SplitViewDetailView.DEFAULT,
    detailMessage: SplitViewDetailMessage.BLANK,
    context: { sort: [], filter: [] },
    inlineMailAttachments: [],
    chatAttachments: [],
  };

  const dispatch = jest.fn();
  const report = jest.fn();

  const useComponentInitReturn = [
    { current: false },
    [report],
  ] as unknown as UseComponentInitReturnType;

  const listOrigin = [
    createListApiSingleEntry({ GUID: 'abc', Modified: '2050-01-01T00:00:00.000Z' }),
    createListApiSingleEntry({ GUID: 'efg', Modified: '2050-01-01T00:00:00.000Z' }),
    createListApiSingleEntry({ GUID: 'hij', Modified: '2050-01-01T00:00:00.000Z' }),
    createListApiSingleEntry({ GUID: '123', Modified: '2000-01-01T00:00:00.000Z' }),
  ];
  const fetchSPOList = jest.fn().mockResolvedValue({ value: listOrigin });
  const fetchGroupId = jest.fn().mockResolvedValue({ });
  const useSharePointApiReturn = [
    undefined, fetchSPOList,
  ] as unknown as UseSharePointApiReturnType;
  const useUserApiReturn = [
    fetchGroupId,
  ]as unknown as IUserApiAccessorReturn;
  const clearCacheMock = jest.fn();
  const useSearchResultRepositoryReturn = {
    clearCacheMock,
  } as unknown as UseSearchResultRepositoryReturnType;

  const postSearchRequestApi = jest.fn();
  const getSearchRequestApi = jest.fn().mockResolvedValue(
    { value: createSearchRequestResultReturnValue },
  );
  const useSearchRequestApiReturn = {
    postSearchRequestApi, getSearchRequestApi,
  } as unknown as UseSearchRequestApiReturnType;
  const mockOnSwitchLastSearchResult = jest.fn();
  const cancellationRefMock = { current: false };

  beforeEach(() => {
    dispatch.mockClear();
    mockOnSwitchLastSearchResult.mockClear();
    onSubmitSearchInputImplMock.mockClear();
  });

  type ParamsType = [
    [listMode: ListModeType, setListMode: (next: ListModeType) => void],
    [
      searchRequest: ISearchRequestResult | null,
      setSearchRequest: (result: ISearchRequestResult | null) => void
    ],
    [ISplitViewState, Dispatch<SplitViewAction>],
    [ISplitViewState, Dispatch<SplitViewAction>],
    UseComponentInitReturnType,
    UseSharePointApiReturnType,
    IUserApiAccessorReturn,
    Dispatch<SetStateAction<string | undefined>>,
    () => void,
    UseSearchRequestApiReturnType,
    UseSearchResultRepositoryReturnType,
    ReturnType<typeof useGraphApiRequestQueue>,
    (start: boolean) => void,
    React.MutableRefObject<boolean>,
    [searchListMode: SearchModeType, setSearchListMode:(nextMode: SearchModeType) => void],
    string,
  ];

  function hookParams(): ParamsType {
    return [
      listModeStateReturn,
      searchRequestStateReturn,
      chatModeListReducerReturn,
      [state, dispatch] as [ISplitViewState, Dispatch<SplitViewAction>],
      useComponentInitReturn,
      useSharePointApiReturn,
      useUserApiReturn,
      setSearchWords,
      hideHighlight,
      useSearchRequestApiReturn,
      useSearchResultRepositoryReturn,
      { process: processMock },
      syncBookmarkStateReturn[1],
      cancellationRefMock,
      listSearchModeStateReturn,
      oid,
    ];
  }

  function getHook() {
    const params = hookParams();
    return renderHook((para) => useSearchResultList(...para), {
      initialProps: params,
    });
  }

  it.skip('should return searchInputValue', async () => {
    const { result } = await getHook();
    expect(result.current.searchInputValue).toStrictEqual(searchInputValue);
  });

  it.skip('should return lastSubmittedWords', async () => {
    const { result } = await getHook();
    expect(result.current.lastSubmittedWords).toStrictEqual(conditionKeywords);
  });
});

describe('checkCancelSubmission', () => {
  const setIsLoadingMock = jest.fn();
  const submitMock = jest.fn();
  beforeEach(() => {
    setIsLoadingMock.mockClear();
    submitMock.mockClear();
  });
  it('should set true when isLoading is false', () => {
    restraintToSubmitWhileLoading(false, setIsLoadingMock, submitMock);

    expect(setIsLoadingMock).toHaveBeenCalledWith(true);
    expect(submitMock).toHaveBeenCalled();
  });
  it('should return true when isLoading is true', () => {
    restraintToSubmitWhileLoading(true, setIsLoadingMock, submitMock);

    expect(setIsLoadingMock).not.toHaveBeenCalled();
    expect(submitMock).not.toHaveBeenCalled();
  });

  // isLoadingが変更されていることを確認するためのテスト用フック
  const useSubmitTestMock = () => {
    const [isLoading, setIsLoading] = React.useState<boolean>(false);
    const submit = React.useCallback(
      (submitImpl: () => void) => restraintToSubmitWhileLoading(
        isLoading, setIsLoading, submitImpl,
      ),
      [isLoading],
    );
    return { submit, isLoading };
  };
  it('should call submit just once', () => {

    const { result } = renderHook(() => useSubmitTestMock());

    result.current.submit(submitMock);
    expect(submitMock).toHaveBeenCalledTimes(1);
    expect(result.current.isLoading).toBe(true);
    result.current.submit(submitMock);

    expect(submitMock).toHaveBeenCalledTimes(1);
    expect(result.current.isLoading).toBe(true);
  });
});

describe('checkListViewLoadingState', () => {
  const setIsLoadingMock = jest.fn();
  beforeEach(() => {
    setIsLoadingMock.mockClear();
  });
  it.each([
    { listview: SplitViewListView.DEFAULT, given: false, callTimes: 1 },
    { listview: SplitViewListView.ERROR, given: false, callTimes: 1 },
    { listview: SplitViewListView.LOADING, given: true, callTimes: 1 },
    { listview: SplitViewListView.ON_INTERVAL, callTimes: 0 },
    { listview: SplitViewListView.SEARCH_COMPLETED, given: false, callTimes: 1 },
  ])('should set $given, call $callTimes times when listview is $listview', ({ listview, given, callTimes }) => {
    checkListViewLoadingState(listview, setIsLoadingMock);
    expect(setIsLoadingMock).toHaveBeenCalledTimes(callTimes);
    if (callTimes > 0) {
      expect(setIsLoadingMock).toHaveBeenCalledWith(given);
    }
  });
});
