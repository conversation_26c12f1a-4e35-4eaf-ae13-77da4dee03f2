import * as React from 'react';
import { SearchListMode, SearchModeType } from '../../types/SearchListMode';

type UseChatListReturnType = {
  listSearchModeStateReturn:
   [searchMode: SearchModeType,
    setSearchMode: (next: SearchModeType) => void],
  setSearchListMode: (nextMode: SearchModeType) => void;
};

/**
 * manage Chat list features
 * @param mainReducerReturn
 * @param useComponentInitReturn
 * @param useSpoListReturn
 * @param readDict
 * @param usePageViewFeatureReturn
 */
const useChatList = (): UseChatListReturnType => {

  // state of tab selection
  const listSearchModeStateReturn = React.useState<SearchModeType>(SearchListMode.DEFAULT);

  /**
    * switch searchListMode and report what was clicked
    */
  const setSearchListMode = React.useCallback((
    nextMode: SearchModeType,
  ) => {
    setSearchListMode(nextMode);
  }, []);

  return {
    listSearchModeStateReturn,
    setSearchListMode,
  };
};

export default useChatList;
