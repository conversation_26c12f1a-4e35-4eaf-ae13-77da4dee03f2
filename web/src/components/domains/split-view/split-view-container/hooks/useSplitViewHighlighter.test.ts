import '@testing-library/jest-dom';
import { renderHook, act } from '@testing-library/react-hooks';
import useSplitViewHighlighter from './useSplitViewHighlighter';

describe('useSplitViewHighlighter', () => {

  it('should return default values', () => {
    const { result } = renderHook(() => useSplitViewHighlighter());
    const { searchWords } = result.current;
    expect(searchWords).toBeUndefined();
  });

  describe('when setSearchWords called', () => {
    it('should change searchWords', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useSplitViewHighlighter());
      const { setSearchWords } = result.current;
      await act(async () => {
        // setSearchWordsはstringを受け取ってそのまま返す
        setSearchWords('a');
        await waitForNextUpdate();
        const { searchWords } = result.current;
        expect(searchWords).toStrictEqual('a');
      });
    });
  });
});
