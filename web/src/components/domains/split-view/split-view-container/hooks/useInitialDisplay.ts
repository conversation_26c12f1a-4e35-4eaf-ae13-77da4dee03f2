import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import * as React from 'react';
import dayjs from 'dayjs';
import useComponentInitUtility from '../../../../../hooks/utilities/useComponentInitUtility';
import { SplitViewDetailMessage } from '../../split-view-detail/SplitViewDetail';
import { SplitViewListMessage } from '../../split-view-list/SplitViewList';
import {
  ISplitViewState,
  SplitViewDispatch,
  SplitViewReducerReturn,
} from '../reducers/splitViewReducer';
import useSearchRequestApiAccessor, { GetSearchRequestApi } from '../../../../../hooks/accessors/useSearchRequestApiAccessor';
import { ISharePointListsResponse } from '../../../../../types/ISharePointListsResponse';
import { ISearchRequestResult } from '../../../../../types/ISearchRequestResult';
import useSearchResultRepositoryAccessor, { ClearCache } from '../../../../../hooks/accessors/useSearchResultRepositoryAccessor';
import useSearchResultList from './useSearchResultList';
import { CacheStatus } from '../../../../../types/IGeraniumAttaneDB';
import { parseContext } from '../../../../../utilities/transform';
import UseRemoteBookmarkFeatureReturn from '../../../../../hooks/features/useRemoteBookmarkFeature';

export type FetchSPOListItems = (
  baseUrl: string, listGUID: string,
) => Promise<ISharePointListsResponse>;

/**
 * APIから検索要求を取得する
 * @param activeId
 * @param fetchList
 * @param dispatchInitialDisplay
 * @param reportEvent ログ送信用のコールバック
 * @param isUnmounted アンマウント判定フラグ
 * @param updatePageViewsOfPosts
 * @param onSuccess 成功時コールバック
 */
export async function fetchSearchRequestResult(
  getSearchRequestApi: GetSearchRequestApi | undefined,
  setSearchRequest: (result: ISearchRequestResult | null) => void,
  setAlreadyFetched: React.Dispatch<React.SetStateAction<boolean>>,
  onClearSearchInput: () => void,
  onInitializeRefs: (newReqId: string) => void,
  onSwitchLastSearchResult: () => void,
  cachedReqId: string | undefined,
  clearCache: ClearCache | undefined,
  dispatchInitialDisplay: SplitViewDispatch,
  dispatchSearchResults: SplitViewDispatch,
  reportEvent: EventReporter,
  isUnmounted: React.MutableRefObject<boolean>,
  setIsSearchResultContextUpdated: (value: boolean) => void,
  searchResultsState: ISplitViewState,
): Promise<void> {

  if (!getSearchRequestApi || !clearCache) return Promise.resolve();
  // 結果取得前にtrueにする(呼び出し失敗時にはエラー画面に遷移する)
  setAlreadyFetched(true);

  // 検索要求を取得
  const remoteSearchResult = await getSearchRequestApi()
    .catch((reason) => {
      reportEvent({
        type: EventReportType.SYS_ERROR,
        name: 'API_REQUEST_FAIL',
        error: new Error(reason),
      });

      if (isUnmounted.current) return;
      dispatchInitialDisplay({
        type: 'SET_ERROR',
        payload: SplitViewListMessage.API_REQUEST_FAIL,
      });
    });

  if (isUnmounted.current === true) return Promise.resolve();

  // 検索要求が取得できない場合は状態をリセットし初期表示メッセージを表示する
  if (!remoteSearchResult || !remoteSearchResult.reqId) {
    setSearchRequest(null);
    onClearSearchInput();
    clearCache();
    dispatchInitialDisplay({
      type: 'SET_NO_ITEMS',
      payload: {
        listMessage: SplitViewListMessage.INITIAL_DISPLAY,
        detailMessage: SplitViewDetailMessage.BLANK,
      },
    });
  } else {
    if (cachedReqId && remoteSearchResult.reqId !== cachedReqId) {
      await clearCache();
      onInitializeRefs(remoteSearchResult.reqId);
      onSwitchLastSearchResult();
    }
    if (remoteSearchResult?.context) {
      const context = parseContext(remoteSearchResult.context);
      if (!searchResultsState.context?.timestamp
        || dayjs(context.timestamp).isAfter(searchResultsState.context?.timestamp)) {
        dispatchSearchResults({
          type: 'REPLACE_CONTEXT',
          payload: {
            context,
          },
        });
      }
    }
    setSearchRequest(remoteSearchResult);
  }
  setIsSearchResultContextUpdated(true);
  return Promise.resolve();
}

/**
 * manage initial display list features
 * @param initialDisplayReducerReturn
 * @param useComponentInitReturn
 * @param useSpoListReturn
 * @param readDict
 * @param usePageViewFeatureReturn
 */
const useInitialDisplay = (
  initialDisplayReducerReturn: SplitViewReducerReturn,
  searchRequestStateReturn: [
    ISearchRequestResult | null, (result: ISearchRequestResult | null) => void
  ],
  useComponentInitReturn: ReturnType<typeof useComponentInitUtility>,
  useSearchRequestApiReturn: ReturnType<typeof useSearchRequestApiAccessor>,
  useSearchResultRepositoryReturn: ReturnType<typeof useSearchResultRepositoryAccessor>,
  useSearchResultListReturn: ReturnType<typeof useSearchResultList>,
  searchResultListReducerReturn: [ISplitViewState, SplitViewDispatch],
  setIsSearchResultContextUpdated: (value: boolean) => void,
  searchResultsState: ISplitViewState,
  useRemoteBookmarkResults: ReturnType<typeof UseRemoteBookmarkFeatureReturn>,
): void => {
  const [searchRequest, setSearchRequest] = searchRequestStateReturn;
  // 各画面StateのDispatherを取得
  const [, dispatchInitialDisplay] = initialDisplayReducerReturn;
  const [, dispatchSearchResults] = searchResultListReducerReturn;

  // get values from other hooks
  const [isUnmounted, [reportEvent], , , , , , [performanceMetrics]] = useComponentInitReturn;
  const { getSearchRequestApi } = useSearchRequestApiReturn;
  const {
    isInitialized: reposInitialized,
    searchRequestCache,
    clearCache,
    // replaceCacheContext,
  } = useSearchResultRepositoryReturn;
  const {
    onClearSearchInput,
    onInitializeRefs,
    onSwitchLastSearchResult,
  } = useSearchResultListReturn;

  const { fetchRemoteBookmarks } = useRemoteBookmarkResults;

  const [alreadyFetched, setAlreadyFetched] = React.useState(false);
  const [alreadyUsedCache, setAlreadyUsedCache] = React.useState(false);

  React.useEffect(() => {
    if (!reposInitialized) return;

    /**
     * 検索要求のキャッシュが存在している、かつまだAPIから取得した検索要求がステートに設定されていない場合は
     * キャッシュを検索要求のステートに設定する
     */
    if (!alreadyUsedCache
      && searchRequestCache
      && searchRequestCache.result
      && searchRequestCache.result.reqId
      && !searchRequest?.reqId
    ) {
      if (searchRequestCache.status !== CacheStatus.ERROR) {
        performanceMetrics.current.viewDetailsFromCache.isAvailable = true;
        onInitializeRefs(searchRequestCache.result.reqId);
        setSearchRequest(searchRequestCache.result);
      } else {
        if (!clearCache) return;
        clearCache();
      }
      setAlreadyUsedCache(true);
    }
    if (alreadyFetched) return;
    fetchSearchRequestResult(
      getSearchRequestApi,
      setSearchRequest,
      setAlreadyFetched,
      onClearSearchInput,
      onInitializeRefs,
      onSwitchLastSearchResult,
      searchRequestCache?.result?.reqId,
      clearCache,
      dispatchInitialDisplay,
      dispatchSearchResults,
      reportEvent,
      isUnmounted,
      setIsSearchResultContextUpdated,
      searchResultsState,
    );
  }, [
    reposInitialized,
    getSearchRequestApi,
    setSearchRequest,
    onClearSearchInput,
    onInitializeRefs,
    onSwitchLastSearchResult,
    dispatchInitialDisplay,
    dispatchSearchResults,
    reportEvent,
    searchRequest,
    searchRequestCache,
    clearCache,
    isUnmounted,
    alreadyUsedCache,
    alreadyFetched,
    setIsSearchResultContextUpdated,
    searchResultsState,
    fetchRemoteBookmarks,
    performanceMetrics,
  ]);
};

export default useInitialDisplay;
