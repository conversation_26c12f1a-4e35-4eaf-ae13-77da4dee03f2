import * as React from 'react';
import { Dispatch } from 'react';
import useHighlighter<PERSON>ehavior from '../../../../../hooks/behaviors/useHighlighterBehavior';

type UseSplitViewHighlighterReturnType = {
  searchWords: string | undefined,
  setSearchWords: Dispatch<React.SetStateAction<string | undefined>>,
  showListHighlight: () => void,
  showDetailHighlight: () => void,
  hideHighlight: () => Promise<void>,
  hideDetailHighlight: () => Promise<void>,
  hideListHighlight: () => Promise<void>,
}

/**
 * 検索結果のハイライト機能
 */
const useSplitViewHighlighter = (): UseSplitViewHighlighterReturnType => {
  // 検索キーワード(類語込み)
  const [searchWords, setSearchWords] = React.useState<string | undefined>(undefined);

  // 一覧用
  const listSelector = React.useMemo(() => '.split-view-list-single-title, .split-view-list-single-spolist-name', []);
  const useHighlighterListResults = useHighlighterBehavior(listSelector, searchWords, true);
  const [showListHighlight, hideListHighlight] = useHighlighterListResults;
  // 詳細用
  const detailSelector = React.useMemo(() => '.chat-subject, .split-view-detail-header-title, .split-view-detail-header-category, .split-view-detail-highlight-object, .split-view-detail-body-content, .split-view-detail-attachments-item', []);
  const useHighlighterDetailResults = useHighlighterBehavior(detailSelector, searchWords, true);
  const [showDetailHighlight, hideDetailHighlight] = useHighlighterDetailResults;

  // 両方のキーワードハイライトを解除
  const hideHighlight = React.useCallback(() => Promise.all([
    hideListHighlight(),
    hideDetailHighlight(),
  ]).then(() => Promise.resolve()), [hideListHighlight, hideDetailHighlight]);

  return {
    searchWords,
    setSearchWords,
    hideHighlight,
    showDetailHighlight,
    showListHighlight,
    hideDetailHighlight,
    hideListHighlight,
  };
};

export default useSplitViewHighlighter;
