import { Dispatch } from 'react';
import type { ISplitViewDetail } from '../../types/ISplitViewDetail';
import {
  SplitViewListMessage,
  SplitViewListMessageType, SplitViewListView, SplitViewListViewType,
} from '../../split-view-list/SplitViewList';
import {
  SplitViewDetailMessage,
  SplitViewDetailMessageType,
  SplitViewDetailView,
  SplitViewDetailViewType,
} from '../../split-view-detail/SplitViewDetail';
import { ISplitViewListSingle } from '../../types/ISplitViewListSingle';
import { IContext, FilterOption, ISortOrder } from '../../../../../types/IContext';
import { DataSourceKindType } from '../../../../../types/DataSourceKind';
import { IMailAttachment } from '../../types/IMailAttachment';
import { IChatAttachment } from '../../types/IChatAttachment';

// type of state object
export interface ISplitViewState {
  listView: SplitViewListViewType,
  listMessage: SplitViewListMessageType,
  list: ISplitViewListSingle[],
  activeId: string,
  detailView: SplitViewDetailViewType,
  detailMessage: SplitViewDetailMessageType,
  detail: ISplitViewDetail | undefined,
  context: IContext,
  inlineMailAttachments: IMailAttachment[],
  chatAttachments: IChatAttachment[],
}

/**
 * create an initial state
 */
export function initSplitViewState(): ISplitViewState {
  return {
    // list part
    listView: SplitViewListView.LOADING,
    listMessage: SplitViewListMessage.BLANK,
    list: [],
    activeId: '',

    // detail part
    detailView: SplitViewDetailView.LOADING,
    detailMessage: SplitViewDetailMessage.BLANK,
    detail: undefined,
    context: { sort: [], filter: [] },
    inlineMailAttachments: [],
    chatAttachments: [],
  };
}
export function initSplitViewStateForAI(): ISplitViewState {
  return {
    // list part
    listView: SplitViewListView.SEARCH_COMPLETED,
    listMessage: SplitViewListMessage.INITIAL_DISPLAY,
    list: [],
    activeId: '',

    // detail part
    detailView: SplitViewDetailView.DEFAULT,
    detailMessage: SplitViewDetailMessage.BLANK,
    detail: undefined,
    context: {
      sort: [],
      filter: [{ key: 'displayDate', option: 6 }],
    },
    inlineMailAttachments: [],
    chatAttachments: [],
  };
}

// types for SET_DATA action
type SetDataAction = { type: 'SET_DATA', payload: Partial<ISplitViewState> };

/**
 * SET_DATAアクションの実装
 * @param state
 * @param action
 */
function setDataImpl(state: ISplitViewState, action: SetDataAction) {
  return {
    ...state,
    ...action.payload,
  };
}

// types for SET_LIST and SET_LIST_WITH_DEFAULT actions
export type SetListActionPayload = { list: ISplitViewListSingle[] };
type SetListAction = { type: 'SET_LIST' | 'SET_LIST_WITH_DEFAULT', payload: SetListActionPayload };

/**
 * SET_LIST, SET_LIST_WITH_DEFAULTアクションの実装
 * @param state
 * @param action
 */
function setListImpl(state: ISplitViewState, action: SetListAction) {
  const overrides: Partial<ISplitViewState> = {
    list: action.payload.list,
    listView: SplitViewListView.ON_INTERVAL,
  };

  // activeIdが存在するときは、現在のactiveIdがlistの内部に存在するかをチェック
  const isActiveIdAvailable = state.activeId
    ? action.payload.list.some((item) => item.id === state.activeId)
    : false;

  // typeがSET_LIST_WITH_DEFAULTのとき、デフォルト値が指定されていない場合は自動選択する
  if (action.type === 'SET_LIST_WITH_DEFAULT' && !isActiveIdAvailable) {
    overrides.activeId = action.payload.list?.[0]?.id ?? '';
  }

  return {
    ...state,
    ...overrides,
  };
}

// types for SET_ERROR action
type SetErrorAction = { type: 'SET_ERROR', payload: SplitViewListMessageType };

/**
 * SET_ERRORアクションの実装
 * @param state
 * @param action
 */
function setErrorImpl(state: ISplitViewState, action: SetErrorAction) {
  return {
    ...state,
    listView: SplitViewListView.ERROR,
    listMessage: action.payload,
  };
}

// types for SET_ACTIVE action
type SetActiveActionPayload = { activeId: string, title: string; kind: DataSourceKindType };
type SetActiveAction = { type: 'SET_ACTIVE', payload: SetActiveActionPayload };

/**
 * SET_ACTIVEアクションの実装
 * @param state
 * @param action
 */
function setActiveActionImpl(state: ISplitViewState, action: SetActiveAction) {
  // 現在と同じ値がセットされている場合は何もしない
  if (state.activeId === action.payload.activeId) return state;

  return {
    ...state,
    activeId: action.payload.activeId,
    detailView: SplitViewDetailView.LOADING,
    detail: {
      // activeIdが変わったら再取得が必要なので、取得済の値をクリアして初期状態に戻す (id, title, kindは事前に分かるのでセット)
      id: action.payload.activeId,
      title: action.payload.title,
      kind: action.payload.kind,
    },
    inlineMailAttachments: [],
    chatAttachments: [],
  } as ISplitViewState;
}

// types for UNSELECT action
type UnselectAction = { type: 'UNSELECT', payload: undefined };

/**
 * UNSELECTアクションの実装
 * @param state
 */
function unselectActionImpl(state: ISplitViewState) {
  return {
    ...state,
    // 非表示にする
    activeId: '',
    detailView: SplitViewDetailView.ERROR,
    detailMessage: SplitViewDetailMessage.NOT_SELECTED,
  };
}

// types for SET_DETAIL_ERROR action
type SetDetailErrorAction = { type: 'SET_DETAIL_ERROR', payload: SplitViewDetailMessageType };

/**
 * SET_DETAIL_ERRORアクションの実装
 * @param state
 * @param action
 */
function setDetailErrorImpl(state: ISplitViewState, action: SetDetailErrorAction) {
  return {
    ...state,
    detailView: SplitViewDetailView.ERROR,
    detailMessage: action.payload,
  };
}

// types for SET_DETAIL action
export type SetDetailActionPayload = {
  detail?: ISplitViewDetail,
};
type SetDetailAction = { type: 'SET_DETAIL', payload: SetDetailActionPayload };

/**
 * SET_DETAILアクションの実装
 * @param state
 * @param action
 */
function setDetailImpl(state: ISplitViewState, action: SetDetailAction) {
  // 現在のactiveIdとdispatchしたい要素のidが違う場合は何もしない
  // (頻繁に切り替えを行なったとき用の対策)
  // TODO:現状の仕様ではGUIDを取得できないため、一旦オフ
  // if (state.activeId !== action.payload.detail?.id) return state;
  if (!state.activeId || !action.payload.detail?.id) return state;
  // 前回とIDが変わっていない場合、更新日時が変更されたときにだけ更新する
  // (同じ詳細を表示したまま2分に一度再取得したとき、ログが重複して送られないための対策)
  const isNoUpdatesForDetails = [
    [state.detail?.id, action.payload.detail.id],
    [state.detail?.displayDate, action.payload.detail.displayDate],
  ].every((diffs) => diffs[0] === diffs[1]);
  if (isNoUpdatesForDetails) return state;

  return {
    ...state,
    detailView: SplitViewDetailView.DEFAULT,
    detail: action.payload.detail ?? state.detail,
  };
}

// types for SET_NO_ITEMS action
type SetNoItemsActionPayload = {
  listMessage: SplitViewListMessageType,
  detailMessage: SplitViewDetailMessageType,
};
type SetNoItemsAction = { type: 'SET_NO_ITEMS', payload: SetNoItemsActionPayload }

/**
 * an implementation of SET_NO_ITEMS
 * @param state
 * @param action
 */
function setNoItemsImpl(state: ISplitViewState, action: SetNoItemsAction) {
  return {
    ...state,
    list: [],
    listView: SplitViewListView.SEARCH_COMPLETED,
    listMessage: action.payload.listMessage,
    activeId: '',
    detail: undefined,
    detailView: SplitViewDetailView.ERROR,
    detailMessage: action.payload.detailMessage,
  };
}

// types for SET_LISTVIEW action
type SetListViewActionPayload = {
  listView: SplitViewListViewType,
  listMessage?: SplitViewListMessageType,
};
type SetListViewAction = { type: 'SET_LISTVIEW', payload: SetListViewActionPayload }

/**
 * an implementation of SET_LISTVIEW
 * @param state
 * @param action
 */
function setListViewImpl(state: ISplitViewState, action: SetListViewAction) {
  return {
    ...state,
    listView: action.payload.listView,
    listMessage: action.payload.listMessage ?? state.listMessage,
  };
}

// types for SET_DETAILVIEW action
type SetDetailViewActionPayload = {
  detailView: SplitViewListViewType,
  detailMessage?: SplitViewListMessageType,
};
type SetDetailViewAction = { type: 'SET_DETAILVIEW', payload: SetDetailViewActionPayload }

/**
 * an implementation of SET_DETAILVIEW
 * @param state
 * @param action
 */
function setDetailViewImpl(state: ISplitViewState, action: SetDetailViewAction) {
  return {
    ...state,
    detailView: action.payload.detailView,
    detailMessage: action.payload.detailMessage ?? state.detailMessage,
  };
}

type SetSortActionPayload = {
  sort: ISortOrder[],
};
type SetSortAction = { type: 'SET_SORT', payload: SetSortActionPayload };
/**
 * 条件変更時には選択記事をリセットする
 */
function setSortImpl(state: ISplitViewState, action: SetSortAction) {
  return {
    ...state,
    activeId: '',
    context: {
      ...state.context,
      sort: action.payload.sort,
      timestamp: new Date(),
    },
  };
}

type SetFilterActionPayload = {
  filter: FilterOption[],
}
type SetFilterAction = { type: 'SET_FILTER', payload: SetFilterActionPayload };
/**
 * 条件変更時には選択記事をリセットする
 */
function setFilterImpl(state: ISplitViewState, action: SetFilterAction) {
  return {
    ...state,
    activeId: '',
    context: {
      ...state.context,
      filter: action.payload.filter,
      timestamp: new Date(),
    },
  };
}

type ReplaceContextActionPayload = {
  context: IContext,
}
type ReplaceContextAction = { type: 'REPLACE_CONTEXT', payload: ReplaceContextActionPayload };
/**
 * 同期時にはRemoteから受け取ったtimestampをそのまま設定したいので
 * デフォルト値としてnew Date()は設定しない
 */
function replaceContextImpl(state: ISplitViewState, action: ReplaceContextAction) {
  return {
    ...state,
    activeId: '',
    context: action.payload.context,
  };
}

type SetInlineMailAttachmentsActionPayload = {
  inlineMailAttachments: IMailAttachment[],
}
type SetInlineMailAttachmentsAction = { type: 'SET_INLINE_MAIL_ATTACHMENTS', payload: SetInlineMailAttachmentsActionPayload };
/**
 * inlineMailAttachmentsはactiveIdが変わった時にリセットされる
 */
function setInlineMailAttachmentsImpl(
  state: ISplitViewState, action: SetInlineMailAttachmentsAction,
) {
  return {
    ...state,
    inlineMailAttachments: action.payload.inlineMailAttachments,
  };
}

type SetChatAttachmentsActionPayload = {
  chatAttachments: IChatAttachment[],
}
type SetChatAttachmentsAction = { type: 'SET_CHAT_ATTACHMENTS', payload: SetChatAttachmentsActionPayload };
/**
 * chatAttachmentsはactiveIdが変わった時にリセットされる
 */
function setChatAttachmentsImpl(
  state: ISplitViewState, action: SetChatAttachmentsAction,
) {
  return {
    ...state,
    chatAttachments: action.payload.chatAttachments,
  };
}

type UpdateItemActionPayload = {
  id: string,
  item: ISplitViewListSingle,
};
type UpdateItemAction = { type: 'UPDATE_ITEM', payload: UpdateItemActionPayload };
function updateItemImpl(state: ISplitViewState, action: UpdateItemAction) {
  const index = state.list.findIndex(((item) => item.id === action.payload.id));
  if (index > 0 && JSON.stringify(state.list[index]) !== JSON.stringify(action.payload.item)) {
    const newList = [...state.list];
    newList[index] = action.payload.item;
    return {
      ...state,
      list: newList,
    };
  }
  return state;

}

// combine all action types for the reducer function
export type SplitViewAction =
  SetDataAction
  | SetListAction
  | SetErrorAction
  | SetActiveAction
  | UnselectAction
  | SetDetailAction
  | SetDetailErrorAction
  | SetNoItemsAction
  | SetListViewAction
  | SetDetailViewAction
  | SetSortAction
  | SetFilterAction
  | ReplaceContextAction
  | SetInlineMailAttachmentsAction
  | SetChatAttachmentsAction
  | UpdateItemAction;
export type SplitViewDispatch = Dispatch<SplitViewAction>;
export type SplitViewReducerReturn = [ISplitViewState, SplitViewDispatch];

/**
 * reducer function
 * @param state
 * @param action
 */
export function splitViewReducer(
  state: ISplitViewState,
  action: SplitViewAction,
): ISplitViewState {

  switch (action.type) {
    case 'SET_DATA':
      return setDataImpl(state, action);
    case 'SET_LIST':
      return setListImpl(state, action);
    case 'SET_LIST_WITH_DEFAULT':
      return setListImpl(state, action);
    case 'SET_ERROR':
      return setErrorImpl(state, action);
    case 'SET_ACTIVE':
      return setActiveActionImpl(state, action);
    case 'UNSELECT':
      return unselectActionImpl(state);
    case 'SET_DETAIL_ERROR':
      return setDetailErrorImpl(state, action);
    case 'SET_DETAIL':
      return setDetailImpl(state, action);
    case 'SET_NO_ITEMS':
      return setNoItemsImpl(state, action);
    case 'SET_LISTVIEW':
      return setListViewImpl(state, action);
    case 'SET_DETAILVIEW':
      return setDetailViewImpl(state, action);
    case 'SET_SORT':
      return setSortImpl(state, action);
    case 'SET_FILTER':
      return setFilterImpl(state, action);
    case 'REPLACE_CONTEXT':
      return replaceContextImpl(state, action);
    case 'SET_INLINE_MAIL_ATTACHMENTS':
      return setInlineMailAttachmentsImpl(state, action);
    case 'SET_CHAT_ATTACHMENTS':
      return setChatAttachmentsImpl(state, action);
    case 'UPDATE_ITEM':
      return updateItemImpl(state, action);
    default:
      return state;
  }
}
