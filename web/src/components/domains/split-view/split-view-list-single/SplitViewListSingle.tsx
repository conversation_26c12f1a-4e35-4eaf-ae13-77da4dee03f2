import * as React from 'react';
import PropTypes from 'prop-types';
import { PaperclipIcon, Skeleton, Text } from '@fluentui/react-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';
import { getYearMonthDay } from '../../../../utilities/date';
import { isEnterKeydownOrClick, ReactEvent } from '../../../../utilities/event';
import { blankToDash } from '../../../../utilities/text';
import { ValueOf } from '../../../../utilities/type';
import { ISplitViewListSingle } from '../types/ISplitViewListSingle';
import BookmarkStar from '../../../commons/atoms/bookmark-star/BookmarkStar';

// CSS
import './SplitViewListSingle.scss';
import { convertSplitViewItemToBookmarkItem, isSpoProperties } from '../../../../utilities/transform';
import DataSourceIcon from '../../../commons/atoms/datasource-icon/DataSourceIcon';

export const SplitViewListSingleView = {
  LOADING: 'loading',
  DEFAULT: 'default',
};
export type SplitViewListSingleViewType = ValueOf<typeof SplitViewListSingleView>;

export interface ISplitViewListSingleProps {
  className?: string;
  view?: SplitViewListSingleViewType;
  onClick?: (item: ISplitViewListSingle) => void;
  onClickBookmark?: (item: ISplitViewListSingle, toBe: boolean) => void;
  item?: ISplitViewListSingle;
  isBookmarked?: boolean;
  // chatMode?: boolean;
}

const SplitViewListSingle: React.FC<ISplitViewListSingleProps> = (props) => {
  const {
    className,
    view,
    item,
    isBookmarked,
    onClick,
    onClickBookmark,
    // chatMode = false,
  } = props;

  const isLoading = React.useMemo(() => (
    view === SplitViewListSingleView.LOADING
  ), [view]);

  // クリック/Enter押下時のコールバック
  const handleLinkClick = React.useCallback((e: ReactEvent) => {
    e.preventDefault();
    if (!item || !onClick || !isEnterKeydownOrClick(e)) return;
    // 値が更新されたときに画面がチラつくのを防ぐ目的で一部渡してないプロパティがある
    onClick({
      id: item?.id ?? '',
      title: item?.title ?? '',
      note: item.note ?? '',
      displayDate: item?.displayDate ?? '',
      kind: item?.kind ?? 'Other',
      properties: {
        ...(isSpoProperties(item?.properties) ? {
          listName: item.properties?.listName,
          createdDate: item?.properties?.createdDate,
        } : {}),
        hasAttachments: item?.properties?.hasAttachments ?? false,
      },
    });
  }, [onClick, item]);

  const displayDate = React.useMemo(() => {
    if (item?.displayDate) {
      return blankToDash(getYearMonthDay(item?.displayDate ?? ''));
    }
    return blankToDash(undefined);
  }, [item]);

  // マージされたCSSクラス名
  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('split-view-list-single', className);
    return mergedClassName(step1, isBookmarked ? '' : 'is-bookmarked');
  }, [className, isBookmarked]);

  // お気に入りボタンクリック
  const handleOnClickBookmark = React.useCallback(
    (toBe: boolean, e: React.SyntheticEvent<HTMLElement>) => {
      e.preventDefault();
      // onClickのある要素の内部に来るのでstopPropagationでイベントバブリングを止める
      e.stopPropagation();
      if (!onClickBookmark) return;
      onClickBookmark(convertSplitViewItemToBookmarkItem(item), toBe);
    }, [item, onClickBookmark],
  );

  return (
    <div className={rootClassName}>
      {/* ローディング */}
      {isLoading && (
        <div className="split-view-list-single-content">
          <Skeleton
            className="split-view-list-single-skeleton"
            animation="wave"
          >
            <div className="split-view-list-single-row split-view-list-single-row-1">
              <Skeleton.Text size="medium" className="split-view-list-single-title" />
              <Skeleton.Text size="medium" className="split-view-list-single-icons" />
            </div>

            <div className="split-view-list-single-row split-view-list-single-row-2">
              <Skeleton.Text size="small" className="split-view-list-single-text" />
              <Skeleton.Text size="small" className="split-view-list-single-date" />
            </div>
          </Skeleton>
        </div>
      )}
      {/* 通常表示 */}
      {!isLoading && (
        <a
          className="split-view-list-single-content split-view-list-single-link split-view-list-single-rows"
          onClick={handleLinkClick}
          onKeyDown={handleLinkClick}
          href={isSpoProperties(item?.properties) ? item?.properties?.editLink ?? '' : ''}
        >
          {/* un-read marker */}
          <div className="split-view-list-single-icon-read" />

          <div className="split-view-list-single-row split-view-list-single-row-1">
            {/* title */}
            {item?.kind && <DataSourceIcon className="split-view-list-single-date-source-icon" kind={item.kind} />}
            <Text
              className="split-view-list-single-title"
              as="h3"
              size="medium"
              weight="regular"
              content={blankToDash(item?.title)}
            />
            {/* (PC) icons on hovering */}
            {/* (SP) not visible */}
            <div className="split-view-list-single-icons split-view-list-single-icons-on-not-hover">
              {/* 添付split-view-list-single-linkファイルアイコン */}
              {item?.properties?.hasAttachments === true && <PaperclipIcon className="split-view-list-single-icon-attachment" />}
              {/* お気に入り状態アイコン・クリック不可・お気に入り済のときにだけ表示 */}
              { isBookmarked && <BookmarkStar className="split-view-list-single-star" isBookmarked={isBookmarked} />}
            </div>

            {/* (PC)ホバー時の表示 */}
            {/* (SP)常時表示 */}
            <div className="split-view-list-single-icons split-view-list-single-icons-on-hover">
              {/* お気に入り状態アイコン・クリック可能 */}
              <BookmarkStar className="split-view-list-single-star" isBookmarked={isBookmarked} onClick={handleOnClickBookmark} />
            </div>
          </div>

          <div className="split-view-list-single-row split-view-list-single-row-2 split-view-list-single-text">
            {/* SPOリスト名称 */}
            <Text
              className="split-view-list-single-spolist-name"
              size="small"
              content={blankToDash(item?.note)}
            />

            {/* (PC) not visible */}
            {/* (SP) attachment icon */}
            {item?.properties?.hasAttachments === true && <PaperclipIcon className="split-view-list-single-icon-attachment-sp" />}

            {/* updated date */}
            <Text
              className="split-view-list-single-updated"
              size="small"
              content={displayDate}
            />
          </div>
        </a>
      )}
    </div>
  );
};

SplitViewListSingle.propTypes = {
  className: PropTypes.string,
  view: PropTypes.string,
  // eslint-disable-next-line react/forbid-prop-types
  item: PropTypes.any,
  isBookmarked: PropTypes.bool,
  onClick: PropTypes.func,
};

SplitViewListSingle.defaultProps = {
  className: undefined,
  view: SplitViewListSingleView.LOADING,
  item: {
    id: '',
    title: '',
    note: '',
    kind: 'Other',
    displayDate: '',
    properties: {},
  },
  isBookmarked: false,
  onClick: undefined,
  onClickBookmark: undefined,
  // chatMode: false,
};

export default React.memo(SplitViewListSingle);
