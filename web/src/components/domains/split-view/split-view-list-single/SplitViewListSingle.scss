@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.split-view-list-single {
  position: relative;

  &.is-unread {
    .split-view-list-single-icon-read {
      display: block;

      @include media-sp {
        @include fix-ios-redraw-issue;
      }
    }
  }

  &.is-bookmarked {
    .split-view-list-single-icons-on-hover {
      @include media-sp {
        @include fix-ios-redraw-issue;
      }
    }
  }
}

.split-view-list-single-content {
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: flex-start;
  min-height: 53px;
  padding-left: 24px;
  padding-right: var(--length-margin-horizontal);

  @include media-sp {
    user-select: none;
    padding-left: 24px;
    padding-right: var(--length-margin-horizontal-sp);
  }

  &.is-unread {
    .split-view-list-single-icon-read {
      display: block;
    }
  }
}

.split-view-list-single-link {
  color: var(--color-guide-default-foreground);

  &:link {
    text-decoration: none;
  }

  &:focus-visible {
    outline: 1px solid var(--color-guide-default-foreground);
    outline-offset: -1px;
  }

  &:link:hover {
    @include media-pc {
      background-color: var(--color-guide-brand-background-1);
    }
  }

  &:link:active {
    @include media-pc {
      background-color: var(--color-guide-brand-foreground-active);
    }
  }

  .split-view-list-single.is-active & {
    background-color: var(--color-guide-brand-foreground-active);

    &:link:hover {
      @include media-pc {
        background-color: var(--color-guide-brand-foreground-active);
      }
    }
  }

  &:hover,
  &.is-hovered {

    // PCのみ ホバー時に表示エリアが変わる
    @include media-pc {
      .split-view-list-single-icons {
        display: none;
      }

      .split-view-list-single-icons-on-hover {
        display: flex;
      }
    }
  }
}

// un-read marker
.split-view-list-single-icon-read {
  // Specify the position based on .split-view-list-single-rows
  position: absolute;
  left: 9px;
  top: calc(50% - 3.5px);

  width: 6px;
  height: 6px;
  background-color: var(--color-guide-brand-main-foreground);
  border-radius: 3px;

  // hide normally
  display: none;
}

.split-view-list-single-columns {
  display: flex;
  flex-flow: row;
  justify-content: left;
  align-items: stretch;
}

.split-view-list-single-rows {
  display: flex;
  flex-flow: column;
  justify-content: center;
  position: relative;
}

.split-view-list-single-text {
  color: var(--color-guide-foreground-2);
}

.split-view-list-single-row {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  width: 100%;

  >* {
    flex-grow: 0;
  }
}

.split-view-list-single-row-1 {
  .split-view-list-single-title {
    @include ellipsis;

    margin-top: 0;
    margin-bottom: 1px;
    height: 1.3em;
    width: 100%;

    .split-view-list-single.is-unread & {
      font-weight: bold;
    }
  }
}

.split-view-list-single-row-2 {
  margin-top: 2px;
  justify-content: flex-start;

  @include media-pc {
    margin-top: 4px;
  }

  .split-view-list-single-spolist-name {
    @include ellipsis;

    flex-shrink: 1;
    margin-right: auto;

    @include media-pc {
      font-size: 11px;
    }

    @include media-sp {
      // @include fix-ios-redraw-issue;

    }
  }

  // SP表示用の添付ファイルクリップアイコン
  .split-view-list-single-icon-attachment-sp {
    display: none;

    @include media-sp {
      @include fix-ios-redraw-issue;

      display: block;
      margin-left: auto;
      margin-right: -4px;
      width: 11px;

      svg {
        width: 11px;
      }
    }
  }

  // 更新日時
  .split-view-list-single-updated {
    margin-left: 1em;
    flex-shrink: 0;

    @include media-pc {
      font-size: 11px;
    }
  }
}

.split-view-list-single-icons {
  // お気に入り + 添付ファイル
  max-width: 200px;
  width: 15%;
  display: flex;
  flex-flow: row;
  justify-content: flex-end;

  &.split-view-list-single-icons-on-hover {

    // SPでは常にクリック可能なアイコンを表示する
    @include media-sp {
      @include fix-ios-redraw-issue;

      display: block;
      width: 24px;
      position: relative;
      left: 5px;
    }
  }
}

// SPでは表示専用のアイコンを表示しない
.split-view-list-single-icons-on-not-hover {
  @include media-sp {
    display: none;
  }
}

// お気に入りアイコン
.split-view-list-single-star {
  position: relative;
  margin-right: -5px;
  margin-top: -5px;
  margin-bottom: -1px;
  margin-left: 6px;

  width: 13px;
  height: 13px;

  .ui-icon svg {
    width: 13px;
    height: 13px;
  }

  // SP用サイズ調整
  @include media-sp {
    margin-right: 0;
    margin-left: auto;
  }
}

.split-view-list-single-icons-on-hover {
  // 親要素のhoverで変化
  display: none;
}

.split-view-list-single-icon-attachment {
  opacity: 0.8;

  width: 14px;
  height: 14px;

  >svg {
    width: 14px;
    height: 14px;
  }
}

// ローディング
.split-view-list-single-skeleton {
  .split-view-list-single-row-1 {
    margin-top: 4px;
    margin-bottom: 4px;
  }

  .split-view-list-single-title {
    width: 28em;
    height: 0.9em;
    color: var(--color-guide-default-foreground);

    @include media-sp {
      width: 20em;
    }
  }

  .split-view-list-single-icons {
    width: 3em;
    height: 1em;
  }

  .split-view-list-single-text {
    width: 12em;
    height: 0.8em;
  }

  .split-view-list-single-date {
    width: 2em;
    height: 0.8em;
  }
}