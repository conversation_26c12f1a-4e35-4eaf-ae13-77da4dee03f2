import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import SplitViewListSingle, { ISplitViewListSingleProps, SplitViewListSingleView } from './SplitViewListSingle';
import { createSplitViewListSingle } from '../../../../utilities/test';

jest.mock('../../../../utilities/environment');

describe('SplitViewListSingle', () => {
  function renderComponent(
    props: Partial<ISplitViewListSingleProps>,
  ) {
    return render(
      <SplitViewListSingle {...props} />,
    );
  }

  describe('className', () => {
    const className = 'split-view-list-single';

    it('should have "split-view-list-single"', () => {
      const { container } = renderComponent({ className });
      expect(container.children[0]).toHaveClass(className);
    });

  });

  describe(`when view = ${SplitViewListSingleView.LOADING}`, () => {
    it('should show ui-skeleton', () => {
      const { container } = renderComponent({
        view: 'loading',
      });
      expect(container.children[0]).toContainHTML('ui-skeleton');
    });

    it('should not show ListName', () => {
      const { container } = renderComponent({
        view: 'loading',
      });
      expect(container.children[0]).not.toContainHTML('split-view-list-single-spolist-name');
    });

    it('should not show PaperclipIcon on PC', () => {
      const { container } = renderComponent({
        view: 'loading',
        item: createSplitViewListSingle({ properties: { hasAttachments: false } }),
      });
      expect(container.children[0]).not.toContainHTML('split-view-list-single-icon-attachment');
    });

    it('should not show PaperclipIcon on SP', () => {
      const { container } = renderComponent({
        view: 'loading',
        item: createSplitViewListSingle({ properties: { hasAttachments: false } }),
      });
      expect(container.children[0]).not.toContainHTML('split-view-list-single-icon-attachment-sp');
    });
  });

  describe(`when view = ${SplitViewListSingleView.DEFAULT}`, () => {
    it('should show ListName', () => {
      const { container } = renderComponent({
        view: 'default',
      });
      expect(container.children[0]).toContainHTML('split-view-list-single-spolist-name');
    });

    it('should show PaperclipIcon on PC', () => {
      const { container } = renderComponent({
        view: 'default',
        item: createSplitViewListSingle({ properties: { hasAttachments: true } }),
      });
      expect(container.children[0]).toContainHTML('split-view-list-single-icon-attachment');
    });

    it('should show PaperclipIcon on SP', () => {
      const { container } = renderComponent({
        view: 'default',
        item: createSplitViewListSingle({ properties: { hasAttachments: true } }),
      });
      expect(container.children[0]).toContainHTML('split-view-list-single-icon-attachment-sp');
    });

    it('should show updated date', () => {
      const { container } = renderComponent({ view: 'default' });
      expect(container.children[0]).toContainHTML('split-view-list-single-updated');
    });

    it('should show DateSourceIcon', () => {
      const { container } = renderComponent({
        view: 'default',
        item: createSplitViewListSingle({ kind: 'SPO' }),
      });
      expect(container.children[0]).toContainHTML('split-view-list-single-date-source-icon');
    });
  });

});
