@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.split-view-detail-header {
  padding-bottom: 9px;

  @include media-sp {
    padding-top: 18px;
  }
}

.split-view-detail-header-row {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  word-break: break-word;
}

// 1行目
.split-view-detail-header-row-1 {
  justify-content: flex-start;

  .datasource-icon {
    svg {
      width: 1.3rem;
      height: 1.3rem;
    }
  }

  @include media-pc {
    margin-top: 26px;
    margin-bottom: 8px;
  }

  @include media-sp {
    margin-bottom: 6px;
  }
}

.split-view-detail-header-title {
  margin: 0;
  font-size: 18px;
  margin-right: auto;

  @include media-sp {
    font-size: 18px;
  }
}

// お気に入り
.split-view-detail-header-star {
  margin-top: 5px;
  margin-left: 8px;
  width: 18px;
  height: 18px;

  // SPでは非表示（ModalCardTopに表示）
  @include must-hide-on-sp;

  .ui-icon svg {
    width: 18px;
    height: 18px;
  }

  @include media-sp {
    margin-left: auto;
    margin-top: 3px;
    margin-right: 0;
    width: 13px;
    height: 13px;

    .ui-icon svg {
      width: 13px;
      height: 13px;
    }
  }
}

// 2行目
.split-view-detail-header-row-2 {
  display: flex;
  flex-flow: column;
  text-align: left;
  margin-bottom: 6px;

  @include media-pc {
    margin-bottom: 9px;
    font-size: 12px;
  }
}

// 3行目
.split-view-detail-header-row-3 {
  display: flex;
  flex-flow: column;
  text-align: left;
  margin-bottom: 6px;

  @include media-pc {
    margin-bottom: 9px;
    font-size: 12px;
  }
}

.split-view-detail-header-row-4 {
  display: flex;
  flex-flow: column;
  text-align: left;
  margin-bottom: 6px;

  @include media-pc {
    margin-bottom: 9px;
    font-size: 12px;
  }
}

.split-view-detail-header-row-5 {
  display: flex;
  flex-flow: column;
  text-align: left;
  margin-bottom: 6px;

  @include media-pc {
    margin-bottom: 9px;
    font-size: 12px;
  }
}

// ハイライトをあてるための空のCSS
.split-view-detail-highlight-object {}

.split-view-detail-header-category {
  @include ellipsis;

  flex-shrink: 1;

  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
}

.split-view-detail-header-counter {
  @include media-pc {
    margin-left: 1em;
    margin-right: 1em;
  }

  @include media-sp {
    margin-left: 8px;
    margin-right: 8px;
  }
}

// 4行目, 5行目
.split-view-detail-header-row-4-5 {
  margin-bottom: 8px;
  justify-content: flex-start;
  flex-flow: row wrap;

  @include media-pc {
    margin-bottom: 4px;
  }
}

.split-view-detail-header-dates {
  color: var(--color-guide-foreground-2);
  flex-grow: 0;

  @include media-pc {
    font-size: 12px;
  }

  @include media-sp {
    >* {
      width: 100%;
    }
  }

  .has-next-item {
    @include media-pc {
      padding-right: 1em;
      margin-right: 1em;
      border-right: 2px solid var(--color-guide-foreground-6);
    }
  }
}

// 5行目 (存在しない場合がある)
.split-view-detail-header-anchor {
  margin-left: -5px;

  &.ui-button {
    padding-left: 0;
    padding-right: 10;
    color: var(--color-guide-brand-main-foreground);

    /* タッチをサポートしないデバイスでは常にアウトライン表示させる */
    @media (hover: none) {
      .ui-icon__outline {
        display: block !important;
      }

      .ui-icon__filled {
        display: none !important;
      }

      &:active {
        .ui-icon__outline {
          display: none !important;
        }

        .ui-icon__filled {
          display: block !important;
        }
      }
    }

    &:hover {
      @include media-pc {
        color: var(--color-guide-brand-main-foreground);
      }
    }
  }

  .ui-box {
    margin-right: 6px;
  }

  .ui-icon {
    margin-right: -8px;
  }

  .ui-button__content {
    font-weight: bold;

    @include media-pc {
      font-size: 12px;
    }
  }
}

// ローディング時のスケルトンのスタイル
.split-view-detail-header-skeleton {
  margin-bottom: 6px;

  .split-view-detail-header-skeleton-1 {
    height: 1em;
    width: 24em;
    margin-top: 6px;
    margin-bottom: 6px;
  }

  .split-view-detail-header-skeleton-2 {
    height: 1em;
    width: 8em;
    margin-right: 1em;
  }
}