import * as React from 'react';
import PropTypes from 'prop-types';
import {
  Header, Text, Skeleton, PaperclipIcon, Button, EmojiIcon,
} from '@fluentui/react-northstar';
import { blankToDash } from '../../../../utilities/text';
import { ValueOf } from '../../../../utilities/type';
import { ISplitViewDetail } from '../types/ISplitViewDetail';
import { DateFormatTemplate, isSameDates, toDateString } from '../../../../utilities/date';
import BookmarkStar from '../../../commons/atoms/bookmark-star/BookmarkStar';
import { DataSourceKind } from '../../../../types/DataSourceKind';
import { isChatProperties, isMailProperties, isSpoProperties } from '../../../../utilities/transform';
import { IChatProperties, IMailProperties } from '../../../../types/ISearchResult';
import DataSourceIcon from '../../../commons/atoms/datasource-icon/DataSourceIcon';
import { UserContext } from '../split-view-container/UserContext';
import { SearchListMode, SearchModeType } from '../types/SearchListMode';

// CSS
import './SplitViewDetailHeader.scss';

/**
 * 表示の状態
 * @property LOADING 表示中・ローディング状態
 * @property DEFAULT 表示中・デフォルト状態
 */
export const SplitViewDetailHeaderView = {
  LOADING: 'loading',
  DEFAULT: 'default',
};
export type SplitViewDetailHeaderViewType = ValueOf<typeof SplitViewDetailHeaderView>;

export interface ISplitViewDetailHeaderProps {
  view?: SplitViewDetailHeaderViewType,
  detail?: ISplitViewDetail,
  useAttachmentAnchor?: boolean;
  isBookmarked?: boolean;
  onClickAttachmentAnchor?: () => void,
  onClickReactionsAnchor?: () => void,
  onClickBookmark?: (toBe: boolean) => void,
  searchMode?: SearchModeType,
}

const SplitViewDetailHeader: React.FC<ISplitViewDetailHeaderProps> = (props) => {
  const {
    view,
    detail,
    useAttachmentAnchor,
    isBookmarked,
    onClickAttachmentAnchor,
    onClickReactionsAnchor,
    onClickBookmark,
    searchMode,
  } = props;

  const isLoading = React.useMemo(() => (
    view === SplitViewDetailHeaderView.LOADING
  ), [view]);

  const isDefault = React.useMemo(() => (
    view === SplitViewDetailHeaderView.DEFAULT
  ), [view]);

  const isChatMode = React.useMemo(() => (
    searchMode === SearchListMode.Chat
  ), [searchMode]);

  const isSpo = React.useMemo(() => (
    detail?.kind === DataSourceKind.SPO
  ), [detail?.kind]);
  const spoProperties = React.useMemo(() => (
    isSpoProperties(detail?.properties) ? detail?.properties ?? {} : {}
  ), [detail]);

  const isMail = React.useMemo(() => (
    detail?.kind === DataSourceKind.Mail
  ), [detail?.kind]);
  // isMailPropertiesによるmailPropertiesの型が{}になるのでasで強制的に記載
  // 型ガードを使ってデータがmailのプロパティを持っていることを判定。
  const mailProperties = React.useMemo(() => (
    (isMailProperties(detail?.properties) ? detail?.properties ?? {} : {}) as IMailProperties
  ), [detail]);

  const isChat = React.useMemo(() => (
    detail?.kind === DataSourceKind.Chat
  ), [detail?.kind]);
  const chatProperties = React.useMemo(() => (
    (isChatProperties(detail?.properties) ? detail?.properties ?? {} : {}) as IChatProperties
  ), [detail]);
  const hasReactions = React.useMemo(
    () => (chatProperties?.reactions ?? []).length > 0,
    [chatProperties],
  );

  // SPO記事の作成日時と更新日時が一致しているかを判定
  const isCreatedDateAndUpdatedDateMatched = React.useMemo(() => (
    isSameDates(spoProperties.createdDate, spoProperties.updatedDate, 'seconds')
  ), [spoProperties]);

  // 添付ファイルアンカークリックのコールバック
  const handleOnClickAnchor = React.useCallback(() => {
    if (!onClickAttachmentAnchor) return;
    onClickAttachmentAnchor();
  }, [onClickAttachmentAnchor]);

  const handleOnClickReactionsAnchor = React.useCallback(() => {
    if (!onClickReactionsAnchor) return;
    onClickReactionsAnchor();
  }, [onClickReactionsAnchor]);

  // お気に入りボタンクリックのコールバック
  const handleOnClickBookmark = React.useCallback((toBe: boolean) => {
    if (!onClickBookmark) return;
    onClickBookmark(toBe);
  }, [onClickBookmark]);

  const from = (detail?.properties as IMailProperties)?.from;
  const isDraft = (detail?.properties as IMailProperties)?.isDraft;
  const currentUser = React.useContext(UserContext);
  const mailDateLabel = React.useMemo(() => {
    if (isDraft) {
      return '作成日：';
    }
    if (currentUser) {
      if (!currentUser) {
        return '受信日：';
      }
      if (from?.emailAddress?.address === currentUser.mail) {
        return '送信日：';
      }
    }
    return '受信日：';
  }, [from, currentUser, isDraft]);

  return (
    <div className="split-view-detail-header">
      {/* the first line */}
      <div className="split-view-detail-header-row split-view-detail-header-row-1">
        {/* title */}
        {/* mark.jsでのDOM改竄が記事切り替え後に残らないようにするため、あえて同じ要素が切り替わるようにしている */}
        {/* ほかの場所は一旦skeletonに置き換わるので同様の対策は不要 */}
        {isLoading && (
          <>
            {detail?.kind && <DataSourceIcon kind={detail.kind} />}
            <Header className="split-view-detail-header-title" as="h2" content={blankToDash(detail?.title)} />
          </>
        )}
        {!isLoading && (
          <>
            {detail?.kind && <DataSourceIcon kind={detail.kind} />}
            <Header className="split-view-detail-header-title" as="h2" content={blankToDash(detail?.title)} />
          </>
        )}
        {/* bookmark star */}
        {isDefault && !isChatMode && <BookmarkStar className="split-view-detail-header-star" isBookmarked={isBookmarked} onClick={handleOnClickBookmark} />}
      </div>
      {isDefault && isSpo && (
        <>
          {/* the second line */}
          <div className="split-view-detail-header-row split-view-detail-header-row-2">
            <div className="split-view-detail-highlight-object">
              <Text content={spoProperties.listName} />
            </div>
          </div>

          {/* the third line */}
          <div className="split-view-detail-header-row split-view-detail-header-row-3">
            {/* category */}
            <div className="split-view-detail-header-category">
              <Text content={blankToDash(detail?.note)} truncated />
            </div>
          </div>

          {/* the fourth line */}
          <div className="split-view-detail-header-row split-view-detail-header-row-4-5 split-view-detail-header-dates">
            {/* date created */}
            <Text
              className={!isCreatedDateAndUpdatedDateMatched ? 'has-next-item' : ''}
              content={`掲載日：${blankToDash(
                toDateString(
                  spoProperties.createdDate, DateFormatTemplate.YearMonthDayTimeWithWeekday,
                ),
              )}`}
            />

            {/* 更新日時 (作成日と一致している場合は非表示) */}
            {!isCreatedDateAndUpdatedDateMatched && (
              <Text content={`更新日：${blankToDash(
                toDateString(
                  spoProperties.updatedDate, DateFormatTemplate.YearMonthDayTimeWithWeekday,
                ),
              )}`}
              />
            )}
          </div>

          {/* the fifth line */}
          {useAttachmentAnchor && (
            <div className="split-view-detail-header-row split-view-detail-header-row-4-5">
              <Button
                className="split-view-detail-header-anchor"
                icon={<PaperclipIcon />}
                content="添付ファイルを見る"
                onClick={handleOnClickAnchor}
                text
                primary
              />
            </div>
          )}
        </>
      )}

      {isDefault && isMail && (
        <>
          {/* the second line */}
          <div className="split-view-detail-header-row split-view-detail-header-row-2">
            <div className="split-view-detail-highlight-object">
              <Text content={`From : ${blankToDash(mailProperties.from?.emailAddress?.name
                ?? mailProperties.from?.emailAddress?.address)}`}
              />
            </div>
          </div>

          {/* the third line */}
          <div className="split-view-detail-header-row split-view-detail-header-row-3 split-view-detail-highlight-object">
            {/* 宛先 */}
            {/* TODO: 折りたたみの要件が増えたら書き方を検討する */}
            <Text content={`To : ${blankToDash(mailProperties.toRecipients?.map((to) => to.emailAddress?.name ?? to.emailAddress?.address).join(', '))}`} />
          </div>

          {mailProperties.ccRecipients && mailProperties.ccRecipients?.length >= 1 && (
            <>
              {/* the fourth line */}
              <div className="split-view-detail-header-row split-view-detail-header-row-4 split-view-detail-highlight-object">
                {/* CC */}
                <Text content={`CC : ${mailProperties.ccRecipients?.map((cc) => cc.emailAddress?.name ?? cc.emailAddress?.address).join(', ')}`} />
              </div>
            </>
          )}

          {mailProperties.bccRecipients && mailProperties.bccRecipients?.length >= 1 && (
            <>
              {/* the fifth line */}
              <div className="split-view-detail-header-row split-view-detail-header-row-5 split-view-detail-highlight-object">
                {/* BCC */}
                <Text content={`BCC : ${mailProperties.bccRecipients?.map((bcc) => bcc.emailAddress?.name ?? bcc.emailAddress?.address).join(', ')}`} />
              </div>
            </>
          )}

          {/* the sixth line */}
          <div className="split-view-detail-header-row split-view-detail-header-row-4">
            {/* date created */}
            <Text
              className={!isCreatedDateAndUpdatedDateMatched ? 'has-next-item' : ''}
              content={`${mailDateLabel}${blankToDash(
                toDateString(
                  mailProperties.receivedDateTime, DateFormatTemplate.YearMonthDayTimeWithWeekday,
                ),
              )}`}
            />
          </div>

          {/* the seventh line */}
          {useAttachmentAnchor && (
            <div className="split-view-detail-header-row split-view-detail-header-row-4-5">
              <Button
                className="split-view-detail-header-anchor"
                icon={<PaperclipIcon />}
                content="添付ファイルを見る"
                onClick={handleOnClickAnchor}
                text
                primary
              />
            </div>
          )}
        </>
      )}

      {isDefault && isChat && (
        <>
          {/* the second line */}
          <div className="split-view-detail-header-row split-view-detail-header-row-2 split-view-detail-highlight-object">
            {/* チーム名やグループ名 */}
            {/* TODO: メンバーを表示するとき、折りたたみの要件が増えたら書き方を検討する */}
            <Text content={chatProperties.detailedTitle} />
          </div>
          <div className="split-view-detail-header-row split-view-detail-header-row-3">
            {/* the third line */}
            {/* date created */}
            <Text
              className={!isCreatedDateAndUpdatedDateMatched ? 'has-next-item' : ''}
              content={`投稿日：${blankToDash(
                toDateString(
                  detail?.displayDate, DateFormatTemplate.YearMonthDayTimeWithWeekday,
                ),
              )}`}
            />
          </div>
          {useAttachmentAnchor && (
            <Button
              className="split-view-detail-header-anchor"
              icon={<PaperclipIcon />}
              content="添付ファイルを見る"
              onClick={handleOnClickAnchor}
              text
              primary
            />
          )}
          {hasReactions && (
            <Button
              className="split-view-detail-header-anchor"
              icon={<EmojiIcon />}
              content="リアクションを見る"
              onClick={handleOnClickReactionsAnchor}
              text
              primary
            />
          )}
        </>
      )}

      {isLoading && (
        <Skeleton
          className="split-view-detail-header-skeleton"
          animation="wave"
        >
          {/* 2行目スケルトン */}
          <div className="split-view-detail-header-row split-view-detail-header-row-2">
            {/* カテゴリ */}
            <Skeleton.Text className="split-view-detail-header-skeleton-1" />
          </div>

          {/* 3行目スケルトン */}
          <div className="split-view-detail-header-row split-view-detail-header-row-3">
            {/* 日付 */}
            <Skeleton.Text className="split-view-detail-header-skeleton-2" />
            <Skeleton.Text className="split-view-detail-header-skeleton-2" />
          </div>
        </Skeleton>
      )}

    </div>
  );
};

SplitViewDetailHeader.propTypes = {
  view: PropTypes.string,
  useAttachmentAnchor: PropTypes.bool,
  onClickAttachmentAnchor: PropTypes.func,
  onClickReactionsAnchor: PropTypes.func,
  onClickBookmark: PropTypes.func,
  searchMode: PropTypes.string,
};

SplitViewDetailHeader.defaultProps = {
  view: SplitViewDetailHeaderView.LOADING,
  detail: undefined,
  isBookmarked: false,
  useAttachmentAnchor: false,
  onClickAttachmentAnchor: undefined,
  onClickReactionsAnchor: undefined,
  onClickBookmark: undefined,
  searchMode: undefined,
};

export default React.memo(SplitViewDetailHeader);
