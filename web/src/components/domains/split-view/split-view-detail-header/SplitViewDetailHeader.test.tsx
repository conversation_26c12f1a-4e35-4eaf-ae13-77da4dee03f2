import * as React from 'react';
import '@testing-library/jest-dom';
import { renderHook } from '@testing-library/react-hooks';
import { render } from '@testing-library/react';
import SplitViewDetailHeader, { SplitViewDetailHeaderView } from './SplitViewDetailHeader';
import { ISplitViewDetail } from '../types/ISplitViewDetail';
import { DataSourceKind } from '../../../../types/DataSourceKind';
import { UserContext } from '../split-view-container/UserContext';
import { IChatProperties } from '../../../../types/ISearchResult';
import { IChatReaction } from '../../../../types/IChatResponse';

jest.mock('../../../../utilities/environment');

describe('SplitViewDetailHeader', () => {
  describe('className', () => {
    const rootClassName = 'split-view-detail-header';

    it('should have split-view-detail-header', () => {
      const { container } = render(<SplitViewDetailHeader />);
      expect(container.children[0]).toHaveClass(rootClassName);
    });
  });
  describe('when view = SplitViewDetail.DEFAULT & isSpo', () => {
    const view = SplitViewDetailHeaderView.DEFAULT;

    it('should not show title & note', () => {
      const detail = {
        id: '',
        kind: DataSourceKind.Other,
        title: '',
        note: '',
      };

      const { container } = render(<SplitViewDetailHeader view={view} detail={detail} />);
      expect(container).toHaveTextContent('–');
      expect(container).toHaveTextContent('–');
    });

    it('should show title & note', () => {
      const detail = {
        title: 'aaa',
        note: 'ここにカテゴリが表示されます',
        listUrl: 'listUrl',
        kind: 'SPO',
      } as unknown as ISplitViewDetail;

      const { container } = render(<SplitViewDetailHeader view={view} detail={detail} />);
      expect(container).toHaveTextContent('aaa');
      expect(container).toHaveTextContent('ここにカテゴリが表示されます');
      expect(container.querySelector('.spo-icon')).not.toBeNull();
    });

    it('should show listName', () => {
      const detail = {
        title: 'aaa',
        note: 'ここにカテゴリが表示されます',
        kind: 'SPO',
        properties: {
          listUrl: 'listUrl',
          listName: 'listname',
        },
      } as ISplitViewDetail;

      const { container } = render(<SplitViewDetailHeader view={view} detail={detail} />);
      expect(container).toHaveTextContent('aaa');
      expect(container).toHaveTextContent('listname');

    });

    it('should show createdDate', () => {
      const detail = {
        kind: 'SPO',
        properties: {
          listUrl: 'listUrl',
          createdDate: '2023/1/24/13:45',
        },
      } as ISplitViewDetail;
      const { container } = render(<SplitViewDetailHeader view={view} detail={detail} />);
      expect(container).toHaveTextContent('掲載日：2023/1/24（火）13:45');
    });

    describe('when useAttachmentAnchor = true', () => {
      it('should exist link to jump to the attachment.', () => {
        const useAttachmentAnchor = true;
        const detail = {
          kind: 'SPO',
          properties: {
            listUrl: 'listUrl',
            createdDate: '2023/1/24/13:45',
          },
        } as ISplitViewDetail;
        const { container } = render(
          <SplitViewDetailHeader
            view={view}
            detail={detail}
            useAttachmentAnchor={useAttachmentAnchor}
          />,
        );
        expect(container).toContainHTML('split-view-detail-header-anchor');
      });
    });

    describe('when view = SplitViewDetail.DEFAULT & isMail', () => {
      const viewMail = SplitViewDetailHeaderView.DEFAULT;

      it('should not show title & note', () => {
        const detail = {
          id: '',
          kind: DataSourceKind.Other,
          title: '',
          note: '',
        };

        const { container } = render(<SplitViewDetailHeader view={viewMail} detail={detail} />);
        expect(container).toHaveTextContent('–');
        expect(container).toHaveTextContent('–');
      });

      it('should show from of name', () => {
        const detail = {
          title: 'aaa',
          note: 'ここにカテゴリが表示されます',
          kind: 'Mail',
          properties: {
            from: {
              emailAddress: {
                name: '田中',
              },
            },
            receivedDateTime: '2023-05-16',
          },
        } as ISplitViewDetail;

        const { container } = render(<SplitViewDetailHeader view={viewMail} detail={detail} />);
        expect(container).toHaveTextContent('aaa');
        expect(container).toHaveTextContent('田中');
      });

      it('should show from of address', () => {
        const detail = {
          title: 'aaa',
          note: 'ここにカテゴリが表示されます',
          kind: 'Mail',
          properties: {
            from: {
              emailAddress: {
                address: '<EMAIL>',
              },
            },
            receivedDateTime: '2023-05-16',
          },
        } as ISplitViewDetail;

        const { container } = render(<SplitViewDetailHeader view={viewMail} detail={detail} />);
        expect(container).toHaveTextContent('aaa');
        expect(container).toHaveTextContent('<EMAIL>');
      });
      it('should show sender & To', () => {
        const detail = {
          title: 'aaa',
          note: 'ここにカテゴリが表示されます',
          kind: 'Mail',
          properties: {
            from: {
              emailAddress: {
                address: '<EMAIL>',
              },
            },
            sender: {
              emailAddress: {
                address: '<EMAIL>',
              },
            },
            toRecipients: [{
              emailAddress: {
                address: '<EMAIL>',
              },
            }],
            receivedDateTime: '2023-05-16',
          },
        } as ISplitViewDetail;

        const { container } = render(<SplitViewDetailHeader view={viewMail} detail={detail} />);
        expect(container).toHaveTextContent('<EMAIL>');
        expect(container).toHaveTextContent('<EMAIL>');
      });

      it('should show CC & BCC', () => {
        const detail = {
          note: 'ここにカテゴリが表示されます',
          kind: 'Mail',
          properties: {
            from: {
              emailAddress: {
                address: '<EMAIL>',
              },
            },
            sender: {
              emailAddress: {
                address: '<EMAIL>',
              },
            },
            toRecipients: [{
              emailAddress: {
                address: '<EMAIL>',
              },
            }],
            ccRecipients: [
              {
                emailAddress: {
                  name: 'ggg',
                },
              }, {
                emailAddress: {
                  name: 'ooo',
                },
              }],
            bccRecipients: [{
              emailAddress: {
                address: '<EMAIL>',
              },
            }],
            receivedDateTime: '2023-05-16',
          },
        } as ISplitViewDetail;
        const { container } = render(<SplitViewDetailHeader view={viewMail} detail={detail} />);
        expect(container).toHaveTextContent('ggg');
        expect(container).toHaveTextContent('<EMAIL>');

        // expect(container).toHaveTextContent('掲載日：2023/1/24（火）13:45');
      });

      describe('when useAttachmentAnchor = true', () => {
        it('should exist link to jump to the attachment.', () => {
          const useAttachmentAnchor = true;
          const detail = {
            title: 'aaa',
            note: 'ここにカテゴリが表示されます',
            kind: 'Mail',
            properties: {
              from: {
                emailAddress: {
                  address: '<EMAIL>',
                },
              },
            },
          } as ISplitViewDetail;
          const { container } = render(
            <SplitViewDetailHeader
              view={viewMail}
              detail={detail}
              useAttachmentAnchor={useAttachmentAnchor}
            />,
          );
          expect(container).toContainHTML('split-view-detail-header-anchor');
        });
      });

      describe('when from mail address ', () => {
        const useAttachmentAnchor = true;
        const detail = {
          title: 'aaa',
          note: 'ここにカテゴリが表示されます',
          kind: 'Mail',
          properties: {
            from: {
              emailAddress: {
                address: '<EMAIL>',
              },
            },
          },
        } as ISplitViewDetail;
        it('shows "送信日" when from is user', () => {
          const memo = renderHook(() => React.useMemo(() => ({ mail: '<EMAIL>' }), []));
          const { container } = render(
            <UserContext.Provider value={memo.result.current}>
              <SplitViewDetailHeader
                view={viewMail}
                detail={detail}
                useAttachmentAnchor={useAttachmentAnchor}
              />
            </UserContext.Provider>,
          );
          expect(container).toHaveTextContent('送信日');
        });
        it('shows "受信日" when from is user', () => {
          const memo = renderHook(() => React.useMemo(() => ({ mail: '<EMAIL>' }), []));
          const { container } = render(
            <UserContext.Provider value={memo.result.current}>
              <SplitViewDetailHeader
                view={viewMail}
                detail={detail}
                useAttachmentAnchor={useAttachmentAnchor}
              />
            </UserContext.Provider>,
          );
          expect(container).toHaveTextContent('受信日');
        });
        it('shows "作成日" when from is user', () => {
          const draft = {
            title: 'aaa',
            note: 'ここにカテゴリが表示されます',
            kind: 'Mail',
            properties: {
              from: {
                emailAddress: {
                  address: '<EMAIL>',
                },
              },
              isDraft: true,
            },
          } as ISplitViewDetail;
          const memo = renderHook(() => React.useMemo(() => ({ mail: '<EMAIL>' }), []));
          const { container } = render(
            <UserContext.Provider value={memo.result.current}>
              <SplitViewDetailHeader
                view={viewMail}
                detail={draft}
                // me={me}
                useAttachmentAnchor={useAttachmentAnchor}
              />
            </UserContext.Provider>,
          );
          expect(container).toHaveTextContent('作成日');
        });
      });

      describe('when useAttachmentAnchor = false', () => {
        it('should not exist link to jump to the attachment.', () => {
          const useAttachmentAnchor = false;
          const { container } = render(
            <SplitViewDetailHeader view={view} useAttachmentAnchor={useAttachmentAnchor} />,
          );
          expect(container).not.toContainHTML('split-view-detail-header-anchor');
        });
      });

      describe('show reaction button', () => {
        it('should not exist the button to show reactions when the chat has no reactions', () => {
          const detail: ISplitViewDetail = {
            id: 'id-1',
            kind: 'Chat',
            title: 'ユーザー１',
            note: 'チャットにリアクションがない',
            properties: {
              messageType: 'chat',
              reactions: [] as IChatReaction[],
            } as IChatProperties,
          };
          const { container } = render(<SplitViewDetailHeader view={view} detail={detail} />);
          expect(container).not.toHaveTextContent('リアクションを見る');
        });

        it('should  exist the button to show reactions when the chat has any reactions', () => {
          const detail: ISplitViewDetail = {
            id: 'id-1',
            kind: 'Chat',
            title: 'ユーザー１',
            note: 'チャットにリアクションがある',
            displayDate: new Date().toISOString(),
            properties: {
              messageType: 'chat',
              reactions: [{
                reactionType: 'like',
                creationDate: new Date().toISOString(),
                user: {
                  user: {
                    id: 'user-id1',
                    displayName: null,
                  },
                },
              }] as IChatReaction[],
            } as IChatProperties,
          };
          const { container } = render(<SplitViewDetailHeader view={view} detail={detail} />);
          expect(container).toContainHTML('リアクションを見る');
        });
      });
    });

    describe('when view = SplitViewDetail.LOADING', () => {
      const viewLoad = SplitViewDetailHeaderView.LOADING;

      it('should not show title & category1', () => {
        const detail = {
          id: '',
          kind: DataSourceKind.Other,
          title: '',
          note: '',
        };

        const { container } = render(<SplitViewDetailHeader view={viewLoad} detail={detail} />);
        expect(container).toHaveTextContent('–');
        expect(container).toHaveTextContent('–');
      });

      it('should show ui-skeleton', () => {
        const detail = {
          id: '',
          kind: DataSourceKind.Other,
          title: 'aaa',
          category1: 'ここにカテゴリが表示されます',
        };
        const { container } = render(<SplitViewDetailHeader view={viewLoad} detail={detail} />);
        expect(container).toContainHTML('ui-skeleton');
      });
    });
  });

  describe('when view = SplitViewDetail.DEFAULT & isChat', () => {
    const viewChat = SplitViewDetailHeaderView.DEFAULT;
    const detail: ISplitViewDetail = {
      id: '',
      title: '',
      kind: 'Chat',
    };

    describe('when useAttachmentAnchor = true', () => {
      it('should exist link to jump to the attachment.', () => {
        const useAttachmentAnchor = true;
        const { container } = render(
          <SplitViewDetailHeader
            view={viewChat}
            detail={detail}
            useAttachmentAnchor={useAttachmentAnchor}
          />,
        );
        expect(container).toContainHTML('split-view-detail-header-anchor');
      });
    });

    describe('when useAttachmentAnchor = false', () => {
      it('should not exist link to jump to the attachment.', () => {
        const useAttachmentAnchor = false;
        const { container } = render(
          <SplitViewDetailHeader
            view={viewChat}
            detail={detail}
            useAttachmentAnchor={useAttachmentAnchor}
          />,
        );
        expect(container).not.toContainHTML('split-view-detail-header-anchor');
      });
    });
  });

  describe('when view = SplitViewDetail.LOADING', () => {
    const viewLoad = SplitViewDetailHeaderView.LOADING;

    it('should not show title & category1', () => {
      const detail = {
        id: '',
        kind: DataSourceKind.Other,
        title: '',
        category1: '',
      };

      const { container } = render(<SplitViewDetailHeader view={viewLoad} detail={detail} />);
      expect(container).toHaveTextContent('–');
      expect(container).toHaveTextContent('–');
    });

    it('should show ui-skeleton', () => {
      const detail = {
        id: '',
        kind: DataSourceKind.Other,
        title: 'aaa',
        category1: 'ここにカテゴリが表示されます',
      };
      const { container } = render(<SplitViewDetailHeader view={viewLoad} detail={detail} />);
      expect(container).toContainHTML('ui-skeleton');
    });
  });
});
