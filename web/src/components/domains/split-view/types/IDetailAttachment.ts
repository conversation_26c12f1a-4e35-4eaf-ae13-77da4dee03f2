import PropTypes from 'prop-types';
import { DocIcon, DocIconType } from '../../../../utilities/icons';

/**
 * 詳細画面表示用添付ファイルの型
 *
 * @export
 * @interface IDetailAttachment
 */
export interface IDetailAttachment {
  id: string;
  extension: string;
  title?: string | null;
  /**
   * 添付ファイルを開くためのURL
   */
  url?: string | null;
  /**
   * 添付ファイルを開くためのURL(SP用)
   */
  spUrl?: string | null;
  icon?: DocIconType | null;
}

export const IDetailAttachmentPropType = PropTypes.arrayOf(
  PropTypes.shape({
    title: PropTypes.string,
    url: PropTypes.string,
    spUrl: PropTypes.string,
    icon: PropTypes.oneOf([...Object.values(DocIcon), '']),
  }).isRequired,
);
