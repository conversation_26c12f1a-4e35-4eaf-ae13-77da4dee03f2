import PropTypes from 'prop-types';
import { DataSourceKind, DataSourceKindType } from '../../../../types/DataSourceKind';
import { SearchResultProperties } from '../../../../types/ISearchResult';

/**
 * アイテム一件分(Base)
 */
export interface ISplitViewListBase {
  id: string;
  kind: DataSourceKindType;
  title: string;
  reposCreatedDate?: string,
  reposUpdatedDate?: string,
}

/**
 * 一覧アイテム一件分の型定義
 */
export interface ISplitViewListSingle extends ISplitViewListBase {
  note: string;
  displayDate: string;
  properties: SearchResultProperties,
  score?: string
}

export const ISplitViewListSinglePropTypeShape = {
  id: PropTypes.string,
  kind: PropTypes.oneOf([
    DataSourceKind.SPO,
    DataSourceKind.Mail,
    DataSourceKind.Chat,
    DataSourceKind.Other,
  ]),
  title: PropTypes.string,
  note: PropTypes.string,
  displayDate: PropTypes.string,
  properties: PropTypes.any,
};
