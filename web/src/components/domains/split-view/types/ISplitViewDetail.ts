import PropTypes from 'prop-types';
import { ISplitViewListBase } from './ISplitViewListSingle';
import { DataSourceKind } from '../../../../types/DataSourceKind';
import { SearchResultProperties } from '../../../../types/ISearchResult';

/**
 * 詳細アイテム一件分の型定義
 */
// 情報源に固有の情報を持つ型をそれぞれ作っておき、リストで扱うときは共通で扱う部分に対しては | で合成した型を公開して使うといいです。
export interface ISplitViewDetail extends ISplitViewListBase {
  note?: string;
  displayDate?: string;
  properties?: SearchResultProperties,
  body?: string;
  // TODO: SPO固有の型をPropertiesに移動する？
  itemId?: number | null;
  expiredDate?: string | null;
}

export const ISplitViewDetailPropTypesShape = {
  id: PropTypes.string,
  kind: PropTypes.oneOf([
    DataSourceKind.SPO,
    DataSourceKind.Mail,
    DataSourceKind.Chat,
    DataSourceKind.Other,
  ]),
  title: PropTypes.string,
  note: PropTypes.string,
  displayDate: PropTypes.string,
  itemId: PropTypes.number,
  body: PropTypes.string,
  expiredDate: PropTypes.string,
};
