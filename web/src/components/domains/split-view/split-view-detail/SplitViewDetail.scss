@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.split-view-detail {
  height: 100%;
  display: flex;
  flex-flow: column;
}

// PCでカードの形状となる領域
.split-view-detail-inner {
  height: 100%;
  display: flex;
  flex-flow: column;
  background: var(--color-guide-background);
  box-shadow: $box-shadow-2;
  position: relative;

  @include media-pc {
    border-radius: 4px;
    // 記事ヘッダー固定時にカゲが外へ漏れないようにしている
    overflow: hidden;
  }
}

.split-view-scrollbar-wrapper {
  height: 100%;
  display: flex;
  flex-flow: column;
}

.split-view-detail-close {

  // SPでのみ表示
  @include media-pc {
    display: none;
  }

  border-bottom: 1px solid var(--color-guide-foreground-6);
  margin-top: 8px;
}

.split-view-detail-header-container {
  border-bottom: 1px solid var(--color-guide-foreground-6);
  background-color: var(--color-guide-background);

  @include media-pc {
    padding-bottom: 1em;
  }

  @include media-sp {
    background-color: var(--color-guide-background-2);
  }

  &.is-fixed {
    @include media-pc {
      border-bottom: 0;
      box-shadow: $box-shadow-1;
      position: relative;
      z-index: 2;
    }
  }
}

.split-view-detail-scroll {
  @include scrollbars-common;

  // exclude error message
  &:not(.is-error)>* {
    // set the custom background to force light color even in dark mode
    background: var(--color-guide-background-force-light);
  }
}

.split-view-detail-message {
  height: 100%;
  flex-grow: 1;
  padding-left: var(--length-margin-horizontal);
  padding-right: var(--length-margin-horizontal);

  @include media-sp {
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
  }
}

.split-view-detail-label {
  margin-right: 1ex;
}

.split-view-detail-main-container {
  height: calc(100% + 1px);
  flex-grow: 1;
}

// PCで、広いパディング付きの詳細コンテンツ幅が確保できる画面サイズ
$detail-content-width-with-padding-screen: 1440px - 70px;

.split-view-detail-column {
  width: 100%;
  max-width: $width-split-view-detail;
  margin-left: auto;
  margin-right: auto;

  padding-left: var(--length-margin-horizontal);
  padding-right: var(--length-margin-horizontal);

  @include media-sp {
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
  }

  @media screen and (max-width: $detail-content-width-with-padding-screen - 1) {
    max-width: 770px + 40;
  }

  @media screen and (min-width: $detail-content-width-with-padding-screen) {
    padding-left: 60px;
    padding-right: 60px;
  }
}

.split-view-detail-main {
  margin-top: 2em;

  // 広めの余白を付ける
  padding-bottom: 6em;
}

.split-view-detail-item-link {
  margin-bottom: 1em;

  // ボタン要素の余白を削除
  .ui-button {
    min-width: fit-content;
    padding: 0;
    border: 0;
    vertical-align: baseline;
  }
}

.split-view-detail-item-link-button {
  // EXCLUDING THIS PART FROM DARK THEME
  @include hyperlink('.ui-button__content', true);

  .ui-button__content {
    font-weight: normal;
  }
}

// 詳細画面本文のスタイル制御
// データソース固有のスタイルを充てたい場合は&.spo {}のような形でネストして指定する
// このエリアは現状ダークモードから除外する
// EXCLUDING THIS PART FROM DARK THEME
.split-view-detail-body-content {
  // URLなどが折り返すようにする
  word-wrap: break-word;

  // 行間をSharePointに近づける
  line-height: 1.6;

  // 横スクロールを抑止
  overflow: hidden;

  // あふれないように調整
  table {
    word-break: break-all;
    max-width: 100% !important;

    @media screen and (max-width: $detail-content-width-with-padding-screen) {
      width: 100% !important;
    }
  }

  // あふれないように調整
  img {
    max-width: 100% !important;
  }

  // ハイパーリンクの色をスタイルガイドに合わせる
  @include hyperlink('a:link', true);

  // force light mode color for markers
  .highlight-search-words {
    background-color: var(--color-guide-highlight-force-light);
    color: inherit;
  }

  blockquote {
    position: relative;
    border-left: 3px solid var(--color-guide-foreground-6-force-light);
    padding-left: 10px;
    margin-inline-start: 0px;
  }

  &.spo {

    // 段間をSharePointに近づける
    p {
      margin-block-start: 1.25em;
      margin-block-end: 1.25em;
    }
  }

  &.mail {
    // 見切れて見えないメールも見えるようにする
    overflow-y: hidden;
    overflow-x: scroll;
  }
}

.chat-subject {
  font-size: 18px;
  margin-bottom: 1rem;
  font-weight: bold;
}

.attachment-reply-card {
  background: var(--color-guide-background-2);
  border-radius: 5px 5px 5px 5px;
  padding-top: 5px;
  padding-left: 5px;
  padding-bottom: 5px;
  margin-right: 5px;
  border-left: 5px solid var(--color-guide-foreground-6-force-light);
  box-shadow: $box-shadow-3;
  margin-bottom: 0.3rem;

  .attachment-sender-name {
    font-size: 12px;
  }

  .attachment-content-preview {
    font-size: 14px;
  }
}

.chat {
  .chat-mention {
    color: var(--color-guide-brand-main-foreground);

    &.me {
      font-weight: bold;
    }
  }
}

.split-view-detail-date {
  margin-top: 4em;
  // set the color variable keeping light color even in dark mode
  color: var(--color-guide-foreground-2-force-light);

  @include media-pc {
    font-size: 12px;
  }
}

.split-view-detail-attachments-head {
  margin-top: 3em;
  padding-top: 2em;
  // set the color variable keeping light color even in dark mode
  border-top: 1px solid var(--color-guide-foreground-6-force-light);
  margin-bottom: 2em;

  @include media-pc {
    margin-top: 30px - 6;
    padding-top: 25px - 10;
    margin-bottom: 10px - 6;
  }

  >.ui-icon {
    margin-right: 4px;
    margin-bottom: 3px;
  }

  >.ui-text {
    @include media-pc {
      font-size: 12px;
    }
  }
}

.split-view-detail-attachments-items {
  list-style: none;
  display: flex;
  flex-flow: row wrap;
  padding: 0;
  margin-left: -8px;

  >li {
    width: 340px;
    max-width: 100%;
  }
}

.split-view-detail-attachments-item {
  margin-bottom: 8px;
  margin-left: 8px;

  .ui-attachment__body {
    // 添付ファイル名を1行で省略するために必要
    overflow: hidden;
  }

  .ui-attachment__header {
    // 添付ファイル名を1行で省略するために必要
    @include ellipsis;

    margin-right: 1em;
  }
}

.split-view-detail-reactions-head {
  margin-top: 3em;
  padding-top: 2em;
  // set the color variable keeping light color even in dark mode
  border-top: 1px solid var(--color-guide-foreground-6-force-light);
  margin-bottom: 2em;

  @include media-pc {
    margin-top: 30px - 6;
    padding-top: 25px - 10;
    margin-bottom: 10px - 6;
  }

  @include media-sp {
    margin-top: 30px - 6;
    padding-top: 25px - 10;
    margin-bottom: 4px;
  }

  >.ui-icon {
    margin-right: 4px;
    margin-bottom: 3px;
  }

  >.ui-text {
    @include media-pc {
      font-size: 12px;
    }
  }
}

.split-view-detail-Reactions {
  list-style: none;
  display: flex;
  flex-flow: row wrap;
  padding: 0;
  margin-bottom: 8px;
}

// ローディング時のスケルトンのスタイル
.split-view-detail-skeleton {

  .split-view-detail-body-content {
    margin-bottom: 16px;

    &:nth-child(2) {
      width: 80%;
    }

    &:nth-child(4) {
      width: 80%;
    }
  }
}