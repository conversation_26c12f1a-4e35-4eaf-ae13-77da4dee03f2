import {
  convertSpoAttachmentsToAttachmentItems,
  convertChatAttachmentsToAttachmentItems,
  convertMailAttachmentsToAttachmentItems,
  createChatPreviewUrl,
  isEncoded,
} from './SplitViewDetail.functions';
import { IChatProperties, IMailProperties, ISpoProperties } from '../../../../types/ISearchResult';

describe('createChatPreviewUrl', () => {
  it('returns empty string when invalid url is given', () => {
    const result = createChatPreviewUrl('https://api.box.com/wopi/files/1133562694369');
    expect(result).toEqual('');
  });

  describe('when team SharePoint file is given', () => {
    it('returns the expected preview URL with encoded sites path', () => {
      const contentUrl = 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Shared Documents/General/添付ファイル-タイトル-Word.docx';
      const result = createChatPreviewUrl(contentUrl);
      const expectedUrl = 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Shared Documents/Forms/AllItems.aspx?id=%2Fsites%2Fprojectgeranium%2FShared+Documents%2FGeneral%2F%E6%B7%BB%E4%BB%98%E3%83%95%E3%82%A1%E3%82%A4%E3%83%AB-%E3%82%BF%E3%82%A4%E3%83%88%E3%83%AB-Word.docx&parent=%2Fsites%2Fprojectgeranium%2FShared+Documents%2FGeneral%2F';
      expect(result).toEqual(expectedUrl);
    });
  });
  describe('when personal OneDrive file is given', () => {
    it('returns the expected preview URL with encoded personal path', () => {
      const contentUrl = 'https://projectgeranium-my.sharepoint.com/personal/takumi_naito_projectgeranium_onmicrosoft_com/Documents/Microsoft Teams チャット ファイル/添付ファイル-タイトル-Word.docx';
      const result = createChatPreviewUrl(contentUrl);
      const expectedUrl = 'https://projectgeranium-my.sharepoint.com/personal/takumi_naito_projectgeranium_onmicrosoft_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Ftakumi_naito_projectgeranium_onmicrosoft_com%2FDocuments%2FMicrosoft+Teams+%E3%83%81%E3%83%A3%E3%83%83%E3%83%88+%E3%83%95%E3%82%A1%E3%82%A4%E3%83%AB%2F%E6%B7%BB%E4%BB%98%E3%83%95%E3%82%A1%E3%82%A4%E3%83%AB-%E3%82%BF%E3%82%A4%E3%83%88%E3%83%AB-Word.docx&parent=%2Fpersonal%2Ftakumi_naito_projectgeranium_onmicrosoft_com%2FDocuments%2FMicrosoft+Teams+%E3%83%81%E3%83%A3%E3%83%83%E3%83%88+%E3%83%95%E3%82%A1%E3%82%A4%E3%83%AB%2F';
      expect(result).toEqual(expectedUrl);
    });
  });
  it('should return the input URL as it is if the input URL is not encoded', () => {
    const inputURL = 'https%3A%2F%2Fprojectgeranium.sharepoint.com%2Fsites%2Fprojectgeranium%2FShared%20Documents%2FGeneral%2F%E6%B7%BB%E4%BB%98%E3%83%95%E3%82%A1%E3%82%A4%E3%83%AB-%E3%82%BF%E3%82%A4%E3%83%88%E3%83%AB-Word.docx';
    const expectedOutputURL = 'https://projectgeranium.sharepoint.com/sites/projectgeranium/Shared Documents/General/添付ファイル-タイトル-Word.docx';
    const outputURL = isEncoded(inputURL);
    expect(outputURL).toBe(expectedOutputURL);
  });
});

describe('convert DetailAttachment To AttachmentItems', () => {
  const emptyAttachments = {
    hasAttachments: true,
    spoAttachments: [],
    mailAttachments: [],
    chatAttachments: [],
  };

  describe('when attachments is empty', () => {
    it('should return nothing', () => {
      const spo = convertSpoAttachmentsToAttachmentItems(
        emptyAttachments as unknown as ISpoProperties,
      );
      expect(spo).toHaveLength(0);
      const mail = convertMailAttachmentsToAttachmentItems(
        emptyAttachments as unknown as IMailProperties,
      );
      expect(mail).toHaveLength(0);
      const chat = convertChatAttachmentsToAttachmentItems(
        emptyAttachments as unknown as IChatProperties,
      );
      expect(chat).toHaveLength(0);
    });
  });

  describe('when mail attachments is given', () => {
    // isInlineがtrueのアイテムだけアイテムだけ返ってくることも検証したほうがいいかも
    it('isInline is false', () => {
      const properties = {
        hasAttachments: true,
        mailAttachments: [{
          id: 'taro_yamada',
          name: 'uuyyy',
          contentType: 'vvv',
          size: 111,
          isInline: false,
          contentId: 'ttt',
        }],
      } as IMailProperties;
      const result = convertMailAttachmentsToAttachmentItems(properties);
      expect(result).toHaveLength(1);
    });
    it('isInline is true', () => {
      const properties = {
        hasAttachments: true,
        mailAttachments: [{
          id: 'taro_yamada',
          name: 'uuyyy',
          contentType: 'vvv',
          size: 111,
          isInline: true,
          contentId: 'ttt',
        }],
      } as IMailProperties;
      const result = convertMailAttachmentsToAttachmentItems(properties);
      expect(result).toHaveLength(0);
    });
    it('returns not inline items only', () => {
      const properties = {
        hasAttachments: true,
        mailAttachments: [{
          id: '1',
          name: 'inline.png',
          contentType: 'image/png',
          size: 111,
          isInline: true,
          contentId: '111-inline',
        }, {
          id: '2',
          name: 'file.txt',
          contentType: 'application/text',
          size: 111,
          isInline: false,
          contentId: '222-file',
        }],
      } as IMailProperties;
      const result = convertMailAttachmentsToAttachmentItems(properties);
      expect(result).toHaveLength(1);
      expect(result.filter((val) => val.title === 'file.txt').length).toBe(1);
    });
  });
  describe('when chat attachments is given', () => {
    it('contentType is "reference"(=attachment)', () => {
      const properties = {
        hasAttachments: true,
        chatAttachments: [{
          id: 'id1',
          name: 'name1',
          contentType: 'reference',
          contentUrl: 'contentUrl1',
          content: '',
          previewUrl: 'previewUrl1',
        }],
      } as IChatProperties;
      const result = convertChatAttachmentsToAttachmentItems(properties);
      expect(result).toHaveLength(1);
    });
    it('contentType is not "reference"', () => {
      const properties = {
        hasAttachments: true,
        chatAttachments: [{
          id: 'id1',
          name: 'name1',
          contentType: 'otherType',
          contentUrl: '',
          content: 'content1',
          previewUrl: 'previewUrl1',
        }],
      } as IChatProperties;
      const result = convertChatAttachmentsToAttachmentItems(properties);
      expect(result).toHaveLength(0);
    });
    it('returns "reference" items only', () => {
      const properties = {
        hasAttachments: true,
        chatAttachments: [{
          id: 'id1',
          name: 'name1',
          contentType: 'reference',
          contentUrl: 'contentUrl1',
          content: '',
          previewUrl: 'previewUrl1',
        }, {
          id: 'id2',
          name: 'name2',
          contentType: 'otherType',
          contentUrl: '',
          content: 'content2',
          previewUrl: 'previewUrl2',
        }],
      } as IChatProperties;
      const result = convertChatAttachmentsToAttachmentItems(properties);
      expect(result).toHaveLength(1);
      expect(result.filter((val) => val.title === 'name1').length).toBe(1);
    });
  });
});
