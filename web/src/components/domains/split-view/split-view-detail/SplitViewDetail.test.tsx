import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import { Simulate } from 'react-dom/test-utils';
import SplitViewDetail, { ISplitViewDetailProps, SplitViewDetailMessage, SplitViewDetailView } from './SplitViewDetail';
import { queryElem } from '../../../../utilities/test';
import mockMatchMedia from '../../../../mocks/match-media';
import { ISplitViewDetail } from '../types/ISplitViewDetail';
import { convertChatAttachment, convertMailAttachment } from './SplitViewDetail.functions';

jest.mock('../../../../utilities/environment');
jest.mock('../../../../utilities/mediaQuery');

describe('SplitViewDetail', () => {
  function renderComponent(
    props: Partial<ISplitViewDetailProps>,
  ) {
    return render(
      <SplitViewDetail {...props} />,
    );
  }

  beforeAll(() => {
    mockMatchMedia();
  });

  describe('className', () => {
    const rootClassName = 'split-view-detail';

    describe('process detail view by source', () => {
      const view = SplitViewDetailView.DEFAULT;
      it('should process spo content', () => {
        const detail: ISplitViewDetail = {
          id: '123',
          title: 'title1',
          kind: 'SPO',
          body: 'test for SPO',
        };
        const { container } = render(
          <SplitViewDetail view={view} detail={detail} />,
        );
        expect(container.innerHTML).toContain('test for SPO');
      });

      it('should process text mail content', () => {
        const detail: ISplitViewDetail = {
          id: '456',
          title: 'title2',
          kind: 'Mail',
          body: 'test text for mail',
          properties: {
            contentType: 'text',
          },
        };
        const { container } = render(
          <SplitViewDetail view={view} detail={detail} />,
        );
        expect(container.innerHTML).toContain('test text for mail');
      });
    });

    describe('when view = SplitViewDetail.DEFAULT', () => {
      const view = SplitViewDetailView.DEFAULT;
      it('should have split-view-detail', () => {
        const { container } = render(<SplitViewDetail view={view} />);
        expect(container.children[0]).toHaveClass(rootClassName);
        expect(container.children[0]).not.toHaveClass('is-open');
      });

      describe('when open exists ', () => {
        it('should have is-open', () => {
          const { container } = render(<SplitViewDetail view={view} open />);
          expect(container.children[0]).toHaveClass(rootClassName);
          expect(container.children[0]).toHaveClass('is-open');
        });
      });

      describe('when className has a "custom-class" value', () => {
        const className = 'custom-class';

        it('should have "custom-class" and "is-default"', () => {
          const { container } = render(<SplitViewDetail view={view} className={className} open />);
          expect(container.children[0]).toHaveClass(rootClassName);
          expect(container.children[0]).toHaveClass('is-open');
          expect(container.children[0]).toHaveClass('custom-class');
        });
      });
    });

    [
      SplitViewDetailView.ERROR,
      SplitViewDetailView.LOADING,
    ].forEach((view) => {
      describe(`when view = ${view}`, () => {
        describe('when className has a "custom-class" value', () => {
          const className = 'custom-class';

          it('should have "custom-class"', () => {
            const { container } = render(<SplitViewDetail view={view} className={className} />);
            expect(container.children[0]).toHaveClass(rootClassName);
            expect(container.children[0]).toHaveClass('custom-class');
          });
        });
      });
    });
  });

  describe('close button', () => {
    const targetClassName = '.split-view-detail-close .modal-card-top-close';
    const onClose = jest.fn();

    beforeEach(() => {
      onClose.mockClear();
    });

    Object.values(SplitViewDetailView).forEach((view) => {
      describe(`when the view is "${view}"`, () => {
        describe('when the user clicks the close button', () => {
          it('should call onClose', () => {
            const { container } = renderComponent({ view, onClose });
            Simulate.click(queryElem(container, targetClassName));
            expect(onClose).toBeCalledTimes(1);
          });
        });
      });
    });
  });

  describe('通常エラー', () => {
    [
      [SplitViewDetailMessage.API_REQUEST_FAIL, '通信エラーが発生しました'],
      [SplitViewDetailMessage.UNAVAILABLE, 'この文書は非公開、掲示期限切れ、削除済み、あるいは閲覧権限がありません。'],
    ].forEach((message) => {
      describe(`when message = "${message[0]}"`, () => {
        describe('when view = SplitViewDetailView.ERROR', () => {
          const view = SplitViewDetailView.ERROR;

          it('should attach "is-error" to the Scrollbars element', () => {
            const { container } = render(<SplitViewDetail view={SplitViewDetailView.ERROR} />);
            const scrollBarElemSelector = '.split-view-detail-scroll';
            expect(queryElem(container, scrollBarElemSelector)).toHaveClass('is-error');
          });

          it(`should show "${message[1]}"`, () => {
            const { container } = render(<SplitViewDetail view={view} message={message[0]} />);
            expect(container).toHaveTextContent(message[1]);
          });
        });

        // viewがERRORでない場合は表示されない
        [
          SplitViewDetailView.DEFAULT,
          SplitViewDetailView.LOADING,
        ].forEach((view) => {
          describe(`when view = ${view}`, () => {

            it(`should not show ${message[1]}`, () => {
              const { container } = render(<SplitViewDetail view={view} message={message[0]} />);
              expect(container).not.toHaveTextContent(message[1]);
            });
          });
        });
      });
    });
  });

  describe('検索詳細ヘッダー', () => {

    describe('when view = SplitViewDetailView.DEFAULT', () => {
      const view = SplitViewDetailView.DEFAULT;
      const detail = {
        id: 'abc',
        itemId: 1,
        kind: 'SPO',
        title: 'title',
        body: 'content',
        editLink: 'aaa',
        note: 'ここにカテゴリが表示されます',
        createdDate: '2021-10-23',
        displayDate: '2021-10-24',
        expiredDate: '2021-10-25',
        properties: {
          hasAttachments: true,
          listUrl: 'listUrl',
        },
      } as ISplitViewDetail;

      it('should show title & category', () => {
        const { container } = render(<SplitViewDetail view={view} detail={detail} />);
        expect(container).toHaveTextContent('title');
        expect(container).toHaveTextContent('ここにカテゴリが表示されます');
      });
    });

    describe('when view = SplitViewDetailView.LOADING', () => {
      const view = SplitViewDetailView.LOADING;

      it('should show ui-skeleton', () => {
        const { container } = render(<SplitViewDetail view={view} />);
        expect(container).toContainHTML('ui-skeleton');
      });
    });
  });

  describe('検索詳細', () => {

    describe('when view = SplitViewDetailView.DEFAULT', () => {
      const view = SplitViewDetailView.DEFAULT;

      it('should show detail SPO item link', () => {
        const detail = {
          id: 'abc',
          itemId: 1,
          kind: 'SPO',
          title: 'title',
          body: 'content',
          editLink: 'aaa',
          note: 'ここにカテゴリが表示されます',
          createdDate: '2021-10-23',
          displayDate: '2021-10-24',
          expiredDate: '2021-10-25',
          properties: {
            hasAttachments: true,
            listUrl: 'listUrl',
          },
        } as ISplitViewDetail;
        const { container } = render(<SplitViewDetail view={view} detail={detail} />);
        expect(container).toHaveTextContent('SharePointで開く');

      });

      it('should show detail Mail item link', () => {
        const detail = {
          id: 'abc',
          itemId: 1,
          kind: 'Mail',
          title: 'title',
          body: 'content',
          editLink: 'aaa',
          note: 'ここにカテゴリが表示されます',
          createdDate: '2021-10-23',
          displayDate: '2021-10-24',
          expiredDate: '2021-10-25',
          properties: {
            hasAttachments: true,
            listUrl: 'listUrl',
          },
        } as ISplitViewDetail;
        const { container } = render(<SplitViewDetail view={view} detail={detail} />);
        expect(container).toHaveTextContent('Outlookで開く');
      });
    });

    describe('when view = SplitViewDetailView.LOADING', () => {
      const view = SplitViewDetailView.LOADING;

      it('should show ui-skeleton', () => {
        const { container } = render(<SplitViewDetail view={view} />);
        expect(container).toContainHTML('ui-skeleton');
      });
    });

    describe('convertMailAttachment', () => {
      it('should return ISplitViewAttachment with defined icon', () => {
        const result1 = convertMailAttachment({
          id: '1',
          name: 'name.txt',
          contentType: '',
          size: 100,
          isInline: false,
          contentId: '',
        });
        expect(result1).toStrictEqual({
          id: '1',
          extension: 'txt',
          icon: '',
          spUrl: '',
          title: 'name.txt',
          url: '',
        });

        const result2 = convertMailAttachment({
          id: '1',
          name: 'name.zip',
          contentType: '',
          size: 100,
          isInline: false,
          contentId: '',
        });
        expect(result2).toStrictEqual({
          id: '1',
          extension: 'zip',
          icon: 'zip',
          spUrl: '',
          title: 'name.zip',
          url: '',
        });

        const result3 = convertMailAttachment({
          id: '1',
          name: 'name.ppt.txt',
          contentType: '',
          size: 100,
          isInline: false,
          contentId: '',
        });
        expect(result3).toStrictEqual({
          id: '1',
          extension: 'txt',
          icon: '',
          spUrl: '',
          title: 'name.ppt.txt',
          url: '',
        });

        const result4 = convertMailAttachment({
          id: '1',
          name: 'name.ppt.xlsx',
          contentType: '',
          size: 100,
          isInline: false,
          contentId: '',
        });
        expect(result4).toStrictEqual({
          id: '1',
          extension: 'xlsx',
          icon: 'excel',
          spUrl: '',
          title: 'name.ppt.xlsx',
          url: '',
        });

      });
    });

    describe('convertChatAttachment', () => {
      it('should return ISplitViewAttachment with defined icon', () => {
        const result1 = convertChatAttachment({
          id: '1',
          name: 'name.txt',
          contentType: '',
          contentUrl: 'https://test.sharepoint.com/sites/Shared Documents/name.txt',
          content: '',
        });
        expect(result1).toStrictEqual({
          id: '1',
          extension: 'txt',
          icon: '',
          spUrl: '',
          title: 'name.txt',
          url: 'https://test.sharepoint.com/sites/Shared Documents/Forms/AllItems.aspx?id=%2Fsites%2FShared+Documents%2Fname.txt&parent=%2Fsites%2FShared+Documents%2F',
        });

        const result2 = convertChatAttachment({
          id: '1',
          name: 'name.zip',
          contentType: '',
          contentUrl: '',
          content: '',
        });
        expect(result2).toStrictEqual({
          id: '1',
          extension: 'zip',
          icon: 'zip',
          spUrl: '',
          title: 'name.zip',
          url: '',
        });

        const result3 = convertChatAttachment({
          id: '1',
          name: 'name.ppt.txt',
          contentType: '',
          contentUrl: '',
          content: '',
        });
        expect(result3).toStrictEqual({
          id: '1',
          extension: 'txt',
          icon: '',
          spUrl: '',
          title: 'name.ppt.txt',
          url: '',
        });

        const result4 = convertChatAttachment({
          id: '1',
          name: 'name.ppt.xlsx',
          contentType: '',
          contentUrl: '',
          content: '',
        });
        expect(result4).toStrictEqual({
          id: '1',
          extension: 'xlsx',
          icon: 'excel',
          spUrl: '',
          title: 'name.ppt.xlsx',
          url: '',
        });

      });
    });
  });
});
