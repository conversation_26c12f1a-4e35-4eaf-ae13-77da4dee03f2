import { getIconTypeByExtension } from '../../../../utilities/icons';
import { splitExtensionFromFileName } from '../../../../utilities/regexp';
import { IDetailAttachment } from '../types/IDetailAttachment';
import { IChatAttachment } from '../types/IChatAttachment';
import { IMailAttachment } from '../types/IMailAttachment';
import { ISpoAttachment } from '../types/ISpoAttachment';
import type { ISplitViewDetail } from '../types/ISplitViewDetail';
import { IChatProperties, IMailProperties, ISpoProperties } from '../../../../types/ISearchResult';
import { filterChatAttachments } from '../../../../utilities/transform';

/**
 * プレビュー表示用にAPIから渡ってきたURLがエンコードされているか確認する関数
 */
export function isEncoded(contentUrl: string): string {
  const decodedURL = decodeURIComponent(contentUrl);
  if (contentUrl !== decodedURL) {
    return decodedURL;
  }
  return contentUrl;
}

function composeSharePointPreviewUrl(contentUrl: string) {
  const urlForCreate = isEncoded(contentUrl);
  const urlMatchedResult = urlForCreate.match(/^(.*Shared Documents\/)/);
  const extractedPath = urlMatchedResult ? urlMatchedResult[1] : '';
  const modifiedURL = `${extractedPath}Forms/AllItems.aspx`;

  // sites以下を取得して、エンコードする
  const sitesPath = urlForCreate.substring(urlForCreate.indexOf('/sites'));
  const encodedSitesPath = encodeURIComponent(sitesPath);

  // エンコードしたsites以下から最後の「%2F」で終わる部分までを取得
  const queryMatchedResult = encodedSitesPath.match(/^(.*%2F)/);
  const extractedQuery = queryMatchedResult ? queryMatchedResult[1] : '';
  const modifiedQuery = (`?id=${encodedSitesPath}&parent=${extractedQuery}`).replace(/%20/g, '+');

  // 全部組み合わせる
  const previewUrl = `${modifiedURL}${modifiedQuery}`;
  return previewUrl;
}

function composeOneDrivePreviewUrl(contentUrl: string) {
  const urlForCreate = isEncoded(contentUrl);
  const urlMatchedResult = urlForCreate.match(/^(.*)\/Documents\//);
  const extractedPath = urlMatchedResult ? urlMatchedResult[1] : '';
  // _layouts/15/onedrive.aspx をつける
  const modifiedURL = `${extractedPath}/_layouts/15/onedrive.aspx`;

  // personal以下を取得して、エンコードする
  const sitesPath = urlForCreate.substring(urlForCreate.indexOf('/personal'));
  const encodedSitesPath = encodeURIComponent(sitesPath);

  // personal以下から最後の「%2F」で終わる部分までを取得
  const queryMatchedResult = encodedSitesPath.match(/^(.*%2F)/);
  const extractedQuery = queryMatchedResult ? queryMatchedResult[1] : '';
  const modifiedQuery = (`?id=${encodedSitesPath}&parent=${extractedQuery}`).replace(/%20/g, '+');

  // 全部組み合わせる
  const previewUrl = `${modifiedURL}${modifiedQuery}`;
  return previewUrl;
}

export function checkIsPersonalContent(contentUrl: string): boolean {
  return (/\.sharepoint\.com\/personal\//).test(contentUrl);
}

export function checkIsTeamContent(contentUrl: string): boolean {
  return (/\.sharepoint\.com\/sites\//).test(contentUrl);
}

export function createChatPreviewUrl(contentUrl: string) {
  if (checkIsPersonalContent(contentUrl)) {
    return composeOneDrivePreviewUrl(contentUrl);
  }
  if (checkIsTeamContent(contentUrl)) {
    return composeSharePointPreviewUrl(contentUrl);
  }

  return '';
}

export function convertSpoAttachment(attachment: ISpoAttachment): IDetailAttachment {
  return attachment;
}

export function convertChatAttachment(
  attachment: IChatAttachment,
): IDetailAttachment {
  const [, , extension] = splitExtensionFromFileName(attachment.name);
  return {
    id: attachment.id,
    extension,
    icon: getIconTypeByExtension(extension),
    title: attachment.name,
    url: createChatPreviewUrl(attachment.contentUrl ?? ''),
    spUrl: '',
  };
}

export function convertMailAttachment(attachment: IMailAttachment): IDetailAttachment {
  const [, , extension] = splitExtensionFromFileName(attachment.name);
  return {
    id: attachment.id,
    extension,
    icon: getIconTypeByExtension(extension),
    title: attachment.name,
    url: '',
    spUrl: '',
  };
}

export function convertSpoAttachmentsToAttachmentItems(
  properties?: ISpoProperties,
): IDetailAttachment[] {
  if (!properties?.spoAttachments) return [];
  return properties.spoAttachments.map((attachment) => convertSpoAttachment(attachment));
}

export function convertMailAttachmentsToAttachmentItems(
  properties?: IMailProperties,
): IDetailAttachment[] {
  if (!properties?.mailAttachments) return [];
  return properties.mailAttachments
    .filter((attachment) => attachment.isInline === false)
    .map((attachment) => convertMailAttachment(attachment));
}

export function convertChatAttachmentsToAttachmentItems(
  properties?: IChatProperties,
): IDetailAttachment[] {
  if (!properties?.chatAttachments) return [];
  // contentとcontentUrlは排他関係にあるので、
  // URL参照するコンテンツ(=添付ファイル)はこれで判定できる
  return filterChatAttachments(properties.chatAttachments)
    .map((attachment) => convertChatAttachment(attachment));
}

export function getDisplayAttachments(
  activeDetail?: ISplitViewDetail,
): IDetailAttachment[] {
  if (!activeDetail) return [];
  switch (activeDetail.kind) {
    case 'SPO':
      return convertSpoAttachmentsToAttachmentItems(activeDetail.properties as ISpoProperties);
    case 'Mail':
      return convertMailAttachmentsToAttachmentItems(activeDetail.properties as IMailProperties);
    case 'Chat':
      return convertChatAttachmentsToAttachmentItems(activeDetail.properties as IChatProperties);
    default:
      return [];
  }
}
