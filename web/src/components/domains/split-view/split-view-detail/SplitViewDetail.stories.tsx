import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import {
  cloneForMobileView,
  setCommonArgTypes,
  setStoryMockCommonArgTypes,
  setStoryWidthHeight,
  StoryMockProps,
} from '../../../../../.storybook/utils';
import SplitViewDetail, {
  ISplitViewDetailProps,
  SplitViewDetailMessage,
  SplitViewDetailView,
} from './SplitViewDetail';

export default {
  title: 'split-view/SplitViewDetail.tsx',
  component: SplitViewDetail,
  argTypes: {
    onClickItem: { action: 'clicked' },
    onClickSharingButton: { action: 'clicked' },
    ...setStoryMockCommonArgTypes(1000, 500),
    ...setCommonArgTypes(SplitViewDetailView, SplitViewDetailMessage),
  },
} as Meta;

const Template: StoryFn<StoryMockProps<ISplitViewDetailProps>> = (args) => (
  <div {...setStoryWidthHeight(args)}>
    <div style={{ overflow: 'hidden', height: '100%' }}>
      <SplitViewDetail {...args} />
    </div>
  </div>
);

export const Default = Template.bind({});
Default.args = {
  open: true,
  view: SplitViewDetailView.DEFAULT,
  storyWidth: 1197,
  storyHeight: 500,
  detail: {
    id: '1234',
    kind: 'SPO',
    title: '個人情報保護に関するセキュリティチェック実施のお知らせ_0013',
    note: '01.通達',
    body: '<div class=\\"ExternalClassCC1C4FCFE3F24632B8FBA750C5C3D236\\"><div style=\\"font-family&#58;Calibri, Arial, Helvetica, sans-serif;font-size&#58;11pt;color&#58;rgb(0, 0, 0);\\"><span style=\\"color&#58;black;\\"><span>当社第××期（２０２１年度）決算につきましてご報告いたします。<br></span><div><br></div><div>内容につきましては、下記よりご覧ください。<br></div><div><br></div><div>〈本件に関するお問い合わせ先〉<br></div><div><br></div><span>経営部　小田</span></span><br></div></div>',
    displayDate: new Date('2021-10-24').toISOString(),
    expiredDate: new Date('2021-10-28').toISOString(),
    properties: {
      createdDate: new Date('2021-10-23').toISOString(),
    },
  },
};
export const DefaultOnMobile = cloneForMobileView(Template, Default);

export const NoAttachments = Template.bind({});
NoAttachments.args = {
  ...Default.args,
};
export const NoAttachmentsOnMobile = cloneForMobileView(Template, NoAttachments);

export const Bookmarked = Template.bind({});
Bookmarked.args = {
  ...Default.args,
  isBookmarked: true,
};
export const BookmarkedOnMobile = cloneForMobileView(Template, Bookmarked);

export const Blank = Template.bind({});
Blank.args = {
  open: true,
  view: SplitViewDetailView.DEFAULT,
  storyWidth: 1197,
  storyHeight: 500,
  detail: {
    id: '1234',
    title: '',
    kind: 'Other',
    note: '',
    body: '',
    displayDate: '',
    expiredDate: '',
    properties: {
      createdDate: '',
    },
  },
};
export const BlankOnMobile = cloneForMobileView(Template, Blank);

export const Error = Template.bind({});
Error.args = {
  open: true,
  view: SplitViewDetailView.ERROR,
  storyWidth: 1197,
  storyHeight: 500,
  message: SplitViewDetailMessage.UNAUTHORIZED,
};
export const ErrorOnMobile = cloneForMobileView(Template, Error);

export const Loading = Template.bind({});
Loading.args = {
  open: true,
  view: SplitViewDetailView.LOADING,
};
export const LoadingOnMobile = cloneForMobileView(Template, Loading);
