import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Route } from 'react-router-dom';
import {
  Provider, teamsTheme, Alert, teamsDarkTheme,
} from '@fluentui/react-northstar';
import {
  initializeIcons,
} from '@fluentui/react';
import { ExclamationTriangleIcon } from '@fluentui/react-icons-northstar';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { TeamsTheme, useTeamsInfo } from '@avanade-teams/teams-info';
import useComponentInitUtility from '../hooks/utilities/useComponentInitUtility';
import useDynamicGlobalVarLog from '../hooks/utilities/useDynamicGlobalVarLog';

import Config from './pages/Config';
import Home from './pages/Home';
import environment from '../utilities/environment';

const FluentUiThemeMap = {
  // teamsDarkTheme, teamsHighContrastThemeも選べるが現状はライトテーマのみ反映する
  [TeamsTheme.DEFAULT]: teamsTheme,
  [TeamsTheme.DARK]: teamsDarkTheme,
  [TeamsTheme.CONTRAST]: teamsDarkTheme,
};

const App: React.FC = () => {
  const [, [reportEvent], , error, , oid] = useComponentInitUtility({
    componentName: 'App',
  });

  // @fluentui/reactで利用されるアイコンセット初期化
  initializeIcons();

  // UIテーマの切り替え
  // switching themes
  const { currentTheme } = useTeamsInfo();
  const fluentUiTheme = React.useMemo(() => (
    FluentUiThemeMap[currentTheme] ?? FluentUiThemeMap[TeamsTheme.DEFAULT]
  ), [currentTheme]);

  // アプリケーション起動ログ
  // sending a log "OPEN_APP"
  React.useEffect(() => {
    const isConfigPage = window.location.pathname.includes('/config');
    reportEvent({
      type: EventReportType.SYS_EVENT,
      name: isConfigPage ? 'OPEN_CONFIG' : 'OPEN_APP',
    });
  }, [reportEvent]);

  // checking global variables and send log
  useDynamicGlobalVarLog(reportEvent);

  // 起動時のテーマログを一度だけ送信する
  // sending a log "APP_THEME_ON_INIT"
  const isFirstTime = React.useRef(true);
  React.useEffect(() => {
    if (!currentTheme || !oid || !isFirstTime.current) return;
    reportEvent({
      type: EventReportType.SYS_EVENT,
      name: 'APP_THEME_ON_INIT',
      customProperties: {
        theme: currentTheme,
        oid,
      },
    });
    isFirstTime.current = false;
  }, [oid, reportEvent, currentTheme]);

  // 認証エラーのログを送信
  // sending a log "TOKEN_INIT_FAIL"
  React.useEffect(() => {
    if (!error) return;
    reportEvent({
      type: EventReportType.SYS_ERROR,
      name: 'TOKEN_INIT_FAIL',
      error: new Error(error),
    });
  }, [error, reportEvent]);

  // base route
  const route = React.useMemo(() => environment.REACT_APP_ROUTE_PREFIX || '/', []);

  return (
    <Provider theme={fluentUiTheme}>
      {/* 認証エラー authentication error message */}
      {error && (
        <Alert
          icon={<ExclamationTriangleIcon />}
          content={`ERROR: ${error}`}
          variables={{
            urgent: true,
          }}
        />
      )}
      {/* ルーティング */}
      <BrowserRouter>
        {/* デフォルト */}
        <Route path={route} exact component={Home} />
        {/* タブ設定用 */}
        <Route path={`${route}config`} exact component={Config} />
      </BrowserRouter>
    </Provider>
  );
};

export default App;
