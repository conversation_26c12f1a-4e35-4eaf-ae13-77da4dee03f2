@import '../../../../styles/variables';

.reaction-single {
  display: flex;
  align-items: flex-start;
  padding-bottom: 0.2rem;

  .reaction-single-icon {
    display: flex;
    padding-right: 0.5rem;
    white-space: nowrap;
    align-items: center;

    .reaction-single-icon-name {
      font-size: larger;
      padding-right: 0.1rem;
    }
  }

  .show-more-text {
    flex: 2;
    width: 100%;
    padding-top: 0.15rem;
  }
}

.more-less-icon {
  color: var(--color-guide-brand-main-foreground);
  font-size: larger;
}
