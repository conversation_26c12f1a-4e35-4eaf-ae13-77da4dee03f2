import * as React from 'react';
import ShowMoreText from 'react-show-more-text';
import { ChevronDownIcon, ChevronUpIcon } from '@fluentui/react-icons-mdl2';
import { IGraphUser } from '../../../../types/IGraphUser';

// CSS
import './ReactionSingle.scss';

export interface IReactionSingleProps {
  reactionType: string;
  users: IGraphUser[];
}
const ReactionSingle: React.FC<IReactionSingleProps> = (props) => {
  const {
    reactionType,
    users,
  } = props;
  const head = users[0];
  const tails = users.slice(1);

  const reaction = (() => {
    switch (reactionType) {
      case 'like':
        return '👍';
      case 'heart':
        return '❤️';
      case 'laugh':
        return '😆';
      case 'surprised':
        return '😮';
      default:
        return reactionType;
    }
  })();

  return (
    <div className="reaction-single">
      <span className="reaction-single-icon">
        <span className="reaction-single-icon-name">{reaction}</span>
        <span>{`${users.length}: `}</span>
      </span>
      <ShowMoreText
        className="show-more-text"
        lines={1}
        more={<ChevronDownIcon className="more-less-icon" />}
        less={<ChevronUpIcon className="more-less-icon" />}
        expanded={false}
        width={0}
      >
        <>
          <span>{head.displayName}</span>
          {tails.map((user) => (
            <>
              {', '}
              <span>{user.displayName}</span>
            </>
          ))}
        </>
      </ShowMoreText>
    </div>
  );
};

export default ReactionSingle;
