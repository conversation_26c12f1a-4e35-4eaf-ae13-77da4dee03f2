import * as React from 'react';
import { Button } from '@fluentui/react-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';

// CSS
import './CancellationButton.scss';

export interface ICancellationButtonProps {
  className?: string;
  onClick?: () => void;
}

const CancellationButton: React.FC<ICancellationButtonProps> = ((props) => {
  const {
    className,
    onClick,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => mergedClassName('cancellation-button', className), [className]);

  // クリックハンドラ
  const handleOnClick = React.useCallback(() => {
    if (onClick) onClick();
  }, [onClick]);

  return (
    <Button
      className={rootClassName}
      content="中断"
      size="small"
      onClick={handleOnClick}
    />
  );
});

CancellationButton.defaultProps = {
  className: '',
  onClick: undefined,
};

export default CancellationButton;
