.datasource-filter-dropdown {
  flex-grow: 2;
  width: 15rem;

  .ui-dropdown__container {
    width: 100%;
  }

  .ui-dropdown__selected-items {
    .ui-button__content {
      font-weight: initial;
      font-size: 12px;
    }
  }
}

.filter-item {
  &:hover {
    background: var(--color-guide-brand-background-1) !important;

    .ui-dropdown__selecteditem__icon {
      // color: var(--color-guide-brand-main-foreground) !important;
    }
  }

  &.result-empty {
    // background-color: var(--color-guide-foreground-1-dark) !important;

    &:hover {
      background-color: var(--color-guide-foreground-1-dark) !important;
    }
  }

  .ui-box {
    display: flex;
  }
}
