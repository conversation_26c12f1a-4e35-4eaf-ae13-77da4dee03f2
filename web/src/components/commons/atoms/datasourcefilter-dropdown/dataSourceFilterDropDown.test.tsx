import * as React from 'react';
import { render } from '@testing-library/react';
import { DropdownProps } from '@fluentui/react-northstar';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import DataSourceFilterDropdown, {
  FilterOptions,
  calculateResultCount,
  onSelectionChanged,
} from './dataSourceFilterDropDown';
import { ListMode } from '../../../domains/split-view/types/ListMode';
import {
  SplitViewDetailMessage,
  SplitViewDetailView,
} from '../../../domains/split-view/split-view-detail/SplitViewDetail';
import {
  SplitViewListMessage,
  SplitViewListView,
} from '../../../domains/split-view/split-view-list/SplitViewList';

// mock environment.ts
jest.mock('../../../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_SPO_COLUMNS: { category: 'ENV_VAR_VAL' },
  },
}));

describe('dataSourceFilterDropDown', () => {
  const dispatch = jest.fn();
  const reportEventMock = jest.fn();
  // 追加：sourceFilterRef を定義
  const sourceFilterRef = { current: '' } as React.MutableRefObject<string>;

  const state = {
    listView: SplitViewListView.LOADING,
    listMessage: SplitViewListMessage.BLANK,
    list: [],
    activeId: '',
    detailView: SplitViewDetailView.LOADING,
    detailMessage: SplitViewDetailMessage.BLANK,
    detail: undefined,
    context: {
      sort: [],
      filter: [],
    },
    inlineMailAttachments: [],
    chatAttachments: [],
  };
  const listMode = ListMode.SEARCH;

  beforeEach(() => {
    dispatch.mockClear();
    reportEventMock.mockClear();
  });

  it('プレースホルダーが表示される（選択肢なし）', () => {
    const { container } = render(
      <DataSourceFilterDropdown
        selectedKey="kind"
        selectedOption={[]}
        state={state}
        listMode={listMode}
        dispatch={dispatch}
        reportEvent={reportEventMock}
        sourceFilterRef={sourceFilterRef} // ← ここを追加
      />,
    );
    expect(container.children[0].innerHTML).toContain('検索対象を選択');
  });

  it('プレースホルダーが表示される（selectedOption が undefined）', () => {
    const { container } = render(
      <DataSourceFilterDropdown
        selectedKey="kind"
        selectedOption={undefined}
        state={state}
        listMode={listMode}
        dispatch={dispatch}
        reportEvent={reportEventMock}
        sourceFilterRef={sourceFilterRef} // ← ここも追加
      />,
    );
    expect(container.children[0].innerHTML).toContain('検索対象を選択');
  });

  it('アイコンが表示される（項目を選択）', () => {
    const { container } = render(
      <DataSourceFilterDropdown
        selectedKey="kind"
        selectedOption={['SPO', 'Mail', 'Chat']}
        state={state}
        listMode={listMode}
        dispatch={dispatch}
        reportEvent={reportEventMock}
        sourceFilterRef={sourceFilterRef} // ← ここも追加
      />,
    );
    expect(container.children[0].innerHTML).toContain('spo-icon');
    expect(container.children[0].innerHTML).toContain('mail-icon');
    expect(container.children[0].innerHTML).toContain('chat-icon');
  });

  describe('calculateResultCount', () => {
    const resultRestCount = [1, 2, 4];

    it('アイテム未選択時は合計を返す', () => {
      expect(calculateResultCount([], resultRestCount)).toBe(7);
    });

    it.each([
      { items: ['SPO'], expected: 1 },
      { items: ['Mail'], expected: 2 },
      { items: ['SPO', 'Mail'], expected: 3 },
      { items: ['Chat'], expected: 4 },
      { items: ['SPO', 'Chat'], expected: 5 },
      { items: ['Mail', 'Chat'], expected: 6 },
      { items: ['SPO', 'Mail', 'Chat'], expected: 7 },
    ])(
      '$items 選択時に合計 $expected を返す',
      ({ items, expected }) => {
        const selected = items
          .map((key) => FilterOptions.find(({ variables }) => variables.key === key))
          .filter((i) => i !== undefined) as { variables: any }[];
        expect(calculateResultCount(selected, resultRestCount)).toBe(expected);
      },
    );
  });

  describe('onSelectionChanged', () => {
    const dataMock = { value: [] } as DropdownProps;
    const onSelectOptionMock = jest.fn();

    beforeEach(() => {
      onSelectOptionMock.mockClear();
    });

    it('reportEvent が呼ばれる', () => {
      onSelectionChanged(
        dataMock,
        'kind',
        [],
        [0],
        listMode,
        onSelectOptionMock,
        reportEventMock,
      );

      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.USER_EVENT,
        name: 'EXECUTE_SEARCH_LIST_FILTER',
        customProperties: {
          filterAction: 'kind',
          filterOptionName: [],
          filterConditions: [{ key: 'kind', option: [] }],
          resultCount: 0,
        },
      });
    });

    // データソース種別選択時の動作確認
    it('onSelectOption が正しい値で呼ばれる', () => {
      const selectedData = {
        value: [
          { variables: { key: 'SPO' } },
          { variables: { key: 'Mail' } },
        ],
      } as DropdownProps;

      onSelectionChanged(
        selectedData,
        'kind',
        [],
        [1, 2, 3],
        listMode,
        onSelectOptionMock,
        reportEventMock,
      );

      expect(onSelectOptionMock).toHaveBeenCalledWith({
        key: 'kind',
        option: ['SPO', 'Mail'],
      });
    });

    // 新規テスト: ブックマークモードでのイベント名変更確認
    it('ブックマークモードでは適切なイベント名が使用される', () => {
      onSelectionChanged(
        dataMock,
        'kind',
        [],
        [0],
        ListMode.BOOKMARKS,
        onSelectOptionMock,
        reportEventMock,
      );

      expect(reportEventMock).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'EXECUTE_BOOKMARK_LIST_FILTER',
        }),
      );
    });
  });

  // sourceFilterRefの状態管理テスト
  describe('sourceFilterRefの動作テスト', () => {
    it('searchModeが変更されるとsourceFilterRefが初期化される', () => {
      sourceFilterRef.current = 'SPO,Mail';

      const { rerender } = render(
        <DataSourceFilterDropdown
          selectedKey="kind"
          selectedOption={[]}
          state={state}
          listMode={listMode}
          dispatch={dispatch}
          reportEvent={reportEventMock}
          sourceFilterRef={sourceFilterRef}
          searchMode="default"
        />,
      );

      // searchModeを変更
      rerender(
        <DataSourceFilterDropdown
          selectedKey="kind"
          selectedOption={[]}
          state={state}
          listMode={listMode}
          dispatch={dispatch}
          reportEvent={reportEventMock}
          sourceFilterRef={sourceFilterRef}
          searchMode="Chat"
        />,
      );

      // sourceFilterRefが初期化されることを確認
      expect(sourceFilterRef.current).toBe('');
    });
  });

  describe('FilterOptionsの定数テスト', () => {
    it('FilterOptionsが正しい構造を持つ', () => {
      expect(FilterOptions).toHaveLength(3);
      expect(FilterOptions[0]).toEqual({
        header: 'SPO',
        variables: { key: 'SPO', index: 0 },
      });
      expect(FilterOptions[1]).toEqual({
        header: 'Outlook',
        variables: { key: 'Mail', index: 1 },
      });
      expect(FilterOptions[2]).toEqual({
        header: 'Teams',
        variables: { key: 'Chat', index: 2 },
      });
    });
  });

  describe('プロパティのデフォルト値テスト', () => {
    it('デフォルトプロパティが正しく設定される', () => {
      expect(DataSourceFilterDropdown.defaultProps).toEqual({
        className: '',
        listMode: undefined,
        resultEmptyList: undefined,
        searchMode: undefined,
      });
    });
  });
});
