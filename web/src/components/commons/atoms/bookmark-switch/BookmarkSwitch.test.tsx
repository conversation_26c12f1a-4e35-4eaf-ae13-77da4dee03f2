import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import BookmarkSwitch, { IBookmarkSwitchProps } from './BookmarkSwitch';

describe('BookmarkSwitch', () => {

  function renderComponent(
    onClick: IBookmarkSwitchProps['onClick'],
    isBookmarked: IBookmarkSwitchProps['isActive'],
  ) {
    return render(
      <div id="test">
        <BookmarkSwitch onClick={onClick} isActive={isBookmarked} />
      </div>,
    );
  }

  describe('when isBookmarked is true', () => {
    it('should attach is-active', () => {
      const { container } = renderComponent(jest.fn(), true);
      const $BookmarkSwitch = container.querySelector('#test > *');
      expect($BookmarkSwitch).not.toBeNull();
      if (!$BookmarkSwitch) return;

      expect($BookmarkSwitch).toHaveClass('is-active');
    });
  });

  describe('when isBookmarked is false', () => {
    it('should not attach is-active', () => {
      const { container } = renderComponent(jest.fn(), false);
      const $BookmarkSwitch = container.querySelector('#test > *');
      expect($BookmarkSwitch).not.toBeNull();
      if (!$BookmarkSwitch) return;

      expect($BookmarkSwitch.classList.contains('is-active')).toBe(false);
    });
  });
});
