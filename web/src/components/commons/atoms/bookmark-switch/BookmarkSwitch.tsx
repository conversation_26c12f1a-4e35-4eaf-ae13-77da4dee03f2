import * as React from 'react';
import PropTypes from 'prop-types';
import { Button } from '@fluentui/react-northstar';

// CSS
import './BookmarkSwitch.scss';
import { mergedClassName } from '../../../../utilities/commonFunction';

export interface IBookmarkSwitchProps {
  className?: string;
  isActive?: boolean;
  onClick?: (toBe: boolean, e: React.SyntheticEvent<HTMLElement>) => void;
}

const BookmarkSwitch: React.FC<IBookmarkSwitchProps> = ((props) => {
  const {
    className,
    isActive,
    onClick,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('bookmark-switch', className);
    return mergedClassName(base, isActive ? 'is-active' : undefined);
  }, [className, isActive]);

  // クリックハンドラ
  const handleOnClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (onClick) onClick(!isActive, e);
  }, [onClick, isActive]);

  return (
    <Button className={rootClassName} onClick={handleOnClick} size="small" text iconOnly>
      <span className="ui-icon">
        <svg viewBox="0 0 17 17" xmlns="http://www.w3.org/2000/svg" role="presentation">
          { isActive ? (
            <path className="ui-icon__filled" d="M9.5936 1.39533C9.22679 0.652082 8.16694 0.652077 7.80013 1.39532L5.86969 5.30682L1.5531 5.93406C0.732878 6.05325 0.405363 7.06121 0.998882 7.63975L4.1224 10.6844L3.38504 14.9836C3.24493 15.8005 4.10236 16.4234 4.83598 16.0378L9.69775 13.4796V11.9963C9.69775 11.1679 10.3693 10.4963 11.1978 10.4963C10.3693 10.4963 9.69775 9.82477 9.69775 8.99634C9.69775 8.16791 10.3693 7.49634 11.1978 7.49634C10.3693 7.49634 9.69775 6.82477 9.69775 5.99634C9.69775 5.19234 10.3303 4.53608 11.1249 4.49808L9.5936 1.39533ZM11.1978 8.49634C10.9216 8.49634 10.6978 8.7202 10.6978 8.99634C10.6978 9.27248 10.9216 9.49634 11.1978 9.49634H16.1978C16.4739 9.49634 16.6978 9.27248 16.6978 8.99634C16.6978 8.7202 16.4739 8.49634 16.1978 8.49634H11.1978ZM10.6978 5.99634C10.6978 5.7202 10.9216 5.49634 11.1978 5.49634H16.1978C16.4739 5.49634 16.6978 5.7202 16.6978 5.99634C16.6978 6.27248 16.4739 6.49634 16.1978 6.49634H11.1978C10.9216 6.49634 10.6978 6.27248 10.6978 5.99634ZM11.1978 11.4963C10.9216 11.4963 10.6978 11.7202 10.6978 11.9963C10.6978 12.2725 10.9216 12.4963 11.1978 12.4963H16.1978C16.4739 12.4963 16.6978 12.2725 16.6978 11.9963C16.6978 11.7202 16.4739 11.4963 16.1978 11.4963H11.1978Z" />
          ) : (
            <path className="ui-icon__outline" d="M7.41449 1.39532C7.78131 0.652065 8.84118 0.652085 9.20798 1.39535L11.2318 5.49641H15.811C16.0872 5.49641 16.311 5.72027 16.311 5.99641C16.311 6.27256 16.0872 6.49641 15.811 6.49641H11.2318C10.8512 6.49641 10.5035 6.2803 10.3351 5.93896L8.31123 1.83789L6.38081 5.74936C6.23514 6.0445 5.95358 6.24907 5.62787 6.2964L1.31127 6.92364L4.43479 9.96831C4.67048 10.198 4.77802 10.529 4.72239 10.8534L3.98502 15.1526L8.07857 13.0005C8.323 12.872 8.62531 12.966 8.75381 13.2104C8.88231 13.4548 8.78834 13.7571 8.54391 13.8856L4.45036 16.0377C3.71673 16.4234 2.85931 15.8005 2.99942 14.9835L3.73678 10.6844L0.613262 7.63972C0.019743 7.06118 0.347258 6.05321 1.16748 5.93403L5.48407 5.30679L7.41449 1.39532ZM10.3102 8.99646C10.3102 8.72032 10.534 8.49646 10.8102 8.49646H15.8102C16.0863 8.49646 16.3102 8.72032 16.3102 8.99646C16.3102 9.2726 16.0863 9.49646 15.8102 9.49646H10.8102C10.534 9.49646 10.3102 9.2726 10.3102 8.99646ZM10.8102 11.4965C10.534 11.4965 10.3102 11.7203 10.3102 11.9965C10.3102 12.2726 10.534 12.4965 10.8102 12.4965H15.8102C16.0863 12.4965 16.3102 12.2726 16.3102 11.9965C16.3102 11.7203 16.0863 11.4965 15.8102 11.4965H10.8102Z" />
          )}
        </svg>
      </span>
    </Button>
  );
});

BookmarkSwitch.propTypes = {
  className: PropTypes.string,
  isActive: PropTypes.bool,
  onClick: PropTypes.func,
};

BookmarkSwitch.defaultProps = {
  className: undefined,
  isActive: false,
  onClick: undefined,
};

export default BookmarkSwitch;
