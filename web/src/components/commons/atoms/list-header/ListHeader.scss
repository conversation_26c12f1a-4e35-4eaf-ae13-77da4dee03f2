@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.list-header {
  display: flex;
  max-width: 100vw; // Bug: 8926の対応
  min-height: 40px;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;

  background-color: var(--color-guide-background-2);
  font-size: 12px;

  padding-left: var(--length-margin-horizontal);
  padding-right: var(--length-margin-horizontal);

  @include media-pc {
    flex-wrap: wrap;
  }

  @include media-sp {
    user-select: none;
    white-space: nowrap;
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
  }
}
