import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import ListHeader from './ListHeader';
import mockMatchMedia from '../../../../mocks/match-media';

// mock matchMedia
mockMatchMedia();

jest.mock('../../../../utilities/environment');

describe('ListHeader', () => {
  describe('className', () => {
    const rootClassName = 'list-header';
    it('should have list-header', () => {
      const { container } = render(<ListHeader />);
      expect(container.children[0]).toHaveClass(rootClassName);
    });
  });

  describe('when contentRight is undefined', () => {
    it('should not show contentRight', () => {
      const { container } = render(<ListHeader content="aaa" contentRight={undefined} />);
      expect(container).toHaveTextContent('aaa');
      expect(container).not.toHaveTextContent('contentRight');
    });
  });

  describe('when contentRight is not undefined', () => {
    it('should show contentRight', () => {
      const { container } = render(<ListHeader content="aaa" contentRight="bbb" />);
      expect(container).toHaveTextContent('aaa');
      expect(container).toHaveTextContent('bbb');
    });
  });
});
