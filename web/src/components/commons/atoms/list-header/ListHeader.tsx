import * as React from 'react';
import PropTypes from 'prop-types';
import { Text, TextProps } from '@fluentui/react-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';

// CSS
import './ListHeader.scss';
import { isPC, isSP } from '../../../../utilities/mediaQuery';

export interface IListHeaderProps {
  className?: string;
  content?: TextProps['content'];
  contentRight?: TextProps['content'];
}

const ListHeader: React.FC<IListHeaderProps> = ((props) => {
  const {
    className,
    content,
    contentRight,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => mergedClassName('list-header', className), [className]);

  return (
    <div className={rootClassName}>
      {isPC() && <Text content={content} />}
      {isSP() && <Text content={content} truncated />}
      {contentRight && <Text content={contentRight} />}
    </div>
  );
});

ListHeader.propTypes = {
  className: PropTypes.string,
  content: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  contentRight: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
};

ListHeader.defaultProps = {
  className: undefined,
  content: '',
  contentRight: '',
};

export default ListHeader;
