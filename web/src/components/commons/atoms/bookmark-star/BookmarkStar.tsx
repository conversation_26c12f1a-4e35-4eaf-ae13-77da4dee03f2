import * as React from 'react';
import PropTypes from 'prop-types';
import { Button, StarIcon } from '@fluentui/react-northstar';

// CSS
import './BookmarkStar.scss';
import { mergedClassName } from '../../../../utilities/commonFunction';

export interface IBookmarkStarProps {
  className?: string;
  isBookmarked?: boolean;
  onClick?: (toBe: boolean, e: React.SyntheticEvent<HTMLElement>) => void;
}

const BookmarkStar: React.FC<IBookmarkStarProps> = ((props) => {
  const {
    className,
    isBookmarked,
    onClick,
  } = props;

  // タップ中フラグ
  const [isPressed, setIsPressed] = React.useState(false);
  const isPressedClassName = React.useMemo(() => (isPressed ? 'is-pressed' : ''), [isPressed]);

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('bookmark-star', className);
    return mergedClassName(
      mergedClassName(base, isPressedClassName ? 'is-pressed' : ''),
      isBookmarked ? 'is-active' : undefined,
    );
  }, [className, isBookmarked, isPressedClassName]);

  // クリックハンドラ
  const handleOnClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (onClick) onClick(!isBookmarked, e);
  }, [onClick, isBookmarked]);

  // タッチイベントハンドラ
  const onTouchStart = React.useCallback(() => {
    setIsPressed(true);
  }, []);
  const onTouchEnd = React.useCallback(() => {
    setIsPressed(false);
  }, []);

  return (
    <Button
      className={rootClassName}
      size="small"
      text
      iconOnly
      onClick={handleOnClick}
      onTouchStart={onTouchStart}
      onTouchEnd={onTouchEnd}
      onTouchCancel={onTouchEnd}
    >
      <StarIcon className="star-icon" />
    </Button>
  );
});

BookmarkStar.propTypes = {
  className: PropTypes.string,
  isBookmarked: PropTypes.bool,
  onClick: PropTypes.func,
};

BookmarkStar.defaultProps = {
  className: undefined,
  isBookmarked: false,
  onClick: undefined,
};

export default BookmarkStar;
