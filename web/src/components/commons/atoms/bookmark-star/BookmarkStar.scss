@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.bookmark-star {
  // fluent-uiのデフォルト挙動だとhover時常に内部が塗られてしまうので
  // スタイルの挙動を上書く

  // ☆のときのhover
  &:hover,
  &.ui-button:hover {
    .ui-icon__outline {
      display: block;
    }

    .ui-icon__filled {
      display: none;
    }
  }

  // ★のときのhover
  &.is-active,
  &.ui-button.is-active {
    .ui-icon__outline {
      display: none;
    }

    .ui-icon__filled {
      display: block;
    }
  }

  &.is-pressed,
  &.ui-button.is-pressed,
  &:active,
  &.ui-button:active {
    .ui-icon__outline {
      display: block;
    }

    .ui-icon__filled {
      display: block;
    }
  }

  .star-icon {
    color: var(--color-guide-brand-main-foreground);

    // ☆と★のときのhover
    &:hover {
      @include media-pc {
        color: var(--color-guide-brand-icon-hover);
      }
    }
  }
}
