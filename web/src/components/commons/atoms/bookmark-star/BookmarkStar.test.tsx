import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import { Simulate } from 'react-dom/test-utils';
import { queryElem } from '../../../../utilities/test';
import BookmarkStar, { IBookmarkStarProps } from './BookmarkStar';

describe('BookmarkStar', () => {

  function renderComponent(
    onClick: IBookmarkStarProps['onClick'],
    isBookmarked: IBookmarkStarProps['isBookmarked'],
  ) {
    return render(
      <div id="test">
        <BookmarkStar onClick={onClick} isBookmarked={isBookmarked} />
      </div>,
    );
  }

  describe('when isBookmarked is true', () => {
    it('should attach is-active', () => {
      const { container } = renderComponent(jest.fn(), true);
      const $bookmarkStar = container.querySelector('#test > *');
      expect($bookmarkStar).not.toBeNull();
      if (!$bookmarkStar) return;

      expect($bookmarkStar).toHaveClass('is-active');
    });
  });

  describe('when isBookmarked is false', () => {
    it('should not attach is-active', () => {
      const { container } = renderComponent(jest.fn(), false);
      const $bookmarkStar = container.querySelector('#test > *');
      expect($bookmarkStar).not.toBeNull();
      if (!$bookmarkStar) return;

      expect($bookmarkStar.classList.contains('is-active')).toBe(false);
    });
  });

  describe('is-pressed className', () => {
    describe('when the user touch the button', () => {
      function touchStartCase() {
        const { container } = render(<BookmarkStar />);
        Simulate.touchStart(queryElem(container, '.ui-button'));
        expect(container.children[0]).toHaveClass('is-pressed');
        return container;
      }

      it('should attach "is-pressed" at the root element', () => {
        touchStartCase();
      });

      describe('when the user stop touching the button', () => {
        it('should remove "is-pressed" from the root element', () => {
          const container = touchStartCase();
          Simulate.touchEnd(queryElem(container, '.ui-button'));
          expect(container.children[0]).not.toHaveClass('is-pressed');
        });
      });

      describe('when the user cancel touching the button', () => {
        it('should remove "is-pressed" from the root element', () => {
          const container = touchStartCase();
          Simulate.touchEnd(queryElem(container, '.ui-button'));
          expect(container.children[0]).not.toHaveClass('is-pressed');
        });
      });
    });
  });
});
