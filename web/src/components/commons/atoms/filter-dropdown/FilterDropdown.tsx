import * as React from 'react';
import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import {
  Dropdown, DropdownItemProps, DropdownProps, ObjectShorthandValue,
} from '@fluentui/react-northstar';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import tz from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';
import { AsyncState } from 'react-use/lib/useAsync';
import type { ISplitViewState, SplitViewDispatch } from '../../../domains/split-view/split-view-container/reducers/splitViewReducer';
import { ListMode, ListModeType } from '../../../domains/split-view/types/ListMode';
import useFilterOptionBehavior from '../../../../hooks/behaviors/useFilterOptionBehavior';
import { mergedClassName } from '../../../../utilities/commonFunction';
import { FilterLeftOptions, MaxFilterDisplayCount, OptionValues } from '../../../../utilities/filter/filterSettings';
import modifyDisplayDateOption from '../../../domains/utilities/logging/modifyDisplayDateOption';
import { ISplitViewListSingle } from '../../../domains/split-view/types/ISplitViewListSingle';
import DateRangeDialog from '../../molecules/date-range-dialog/DateRangeDialog';
import { SearchListMode, SearchModeType } from '../../../domains/split-view/types/SearchListMode';

import './FilterDropdown.scss';

// TODO: プラグインの記述を一箇所にまとめたい(要テストファイル修正)
dayjs.extend(utc);
dayjs.extend(tz);
dayjs.extend(isBetween);

// TODO: optionは固定値を渡している
const DateRangeOption = 6;

export interface IFilterDropdownProps {
  className?: string;
  selectedOption: number | undefined;
  selectedKey: 'displayDate';
  state: ISplitViewState;
  listMode?: ListModeType,
  dispatch: SplitViewDispatch;
  reportEvent: EventReporter,
  resultEmptyList?: AsyncState<FilterLeftOptions>,
  dateFilterRef: React.MutableRefObject<{
    from?: string;
    to?: string;
  }>;
  searchMode?: SearchModeType;
}
// フィルターオプションのヘッダ
export const FilterOptions = {
  displayDate: [
    {
      header: '期間指定なし',
      className: '',
    } as DropdownItemProps,
    {
      header: '24時間以内',
      className: '',
    } as DropdownItemProps,
    {
      header: '1週間以内',
      className: '',
    } as DropdownItemProps,
    {
      header: '1ヶ月以内',
      className: '',
    } as DropdownItemProps,
    {
      header: '半年以内',
      className: '',
    } as DropdownItemProps,
    {
      header: '1年以内',
      className: '',
    } as DropdownItemProps,
    {
      header: '期間を指定',
      className: '',
      variables: {
        key: 'range',
      },
    } as DropdownItemProps,
  ],
};

export function calculateResultCount(items: ISplitViewListSingle[], from?: string, to?: string) {
  const fromDateTime = from ? dayjs.tz(from, 'Asia/Tokyo') : dayjs.tz('1970-01-01', 'Asia/Tokyo').startOf('day');
  const toDateTime = (to ? dayjs.tz(to, 'Asia/Tokyo') : dayjs.tz(dayjs(), 'Asia/Tokyo')).add(1, 'day').startOf('day');
  return items.filter((item) => dayjs(item.reposUpdatedDate).isBetween(fromDateTime, toDateTime, undefined, '[]')).length;
}

const FilterDropdown: React.FC<IFilterDropdownProps> = (props) => {
  const {
    selectedOption,
    selectedKey,
    state,
    listMode,
    dispatch,
    reportEvent,
    className,
    resultEmptyList,
    dateFilterRef,
    searchMode,
  } = props;

  const [open, setOpen] = React.useState(false);
  // 「期間を指定」の始まり
  const [defaultFrom, setDefaultFrom] = React.useState<Date | undefined>();
  // 「期間を指定」の終わり
  const [defaultTo, setDefaultTo] = React.useState<Date | undefined>();

  const [dateRangeHeaderName, setDateRangeHeaderName] = React.useState<string | undefined>();

  const rootClassName = React.useMemo(() => mergedClassName('filter-dropdown', className), [className]);
  // 古い条件と新しい条件を比較し、更新する関数を提供。関数内でdispatchを使用し更新。
  const filterOptionBehavior = useFilterOptionBehavior(state, dispatch);

  function isDateRange(value: DropdownItemProps) {
    if (value?.variables?.key === 'range') {
      return true;
    }
    return false;
  }
  // OptionValuesからdisplayDateを取得
  // optionValuesは選択肢を持っている
  const optionValues = OptionValues[selectedKey];
  // state.list が空ならカウントを出さない
  const hasListItems = (state.list?.length ?? 0) > 0;
  // ドロップダウンをクリックした時に展開される部分を作成
  const options = FilterOptions[selectedKey].map((option, index) => {
    const count = (
      (resultEmptyList?.value ?? { displayDate: [], kind: [] })[selectedKey][index] ?? 0
    );
    const capped = count > MaxFilterDisplayCount ? `${count - 1}+` : count;
    // チャットモード判定
    const isChat = searchMode === SearchListMode.Chat;
    // className の決定
    let classNameComputed = '';
    if (hasListItems) {
      if (isChat) {
        classNameComputed = option.className ?? '';
      } else if (count === 0) {
        classNameComputed = 'result-empty';
      } else {
        classNameComputed = option.className ?? '';
      }
    }
    return {
      ...option,
      header: isDateRange(option) ? dateRangeHeaderName ?? option.header : option.header,
      variables: {
        ...option.variables,
        count: resultEmptyList?.loading === true ? '計算中' : capped,
      },
      className: classNameComputed,
      selected: optionValues[index]?.option === selectedOption,
    } as DropdownItemProps;
  });

  function onSelectionChanged(
    data: DropdownProps,
  ) {
    const { option, span } = optionValues[data.highlightedIndex ?? (optionValues.length - 1)];
    if (isDateRange(data.value as ObjectShorthandValue<DropdownItemProps>)) {
      setOpen(true);
    } else {
      setDefaultFrom(undefined);
      setDefaultTo(undefined);
      setDateRangeHeaderName(undefined);
      // span情報を使って期間を計算
      let fromTimeString = '';
      if (Array.isArray(span)) {
      // span = [number, unit] の場合
        const [amount, unit] = span; // 例: [-1, 'week']
        const fromTime = dayjs().tz('Asia/Tokyo').add(amount, unit as dayjs.ManipulateType);
        fromTimeString = fromTime.toISOString();
      }
      // 選択された値を保持
      dateFilterRef.current.from = fromTimeString;
      dateFilterRef.current.to = undefined;
      const filterOptions = state.context.filter ?? [];
      const changedFilter = [
        ...filterOptions.filter(({ key }) => key !== selectedKey),
        { key: selectedKey, option },
      ];
      const resultCount = (resultEmptyList?.value?.[selectedKey] ?? [])[
        data.highlightedIndex ?? (optionValues.length - 1)
      ];
      reportEvent({
        type: EventReportType.USER_EVENT,
        name: listMode === ListMode.SEARCH ? 'EXECUTE_SEARCH_LIST_FILTER' : 'EXECUTE_BOOKMARK_LIST_FILTER',
        customProperties: {
          filterAction: selectedKey,
          filterOptionName: data.searchQuery,
          filterConditions: modifyDisplayDateOption(changedFilter),
          resultCount,
        },
      });
      // 新しいoptionでフィルターの状態を更新
      filterOptionBehavior.onSelectOption({
        key: selectedKey,
        option,
      });
    }
  }

  const onCancel = React.useCallback(() => setOpen(false), []);
  // モーダルを閉じるとstateのfilterが更新される
  const onModalClosed = React.useCallback(
    ({ from, to }: { from?: string, to?: string }) => {
      const optionNew = (!from && !to) ? 0 : DateRangeOption;

      const filterOptions = state.context.filter ?? [];
      const changedFilter = [
        ...filterOptions.filter(({ key }) => key !== selectedKey),
        {
          key: selectedKey, option: optionNew, from, to,
        },
      ];

      reportEvent({
        type: EventReportType.USER_EVENT,
        name: listMode === ListMode.SEARCH ? 'EXECUTE_SEARCH_LIST_FILTER' : 'EXECUTE_BOOKMARK_LIST_FILTER',
        customProperties: {
          filterAction: selectedKey,
          filterOptionName: FilterOptions[selectedKey][optionNew].header,
          filterConditions: modifyDisplayDateOption(changedFilter),
          from,
          to,
          resultCount: calculateResultCount(state.list, from, to),
        },
      });
      // 選択されたフィルターにdispatch
      filterOptionBehavior.onSelectOption({
        key: selectedKey,
        option: optionNew,
        from,
        to,
      });

      if (from || to) {
        setDateRangeHeaderName(`${from} ~ ${to}`);
        dateFilterRef.current.from = from;
        dateFilterRef.current.to = to;
      } else {
        setDateRangeHeaderName(undefined);
        dateFilterRef.current.from = '';
        dateFilterRef.current.to = undefined;
      }
      setOpen(false);
    },
    [state.context.filter,
      state.list,
      selectedKey,
      reportEvent,
      listMode,
      filterOptionBehavior,
      dateFilterRef],
  );
  const defaultOption = optionValues.findIndex(
    ({ option }) => option === selectedOption,
  ) ?? (options.length - 1);

  // searchMode切り替えでフィルターを初期化
  React.useEffect(() => {
    setDefaultFrom(undefined);
    setDefaultTo(undefined);
    setDateRangeHeaderName(undefined);
    dateFilterRef.current = {
      from: undefined,
      to: undefined,
    };
  }, [dateFilterRef, searchMode]);

  React.useEffect(() => {
    if (listMode === ListMode.BOOKMARKS) return;
    // if (searchMode === SearchListMode.Chat) return;
    // 画面初期化時の処理
    const displayDateFilter = state?.context?.filter?.find((f) => f.key === 'displayDate') as { from?: string, to?: string } | undefined;
    const fromValue = displayDateFilter?.from;
    const toValue = displayDateFilter?.to;
    if (fromValue || toValue) {
      setDateRangeHeaderName(`${fromValue} ~ ${toValue}`);
    }
    setDefaultFrom(fromValue ? new Date(fromValue) : undefined);
    setDefaultTo(toValue ? new Date(toValue) : undefined);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state]);
  return (
    <>
      <Dropdown
        className={rootClassName}
        placeholder="期間を選択"
        items={options}
        defaultHighlightedIndex={defaultOption}
        value={options[defaultOption]}
        onSearchQueryChange={(_, data) => onSelectionChanged(data)}
        renderItem={(Item, p) => (
          <Item
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...p}
            header={isDateRange(p) ? p.header : `${p.header} (${p.variables.count})`}
            className={mergedClassName('filter-item', p.className)}
          />
        )}
        renderSelectedItem={(Item, option) => (
          <Item
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...option}
            className={mergedClassName('filter-item selected', option.className)}
          />
        )}
      />
      <DateRangeDialog
        open={open}
        onModalClosed={onModalClosed}
        onCancel={onCancel}
        defaultFrom={defaultFrom}
        defaultTo={defaultTo}
      />
    </>
  );
};

FilterDropdown.defaultProps = {
  className: undefined,
  listMode: undefined,
  resultEmptyList: undefined,
  searchMode: SearchListMode.DEFAULT,
};

export default FilterDropdown;
