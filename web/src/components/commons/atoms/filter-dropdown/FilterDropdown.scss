@import '../../../../styles/variables';

.filter-dropdown {
  flex-grow: 1;
  width: 9rem;

  & [role=button] span {
    font-size: 0.75rem;
    font-weight: normal;
  }

  & [role=listbox] .ui-box {
    font-size: 0.75rem;
  }

  .ui-dropdown__container {
    max-width: 100%;

    button {
      display: flex;
      justify-content: flex-start;
    }
  }

  .filter-item {
    &:hover {
      background: var(--color-guide-brand-background-1);
    }

    &.result-empty {
      background-color: var(--color-guide-foreground-1-dark);

      &:hover {
        background-color: var(--color-guide-foreground-1-dark);
      }
    }

    &.selected {
      background-color: var(--color-guide-brand-foreground-active);
    }
  }
}
