import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import InformationButton from './InformationButton';

describe('InformationButton', () => {
  describe('className', () => {
    const rootClassName = 'information-button';

    describe('when className = undefined', () => {
      const className = undefined;

      it('should have only "information-button"', () => {
        const { container } = render(<InformationButton className={className} />);
        expect(container.children[0]).toHaveClass(rootClassName);
      });
    });

    describe('when className = abc', () => {
      const className = 'abc';

      it('should have "information-button" & "abc"', () => {
        const { container } = render(<InformationButton className={className} />);
        expect(container.children[0]).toHaveClass(rootClassName);
        expect(container.children[0]).toHaveClass('abc');
      });
    });
  });

  describe('QuestionCircleIcon', () => {
    it('should show ui-icon', () => {
      const { container } = render(<InformationButton className="information-button" />);
      expect(container.children[0]).toContainHTML('こちら');
    });
  });
});
