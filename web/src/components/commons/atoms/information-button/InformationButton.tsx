import * as React from 'react';
import PropTypes from 'prop-types';
import { Button } from '@fluentui/react-northstar';

// CSS
import './InformationButton.scss';
import { mergedClassName } from '../../../../utilities/commonFunction';

export interface IInformationButtonProps {
  className?: string;
  onClick?: (e: React.SyntheticEvent<HTMLElement>) => void;
}

const InformationButton: React.FC<IInformationButtonProps> = ((props) => {
  const {
    className,
    onClick,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(
    () => mergedClassName('information-button', className), [className],
  );

  // クリックハンドラ
  const handleOnClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (onClick) onClick(e);
  }, [onClick]);

  return (
    <Button
      className={rootClassName}
      text
      iconOnly
      onClick={handleOnClick}
      content="こちら"
    />
  );
});

InformationButton.propTypes = {
  className: PropTypes.string,
  onClick: PropTypes.func,
};

InformationButton.defaultProps = {
  className: undefined,
  onClick: undefined,
};

export default InformationButton;
