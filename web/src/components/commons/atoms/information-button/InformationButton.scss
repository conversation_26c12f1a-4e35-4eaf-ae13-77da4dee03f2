@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.information-button {
  color: var(--color-guide-brand-main-foreground) !important;
  margin-bottom: 3px;

  @include media-sp {
    margin-bottom: 3.5px;
  }

  &:hover {
    @include media-pc {
      color: var(--color-guide-brand-main-foreground) !important;
      text-decoration: underline;
    }
  }

  .ui-icon {
    color: var(--color-guide-brand-main-foreground) !important;

    &:hover {
      @include media-pc {
        color: var(--color-guide-brand-main-foreground) !important;
      }
    }
  }
}
