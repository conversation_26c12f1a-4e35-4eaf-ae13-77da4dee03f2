import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import DataSourceIcon, { IDataSourceIconProps } from './DataSourceIcon';

describe('DataSourceIcon', () => {

  function renderComponent(
    kind: IDataSourceIconProps['kind'],
  ) {
    return render(
      <div id="test">
        <DataSourceIcon kind={kind} />
      </div>,
    );
  }

  describe('when kind is undefined', () => {
    it('should not attach anything', () => {
      const { container } = renderComponent('Other');
      const $datasourceIcon = container.querySelector('#test > *');
      expect($datasourceIcon).not.toBeNull();
      if (!$datasourceIcon) return;

      expect($datasourceIcon).not.toHaveClass('spo-icon');
      expect($datasourceIcon).not.toHaveClass('mail-icon');
      expect($datasourceIcon).not.toHaveClass('chat-icon');
    });
  });

  describe('when kind is Other', () => {
    it('should not attach anything', () => {
      const { container } = renderComponent('Other');
      const $datasourceIcon = container.querySelector('#test > *');
      expect($datasourceIcon).not.toBeNull();
      if (!$datasourceIcon) return;

      expect($datasourceIcon).not.toHaveClass('spo-icon');
      expect($datasourceIcon).not.toHaveClass('mail-icon');
      expect($datasourceIcon).not.toHaveClass('chat-icon');
    });
  });

  describe('when kind is SPO', () => {
    it('should attach spo-icon', () => {
      const { container } = renderComponent('SPO');
      const $datasourceIcon = container.querySelector('#test > *');
      expect($datasourceIcon).not.toBeNull();
      if (!$datasourceIcon) return;

      expect($datasourceIcon).toHaveClass('spo-icon');
      expect($datasourceIcon).not.toHaveClass('mail-icon');
      expect($datasourceIcon).not.toHaveClass('chat-icon');
    });
  });

  describe('when kind is Mail', () => {
    it('should attach mail-icon', () => {
      const { container } = renderComponent('Mail');
      const $datasourceIcon = container.querySelector('#test > *');
      expect($datasourceIcon).not.toBeNull();
      if (!$datasourceIcon) return;

      expect($datasourceIcon).not.toHaveClass('spo-icon');
      expect($datasourceIcon).toHaveClass('mail-icon');
      expect($datasourceIcon).not.toHaveClass('chat-icon');
    });
  });

  describe('when kind is Chat', () => {
    it('should attach chat-icon', () => {
      const { container } = renderComponent('Chat');
      const $datasourceIcon = container.querySelector('#test > *');
      expect($datasourceIcon).not.toBeNull();
      if (!$datasourceIcon) return;

      expect($datasourceIcon).not.toHaveClass('spo-icon');
      expect($datasourceIcon).not.toHaveClass('mail-icon');
      expect($datasourceIcon).toHaveClass('chat-icon');
    });
  });
});
