import * as React from 'react';
import { ChatIcon, EmailIcon, TextBulletListTreeIcon } from '@fluentui/react-northstar';

import { mergedClassName } from '../../../../utilities/commonFunction';
import { DataSourceKind, DataSourceKindType } from '../../../../types/DataSourceKind';

// CSS
import './DataSourceIcon.scss';

export interface IDataSourceIconProps {
  className?: string;
  kind?: DataSourceKindType;
}

const DataSourceIcon: React.FC<IDataSourceIconProps> = ((props) => {
  const {
    className,
    kind,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('datasource-icon', className);
    return mergedClassName(base, kind ? `${kind.toLocaleLowerCase()}-icon` : '');
  }, [className, kind]);

  return (
    <div className={rootClassName}>
      {kind === DataSourceKind.SPO && <TextBulletListTreeIcon outline className="logo-icon" />}
      {kind === DataSourceKind.Mail && <EmailIcon outline className="logo-icon" />}
      {kind === DataSourceKind.Chat && <ChatIcon outline className="logo-icon" />}
    </div>
  );
});

DataSourceIcon.defaultProps = {
  className: '',
  kind: undefined,
};

export default DataSourceIcon;
