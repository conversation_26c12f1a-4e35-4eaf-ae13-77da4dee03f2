import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import SortButton, { SortButtonState } from './SortButton';
import { SearchListMode } from '../../../domains/split-view/types/SearchListMode';
import { ListMode } from '../../../domains/split-view/types/ListMode';

describe('SortButton', () => {
  describe('className', () => {
    const rootClassName = 'sort-button';

    describe('when className = undefined', () => {
      const className = undefined;

      it('should have only "sort-button"', () => {
        const { container } = render(<SortButton className={className} />);
        expect(container.children[0]).toHaveClass(rootClassName);
      });
    });

    describe('when className = abc', () => {
      const className = 'abc';

      it('should have "sort-button" & "abc"', () => {
        const { container } = render(<SortButton className={className} />);
        expect(container.children[0]).toHaveClass(rootClassName);
        expect(container.children[0]).toHaveClass('abc');
      });
    });
  });

  describe('state is default', () => {
    it('should Contain "arrow-default-sort-icon"', () => {
      const { container } = render(<SortButton state={SortButtonState.DEFAULT} />);
      expect(container.children[0]).toHaveClass('is-default');
      expect(container.children[0]).not.toHaveClass('is-ascending');
      expect(container.children[0]).not.toHaveClass('is-descending');
      expect(container).toContainHTML('arrow-default-sort-icon');
    });
  });

  describe('state is ascending', () => {
    it('should Contain "arrow-ascending-sort-icon"', () => {
      const { container } = render(<SortButton state={SortButtonState.ASC} />);
      expect(container.children[0]).toHaveClass('is-asc');
      expect(container.children[0]).not.toHaveClass('is-default');
      expect(container.children[0]).not.toHaveClass('is-descending');
      expect(container).toContainHTML('arrow-ascending-sort-icon');
    });
  });

  describe('state is descending', () => {
    it('should Contain "arrow-descending-sort-icon"', () => {
      const { container } = render(<SortButton state={SortButtonState.DESC} />);
      expect(container.children[0]).toHaveClass('is-desc');
      expect(container.children[0]).not.toHaveClass('is-default');
      expect(container.children[0]).not.toHaveClass('is-ascending');
      expect(container).toContainHTML('arrow-descending-sort-icon');
    });
  });

  describe('AI functionality - button label display based on searchMode and listMode', () => {
    describe('when listMode is BOOKMARKS', () => {
      // ブックマークモードでは常に「日付」を表示
      it('should display "日付" regardless of searchMode', () => {
        const { getByText } = render(
          <SortButton
            listMode={ListMode.BOOKMARKS}
            searchMode={SearchListMode.DEFAULT}
          />,
        );
        expect(getByText('日付')).toBeInTheDocument();
      });

      it('should display "日付" even when searchMode is Chat', () => {
        const { getByText } = render(
          <SortButton
            listMode={ListMode.BOOKMARKS}
            searchMode={SearchListMode.Chat}
          />,
        );
        expect(getByText('日付')).toBeInTheDocument();
      });
    });

    describe('when listMode is not BOOKMARKS', () => {
      describe('when searchMode is Chat', () => {
        // チャットモードでは「スコア」を表示
        it('should display "スコア" when searchMode is Chat', () => {
          const { getByText } = render(
            <SortButton
              listMode={ListMode.SEARCH}
              searchMode={SearchListMode.Chat}
            />,
          );
          expect(getByText('スコア')).toBeInTheDocument();
        });

        it('should display "スコア" when listMode is INITIAL_DISPLAY and searchMode is Chat', () => {
          const { getByText } = render(
            <SortButton
              listMode={ListMode.INITIAL_DISPLAY}
              searchMode={SearchListMode.Chat}
            />,
          );
          expect(getByText('スコア')).toBeInTheDocument();
        });
      });

      describe('when searchMode is DEFAULT', () => {
        // デフォルトモードでは「日付」を表示
        it('should display "日付" when searchMode is DEFAULT', () => {
          const { getByText } = render(
            <SortButton
              listMode={ListMode.SEARCH}
              searchMode={SearchListMode.DEFAULT}
            />,
          );
          expect(getByText('日付')).toBeInTheDocument();
        });

        it('should display "日付" when listMode is INITIAL_DISPLAY and searchMode is DEFAULT', () => {
          const { getByText } = render(
            <SortButton
              listMode={ListMode.INITIAL_DISPLAY}
              searchMode={SearchListMode.DEFAULT}
            />,
          );
          expect(getByText('日付')).toBeInTheDocument();
        });
      });

      describe('when searchMode is undefined (default props)', () => {
        // searchModeが未定義の場合はデフォルト値が使用され「日付」を表示
        it('should display "日付" when searchMode is undefined', () => {
          const { getByText } = render(
            <SortButton listMode={ListMode.SEARCH} />,
          );
          expect(getByText('日付')).toBeInTheDocument();
        });
      });
    });

    describe('when listMode is undefined', () => {
      // listModeが未定義の場合はsearchModeに基づいて決定
      it('should display "スコア" when listMode is undefined and searchMode is Chat', () => {
        const { getByText } = render(
          <SortButton searchMode={SearchListMode.Chat} />,
        );
        expect(getByText('スコア')).toBeInTheDocument();
      });

      it('should display "日付" when listMode is undefined and searchMode is DEFAULT', () => {
        const { getByText } = render(
          <SortButton searchMode={SearchListMode.DEFAULT} />,
        );
        expect(getByText('日付')).toBeInTheDocument();
      });
    });
  });
});
