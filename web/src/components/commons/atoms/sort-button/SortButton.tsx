import * as React from 'react';
import PropTypes from 'prop-types';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowSortIcon,
  Text,
  Button,
} from '@fluentui/react-northstar';
import { ValueOf } from '../../../../utilities/type';
import { SearchListMode, SearchModeType } from '../../../domains/split-view/types/SearchListMode';
import { ListMode, ListModeType } from '../../../domains/split-view/types/ListMode';

// CSS
import './SortButton.scss';
import { mergedClassName } from '../../../../utilities/commonFunction';

export const SortButtonState = {
  DEFAULT: 'default',
  ASC: 'asc',
  DESC: 'desc',
};
export type SplitViewListSingleViewType = ValueOf<typeof SortButtonState>;

export interface ISortButtonProps {
  className?: string;
  searchMode?: SearchModeType;
  listMode?: ListModeType;
  state?: string;
  onClick?: (e: React.SyntheticEvent<HTMLElement>) => void;
}

const SortButton: React.FC<ISortButtonProps> = ((props) => {
  const {
    className,
    searchMode,
    listMode,
    state,
    onClick,
  } = props;

  // 表示するラベルを動的に決定
  const buttonLabel = React.useMemo(() => {
    if (listMode === ListMode.BOOKMARKS) {
      return '日付';
    }
    return searchMode === 'Chat' ? 'スコア' : '日付';
  }, [searchMode, listMode]);

  // クラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('sort-button', className);
    return mergedClassName(base, `is-${state}`);
  }, [className, state]);

  const isDefault = React.useMemo(() => (
    state === SortButtonState.DEFAULT
  ), [state]);
  const isAscending = React.useMemo(() => (
    state === SortButtonState.ASC
  ), [state]);
  const isDescending = React.useMemo(() => (
    state === SortButtonState.DESC
  ), [state]);

  // クリックハンドラ
  const handleOnClick = React.useCallback((e: React.SyntheticEvent<HTMLElement>) => {
    if (onClick) onClick(e);
  }, [onClick]);

  return (
    <Button
      className={rootClassName}
      onClick={handleOnClick}
      content={<Text weight="regular" className="button-label">{buttonLabel}</Text>}
      text
      size="small"
      icon={(
        <span className="ui-icon">
          {isDefault && <ArrowSortIcon className="sort-icon arrow-default-sort-icon" size="small" />}
          {isDescending && <ArrowDownIcon className="sort-icon arrow-descending-sort-icon" size="small" />}
          {isAscending && <ArrowUpIcon className="sort-icon arrow-ascending-sort-icon" size="small" />}
        </span>
      )}
    />
  );
});

SortButton.propTypes = {
  className: PropTypes.string,
  state: PropTypes.string,
  onClick: PropTypes.func,
  listMode: PropTypes.string,
};

SortButton.defaultProps = {
  className: undefined,
  state: undefined,
  onClick: undefined,
  searchMode: SearchListMode.DEFAULT,
  listMode: undefined,
};

export default SortButton;
