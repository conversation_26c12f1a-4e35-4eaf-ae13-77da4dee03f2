@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.sort-button {
  .ui-icon {

    &.arrow-default-sort-icon {
      >* {
        width: 16px;
        height: 16px;
      }

      padding-left: 8px;
    }

    &.arrow-ascending-sort-icon {
      >* {
        width: 12px;
        height: 12px;
      }

      padding-left: 13px;
    }

    &.arrow-descending-sort-icon {
      >* {
        width: 12px;
        height: 12px;
      }

      padding-left: 13px;
    }

    display: flex;
    align-items: center;
  }

  .button-label {
    font-size: 0.75rem;
  }

  &:hover {

    .button-label {
      @include media-pc {
        color: var(--color-guide-brand-main-foreground);
        font-weight: bold;
      }
    }

    .sort-icon {
      @include media-pc {
        color: var(--color-guide-brand-main-foreground);
      }
    }
  }

  &.is-desc {
    .button-label {
      color: var(--color-guide-brand-main-foreground);
    }

    .arrow-descending-sort-icon {
      color: var(--color-guide-brand-main-foreground);
    }
  }

  &.is-asc {
    .button-label {
      color: var(--color-guide-brand-main-foreground);
    }

    .arrow-ascending-sort-icon {
      color: var(--color-guide-brand-main-foreground);
    }
  }
}
