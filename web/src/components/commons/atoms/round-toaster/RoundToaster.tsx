import * as React from 'react';
import PropTypes from 'prop-types';

// CSS
import './RoundToaster.scss';
import { mergedClassName } from '../../../../utilities/commonFunction';

export interface IRoundToasterProps {
  className?: string;
  isActive?: boolean;
  children?: React.ReactNode | null,
}

const RoundToaster: React.FC<IRoundToasterProps> = ((props) => {
  const {
    className,
    isActive,
    children,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('round-toaster', className);
    return mergedClassName(base, isActive ? 'is-active' : undefined);
  }, [className, isActive]);

  return (
    <div className={rootClassName}>
      {children}
    </div>
  );
});

RoundToaster.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node,
  isActive: PropTypes.bool,
};

RoundToaster.defaultProps = {
  className: undefined,
  children: undefined,
  isActive: false,
};

export default RoundToaster;
