@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.round-toaster {
  width: 100%;
  max-width: 250px;
  height: 65px;
  padding: 0 1em;

  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: center;

  background-color: var(--color-custom-transparent-black-90);
  border-radius: 40px;

  color: var(--color-guide-foreground-3);
  user-select: none;

  position: fixed;
  bottom: 32px;
  left: 50%;

  @include media-sp {
    max-width: 280px;
  }

  // アニメーション
  transition: transform 0.2s ease-in;
  transform: translateX(-50%) translateY(200%);

  &.is-active {
    transition: transform 0.2s ease-out;
    transform: translateX(-50%) translateY(0);
  }
}
