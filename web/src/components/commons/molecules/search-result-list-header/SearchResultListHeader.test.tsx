import * as React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { queryElem } from '../../../../utilities/test';
import SearchResultListHeader from './SearchResultListHeader';
import mockMatchMedia from '../../../../mocks/match-media';
import { SearchListMode } from '../../../domains/split-view/types/SearchListMode';

// mock matchMedia
mockMatchMedia();

describe('SearchResultListHeader', () => {
  const MAX_COUNT = 100;
  const SINGLE_STRING = 'テスト';
  const MULTI_STRING = 'テスト テスト2';
  const MULTI_STRING_WITH_AND = 'テスト AND テスト2';
  const MULTI_STRING_WITH_OR = 'テスト OR テスト2';
  const MULTI_STRING_WITH_BRACKETS = 'テスト (テスト2 OR テスト3)';

  function renderComponent(
    className: string | undefined,
    count: number | null | undefined,
    maxCount: number | null | undefined,
    words: string | null | undefined,
    searchMode?: string,
  ) {
    return render(
      <SearchResultListHeader
        className={className}
        count={count}
        maxCount={maxCount}
        words={words}
        searchMode={searchMode}
      />,
    );
  }

  describe('when className is passed', () => {
    it('should set className to a root element', () => {
      const { container } = renderComponent('abc', undefined, MAX_COUNT, undefined);
      expect(queryElem(container, '.search-result-list-header')).toHaveClass('abc');
    });
  });

  describe('when count is not passed', () => {
    it('should show "「words」を含む検索結果 0件"', () => {
      const { container } = renderComponent(undefined, undefined, MAX_COUNT, SINGLE_STRING);
      expect(container).toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent('0件');
    });
  });

  describe('when count: 1 is passed', () => {
    it('should show "「words」を含む検索結果 {count}件"', () => {
      const { container } = renderComponent(undefined, 1, MAX_COUNT, SINGLE_STRING);
      expect(container).toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent('1件');
    });
  });

  describe('when count: 99.5 is passed', () => {
    it('should show "「words」を含む検索結果 100件"', () => {
      const { container } = renderComponent(undefined, 99.5, MAX_COUNT, SINGLE_STRING);
      expect(container).toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent('100件');
    });
  });

  describe('when count: over MAX_COUNT is passed', () => {
    it('should show "「words」を含む検索結果 上位{MAX_COUNT}件"', () => {
      const { container } = renderComponent(undefined, MAX_COUNT + 1, MAX_COUNT, SINGLE_STRING);
      expect(container).toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent(`上位${MAX_COUNT}件`);
    });
  });

  describe('when count: -3.5 is passed', () => {
    it('should show "「words」を含む検索結果 4件"', () => {
      const { container } = renderComponent(undefined, -3.5, MAX_COUNT, SINGLE_STRING);
      expect(container).toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent('4件');
    });
  });

  describe('when words: multiple strings are passed', () => {
    it('should show "「テスト テスト2」を含む検索結果 {count}件"', () => {
      const { container } = renderComponent(undefined, 1, MAX_COUNT, MULTI_STRING);
      expect(container).toHaveTextContent('「テスト テスト2」を含む検索結果');
      expect(container).toHaveTextContent('1件');
    });
  });

  describe('when words: multiple strings are passed', () => {
    it('should show "「テスト AND テスト2」を含む検索結果 {count}件"', () => {
      const { container } = renderComponent(undefined, 1, MAX_COUNT, MULTI_STRING_WITH_AND);
      expect(container).toHaveTextContent('「テスト AND テスト2」を含む検索結果');
      expect(container).toHaveTextContent('1件');
    });
  });

  describe('when words: multiple strings are passed', () => {
    it('should show "「テスト OR テスト2」を含む検索結果 {count}件"', () => {
      const { container } = renderComponent(undefined, 1, MAX_COUNT, MULTI_STRING_WITH_OR);
      expect(container).toHaveTextContent('「テスト OR テスト2」を含む検索結果');
      expect(container).toHaveTextContent('1件');
    });
  });

  describe('when words: multiple strings are passed', () => {
    it('should show "「テスト (テスト2 OR テスト3)」を含む検索結果 {count}件"', () => {
      const { container } = renderComponent(undefined, 1, MAX_COUNT, MULTI_STRING_WITH_BRACKETS);
      expect(container).toHaveTextContent('「テスト (テスト2 OR テスト3)」を含む検索結果');
      expect(container).toHaveTextContent('1件');
    });
  });

  describe('searchModeがチャットモードの場合', () => {
    // searchModeがチャットモードの場合にcontentが空になることをテスト
    it('wordsが設定されていてもcontentが空文字列になること', () => {
      const { container } = renderComponent(undefined,
        5,
        MAX_COUNT,
        SINGLE_STRING,
        SearchListMode.Chat);
      expect(container).not.toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent('5件');
    });

    // searchModeがチャットモードの場合でも件数表示は正常に動作することをテスト
    it('件数表示は正常に表示されること', () => {
      const { container } = renderComponent(undefined,
        10,
        MAX_COUNT,
        SINGLE_STRING,
        SearchListMode.Chat);
      expect(container).not.toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent('10件');
    });

    // searchModeがチャットモードで上限を超えた場合のテスト
    it('上限を超えた場合でも「上位{MAX_COUNT}件」が表示されること', () => {
      const { container } = renderComponent(undefined,
        MAX_COUNT + 10,
        MAX_COUNT,
        SINGLE_STRING,
        SearchListMode.Chat);
      expect(container).not.toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent(`上位${MAX_COUNT}件`);
    });
  });

  describe('searchModeがデフォルトモードの場合', () => {
    // searchModeがデフォルトモードの場合は従来通りの動作をすることをテスト
    it('従来通りcontentが表示されること', () => {
      const { container } = renderComponent(
        undefined, 5, MAX_COUNT, SINGLE_STRING, SearchListMode.DEFAULT,
      );
      expect(container).toHaveTextContent('「テスト」を含む検索結果');
      expect(container).toHaveTextContent('5件');
    });
  });

});
