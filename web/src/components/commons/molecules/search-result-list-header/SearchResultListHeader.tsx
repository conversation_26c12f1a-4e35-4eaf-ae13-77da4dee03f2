import * as React from 'react';
import PropTypes from 'prop-types';
import { mergedClassName } from '../../../../utilities/commonFunction';
import { makePositiveInt } from '../../../../utilities/number';
import { printTemplate } from '../../../../utilities/text';
import ListHeader from '../../atoms/list-header/ListHeader';
import { SearchListMode, SearchModeType } from '../../../domains/split-view/types/SearchListMode';

// CSS
import './SearchResultListHeader.scss';

export interface ISearchResultListHeaderProps {
  className?: string;
  count?: number | null;
  maxCount?: number | null;
  words?: string | null;
  searchMode?: SearchModeType,
}

const SearchResultListHeader: React.FC<ISearchResultListHeaderProps> = ((props) => {
  const {
    className,
    count,
    maxCount,
    words,
    searchMode,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(
    () => mergedClassName('search-result-list-header', className),
    [className],
  );

  const [calculatedCount, isLimit] = React.useMemo(() => {
    if (!count) return [0, false];
    if (!maxCount) return [count, false];
    return (count > maxCount) ? [maxCount, true] : [count, false];
  }, [count, maxCount]);

  // 見出し
  // searchMode がチャットモードの場合は content を空にする
  const content = React.useMemo(() => {
    // チャットモードの場合は何も表示しない
    if (searchMode === SearchListMode.Chat) {
      return '';
    }
    const joinedWordsWithBracket = words ? `「${words}」` : '';
    return printTemplate`${joinedWordsWithBracket}を含む検索結果`;
  }, [words, searchMode]);

  // 件数表記
  const counterContent = React.useMemo(
    () => printTemplate`${isLimit ? '上位' : ''}${String(makePositiveInt(calculatedCount))}件`,
    [calculatedCount, isLimit],
  );

  return (
    <ListHeader
      className={rootClassName}
      content={content}
      contentRight={counterContent}
    />
  );
});

SearchResultListHeader.propTypes = {
  className: PropTypes.string,
  count: PropTypes.number,
  maxCount: PropTypes.number,
  words: PropTypes.string,
  searchMode: PropTypes.string,
};

SearchResultListHeader.defaultProps = {
  className: undefined,
  count: null,
  maxCount: null,
  words: '',
  searchMode: SearchListMode.DEFAULT,
};

export default SearchResultListHeader;
