import * as React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { queryElem } from '../../../../utilities/test';
import MessageToaster, { ToasterMessage, ToasterMessageType } from './MessageToaster';

describe('MessageToaster', () => {
  function renderComponent(
    className: string | undefined,
    messageType: ToasterMessageType | undefined,
    isActive: boolean | undefined,
  ) {
    return render(
      <MessageToaster className={className} messageType={messageType} isActive={isActive} />,
    );
  }

  describe('when isActive is true', () => {
    it('should attach is-active', () => {
      const { container } = renderComponent('', undefined, true);
      expect(queryElem(container, '.message-toaster')).toHaveClass('is-active');
    });
  });

  describe('when isActive is false', () => {
    it('should not attach is-active', () => {
      const { container } = renderComponent('', undefined, false);
      expect(queryElem(container, '.message-toaster')).not.toHaveClass('is-active');
    });
  });

  describe('when messageType is BOOKMARKED', () => {
    it('should show "お気に入りに保存されました" with a star icon', () => {
      const { container } = renderComponent('', ToasterMessage.BOOKMARKED, false);
      expect(container).toHaveTextContent('お気に入りに保存されました');
      expect(container.querySelector('.message-toaster-star')).toBeInTheDocument();
    });
  });

  describe('when messageType is UN_BOOKMARKED', () => {
    it('should show "お気に入りから削除されました" with a star icon', () => {
      const { container } = renderComponent('', ToasterMessage.UN_BOOKMARKED, false);
      expect(container).toHaveTextContent('お気に入りから削除されました');
      expect(container.querySelector('.message-toaster-star')).toBeInTheDocument();
    });
  });

  describe('when messageType is MAX_BOOKMARKS', () => {
    it('should show "お気に入りから削除されました" with no star icons', () => {
      const { container } = renderComponent('', ToasterMessage.MAX_BOOKMARKS, false);
      expect(container).toHaveTextContent('登録可能数の上限のためお気に入りに保存できません');
      expect(container.querySelector('.message-toaster-star')).not.toBeInTheDocument();
    });
  });
});
