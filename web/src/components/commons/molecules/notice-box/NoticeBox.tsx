import * as React from 'react';
import PropTypes from 'prop-types';
import { Text } from '@fluentui/react-northstar';
import { ExclamationCircleIcon } from '@fluentui/react-icons-northstar';

// CSS
import './NoticeBox.scss';
import { mergedClassName } from '../../../../utilities/commonFunction';

export interface INoticeBoxProps {
  className?: string;
  message?: string;
  useIcon?: boolean;
}

const NoticeBox: React.FC<INoticeBoxProps> = (props) => {
  const {
    className,
    message,
    useIcon,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => mergedClassName('notice-box', className), [className]);

  return (
    <div className={rootClassName}>
      { useIcon && (
        <ExclamationCircleIcon size="largest" className="notice-box-icon" />
      )}
      <Text className="notice-box-text">{ message ?? '' }</Text>
    </div>
  );
};

NoticeBox.propTypes = {
  className: PropTypes.string,
  message: PropTypes.string,
  useIcon: PropTypes.bool,
};

NoticeBox.defaultProps = {
  className: undefined,
  message: undefined,
  useIcon: true,
};

export default NoticeBox;
