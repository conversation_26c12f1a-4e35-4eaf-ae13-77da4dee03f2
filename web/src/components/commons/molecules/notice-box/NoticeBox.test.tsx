import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import NoticeBox from './NoticeBox';

describe('NoticeBox', () => {

  describe('className', () => {
    const rootClassName = 'notice-box';

    describe('when className = undefined', () => {
      const className = undefined;

      it('should have only "notice-box"', () => {
        const { container } = render(<NoticeBox {...{ className }} />);
        expect(container.children[0]).toHaveClass(rootClassName);
      });
    });

    describe('when className = abc', () => {
      const className = 'abc';

      it('should have "notice-box" & "abc"', () => {
        const { container } = render(<NoticeBox {...{ className }} />);
        expect(container.children[0]).toHaveClass(rootClassName);
        expect(container.children[0]).toHaveClass('abc');
      });
    });

    describe('when useIcon = true', () => {
      const useIcon = true;

      it('should have "notice-box-icon"', () => {
        const { container } = render(<NoticeBox {...{ useIcon }} />);
        expect(container.children[0]).toContainHTML('notice-box-icon');
      });
    });

    describe('when useIcon = false', () => {
      const useIcon = false;

      it('should have "notice-box-icon"', () => {
        const { container } = render(<NoticeBox {...{ useIcon }} />);
        expect(container.children[0]).not.toContainHTML('notice-box-icon');
      });
    });

    describe('when message = aaa', () => {
      const message = 'aaa';

      it('should show "aaa"', () => {
        const { container } = render(<NoticeBox {...{ message }} />);
        expect(container.children[0]).toHaveTextContent('aaa');
      });
    });

    describe('when message = ""', () => {
      const message = '';

      it('should show "aaa"', () => {
        const { container } = render(<NoticeBox {...{ message }} />);
        expect(container.children[0]).not.toHaveTextContent('aaa');
        expect(container.children[0]).toHaveTextContent('');
      });
    });
  });
});
