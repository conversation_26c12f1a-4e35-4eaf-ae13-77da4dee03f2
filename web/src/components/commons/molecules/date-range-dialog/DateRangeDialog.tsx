import * as React from 'react';
import { Dialog } from '@fluentui/react-northstar';
import { DateFormatTemplate, toDateString } from '../../../../utilities/date';
import DateRangeSelector from '../date-range-selector/DateRangeSelector';
import './DataRangeDialog.scss';

const DateRangeDialog = ({
  open, onModalClosed, onCancel, defaultFrom, defaultTo,
}: {
  open: boolean,
  onModalClosed: ({ from, to }: {
    from?: string | undefined;
    to?: string | undefined;
  }) => void,
  onCancel: () => void,
  defaultFrom: Date | undefined,
  defaultTo: Date | undefined,
}) => {

  const [fromDate, setFromDate] = React.useState<Date | undefined>(undefined);
  const [toDate, setToDate] = React.useState<Date | undefined>(undefined);
  React.useEffect(() => {
    // 画面初期化時の処理
    setFromDate(defaultFrom);
    setToDate(defaultTo);
  }, [defaultFrom, defaultTo]);

  const [hasError, setHasError] = React.useState(false);
  React.useEffect(() => {
    if (!open) {
      setHasError(false);
    }
  }, [open]);

  const handleConfirm = React.useCallback(() => {
    onModalClosed({
      from: toDateString(fromDate, DateFormatTemplate.YearMonthDay),
      to: toDateString(toDate, DateFormatTemplate.YearMonthDay),
    });
  }, [fromDate, toDate, onModalClosed]);

  const handleCancel = React.useCallback(() => {
    setFromDate(defaultFrom);
    setToDate(defaultTo);
    onCancel();
  }, [defaultFrom, defaultTo, onCancel]);

  const onDateChange = React.useCallback(({ from, to }) => {
    setFromDate(from);
    setToDate(to);
  }, []);

  const onErrorChange = React.useCallback((value) => {
    setHasError(value);
  }, []);

  return (
    <Dialog
      confirmButton={{ content: '設定', disabled: hasError }}
      onConfirm={handleConfirm}
      open={open}
      content={(
        <DateRangeSelector
          from={fromDate ? toDateString(fromDate, DateFormatTemplate.YearMonthDay) : undefined}
          to={toDate ? toDateString(toDate, DateFormatTemplate.YearMonthDay) : undefined}
          onDateChange={onDateChange}
          onErrorChange={onErrorChange}
          onCancel={handleCancel}
        />
)}
      header="期間を指定"
    />
  );
};

export default DateRangeDialog;
