import { But<PERSON>, CloseIcon } from '@fluentui/react-northstar';
import * as React from 'react';
import PropTypes from 'prop-types';
import { mergedClassName } from '../../../../utilities/commonFunction';

// CSS
import './ModalCardTop.scss';
import { ReactEvent, TouchOrMouseEvent } from '../../../../utilities/event';
import BookmarkStar from '../../atoms/bookmark-star/BookmarkStar';

export interface IModalCardTopProps {
  className?: string;
  showBookmark?: boolean;
  onClickClose?: (e: ReactEvent) => void,
  onClickBookmark?: (toBe: boolean, e: ReactEvent) => void,
  isBookmarked?: boolean;
  onTouchStart?: (e: TouchOrMouseEvent) => void,
  onTouchMove?: (e: TouchOrMouseEvent) => void,
  onTouchEnd?: (e: TouchOrMouseEvent) => void,
  onTouchCancel?: (e: TouchOrMouseEvent) => void,
}

/**
 * ModalCardTop
 * @param props
 */
const ModalCardTop: React.FC<IModalCardTopProps> = (props) => {
  const {
    className,
    showBookmark,
    onClickClose,
    onClickBookmark,
    isBookmarked,
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    onTouchCancel,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => mergedClassName('modal-card-top', className), [className]);

  // 閉じるボタンのハンドラ
  const handleOnClickClose = React.useCallback((e: ReactEvent) => {
    if (onClickClose) onClickClose(e);
  }, [onClickClose]);

  // お気に入りボタンのハンドラ
  const handleOnClickBookmark = React.useCallback((toBe: boolean, e: ReactEvent) => {
    if (onClickBookmark) onClickBookmark(toBe, e);
  }, [onClickBookmark]);

  // タッチイベントのハンドラ
  const handleOnTouchStart = React.useCallback((e: TouchOrMouseEvent) => {
    if (onTouchStart) onTouchStart(e);
  }, [onTouchStart]);
  const handleOnTouchMove = React.useCallback((e: TouchOrMouseEvent) => {
    if (onTouchMove) onTouchMove(e);
  }, [onTouchMove]);
  const handleOnTouchEnd = React.useCallback((e: TouchOrMouseEvent) => {
    if (onTouchEnd) onTouchEnd(e);
  }, [onTouchEnd]);
  const handleOnTouchCancel = React.useCallback((e: TouchOrMouseEvent) => {
    if (onTouchCancel) onTouchCancel(e);
  }, [onTouchCancel]);

  return (
    <div
      className={rootClassName}
      onTouchStart={handleOnTouchStart}
      onTouchMove={handleOnTouchMove}
      onTouchEnd={handleOnTouchEnd}
      onTouchCancel={handleOnTouchCancel}
    >
      <Button
        className="modal-card-top-close"
        title="Close"
        icon={<CloseIcon />}
        iconOnly
        text
        onClick={handleOnClickClose}
      />
      {showBookmark && (
        // show the star icon if showBookmark is true
        <BookmarkStar
          className="modal-card-top-bookmark"
          isBookmarked={isBookmarked}
          onClick={handleOnClickBookmark}
        />
      )}
    </div>
  );

};

ModalCardTop.propTypes = {
  className: PropTypes.string,
  showBookmark: PropTypes.bool,
  isBookmarked: PropTypes.bool,
  onClickBookmark: PropTypes.func,
  onClickClose: PropTypes.func,
  onTouchStart: PropTypes.func,
  onTouchMove: PropTypes.func,
  onTouchEnd: PropTypes.func,
  onTouchCancel: PropTypes.func,
};

ModalCardTop.defaultProps = {
  className: undefined,
  showBookmark: true,
  isBookmarked: false,
  onClickBookmark: undefined,
  onClickClose: undefined,
  onTouchStart: undefined,
  onTouchMove: undefined,
  onTouchEnd: undefined,
  onTouchCancel: undefined,
};

export default ModalCardTop;
