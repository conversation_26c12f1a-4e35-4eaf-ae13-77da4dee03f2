@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.modal-card-top {
  height: 44px;
  background-color: var(--color-guide-background);

  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-start;

  padding-left: var(--length-margin-horizontal-sp);
  padding-right: var(--length-margin-horizontal-sp);

  overflow: hidden;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.modal-card-top-close {
  position: relative;
  left: -11px;
  margin-right: auto;
}

.modal-card-top-bookmark {
  position: relative;
  left: 3px;
}

.modal-card-top-sharing {
  margin-top: 1px;
  margin-left: 12px;
}
