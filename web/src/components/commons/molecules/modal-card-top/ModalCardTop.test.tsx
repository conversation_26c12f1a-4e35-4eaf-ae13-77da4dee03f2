import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import ModalCardTop from './ModalCardTop';
import { queryElem } from '../../../../utilities/test';
// import { queryElem } from '../../../../utilities/test';

describe('ModalCardTop', () => {

  describe('className', () => {
    const baseNameClass = 'modal-card-top';

    describe('when className = undefined', () => {
      const className = undefined;

      it('should have only "modal-card-top"', () => {
        const { container } = render(<ModalCardTop {...{ className }} />);
        expect(container.children[0]).toHaveClass(baseNameClass);
      });
    });

    describe('when className = "abc"', () => {
      const className = 'abc';

      it('should have "modal-card-top" and "abc"', () => {
        const { container } = render(<ModalCardTop {...{ className }} />);
        expect(container.children[0]).toHaveClass(baseNameClass);
        expect(container.children[0]).toHaveClass('abc');
      });
    });

    describe('showBookmark', () => {
      const effectTarget = '.modal-card-top-bookmark';

      describe('when showBookmark is not set', () => {
        it('should show a bookmark star', () => {
          const { container } = render(<ModalCardTop />);
          expect(queryElem(container, effectTarget)).toBeInTheDocument();
        });
      });

      describe('when showBookmark is true', () => {
        const showBookmark = true;

        it('should show a bookmark star', () => {
          const { container } = render(<ModalCardTop {...{ showBookmark }} />);
          expect(queryElem(container, effectTarget)).toBeInTheDocument();
        });
      });

      describe('when showBookmark is false', () => {
        const showBookmark = false;

        it('should not show a bookmark star', () => {
          const { container } = render(<ModalCardTop {...{ showBookmark }} />);
          expect(() => queryElem(container, effectTarget)).toThrow();
        });
      });
    });
  });
});
