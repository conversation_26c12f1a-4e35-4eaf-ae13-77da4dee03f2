import * as React from 'react';
import { IChatReaction } from '../../../../types/IChatResponse';
import ReactionSingle, { IReactionSingleProps } from '../../atoms/reaction-single/ReactionSingle';
import { IGraphUser } from '../../../../types/IGraphUser';

export interface IReactionListProps {
  reactions: IChatReaction[];
  fetchDisplayNames?: (reactions: IChatReaction[]) => Promise<IGraphUser[] | void>;
  groupByReactions?: (users: IGraphUser[], reactions: IChatReaction[]) => IReactionSingleProps[];
}
const ReactionList: React.FC<IReactionListProps> = (props) => {
  const { reactions, fetchDisplayNames, groupByReactions } = props;
  const [groupedReactions, setGroupedReactions] = React.useState<IReactionSingleProps[]>([]);

  React.useEffect(() => {
    if (!fetchDisplayNames || !groupByReactions) return;

    (async () => {
      const users = await fetchDisplayNames(reactions);
      if (!users) return;
      const grouped = groupByReactions(users, reactions);
      const sorted = grouped.sort((a, b) => b.users.length - a.users.length);
      setGroupedReactions(sorted);
    })();
  }, [fetchDisplayNames, groupByReactions, reactions]);

  return (
    <div>
      {(groupedReactions).map(
        ({ reactionType, users }) => (<ReactionSingle reactionType={reactionType} users={users} />),
      )}
    </div>
  );
};

ReactionList.defaultProps = {
  fetchDisplayNames: async () => [],
  groupByReactions: () => [],
};

export default ReactionList;
