import * as React from 'react';
import PropTypes from 'prop-types';
import { mergedClassName } from '../../../../utilities/commonFunction';
import { makePositiveInt } from '../../../../utilities/number';
import ListHeader from '../../atoms/list-header/ListHeader';

// CSS
import './BookmarkListHeader.scss';

export interface IBookmarkListHeaderProps {
  className?: string;
  count?: number | null;
}

const BookmarkListHeader: React.FC<IBookmarkListHeaderProps> = ((props) => {
  const {
    className,
    count,
  } = props;

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(
    () => mergedClassName('bookmark-list-header', className),
    [className],
  );

  // 件数
  const counterContent = React.useMemo(() => `${String(makePositiveInt(count ?? 0))}件`, [count]);

  return (
    <ListHeader
      className={rootClassName}
      content="お気に入り"
      contentRight={counterContent}
    />
  );
});

BookmarkListHeader.propTypes = {
  className: PropTypes.string,
  count: PropTypes.number,
};

BookmarkListHeader.defaultProps = {
  className: undefined,
  count: null,
};

export default BookmarkListHeader;
