import * as React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { queryElem } from '../../../../utilities/test';
import BookmarkListHeader from './BookmarkListHeader';
import mockMatchMedia from '../../../../mocks/match-media';

// mock matchMedia
mockMatchMedia();

describe('BookmarksListHeader', () => {
  function renderComponent(
    className: string | undefined,
    count: number | null | undefined,
  ) {
    return render(
      <BookmarkListHeader className={className} count={count} />,
    );
  }

  describe('when className is passed', () => {
    it('should set className to a root element', () => {
      const { container } = renderComponent('abc', undefined);
      expect(queryElem(container, '.bookmark-list-header')).toHaveClass('abc');
    });
  });

  describe('when count is not passed', () => {
    it('should show "お気に入り（0件）"', () => {
      const { container } = renderComponent(undefined, undefined);
      expect(container).toHaveTextContent('お気に入り');
      expect(container).toHaveTextContent('0件');
    });
  });

  describe('when count: 1 is passed', () => {
    it('should show "お気に入り {count}件"', () => {
      const { container } = renderComponent(undefined, 1);
      expect(container).toHaveTextContent('お気に入り');
      expect(container).toHaveTextContent('1件');
    });
  });

  describe('when count: 99.5 is passed', () => {
    it('should show "お気に入り 100件"', () => {
      const { container } = renderComponent(undefined, 99.5);
      expect(container).toHaveTextContent('お気に入り');
      expect(container).toHaveTextContent('100件');
    });
  });

  describe('when count: -3.5 is passed', () => {
    it('should show "お気に入り 4件"', () => {
      const { container } = renderComponent(undefined, -3.5);
      expect(container).toHaveTextContent('お気に入り');
      expect(container).toHaveTextContent('4件');
    });
  });
});
