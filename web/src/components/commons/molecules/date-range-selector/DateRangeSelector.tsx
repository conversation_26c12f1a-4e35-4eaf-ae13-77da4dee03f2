import PropTypes from 'prop-types';
import * as React from 'react';
import { Button, CloseIcon } from '@fluentui/react-northstar';
import { createTheme } from '@fluentui/theme';
import {
  DatePicker,
  IDatePicker,
  mergeStyleSets,
  defaultDatePickerStrings,
  IDatePickerStrings,
  IDateFormatting,
} from '@fluentui/react';

// CSS
import './DateRangeSelector.scss';
import { useUpdate } from 'react-use';

const geraniumTheme = {
  palette: {
    themeDarker: '#5D3E3D',
    themeDark: '#D56E6A',
    themeDarkAlt: '#BF3F39',
    themePrimary: '#D56E6A',
    themeSecondary: '#D56E6A',
    themeTertiary: '#D56E6A',
    themeLight: '#E9B0AF',
    themeLighter: '#ECC0C0',
    themeLighterAlt: '#FBEFEF',
    redDark: '#D56E6A',
  },
  semanticColors: {
    errorText: '#D56E6A',
  },
};

const dateRangeSelectorMessages = {
  INVALID_DATE: '不正な日付が入力されています',
  OUT_OF_BOUNDARY: '指定できない日付が入力されたためリセットされました',
  REVERSE_RANGE: '開始日より前の終了日が指定されています',
  RESET_STATUS: '不正な日付「{0}」が入力されたため、「{1}」にリセットされました',
};

const localizedStrings = {
  ...defaultDatePickerStrings,
  days: ['日', '月', '火', '水', '木', '金', '土'],
  shortDays: ['日', '月', '火', '水', '木', '金', '土'],
  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  shortMonths: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  goToToday: '今日',
  invalidInputErrorMessage: dateRangeSelectorMessages.INVALID_DATE,
  isOutOfBoundsErrorMessage: dateRangeSelectorMessages.OUT_OF_BOUNDARY,
  isResetStatusMessage: dateRangeSelectorMessages.RESET_STATUS,
} as IDatePickerStrings;

const localizedDateFormatter = {
  formatDay(date) {
    return date.getDate().toString();
  },
  formatMonth(date) {
    return `${date.getMonth() + 1}月`;
  },
  formatMonthDayYear(date) {
    return `${date.getFullYear()}年 ${date.getMonth() + 1}月 ${date.getDate()}日`;
  },
  formatMonthYear(date) {
    return `${date.getFullYear()}年 ${date.getMonth() + 1}月`;
  },
  formatYear(date) {
    return `${date.getFullYear()}年`;
  },
} as IDateFormatting;

export interface IDateRangeSelectorProps {
  from?: string;
  to?: string;
  onDateChange: (value: {
    from?: Date, to?: Date
  }) => void,
  onErrorChange: (hasError: boolean) => void
  onCancel: () => void
}

const DateRangeSelector: React.FC<IDateRangeSelectorProps> = (
  {
    from, to, onDateChange, onErrorChange, onCancel,
  },
) => {
  const theme = createTheme(geraniumTheme);
  const update = useUpdate();
  const TODAY = new Date();
  const fromRef = React.useRef<IDatePicker>(null);
  const toRef = React.useRef<IDatePicker>(null);
  const fromDate = from ? new Date(from) : undefined;
  const toDate = to ? new Date(to) : undefined;
  const [selectedFrom, setSelectedFrom] = React.useState(fromDate);
  const [selectedTo, setSelectedTo] = React.useState(toDate);
  const [reverseError, setReverseError] = React.useState<string | undefined>();
  const checkReverseRangeSelected = (fromInput?: Date, toInput?: Date) => {
    if (fromInput && toInput && fromInput > toInput) {
      setReverseError(dateRangeSelectorMessages.REVERSE_RANGE);
      return true;
    }
    setReverseError(undefined);
    return false;
  };

  const styles = mergeStyleSets({
    root: { selectors: { '> *': { marginBottom: 15 } } },
    control: { maxWidth: 300, marginBottom: 15 },
  });

  const onSelectFromDate = (selectedFromDate: Date | undefined | null) => {
    const selectedFromDateValue = selectedFromDate ?? undefined;
    const hasError = checkReverseRangeSelected(selectedFromDateValue, selectedTo);
    onErrorChange(hasError);
    setSelectedFrom(selectedFromDateValue);
    if (!hasError) {
      onDateChange({ from: selectedFromDateValue, to: selectedTo });
    }
  };

  const onSelectToDate = (selectedToDate: Date | undefined | null) => {
    const selectedToDateValue = selectedToDate ?? undefined;
    const hasError = checkReverseRangeSelected(selectedFrom, selectedToDateValue);
    onErrorChange(hasError);
    setSelectedTo(selectedToDateValue);
    if (!hasError) {
      onDateChange({ from: selectedFrom, to: selectedToDateValue });
    }
  };

  const rerender = React.useCallback(() => {
    update();
  }, [update]);

  const onClickFromClear = React.useCallback((): void => {
    setSelectedFrom(undefined);
    onErrorChange(false);
    fromRef.current?.focus();
  }, [onErrorChange]);

  const onClickToClear = React.useCallback((): void => {
    setSelectedTo(undefined);
    onErrorChange(false);
    toRef.current?.focus();
  }, [onErrorChange]);

  const onFormatDate = (date?: Date): string => (!date ? '' : `${date.getFullYear()}/${(date.getMonth() + 1)}/${date.getDate()}`);

  return (
    <>
      <div className="date-range-selector-close">
        <Button
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={onCancel}
        />
      </div>

      {/* 開始日 */}
      <div className="date-range-container">
        <DatePicker
          allowTextInput
          componentRef={fromRef}
          placeholder="開始日(yyyy/M/d)"
          value={selectedFrom}
          maxDate={TODAY}
          onBlur={rerender}
          formatDate={onFormatDate}
          dateTimeFormatter={localizedDateFormatter}
          onSelectDate={onSelectFromDate}
          className={styles.control}
          strings={localizedStrings}
          theme={theme}
          calendarProps={{ theme, calendarDayProps: { theme } }}
          textField={{
            theme,
            style: {
              borderRadius: '4px',
              backgroundColor: '#f3f2f1',
            },
          }}
        />
        <Button
          onClick={onClickFromClear}
          className="clear-button"
          content="日付のクリア"
        />
      </div>
      {/* 終了日 */}
      <div className="date-range-container">
        <DatePicker
          allowTextInput
          componentRef={toRef}
          placeholder="終了日(yyyy/M/d)"
          value={selectedTo}
          maxDate={TODAY}
          onBlur={rerender}
          formatDate={onFormatDate}
          dateTimeFormatter={localizedDateFormatter}
          onSelectDate={onSelectToDate}
          className={styles.control}
          strings={localizedStrings}
          theme={theme}
          calendarProps={{ theme, calendarDayProps: { theme } }}
          textField={{
            theme,
            style: {
              borderRadius: '4px',
              backgroundColor: '#f3f2f1',
            },
          }}
        />
        <Button
          onClick={onClickToClear}
          className="clear-button"
          content="日付のクリア"
        />
      </div>

      <div className="error">{reverseError}</div>
    </>
  );
};

DateRangeSelector.propTypes = {
  from: PropTypes.string,
  to: PropTypes.string,
};

DateRangeSelector.defaultProps = {
  from: undefined,
  to: undefined,
};

export default DateRangeSelector;
