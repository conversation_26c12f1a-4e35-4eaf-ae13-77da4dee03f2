@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.date-range-selector-close {
  position: absolute;
  top: 18px;
  right: 18px;
  z-index: 1;

  &:hover {
    .ui-icon__filled {
      fill: var(--color-guide-brand-icon-hover);
    }
  }
}

.date-range-container {
  display: flex;
  align-items: baseline;
  column-gap: 0.5rem;

  /* stylelint-disable-next-line selector-class-pattern */
  .ms-TextField-fieldGroup {
    border-radius: 4px;
  }
}

.clear-button.ui-button {
  padding-left: 0;
  padding-right: 0;

  .ui-button__content {
    font-size: 0.8rem;
    font-weight: 400;
    padding-left: 0.3rem;
    padding-right: 0.3rem;
  }
}

.error {
  height: 2em;
  color: var(--color-guide-brand-main-background);
  font-size: 12px;
}
