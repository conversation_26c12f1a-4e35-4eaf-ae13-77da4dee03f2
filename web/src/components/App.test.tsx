import { EventReportType } from '@avanade-teams/app-insights-reporter';
import React from 'react';
import '@testing-library/jest-dom';
import '../mocks/match-media-loader';
import { render, waitFor, screen } from '@testing-library/react';
import { TeamsTheme, useTeamsInfo } from '@avanade-teams/teams-info';
import { MemoryRouter } from 'react-router-dom';
import useComponentInitUtility from '../hooks/utilities/useComponentInitUtility';
import environment from '../utilities/environment';
import { queryElem } from '../utilities/test';
import App from './App';

// mock <BrowserRouter> to fake a routing behavior
jest.mock('react-router-dom', () => {
  const original = jest.requireActual('react-router-dom');
  return {
    ...original,
    // ignore type check because this is a test mock
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    // eslint-disable-next-line react/prop-types
    BrowserRouter: ({ children }) => <div className="mock_BrowserRouter">{ children }</div>,
  };
});

jest.mock('../utilities/environment');

jest.mock('@avanade-teams/teams-info');
const useTeamsInfoMock = (useTeamsInfo as jest.Mock).mockReturnValue({
  currentTheme: TeamsTheme.DEFAULT,
});

jest.mock('@avanade-teams/auth');

jest.mock('../hooks/utilities/useComponentInitUtility', () => jest.fn());
const reportEventMock = jest.fn();
const useComponentInitMock = useComponentInitUtility as jest.Mock;
useComponentInitMock.mockReturnValue([
  false,
  [reportEventMock],
  undefined,
  undefined,
  undefined,
  'abc',
]);

jest.mock('./pages/Config', () => () => <p className="mock_Config">config</p>);
jest.mock('./pages/Home', () => () => <p className="mock_Home">home</p>);

describe('App.tsx', () => {

  beforeEach(() => {
    useTeamsInfoMock.mockClear();
    reportEventMock.mockClear();
  });

  function renderComponent(path = '/') {
    return render(
      <MemoryRouter initialEntries={[path]}>
        <App />
      </MemoryRouter>,
    );
  }

  it('should report OPEN_APP', () => {
    renderComponent();
    expect(reportEventMock).toHaveBeenCalledWith({
      type: EventReportType.SYS_EVENT,
      name: 'OPEN_APP',
    });
  });

  describe('when window.location.pathname includes "/config"', () => {
    beforeEach(() => {
      // mock window.location
      Object.defineProperty(window, 'location', { value: { pathname: '/config' } });
    });

    it('should report OPEN_CONFIG', () => {
      renderComponent();
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_EVENT,
        name: 'OPEN_CONFIG',
      });
    });
  });

  it('should report APP_THEME_ON_INIT', () => {
    renderComponent();
    expect(reportEventMock).toHaveBeenCalledWith({
      type: EventReportType.SYS_EVENT,
      name: 'APP_THEME_ON_INIT',
      customProperties: {
        theme: TeamsTheme.DEFAULT,
        oid: 'abc',
      },
    });
  });

  describe('when an authorization error passed', () => {
    it('should show an alert element and should report the error', async () => {
      useComponentInitMock.mockReturnValueOnce([
        false,
        [reportEventMock],
        undefined,
        'error-text',
        undefined,
        'abc',
      ]);
      renderComponent();

      await waitFor(() => expect(screen.getByText('ERROR: error-text')).toBeInTheDocument());

      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'TOKEN_INIT_FAIL',
        error: new Error('error-text'),
      });
    });
  });

  describe('logging dynamic global variables', () => {
    // We moved this feature as a hook: useDynamicGlobalVarsLog.
    // That has individual tests, so you don't need the env logging tests
    // in this test file any more in the future.

    function resetEnvs() {
      // reset the all env variables to test
      environment.REACT_APP_ROUTE_PREFIX = '0';
      environment.REACT_APP_CONNECTION_STRING = 'a';
      environment.REACT_APP_SHAREPOINT_HOST_NAME = 'b';
    }

    beforeEach(() => {
      resetEnvs();
    });

    describe('when the all string global variables are available', () => {
      it('should report GET_GLOBAL_VARIABLES', () => {
        renderComponent();
        expect(reportEventMock).toHaveBeenCalledWith({
          type: EventReportType.SYS_EVENT,
          name: 'GET_GLOBAL_VARIABLES',
          customProperties: {
            routerPrefix: '0',
            connectionString: 'a',
            spoHostName: 'b',
          },
        });
      });
    });
  });

  describe('routing', () => {
    describe('when REACT_APP_ROUTE_PREFIX is "/abc/"', () => {
      beforeEach(() => {
        environment.REACT_APP_ROUTE_PREFIX = '/abc/';
      });

      describe('when the url path is "/abc/"', () => {
        const path = '/abc/';

        it('should show Home component', () => {
          renderComponent(path);
          expect(screen.getByText('home')).toBeInTheDocument();
        });
      });

      describe('when the url path is "/abc"', () => {
        const path = '/abc';

        it('should show Home component', () => {
          renderComponent(path);
          expect(screen.getByText('home')).toBeInTheDocument();
        });
      });

      describe('when the url path is "/abc/config/"', () => {
        const path = '/abc/config/';

        it('should show Config component', () => {
          renderComponent(path);
          expect(screen.getByText('config')).toBeInTheDocument();
        });
      });

      describe('when the url path is "/abc/config"', () => {
        const path = '/abc/config';

        it('should show Config component', () => {
          renderComponent(path);
          expect(screen.getByText('config')).toBeInTheDocument();
        });
      });

      describe('when the url path does not match with any routes', () => {
        const path = '/ab';

        it('should not show Home and Config components', () => {
          const { container } = renderComponent(path);
          expect(() => queryElem(container, '.mock_Home')).toThrow();
          expect(() => queryElem(container, '.mock_Config')).toThrow();
        });
      });

    });

    describe('when REACT_APP_ROUTE_PREFIX is not set', () => {
      beforeEach(() => {
        environment.REACT_APP_ROUTE_PREFIX = '';
      });

      describe('when the url path is "/"', () => {
        const path = '/';

        it('should show Home component', () => {
          renderComponent(path);
          expect(screen.getByText('home')).toBeInTheDocument();
        });
      });

      describe('when the url path is "/config"', () => {
        const path = '/config';

        it('should show Config component', () => {
          renderComponent(path);
          expect(screen.getByText('config')).toBeInTheDocument();
        });
      });

      describe('when the url path is "/config/"', () => {
        const path = '/config/';

        it('should show Config component', () => {
          renderComponent(path);
          expect(screen.getByText('config')).toBeInTheDocument();
        });
      });

      describe('when the url path does not match with any routes', () => {
        const path = '/configuration';

        it('should not show Home and Config components', () => {
          const { container } = renderComponent(path);
          expect(() => queryElem(container, '.mock_Home')).toThrow();
          expect(() => queryElem(container, '.mock_Config')).toThrow();
        });
      });
    });

  });
});
