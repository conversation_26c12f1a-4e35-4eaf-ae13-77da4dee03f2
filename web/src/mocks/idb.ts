export const storeMock = {
  clear: jest.fn().mockResolvedValue(undefined),
  put: jest.fn().mockResolvedValue(undefined),
  get: jest.fn().mockResolvedValue(undefined),
  delete: jest.fn().mockResolvedValue(undefined),
};

export const txMock = {
  done: Promise.resolve(),
  objectStore: jest.fn().mockReturnValue(storeMock),
};

export const dbMock = {
  getAll: jest.fn().mockResolvedValue(undefined),
  get: jest.fn().mockResolvedValue(undefined),
  put: jest.fn().mockResolvedValue(undefined),
  delete: jest.fn().mockResolvedValue(undefined),
  close: jest.fn(),
  transaction: jest.fn().mockReturnValue(txMock),
};

export function clearIdbMocks() {
  storeMock.clear.mockClear();
  storeMock.put.mockClear();
  storeMock.get.mockClear();
  storeMock.delete.mockClear();
  txMock.objectStore.mockClear();
  dbMock.getAll.mockClear();
  dbMock.put.mockClear();
  dbMock.get.mockClear();
  dbMock.delete.mockClear();
  dbMock.close.mockClear();
}
