/* global jest */
import { MediaQueryStrings } from '../utilities/mediaQuery';

function mockMatchMedia() {
  const mock = jest.fn();
  Object.defineProperty(global.window, 'matchMedia', {
    value: mock.mockImplementation((query) => ({
      // デフォルトでPC基準にする
      matches: query === MediaQueryStrings.PC,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
  return mock;
}

export default mockMatchMedia;
