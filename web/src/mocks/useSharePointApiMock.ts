import * as React from 'react';
import { ISharePointListItemResponseWithAttachment } from '../types/ISharePointListItemResponseWithAttachment';
import { ISharePointListsSingleResponse } from '../types/ISharePointListsSingleResponse';

/**
 * Teamsの外部でデバッグしたいときなどに使うmock
 * @example containerなどで既存のuseSPListの代わりに挿入する
 * const useSpListResults = useSPListMock(spoTokenProvider);
 */

export interface ISharePointListsResponse {
  'odata.metadata'?: string | null;
  'odata.nextLink'?: string | null;
  value?: ISharePointListsSingleResponse[] | null;
}

export type FetchSPODetail = (
  baseUrl: string, editLink: string,
) => Promise<ISharePointListItemResponseWithAttachment>;

export type FetchSPOList = (
  baseUrl: string, listGUID: string, substringofParams?: string[],
) => Promise<ISharePointListsResponse>;

type UseSPListItemDetailReturnType = [
  fetchDetail?: FetchSPODetail,
  fetchList?: FetchSPOList,
];

/* eslint-disable @typescript-eslint/no-unused-vars */

const useSPList = (
  tokenProvider: (() => Promise<string>) | undefined,
): UseSPListItemDetailReturnType => {

  const fetchList: FetchSPOList = React.useCallback(async (
    baseUrl: string, listGUID: string, substringofParams?: string[],
  ) => Promise.resolve({
    value: new Array(50).fill(0).map((_, i) => ({
      id: i,
      GUID: 'abc',
      'odata.editLink': 'abc',
      Title: 'ここに記事タイトルが表示されるようになります／人事異動のお知らせ',
      Created: new Date().toISOString(),
      Modified: new Date().toISOString(),
      releaseState: '公開',
      hasAttachments: true,
      category1: 'ここにカテゴリが表示されます',
    })),
  }), []);

  const fetchDetail: FetchSPODetail = React.useCallback(async (
    baseUrl: string, editLink: string,
  ) => Promise.resolve({
    id: 1,
    GUID: 'abc',
    'odata.editLink': 'abc',
    Title: 'ここに記事タイトルが表示されるようになります／人事異動のお知らせ',
    Created: new Date().toISOString(),
    Modified: new Date().toISOString(),
    releaseState: '公開',
    hasAttachments: true,
    category1: 'ここにカテゴリが表示されます',
    docBody: `
<div class="ExternalClassE152E6C1400F4D10AC725D82AB162E4D">
   <img src="https://www.avanade.com/-/media/images/content/background/about/image-manufacturing.webp?la=en&ver=1&w=320&hash=68C02AC60AF076059A9068A621730E12" data-themekey="#" alt="" style="margin: 5px;"/>
   <br/>
</div>
<div class="ExternalClass5EB8E56A5ACB4882AC47F7FF41421918">
   <a href="/sites/projectgeranium/Lists/0000_TEST/DispForm.aspx?ID=475&amp;e=Tv5j8k">URL 1</a><a href="/sites/projectgeranium/Shared%20Documents/sample.png">URL 2</a><a href="/SitePages/Home.aspx">URL 3</a><a href="/_layouts/15/images/icxlsx.png">URL 4</a><br/></div>
<br/>
<br/>
      <p>Armarium experimentums, tanquam albus fraticinida.A falsis, impositio clemens exsul.Sunt gemnaes promissio alter, lotus triticumes.Emeritis indexs ducunt ad byssus.Albus, secundus danistas semper pugna de raptus, brevis galatae.Ferox, nobilis eleatess etiam pugna de neuter, grandis vortex.Paluss studere in alter rugensis civitas!</p>
      <p>Armarium experimentums, tanquam albus fraticinida.A falsis, impositio clemens exsul.Sunt gemnaes promissio alter, lotus triticumes.Emeritis indexs ducunt ad byssus.Albus, secundus danistas semper pugna de raptus, brevis galatae.Ferox, nobilis eleatess etiam pugna de neuter, grandis vortex.Paluss studere in alter rugensis civitas!</p>
      <p>Armarium experimentums, tanquam albus fraticinida.A falsis, impositio clemens exsul.Sunt gemnaes promissio alter, lotus triticumes.Emeritis indexs ducunt ad byssus.Albus, secundus danistas semper pugna de raptus, brevis galatae.Ferox, nobilis eleatess etiam pugna de neuter, grandis vortex.Paluss studere in alter rugensis civitas!</p>
    `,
  }), []);

  return [fetchDetail, fetchList];
};

export default useSPList;
