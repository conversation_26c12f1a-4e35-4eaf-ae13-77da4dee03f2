export const HTML_CONTENT_MOCK = `

  <!-- サニタイズされるコンテンツ -->
  <html id="html"></html>
  <body id="html"></body>
  <iframe id="iframe" src="https://example.com"></iframe>
  <link id="link" href="https://example.com/style.css" type="text/css" />
  <script id="script-link" src="https://example.com/script.js"></script>
  <script id="script-inline">alert('demo');</script>
  <style id="style"></style>

  <!-- サニタイズされないコンテンツ -->
  <table border="1px">
    <tbody>
      <tr height="100px">
        <td colspan="2" rowspan="2">
           <div width="100">
              <ul>
                <li>
                  <ol>
                    <li>
                      <p>
                        <a href="https://example.com/">
                          <font color="red">
                            <span style="height:10em">abcd</span>
                          </font>
                        </a>
                        <img src="https://example.com/image.png" height="200" />
                        <img src="/img/image.png" height="200" />
                        <img src="cid:aaaa" height="200" />
                        <img src="https://graph.microsoft.com/info.png" height="200" />
                        <img src="https://graph.microsoft.com/v1.0/test/$value" height="200" />
                        <a href="/123/site.html">site</a>
                        <a href="#abc">abc</a>
                      </p>
                    </li>
                   </ol>
                 </li>
              </ul>
           </div>
        </td>
      </tr>
    </tbody>
  </table>`;

export const HTML_IMG_CONTENT_MOCK = `

  <div width="100">
    <!-- 外部サイトの絶対URL -->
    <img src="https://example.com/image.png" height="200" alt="test" />
    <!-- 外部サイトの相対URL -->
    <img src="/img/image.png" height="200" />
    <!-- "/_"で始まる絶対URL -->
    <img src="https://projectgeranium.sharepoint.com/_layouts/15/images/icxlsx.png" height="200" />
    <!-- "/_"で始まる相対URL -->
    <img src="/_layouts/15/images/icxlsx.png" height="200" />
    <!-- SPOサイトの絶対URL -->
    <img src="https://projectgeranium.sharepoint.com/sites/projectgeranium/sample/image.png" height="200" />
    <!-- SPOサイトの相対URL -->
    <img src="/sites/projectgeranium/sample/image.png" height="200" />
    <!-- SPOサイト(サブサイト)の相対URL -->
    <img src="/sites/projectgeranium/sub_project/sample/image.png" height="200" />
    <!-- SPO別サイトの相対URL -->
    <img src="/sites/another_site/sample/image.png" height="200" />
    <!-- SPO別サイト(サブサイト)の相対URL -->
    <img src="/sites/another_site/sub_project/sample/image.png" height="200" />
    <!-- SPO別サイト(サブサイトのサブサイト)の相対URL -->
    <img src="/sites/another_site/sub_project/subsub/sample/image.png" height="200" />

  </div>`;

export const HTML_CHAT_CONTENT_MOCK = `
<!-- リプライ -->
<at id="0">原 悠那</at>
これ見ろ
<!-- 添付コンポーネント(リプライカードなど) -->
<attachment id="123456789"></attachment>
<!-- 絵文字タグ -->
<emoji title="bow" alt="🙇‍♀️"></emoji>
`;
