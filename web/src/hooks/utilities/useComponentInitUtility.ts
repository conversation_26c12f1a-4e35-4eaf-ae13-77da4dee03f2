import * as React from 'react';
import { useTeamsToken } from '@avanade-teams/auth';
import {
  EventReporter, MetricReporter,
  useEventReporter, useInitializingLog,
} from '@avanade-teams/app-insights-reporter';
import { ICustomProperties, isLogInitTiming } from '../../utilities/commonFunction';
import useUnmountFlagUtility from './useUnmountFlagUtility';
import { decodeJwt } from '../../utilities/token/jwt';
import { PerformanceMetrics, initialPerformanceMetrics } from '../../types/PerformanceMetrics';

/**
 * フックのパラメータ型
 */
type UseComponentInitProps = {
  componentName: string;
  maxCountToTrack?: number;
  customPropertiesEventReporter?: ICustomProperties;
  customPropertiesInitializingLog?: ICustomProperties;
  initLogConf?: [string, { LOADING: string }];
};

/**
 * フックの戻り値型
 */
export type UseComponentInitReturnType = [
  isUnmounted: React.MutableRefObject<boolean>,
  reporters: [reportEvent: EventReporter, reportMEtric: MetricReporter],
  tokens: (Map<string, () => Promise<string>>) | undefined,
  tokenError: string | undefined,
  onInit: (customProperties?: ICustomProperties) => void,
  oid: string,
  teamsToken: string,
  metrics: [
    performanceMetrics: React.MutableRefObject<PerformanceMetrics>,
    setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void,
  ]
];

/**
 * a custom hook providing features usually used in components
 * @param props
 */
const useComponentInitUtility = (props: UseComponentInitProps): UseComponentInitReturnType => {
  const {
    componentName,
    maxCountToTrack,
    customPropertiesInitializingLog,
    customPropertiesEventReporter,
    initLogConf,
  } = props;

  // destructure useTeamsToken return
  //   error: can have an error message when token fails to init
  //   callbacks: map of the token provider functions
  //   teamsToken: returns token gotten from Teams SDK
  const { error, callbacks, teamsToken } = useTeamsToken();

  const [oid, setOid] = React.useState('');

  // ログ送信時に共通でoidを出力する為のRefオブジェクト
  // この実装だと初期化直後などで常にoidが取れる訳ではないが、
  // stateに依存させるとreportersに依存する既存のuseEffectが複数回ログを送ってしまう懸念があるためrefを参照させている
  // どうしても初期化直後にoidを取りたいときは戻り値にあるoidを使い、値がセットされるのを待つようにする
  const oidRef = React.useRef('');
  React.useEffect(() => {
    if (oidRef.current) return;
    if (!teamsToken) return;
    try {
      const decodedOid = decodeJwt(teamsToken)?.payload.oid ?? '';
      oidRef.current = decodedOid;
      setOid(decodedOid);
    } catch (e) {
      oidRef.current = teamsToken;
      setOid(teamsToken);
    }
  }, [teamsToken]);

  // setting default values of the event reporters
  const reporters = useEventReporter({
    componentName,
    customProperties: {
      ...customPropertiesEventReporter,
    },
    customRefProperties: {
      oid: oidRef,
    },
  });

  // 初期化ログフック
  const { onInit } = useInitializingLog({
    reporter: reporters.reportEvent,
    maxCountToTrack,
    customProperties: customPropertiesInitializingLog,
  });

  // initLogConfがある場合はhook内で初期化ログを計測
  const viewCache = React.useRef('');
  React.useEffect(() => {
    if (!initLogConf) return;
    const [view, viewOptions] = initLogConf;
    if (isLogInitTiming(view, viewCache, viewOptions)) {
      viewCache.current = view;
      onInit({ note: `View: ${view}` });
    }
  }, [initLogConf, onInit]);

  const isUnmounted = useUnmountFlagUtility();

  const performanceMetrics = React.useRef<PerformanceMetrics>(initialPerformanceMetrics);
  const setPerformanceMetrics = React.useCallback(
    (override: Partial<PerformanceMetrics> | null) => {
      performanceMetrics.current = override
        ? {
          ...performanceMetrics.current,
          ...override,
        }
        : initialPerformanceMetrics;
    }, [],
  );

  return [
    isUnmounted,
    [reporters.reportEvent, reporters.reportMetric],
    callbacks,
    error,
    onInit,
    oid,
    teamsToken,
    [performanceMetrics, setPerformanceMetrics],
  ];
};

export default useComponentInitUtility;
