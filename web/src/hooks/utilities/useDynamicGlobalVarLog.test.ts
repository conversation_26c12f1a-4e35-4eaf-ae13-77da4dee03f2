import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { renderHook } from '@testing-library/react-hooks';
import environment from '../../utilities/environment';
import useDynamicGlobalVarLog from './useDynamicGlobalVarLog';

jest.mock('../../utilities/environment');

// reset the all env variables to test
function resetEnvs() {
  environment.REACT_APP_ROUTE_PREFIX = '0';
  environment.REACT_APP_CONNECTION_STRING = 'a';
  environment.REACT_APP_SHAREPOINT_HOST_NAME = 'b';
}

const reportEventMock = jest.fn();

function getHook() {
  return renderHook(() => useDynamicGlobalVarLog(reportEventMock));
}

beforeEach(() => {
  resetEnvs();
  reportEventMock.mockClear();
});

describe('when one of the string global variable is missing', () => {
  // a basic expected object
  const expected: { [k: string]: string | undefined | { category: string | undefined } } = {
    routerPrefix: '0',
    connectionString: 'a',
    spoHostName: 'b',
  };

  // target env variables to remove in a test
  [
    // { KEY: 'REACT_APP_ROUTE_PREFIX' as const, key: 'routerPrefix' as const },
    { KEY: 'REACT_APP_CONNECTION_STRING' as const, key: 'connectionString' as const },
  ].forEach(({ KEY, key }) => {

    it('should report MISSING_GLOBAL_VARIABLES', () => {
      // restore env variables
      resetEnvs();

      // set undefined for the target env variable
      environment[KEY] = '';
      expected[key] = '';

      getHook();
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'MISSING_GLOBAL_VARIABLES',
        customProperties: expected,
      });
    });
  });
});

describe('when the all string global variables are available', () => {
  it('should report GET_GLOBAL_VARIABLES', () => {
    getHook();
    expect(reportEventMock).toHaveBeenCalledWith({
      type: EventReportType.SYS_EVENT,
      name: 'GET_GLOBAL_VARIABLES',
      customProperties: {
        routerPrefix: '0',
        connectionString: 'a',
        spoHostName: 'b',
      },
    });
  });
});
