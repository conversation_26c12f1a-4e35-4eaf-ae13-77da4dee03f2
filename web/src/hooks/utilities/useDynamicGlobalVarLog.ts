import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import React from 'react';
import environment from '../../utilities/environment';

/**
 * check the dynamic global variables and send a log only once
 * @param reportEvent
 */
const useDynamicGlobalVarLog = (reportEvent: EventReporter): void => {
  React.useEffect(() => {
    const spoDynamicVariables = {
      routerPrefix: environment.REACT_APP_ROUTE_PREFIX,
      connectionString: environment.REACT_APP_CONNECTION_STRING,
      spoHostName: environment.REACT_APP_SHAREPOINT_HOST_NAME,
    };
    const isSpoVariablesHasValues = Object.values(spoDynamicVariables).every((values) => !!values);

    reportEvent({
      type: isSpoVariablesHasValues ? EventReportType.SYS_EVENT : EventReportType.SYS_ERROR,
      name: isSpoVariablesHasValues ? 'GET_GLOBAL_VARIABLES' : 'MISSING_GLOBAL_VARIABLES',
      customProperties: spoDynamicVariables,
    });
  }, [reportEvent]);
};

export default useDynamicGlobalVarLog;
