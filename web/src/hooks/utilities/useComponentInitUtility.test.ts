import { useTeamsToken } from '@avanade-teams/auth';
import { renderHook } from '@testing-library/react-hooks';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { appInsightsContextMock } from '@avanade-teams/app-insights-reporter/dist/mocks/appInsights';
import useComponentInitUtility from './useComponentInitUtility';

// mock the auth library
const tokenGetMock = jest.fn().mockResolvedValue('token-mock');
jest.mock('@avanade-teams/auth', () => ({
  useTeamsToken: jest.fn(),
}));

// mock useTeamsToken
const mockUseTeamsToken = useTeamsToken as jest.Mock;
mockUseTeamsToken.mockReturnValue({
  callbacks: {
    get: () => tokenGetMock,
  },
  error: 'token-error-mock',
  teamsToken: 'abc',
});

describe('useComponentInitUtility', () => {

  function getRenderedHook(initLogConf?: [string, { LOADING: string }]) {
    const { result } = renderHook(() => useComponentInitUtility({
      componentName: 'test',
      initLogConf,
    }));
    return result.current;
  }

  beforeEach(() => {
    appInsightsContextMock.trackEvent.mockClear();
    tokenGetMock.mockClear();
  });

  it('should return isUnmounted', () => {
    const [isUnmounted] = getRenderedHook();
    expect(isUnmounted.current).toBe(false);
  });

  it('should return initializing logger', () => {
    const [,,,, onInit] = getRenderedHook();
    onInit();
    expect(appInsightsContextMock.trackEvent).toBeCalledTimes(1);
  });

  describe('when the initLogConf is passed on init', () => {
    it('should log initializing', () => {
      const mockView = 'loading';
      const mockViewType = {
        LOADING: 'loading',
        DEFAULT: 'default',
      };
      getRenderedHook([mockView, mockViewType]);
      expect(appInsightsContextMock.trackEvent).toBeCalledTimes(1);
      expect(appInsightsContextMock.trackEvent).toBeCalledWith(
        expect.anything(),
        expect.objectContaining({
          note: 'View: loading',
        }),
      );
    });
  });

  it('should return EventReporter', () => {
    const [, [report]] = getRenderedHook();
    report({
      type: EventReportType.USER_EVENT,
      name: 'test',
    });
    expect(appInsightsContextMock.trackEvent).toBeCalledTimes(1);
    expect(appInsightsContextMock.trackEvent).toBeCalledWith(
      expect.objectContaining({
        name: `${EventReportType.USER_EVENT}: test`,
      }),
      expect.objectContaining({
        reactComponentName: 'test',
      }),
    );
  });

  it('should return MetricReporter', () => {
    const [, [, report]] = getRenderedHook();
    report({
      name: 'abc',
      average: 1,
    });
    expect(appInsightsContextMock.trackMetric).toBeCalledTimes(1);
    expect(appInsightsContextMock.trackMetric).toBeCalledWith(
      expect.objectContaining({
        name: 'abc',
        average: 1,
      }),
      expect.objectContaining({
        reactComponentName: 'test',
      }),
    );
  });

  it('should return tokens map', () => {
    const [,, tokens] = getRenderedHook();
    expect(tokens?.get('graph')?.()).resolves.toBe('token-mock');
  });

  it('should return tokensError', () => {
    const [,,, error] = getRenderedHook();
    expect(error).toBe('token-error-mock');
  });

  it('should return teamsToken', () => {
    const [,,,,,, teamsToken] = getRenderedHook();
    expect(teamsToken).toBe('abc');
  });

});
