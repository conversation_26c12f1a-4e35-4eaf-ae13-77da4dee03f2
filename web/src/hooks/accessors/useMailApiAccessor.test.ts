import '@testing-library/jest-dom';
import { createGraphClient } from '@avanade-teams/auth';
import { renderHook } from '@testing-library/react-hooks';
import { WeakTokenProvider } from '../../types/TokenProvider';
import useMailApiAccessor, { MailAttachmentParams } from './useMailApiAccessor';

// mock environment.ts
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 3,
    REACT_APP_RETRY_DEAFULT_TIME: 5000,
    REACT_APP_BATCH_REQUEST_CHUNK_SIZE: 20,
  },
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));
const mockCreateGraphClient = createGraphClient as jest.Mock;

// mock the client of microsoft-graph-client
const getMock = jest.fn();
const postMock = jest.fn();
const apiMock = jest.fn().mockReturnThis();
const mockClient = {
  api: apiMock,
  select: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  get: getMock,
  post: postMock,
};

const callbackFn = jest.fn();

type RenderHookProps = {
  tokenProvider: WeakTokenProvider,
};

function getHook(props: RenderHookProps) {
  return renderHook((p) => useMailApiAccessor(p.tokenProvider), {
    initialProps: props,
  });
}

describe('useMailApiHandler', () => {
  beforeEach(() => {
    mockCreateGraphClient.mockClear();
    Object.values(mockClient).forEach((v) => v.mockClear());
    callbackFn.mockClear();
  });
  describe('when the tokenProvider is undefined', () => {
    const tokenProvider = undefined;

    it('should be undefined', () => {
      const { result } = getHook({ tokenProvider });
      Object.values(result.current).forEach((v) => expect(v).toBeUndefined());
    });
  });

  describe('when the tokenProvider is available', () => {
    const tokenProvider = jest.fn();
    it('should return the function', () => {
      const { result } = getHook({ tokenProvider });
      Object.values(result.current).forEach((v) => expect(v).toBeInstanceOf(Function));
    });

    describe('fetchUser', () => {
      describe('when the client.get resolves response', () => {
        beforeEach(() => {
          mockCreateGraphClient.mockReturnValue(mockClient);
          mockClient.get.mockResolvedValue({
            value: [{ id: 'abc' }],
          });
        });

        it('should resolve what client.get resolves', async () => {
          const { result } = getHook({ tokenProvider });
          const { fetchUser } = result.current;

          const expectedValue = { value: [{ id: 'abc' }] };
          await expect(fetchUser?.()).resolves.toStrictEqual(expectedValue);
          const expectedEndpoint = 'me';
          expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
        });

        it('should call client.api with the expected parameters', async () => {
          const { result } = getHook({ tokenProvider });
          const { fetchUser } = result.current;
          await fetchUser?.('testId');

          expect(mockClient.api).toHaveBeenCalledTimes(1);
          const expectedEndpoint = 'users/testId';
          expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
        });
      });
    });
    describe('fetchMailDetail', () => {
      describe('when the client.get resolves response', () => {
        beforeEach(() => {
          mockCreateGraphClient.mockReturnValue(mockClient);
          mockClient.get.mockResolvedValue({
            value: [{ id: 'abc' }],
          });
        });

        it('should resolve what client.get resolves', async () => {
          const { result } = getHook({ tokenProvider });
          const { fetchMailDetail } = result.current;

          const expected = { value: [{ id: 'abc' }] };
          await expect(fetchMailDetail?.('id')).resolves.toStrictEqual(expected);
        });

        it('should call client.api with the expected parameters', async () => {
          const { result } = getHook({ tokenProvider });
          const { fetchMailDetail } = result.current;
          await fetchMailDetail?.('testId');

          expect(mockClient.api).toHaveBeenCalledTimes(1);
          const expectedEndpoint = 'me/messages/testId/?$expand=attachments($select=name,contentType,size,isInline,id,name,microsoft.graph.fileAttachment/contentId)';
          expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
        });
      });
    });

    describe('fetchMailDetailAttachmentsImpl', () => {
      describe('when the client.get resolves response', () => {
        beforeEach(() => {
          mockCreateGraphClient.mockReturnValue(mockClient);
          mockClient.get.mockResolvedValue({
            value: [{ mailId: 'abc', id: 'abc' }],
          });
        });

        it('should resolve what client.get resolves', async () => {
          const { result } = getHook({ tokenProvider });
          const { fetchMailDetailAttachments } = result.current;

          const expected = { value: [{ mailId: 'abc', id: 'abc' }] };
          await expect(fetchMailDetailAttachments?.('mailId', 'id')).resolves.toStrictEqual(expected);
        });

        it('should call client.api with the expected parameters', async () => {
          const { result } = getHook({ tokenProvider });
          const { fetchMailDetailAttachments } = result.current;
          await fetchMailDetailAttachments?.('testId', 'id');

          expect(mockClient.api).toHaveBeenCalledTimes(1);
          const expectedEndpoint = 'me/messages/testId/attachments/id';
          expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
        });
      });
    });

    describe('fetchBulkAttachments', () => {
      beforeEach(() => {
        postMock.mockImplementation(({ requests }) => Promise.resolve({
          responses: requests.map(
            (request: { mailId: string, id: string }) => ({
              id: request.id, status: 200, body: { data: request.id },
            }),
          ),
        }));
      });
      afterEach(() => {
        postMock.mockClear();
      });
      it('should be accept to fetch no messages', async () => {
        const { result } = getHook({ tokenProvider });
        const response = await result.current.fetchMailAttachments?.([]);
        expect(response).toStrictEqual([]);
      });

      it('should be accept single message', async () => {
        const ids = [{
          mailId: 'mailId',
          id: 'attachmentId',
          contentId: 'contentId',
        } as MailAttachmentParams];
        const { result } = getHook({ tokenProvider });
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const response = await result.current.fetchMailAttachments?.(ids);
        expect(postMock).toHaveBeenCalledWith({
          requests: [
            {
              url: 'me/messages/mailId/attachments/attachmentId/$value',
              id: 'contentId',
              method: 'GET',
            },
          ],
        });
        expect(response).toStrictEqual([{ id: 'contentId', response: { data: 'contentId' } }]);
      });

      it('should be accept over 20 messages', async () => {
        const ids = 'abcdefghijklmnopqrstuvwxyz'.split('').map((attachmentId) => ({
          mailId: 'mailId',
          id: attachmentId,
          contentId: `cid:${attachmentId}`,
        }));
        const { result } = getHook({ tokenProvider });
        await result.current.fetchMailAttachments?.(ids);
        expect(postMock).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('fetchMailAttachment', () => {
    const tokenProvider = jest.fn();
    beforeEach(() => {
      getMock.mockImplementation(() => Promise.resolve('blob content'));
    });
    afterEach(() => {
      getMock.mockClear();
    });
    it('should called content', async () => {

      const { result } = getHook({ tokenProvider });
      const input = {
        id: 'id',
        mailId: 'mailId',
        contentId: 'contentId',
      };
      await result.current.fetchMailAttachment?.(input);
      expect(getMock).toHaveBeenCalledTimes(1);
      expect(apiMock).toHaveBeenCalledWith('me/messages/mailId/attachments/id/$value');
    });
  });
});
