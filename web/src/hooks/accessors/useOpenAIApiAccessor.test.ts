import '@testing-library/jest-dom';
import { renderHook } from '@testing-library/react-hooks';
import useOpenAIApiAccessor from './useOpenAIApiAccessor';
import mockFetch from '../../mocks/fetch';

jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://localhost:5001',
  },
}));

beforeEach(() => {
  mockFetch.mockClear();
});

describe('useOpenAIApiAccessor', () => {
  describe('fetchResultFromAI', () => {
    // 基本的なパラメータでのテスト
    describe('when called with basic parameters', () => {
      const searchInputValue = 'test query';
      const userId = 'user123';

      beforeEach(() => {
        mockFetch.mockResolvedValue({
          ok: true,
          headers: {
            get: jest.fn().mockReturnValue('application/json'),
          },
          json: jest.fn().mockResolvedValue({
            mergedResult: [
              { id: '1', title: 'Test Result 1' },
              { id: '2', title: 'Test Result 2' },
            ],
          }),
        });
      });

      it('正常にAPIを呼び出し、結果を返すこと', async () => {
        const { result } = renderHook(() => useOpenAIApiAccessor());
        const response = await result.current.fetchResultFromAI(searchInputValue, userId);

        expect(mockFetch).toHaveBeenCalledTimes(1);
        expect(mockFetch).toHaveBeenCalledWith(
          'https://localhost:5001/openai/aisearch',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              Input: searchInputValue,
              UserId: userId,
              GroupIds: undefined,
              From: undefined,
              To: undefined,
              SourceFilter: undefined,
            }),
          },
        );

        expect(response).toHaveProperty('reply');
        expect(response).toHaveProperty('list');
        expect(response.list).toEqual([
          { id: '1', title: 'Test Result 1' },
          { id: '2', title: 'Test Result 2' },
        ]);
        expect(typeof response.reply).toBe('string');
      });
    });

    // 全パラメータを指定したテスト
    describe('when called with all parameters', () => {
      const searchInputValue = 'test query';
      const userId = 'user123';
      const groupIds = ['group1', 'group2'];
      const from = '2023-01-01';
      const to = '2023-12-31';
      const sourceFilter = 'sharepoint';

      beforeEach(() => {
        mockFetch.mockResolvedValue({
          ok: true,
          headers: {
            get: jest.fn().mockReturnValue('application/json'),
          },
          json: jest.fn().mockResolvedValue({
            mergedResult: [],
          }),
        });
      });

      it('全てのパラメータを正しく送信すること', async () => {
        const { result } = renderHook(() => useOpenAIApiAccessor());
        await result.current.fetchResultFromAI(
          searchInputValue,
          userId,
          groupIds,
          from,
          to,
          sourceFilter,
        );

        expect(mockFetch).toHaveBeenCalledWith(
          'https://localhost:5001/openai/aisearch',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              Input: searchInputValue,
              UserId: userId,
              GroupIds: groupIds,
              From: from,
              To: to,
              SourceFilter: sourceFilter,
            }),
          },
        );
      });
    });

    // エラーハンドリングのテスト
    describe('when API returns error', () => {
      const searchInputValue = 'test query';
      const userId = 'user123';

      describe('when response is not ok', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: false,
            status: 500,
            headers: {
              get: jest.fn().mockReturnValue('application/json'),
            },
            text: jest.fn().mockResolvedValue('Internal Server Error'),
          });
        });

        it('エラーをスローすること', async () => {
          const { result } = renderHook(() => useOpenAIApiAccessor());

          await expect(
            result.current.fetchResultFromAI(searchInputValue, userId),
          ).rejects.toThrow('API error (500): Internal Server Error');
        });
      });

      describe('when content-type is not JSON', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            headers: {
              get: jest.fn().mockReturnValue('text/html'),
            },
          });
        });

        it('Content-Typeエラーをスローすること', async () => {
          const { result } = renderHook(() => useOpenAIApiAccessor());

          await expect(
            result.current.fetchResultFromAI(searchInputValue, userId),
          ).rejects.toThrow('Expected JSON response but got text/html');
        });
      });

      describe('when content-type header is missing', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            headers: {
              get: jest.fn().mockReturnValue(null),
            },
          });
        });

        it('Content-Typeエラーをスローすること', async () => {
          const { result } = renderHook(() => useOpenAIApiAccessor());

          await expect(
            result.current.fetchResultFromAI(searchInputValue, userId),
          ).rejects.toThrow('Expected JSON response but got null');
        });
      });

      describe('when fetch throws an error', () => {
        beforeEach(() => {
          mockFetch.mockRejectedValue(new Error('Network error'));
        });

        it('ネットワークエラーをスローすること', async () => {
          const { result } = renderHook(() => useOpenAIApiAccessor());

          await expect(
            result.current.fetchResultFromAI(searchInputValue, userId),
          ).rejects.toThrow('Network error');
        });
      });
    });

    // ランダム生成のテスト
    describe('random summary generation', () => {
      const searchInputValue = 'test query';
      const userId = 'user123';

      beforeEach(() => {
        mockFetch.mockResolvedValue({
          ok: true,
          headers: {
            get: jest.fn().mockReturnValue('application/json'),
          },
          json: jest.fn().mockResolvedValue({
            mergedResult: [],
          }),
        });
      });

      it('replyフィールドにランダムな文字列が生成されること', async () => {
        const { result } = renderHook(() => useOpenAIApiAccessor());

        const response1 = await result.current.fetchResultFromAI(searchInputValue, userId);
        const response2 = await result.current.fetchResultFromAI(searchInputValue, userId);

        expect(response1.reply).toBeDefined();
        expect(response2.reply).toBeDefined();
        expect(typeof response1.reply).toBe('string');
        expect(typeof response2.reply).toBe('string');
        // ランダム生成なので、2回の呼び出しで異なる値になる可能性が高い
        // ただし、同じ値になる可能性もあるため、型チェックのみ行う
      });
    });
  });
});
