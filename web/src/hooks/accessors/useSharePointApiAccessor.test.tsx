import '../../mocks/match-media-loader';
import { renderHook } from '@testing-library/react-hooks';
import { createGraphClient } from '@avanade-teams/auth';
import { GraphError, ResponseType } from '@microsoft/microsoft-graph-client';
import useSharePointApiAccessor, {
  createBaseUrlForRelativePath,
  createFileBlobUrl,
  createListUrlOfCaml,
  fetchFileBlobImpl,
  fetchListItemImpl,
  getAbsoluteUrlForRelativePath,
  getFirstLevelSiteName,
  getSiteUrl,
  UseSharePointApiError,
} from './useSharePointApiAccessor';

// mock environment.ts
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_SPO_COLUMNS: { category: 'ENV_VAR_VAL' },
  },
}));

// authのmock
const tokenProviderMock = jest.fn().mockResolvedValue('token-mock');

// @avanade-teams/authのmock
const getMock = jest.fn();
const postMock = jest.fn();
const selectMock = jest.fn();
const clientMock = {
  api: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  orderby: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  top: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  responseType: jest.fn().mockReturnThis(),
  get: getMock,
  post: postMock,
};
const createGraphClientMock = (createGraphClient as unknown as jest.Mock);
jest.mock('@avanade-teams/auth');

beforeAll(() => {
  jest.useFakeTimers('modern');
});

beforeEach(() => {
  createGraphClientMock.mockClear().mockReturnValue(clientMock);
  jest.setSystemTime(new Date('2021-01-01T12:00:00.000+09:00'));
  Object.values(clientMock).forEach((mock) => {
    mock.mockClear();
  });
  postMock.mockClear();
});

afterAll(() => {
  jest.useRealTimers();
});

describe('createListUrlOfCaml', () => {
  describe('when the listGUID is blank', () => {
    it('should return blank', () => {
      expect(createListUrlOfCaml('')).toBe('');
    });
  });

  describe('when the listGUID is not blank', () => {
    it('should return an url of the endpoint', () => {
      expect(createListUrlOfCaml('a'))
        .toBe("web/Lists('a')/GetItems");
    });
  });
});

describe('createFileBlobUrl', () => {
  describe('when the relativePath is blank', () => {
    it('should return blank', () => {
      expect(createFileBlobUrl('')).toBe('');
    });
  });

  describe('when the relativePath is not blank', () => {
    it('should return an url of the endpoint', () => {
      expect(createFileBlobUrl('a'))
        .toBe("web/GetFileByServerRelativeUrl('a')/$value");
    });
  });
});

describe('fetchListItemImpl', () => {
  describe('when the listItemGUID is empty', () => {
    const listItemGUID: string[] = [];

    it('should resolve empty response', async () => {
      const tokenProvider = undefined;
      const baseUrl = '';
      const listGUID = '';
      const category1 = '';

      await expect(fetchListItemImpl(
        tokenProvider,
        baseUrl,
        listGUID,
        listItemGUID,
        category1,
      )).rejects.toThrowError(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE);
    });
  });

  describe('when the listItemGUID is not empty', () => {
    const listItemGUID = ['a', 'b', 'c'];
    const tokenProvider = jest.fn();
    const baseUrl = 'https://example.com/sites/abc';
    const listGUID = '123';
    const category = 'category';

    describe('when createGraphClientMock return client', () => {

      beforeEach(() => {
        createGraphClientMock.mockReturnValue(clientMock);
        postMock.mockResolvedValue({
          value: [{ id: 'abc' }],
        });
      });

      it('should call createGraphClient', async () => {
        await fetchListItemImpl(
          tokenProvider,
          baseUrl,
          listGUID,
          listItemGUID,
          category,
        );
        expect(createGraphClientMock).toHaveBeenCalledTimes(1);
        expect(createGraphClientMock).toHaveBeenCalledWith(
          tokenProvider,
          expect.objectContaining({
            baseUrl,
            customHosts: new Set('example.com'),
          }),
        );
      });

      it('should resolve what client.post resolves with a blank category', async () => {
        await expect(fetchListItemImpl(
          tokenProvider,
          baseUrl,
          listGUID,
          listItemGUID,
          category,
        )).resolves.toStrictEqual({ value: [{ id: 'abc', category: '' }] });
      });

      it('should call client.api with expected url', async () => {
        await fetchListItemImpl(
          tokenProvider,
          baseUrl,
          listGUID,
          listItemGUID,
          category,
        );
        expect(clientMock.api).toHaveBeenCalledTimes(1);
        expect(clientMock.api).toHaveBeenCalledWith("web/Lists('123')/GetItems");
      });

      it('should call client.post with expected body', async () => {
        await fetchListItemImpl(
          tokenProvider,
          baseUrl,
          listGUID,
          listItemGUID,
          category,
        );

        expect(clientMock.post).toHaveBeenCalledTimes(1);

        const xmlExpected1 = /<In>.*"GUID".*>a<.*>b<.*>c<.*<\/In>/;
        expect(clientMock.post).toHaveBeenCalledWith({
          query: {
            ViewXml: expect.stringMatching(xmlExpected1),
          },
        });

        const xmlExpected2 = /<ViewFields>.*"Modified".*"GUID".*"Title".*"category".*<\/ViewFields>/;
        expect(clientMock.post).toHaveBeenCalledWith({
          query: {
            ViewXml: expect.stringMatching(xmlExpected2),
          },
        });
      });
    });
  });
});

describe('getAbsoluteUrlForRelativePath', () => {
  describe('when hostNameWithProtocol is "https://example.com"', () => {
    const hostNameWithProtocol = 'https://example.com';

    describe('when path is "/sites/9000_OTH/T_NEWS_NEW/SiteAssets/Lists/TP1/NewForm/abc.jpg"', () => {
      const path = '/sites/9000_OTH/T_NEWS_NEW/SiteAssets/Lists/TP1/NewForm/abc.jpg';

      it('should return the merged absolute URL', () => {
        const expected = 'https://example.com/sites/9000_OTH/T_NEWS_NEW/SiteAssets/Lists/TP1/NewForm/abc.jpg';
        expect(getAbsoluteUrlForRelativePath(hostNameWithProtocol, path)).toBe(expected);
      });
    });
  });
});

describe('getSiteUrl', () => {
  const tokenProvider = undefined;

  describe('when the createGraphClient returns undefined', () => {
    beforeEach(() => {
      createGraphClientMock.mockReturnValue(undefined);
    });

    it('should reject with TOKEN_PROVIDER_NOT_AVAILABLE', () => {
      const customHost = '';
      const absolutePath = '';
      expect(getSiteUrl(tokenProvider, customHost, absolutePath))
        .rejects.toThrowError(new Error(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
    });
  });

  describe('when the createGraphClient returns Client', () => {
    beforeEach(() => {
      createGraphClientMock.mockReturnValue(clientMock);
    });

    describe('when the post resolves', () => {
      beforeEach((() => {
        postMock.mockResolvedValue({ WebFullUrl: 'https://example.sharepoint.com/abc' });
      }));

      it('should resolve response.WebFullUrl', async () => {
        const customHost = '';
        const absolutePath = '';

        await expect(getSiteUrl(tokenProvider, customHost, absolutePath))
          .resolves.toBe('https://example.sharepoint.com/abc');
      });

      describe('when customHost is "example.com"', () => {
        describe('when absolutePath is "https://example.com/sites/abc/efg"', () => {
          const customHost = 'example.com';
          const absolutePath = 'https://example.com/sites/abc/efg';

          it('should post to the "contextinfo" endpoint', async () => {
            await getSiteUrl(tokenProvider, customHost, absolutePath);
            expect(createGraphClientMock).toHaveBeenCalledWith(undefined, expect.objectContaining({
              baseUrl: 'https://example.com/sites/abc',
              customHosts: new Set([customHost]),
            }));
            expect(clientMock.api).toHaveBeenCalledWith('contextinfo');
            expect(clientMock.responseType).toHaveBeenCalledWith(ResponseType.JSON);
            expect(clientMock.post).toHaveBeenCalledWith({});
          });
        });
      });
    });

    describe('when the post resolves but it does not have WebFullUrl', () => {
      beforeEach((() => {
        postMock.mockResolvedValue({});
      }));

      it('should resolve blank', async () => {
        const customHost = '';
        const absolutePath = '';

        await expect(getSiteUrl(tokenProvider, customHost, absolutePath))
          .resolves.toBe('');
      });
    });
  });
});

describe('getFirstLevelSiteName', () => {
  describe('when the relPath does not have "/sites/" at the top', () => {
    it('should return blank', () => {
      expect(getFirstLevelSiteName('/abc/efg')).toBe('');
      expect(getFirstLevelSiteName('sites/efg')).toBe('');
      expect(getFirstLevelSiteName('/site/efg')).toBe('');
      expect(getFirstLevelSiteName('/sites_of_example/efg')).toBe('');
    });
  });

  describe('when the relPath has "/sites/" at the top', () => {
    it('should return a site name of the first level', () => {
      expect(getFirstLevelSiteName('/sites/abc')).toBe('abc');
      expect(getFirstLevelSiteName('/sites/abc/efg')).toBe('abc');
    });
  });
});

describe('createBaseUrlForRelativePath', () => {
  describe('when hostName = "HOST_NAME"', () => {
    const hostName = 'HOST_NAME';

    describe('when relativePath has "/sites/" at the top', () => {
      const relativePath = '/sites/abc/efg';

      it('should return "https://HOST_NAME/sites/abc"', () => {
        expect(createBaseUrlForRelativePath(hostName, relativePath)).toBe('https://HOST_NAME/sites/abc');
      });
    });

    describe('when relativePath does not have "/sites/" at the top', () => {
      const relativePath = '/abc/efg';

      it('should return "https://HOST_NAME/sites/"', () => {
        expect(createBaseUrlForRelativePath(hostName, relativePath)).toBe('https://HOST_NAME');
      });
    });
  });
});

describe('fetchFileBlobImpl', () => {
  const tokenProvider = undefined;

  describe('when the baseUrl does not includes a hostname', () => {
    const baseUrl = 'abc';

    it('should reject with REQUIRED_PARAM_NOT_AVAILABLE', async () => {
      await expect(fetchFileBlobImpl(undefined, baseUrl, 'abc'))
        .rejects.toThrowError(UseSharePointApiError.REQUIRED_PARAM_NOT_AVAILABLE);
    });
  });

  describe('when the baseUrl is valid', () => {
    const baseUrl = 'https://example.sharepoint.com/sites/abc';

    describe('when the createGraphClient returns undefined', () => {
      beforeEach(() => {
        createGraphClientMock.mockReturnValue(undefined);
      });

      it('should reject with REQUIRED_PARAM_NOT_AVAILABLE', async () => {
        await expect(fetchFileBlobImpl(undefined, baseUrl, 'abc'))
          .rejects.toThrowError(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE);
      });
    });

    describe('when the createGraphClient returns Client', () => {
      beforeEach(() => {
        createGraphClientMock.mockReturnValue(clientMock);
      });

      describe('when the serverRelativeUrl does not start with "/sites/"', () => {
        const serverRelativeUrl = '/abc/efg.png';

        it('should return what client.get() returns and should use the correct baseUrl', async () => {
          getMock.mockResolvedValue({ data: 'test' });
          postMock.mockResolvedValue({ WebFullUrl: 'https://example.sharepoint.com/abc' });

          await expect(fetchFileBlobImpl(tokenProvider, baseUrl, serverRelativeUrl))
            .resolves.toStrictEqual({ data: 'test' });

          // baseUrlとcustomHostsの検証
          expect(createGraphClientMock).toHaveBeenCalledWith(
            tokenProvider,
            expect.objectContaining({
              baseUrl: 'https://example.sharepoint.com/abc',
              customHosts: new Set(['example.sharepoint.com']),
            }),
          );

          // APIエンドポイントの検証
          expect(clientMock.api).toHaveBeenCalledTimes(2);
          expect(clientMock.api).toHaveBeenCalledWith("web/GetFileByServerRelativeUrl('/abc/efg.png')/$value");

          // レスポンスタイプがBLOBであることの検証
          expect(clientMock.responseType).toHaveBeenCalledTimes(2);
          expect(clientMock.responseType).toHaveBeenCalledWith(ResponseType.BLOB);
        });
      });

    });

    describe('when the serverRelativeUrl starts with "/sites/"', () => {
      const serverRelativeUrl = '/sites/abc/efg/hij.png';

      describe('when client.post rejects', () => {
        beforeEach(() => {
          postMock.mockRejectedValue(new Error('abc'));
        });

        describe('when client.get resolves', () => {
          beforeEach(() => {
            getMock.mockResolvedValue({ data: 'test' });
          });

          it('should create baseUrl with fallback logic', async () => {
            await expect(fetchFileBlobImpl(tokenProvider, 'https://example.sharepoint.com/sites/efg', serverRelativeUrl))
              .resolves.toStrictEqual({ data: 'test' });

            // baseUrlとcustomHostsの検証
            expect(createGraphClientMock).toHaveBeenCalledWith(
              tokenProvider,
              expect.objectContaining({
                baseUrl: 'https://example.sharepoint.com/sites/abc',
                customHosts: new Set(['example.sharepoint.com']),
              }),
            );

            // APIエンドポイントの検証
            expect(clientMock.api).toHaveBeenCalledTimes(2);
            expect(clientMock.api).toHaveBeenCalledWith("web/GetFileByServerRelativeUrl('/sites/abc/efg/hij.png')/$value");
          });

          describe('when the serverRelativeUrl includes hostNameWithSitePath', () => {
            it('should create baseUrl with fallback logic', async () => {
              await expect(fetchFileBlobImpl(tokenProvider, 'https://example.sharepoint.com/sites/geranium/abc', '/sites/geranium/abc/123/abc.jpg'))
                .resolves.toStrictEqual({ data: 'test' });

              // baseUrlとcustomHostsの検証
              expect(createGraphClientMock).toHaveBeenCalledWith(
                tokenProvider,
                expect.objectContaining({
                  baseUrl: 'https://example.sharepoint.com/sites/geranium/abc',
                  customHosts: new Set(['example.sharepoint.com']),
                }),
              );

              // APIエンドポイントの検証
              expect(clientMock.api).toHaveBeenCalledTimes(2);
              expect(clientMock.api).toHaveBeenCalledWith("web/GetFileByServerRelativeUrl('/sites/geranium/abc/123/abc.jpg')/$value");
            });
          });
        });
      });

      describe('when client.post resolves', () => {
        beforeEach(() => {
          postMock.mockResolvedValue({ WebFullUrl: 'https://example.sharepoint.com/sites/abc/efg' });
        });

        it('should return what client.get() returns and should use the correct baseUrl', async () => {
          getMock.mockResolvedValue({ data: 'test' });

          await expect(fetchFileBlobImpl(tokenProvider, baseUrl, serverRelativeUrl))
            .resolves.toStrictEqual({ data: 'test' });

          // baseUrlとcustomHostsの検証
          expect(createGraphClientMock).toHaveBeenCalledWith(
            tokenProvider,
            expect.objectContaining({
              baseUrl: 'https://example.sharepoint.com/sites/abc/efg',
              customHosts: new Set(['example.sharepoint.com']),
            }),
          );

          // APIエンドポイントの検証
          expect(clientMock.api).toHaveBeenCalledTimes(2);
          expect(clientMock.api).toHaveBeenCalledWith("web/GetFileByServerRelativeUrl('/sites/abc/efg/hij.png')/$value");

          // レスポンスタイプがBLOBであることの検証
          expect(clientMock.responseType).toHaveBeenCalledTimes(2);
          expect(clientMock.responseType).toHaveBeenCalledWith(ResponseType.BLOB);
        });

        describe('when client.get rejects', () => {
          beforeEach(() => {
            clientMock.get.mockRejectedValue(new GraphError(404));
          });

          it('should reject with GraphError including URL details', async () => {
            const expected = new GraphError(404);
            expected.body = {
              body: expected.body,
              serverRelativeUrl,
              baseUrl,
              absoluteUrl: '',
              customHost: '',
              endpoint: '',
            };

            await expect(fetchFileBlobImpl(tokenProvider, baseUrl, serverRelativeUrl))
              .rejects.toThrow(expected);
          });
        });
      });
    });
  });
});

describe('useSharePointApiAccessor', () => {
  const cancellationRefMock = { current: false };
  function getRenderedHook(tokenProvider?: () => Promise<string>) {
    const { result } = renderHook(
      () => useSharePointApiAccessor(tokenProvider, cancellationRefMock),
    );
    return result.current;
  }

  beforeEach(() => {
    tokenProviderMock.mockClear();
    createGraphClientMock.mockClear();
    selectMock.mockClear();
    getMock.mockClear();
    postMock.mockClear();
  });

  describe('fetchDetail', () => {
    // including a perspective for fetchDetailImpl

    describe('when the tokenProvider is not available', () => {
      it('should return undefined', () => {
        const [fetchDetail] = getRenderedHook(undefined);
        expect(fetchDetail).toBeUndefined();
      });
    });

    it('should reject if the required params are unavailable', () => {
      const [fetchDetail] = getRenderedHook(tokenProviderMock);

      expect(fetchDetail).not.toBeUndefined();
      if (!fetchDetail) return;

      expect(fetchDetail('https://example.com/sites/abc', '')).rejects.toThrow();
      expect(fetchDetail('', 'abc')).rejects.toThrow();
      expect(fetchDetail('', '')).rejects.toThrow();
    });

    it('should GET from the SharePoint listItem endpoint', async () => {
      getMock.mockResolvedValue({ data: 'test' });
      const [fetchDetail] = getRenderedHook(tokenProviderMock);

      expect(fetchDetail).not.toBeUndefined();
      if (!fetchDetail) return;

      expect(await fetchDetail('https://example.com/sites/abc', '1234/editLink/Item(1)', 'category1')).toStrictEqual({
        data: 'test',
        category: '',
      });
      expect(createGraphClientMock).toBeCalledTimes(1);
      expect(createGraphClientMock).toBeCalledWith(tokenProviderMock, {
        baseUrl: 'https://example.com/sites/abc',
        customHosts: new Set<string>(['example.com']),
        defaultVersion: '_api',
        fetchOptions: {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      });

      const expectedEditLink = '1234/editLink/Item(1)';
      expect(clientMock.api).toHaveBeenCalledWith(expectedEditLink);
    });
  });

  describe('fetchListItem', () => {
    // fetchListItemImplのテストコードがあるので
    // ここでのテストは最低限で良い

    it('should reject if the required params are unavailable', () => {
      const [, fetchListItem] = getRenderedHook(tokenProviderMock);

      expect(fetchListItem).not.toBeUndefined();
      if (!fetchListItem) return;

      expect(fetchListItem('https://example.com/sites/abc', '', ['', ''], 'category1')).rejects.toThrow();
      expect(fetchListItem('', 'abc', ['aa', 'aa'], 'category1')).rejects.toThrow();
      expect(fetchListItem('https://example.com/sites/abc', '', ['aa', 'aa'], 'category1')).rejects.toThrow();
      expect(fetchListItem('', '', ['', ''], '')).rejects.toThrow();
    });

    it('should GET from the SharePoint list endpoint', async () => {
      postMock.mockResolvedValue({
        value: [
          {
            category1: 'category1',
            id: 'abc',
          },
        ],
      });
      const expected = {
        value: [
          {
            // remapされcategoryにcategory1の値が入る
            category: 'category1',
            category1: 'category1',
            id: 'abc',
          },
        ],
      };
      const [, fetchListItem] = getRenderedHook(tokenProviderMock);

      expect(fetchListItem).not.toBeUndefined();
      if (!fetchListItem) return;

      expect(await fetchListItem('https://example.com/sites/abc', 'aaa-bbb-ccc', ['aa', 'aa'], 'category1')).toStrictEqual(expected);

      expect(createGraphClientMock).toBeCalledTimes(1);
      expect(createGraphClientMock).toBeCalledWith(tokenProviderMock, {
        baseUrl: 'https://example.com/sites/abc',
        customHosts: new Set<string>(['example.com']),
        defaultVersion: '_api',
        fetchOptions: {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      });
      const expectedEditLink = "web/Lists('aaa-bbb-ccc')/GetItems";
      expect(clientMock.api).toHaveBeenCalledWith(expectedEditLink);
    });
  });

  describe('fetchFileBlob', () => {
    // fetchFileBlobImplのテストコードがあるので
    // ここでのテストは最低限で良い

    describe('when the tokenProvider is undefined', () => {
      const tokenProvider = undefined;

      it('should be undefined', () => {
        const [, , fetchFileBlob] = getRenderedHook(tokenProvider);
        expect(fetchFileBlob).toBeUndefined();
      });
    });

    describe('when the tokenProvider is available', () => {
      const tokenProvider = tokenProviderMock;
      createGraphClientMock.mockReturnValue(clientMock);

      describe('when client.get() resolves', () => {
        beforeEach(() => {
          getMock.mockResolvedValue({ data: 'test' });
        });

        it('should resolve what client.get() resolves', async () => {
          const [, , fetchFileBlob] = getRenderedHook(tokenProvider);
          expect(fetchFileBlob).not.toBeUndefined();
          if (!fetchFileBlob) return;

          await expect(fetchFileBlob('https://example.sharepoint.com/sites/sample', 'abc'))
            .resolves.toStrictEqual({ data: 'test' });
        });
      });

      describe('when client.get() rejects', () => {
        beforeEach(() => {
          getMock.mockRejectedValue(new GraphError(400));
        });

        it('should reject what client.get() rejects', async () => {
          const [, , fetchFileBlob] = getRenderedHook(tokenProvider);
          expect(fetchFileBlob).not.toBeUndefined();
          if (!fetchFileBlob) return;

          await expect(fetchFileBlob('https://example.sharepoint.com/sites/sample', 'abc'))
            .rejects.toThrow(new GraphError(400));
        });
      });

    });
  });
});
