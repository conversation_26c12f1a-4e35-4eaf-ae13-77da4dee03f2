import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { createGraphClient } from '@avanade-teams/auth';
import { waitFor } from '@testing-library/react';
import { sendRequests } from './useGraphApiAccessor';
import { WeakTokenProvider } from '../../types/TokenProvider';

/**
 * 取得処理の完了を持たず(awaitしてない)にテストをしているので他のテストスイートに影響を与えないように分割している
 * 本テストのようなテストを新規追加したい場合はファイルを分けて別で実施する
 *  */

dayjs.extend(isBetween);

const MockDefaultTime = 2000;

jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 3,
    REACT_APP_RETRY_DEAFULT_TIME: MockDefaultTime,
    REACT_APP_ADDITIONAL_WAITING_MSEC: 500,
    REACT_APP_BATCH_REQUEST_CHUNK_SIZE: 20,
  },
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));

const mockCreateGraphClient = createGraphClient as jest.Mock;
const mockProvider = jest.mock;
const cancellationRefMock = { current: false };

const postMock = jest.fn();
const apiMock = jest.fn();
const mockClient = {
  api: apiMock,
  select: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  post: postMock,
  get: jest.fn(),
};

describe('useGraphAPiAccessor', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });
  beforeEach(() => {
    postMock.mockClear();
    mockCreateGraphClient.mockReturnValue(mockClient);
    mockCreateGraphClient.mockClear();
    Object.values(mockClient).forEach((v) => v.mockClear());
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('retry timer addition', () => {
    beforeEach(() => {
      jest.setTimeout(10000);
    });

    describe('when retry after does not exist', () => {
      beforeEach(() => {
        jest.setTimeout(10000);
      });
      it('should wait default seconds + additional(msec) * times', async () => {

        apiMock.mockImplementation(() => ({ post: postMock }));
        postMock.mockResolvedValue({
          responses: [{
            body: '',
            id: '1',
            status: 409,
            headers: {},
          }],
        });

        sendRequests(mockProvider as unknown as WeakTokenProvider, [{ id: '1', url: '/me/user', method: 'GET' }], cancellationRefMock);
        const start = new Date();
        expect(postMock).toHaveBeenCalledTimes(1);
        await waitFor(() => {
          expect(postMock).toHaveBeenCalledTimes(3);
          // 終了時の時間を比較（500ms程度幅を持たせる）
          expect(dayjs().isBetween(dayjs(start).add(7500, 'ms'), dayjs(start).add(8000, 'ms'))).toBeTruthy();
        }, { timeout: 8500 });
      });
    });

    describe('when retry after exists', () => {
      beforeEach(() => {
        jest.setTimeout(10000);
      });
      it('should wait specific seconds + additional(msec) * times', async () => {

        apiMock.mockImplementation(() => ({ post: postMock }));
        postMock.mockResolvedValue({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: { 'Retry-After': '1' },
          }],
        });

        sendRequests(mockProvider as unknown as WeakTokenProvider, [{ id: '1', url: '/me/user', method: 'GET' }], cancellationRefMock);
        const start = new Date();
        expect(postMock).toHaveBeenCalledTimes(1);
        await waitFor(() => {
          expect(postMock).toHaveBeenCalledTimes(3);
          // 終了時の時間を比較（500ms程度幅を持たせる）
          expect(dayjs().isBetween(dayjs(start).add(4500, 'ms'), dayjs(start).add(5000, 'ms'))).toBeTruthy();
        }, { timeout: 5500 });
      });
    });
  });
});
