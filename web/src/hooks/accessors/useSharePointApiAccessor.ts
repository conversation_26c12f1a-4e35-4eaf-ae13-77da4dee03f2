import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import { Client, GraphError, ResponseType } from '@microsoft/microsoft-graph-client';
import * as React from 'react';
import { createGraphClient } from '@avanade-teams/auth';
import CamlBuilder from 'camljs';
import { complementRelativeUrl, parseSharePointPath } from '../../utilities/url';
import { remapPostCategory, remapPostCategorySingle } from '../../utilities/transform';
import { ISharePointListsResponse } from '../../mocks/useSharePointApiMock';
import { ISharePointListItemResponseWithAttachment } from '../../types/ISharePointListItemResponseWithAttachment';

export type FetchSPODetail = (
  baseUrl: string, editLink: string, category?: string,
) => Promise<ISharePointListItemResponseWithAttachment>;

export type FetchSPOList = (
  baseUrl: string, listGUID: string, listItemGUID: string[], category?: string,
) => Promise<ISharePointListsResponse>;

export type FetchFileBlob = (
  baseUrl: string, serverRelativeUrl: string, report?: EventReporter,
) => Promise<Blob>;

export type UseSharePointApiReturnType = [
  fetchDetail?: FetchSPODetail,
  fetchListItem?: FetchSPOList,
  fetchFileBlob?: FetchFileBlob,
];

type TokenProvider = (() => Promise<string>) | undefined;

export const UseSharePointApiError = {
  TOKEN_PROVIDER_NOT_AVAILABLE: 'tokenProvider is not provided yet',
  REQUIRED_PARAM_NOT_AVAILABLE: 'required parameters are not provided',
};

/**
 * SharePointのサイトディレクトリ
 */
const SPO_SITES_DIR = '/sites/';

/**
 * CAMLでリスト一覧を取得するためのURLを生成する
 * @param listGUID
 */
export function createListUrlOfCaml(listGUID: string): string {
  if (!listGUID) return '';
  return `web/Lists('${listGUID}')/GetItems`;
}

/**
 * 相対パスからファイル情報を取得するためのURLを生成する
 * @param relativePath
 */
export function createFileBlobUrl(relativePath: string): string {
  if (!relativePath) return '';
  return `web/GetFileByServerRelativeUrl('${relativePath}')/$value`;
}

/**
 * SharePoint通信用のクライアントを生成する
 * @param tokenProvider
 * @param baseUrl
 * @param customHost
 */
function createSpClient(
  tokenProvider: TokenProvider,
  baseUrl: string,
  customHost: string,
): Client | undefined {

  return createGraphClient(tokenProvider, {
    baseUrl,
    customHosts: new Set<string>([customHost]),
    defaultVersion: '_api',
    fetchOptions: {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    },
  });
}

/**
 * SharePointリストアイテムの取得(GET API)
 * @param tokenProvider
 * @param baseUrl
 * @param listGUID
 * @param listItemGUID
 * @return reject時にはErrorまたはGraphErrorを返却する
 */
export function fetchListItemImpl(
  tokenProvider: TokenProvider | undefined,
  baseUrl: string, // azfuncからsiteとして渡ってきている
  listGUID: string, // azfuncからlistとして渡ってきている
  listItemGUID: string[],
  category?: string, // azfuncからcategoryとして渡ってきている
): Promise<ISharePointListsResponse> {

  if (!tokenProvider) {
    return Promise.reject(new Error(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }

  // 必須パラメータが存在しない場合はreject
  const [customHost] = parseSharePointPath(baseUrl);
  if (!baseUrl || !listGUID || !customHost) {
    const message = `${UseSharePointApiError.REQUIRED_PARAM_NOT_AVAILABLE}: ${JSON.stringify({ baseUrl, listGUID, customHost })}`;
    return Promise.reject(new Error(message));
  }

  const client = createSpClient(tokenProvider, baseUrl, customHost);
  if (!client) {
    return Promise.reject(new Error(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }

  if (listItemGUID.length === 0) {
    return Promise.resolve({ value: [] });
  }

  const camlBody = {
    query: {
      ViewXml: new CamlBuilder()
        .View(['Modified', 'Created', 'GUID', 'Title', 'Attachments'].concat([category ?? '']))
        .Query()
        .Where()
        .TextField('GUID')
        .In(listItemGUID)
        .OrderByDesc('Modified')
        .ToString(),
    },
  };

  return client
    .api(createListUrlOfCaml(listGUID))
    .post(camlBody)
    .then((res) => remapPostCategory(res, category ?? ''));
}

/**
 * SharePointリスト項目の添付ファイル付き詳細情報の取得
 * @param tokenProvider
 * @param baseUrl
 * @param editLink
 * @return reject時にはErrorまたはGraphErrorを返却する
 */
function fetchDetailImpl(
  tokenProvider: TokenProvider, baseUrl: string, editLink: string, category?: string,
): Promise<ISharePointListItemResponseWithAttachment> {
  // SPOはMailと違って、idで取得しているわけではない
  // tokenProviderが存在しない場合はreject
  if (!tokenProvider) {
    return Promise.reject(new Error(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }

  // 必須パラメータが存在しない場合はreject
  const [customHost] = parseSharePointPath(baseUrl);
  if (!baseUrl || !editLink || !customHost) {
    return Promise.reject(new Error(UseSharePointApiError.REQUIRED_PARAM_NOT_AVAILABLE));
  }

  const client = createSpClient(tokenProvider, baseUrl, customHost);
  if (!client) return Promise.reject(new Error(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  return client
    .api(editLink)
    .expand('AttachmentFiles')
    .get()
    .then((res) => remapPostCategorySingle(res, category ?? 'category1'));
}

/**
 * ホスト名 + パス = 絶対URL
 * @param hostNameWithProtocol https://を含むホスト名
 * @param path パス
 */
export function getAbsoluteUrlForRelativePath(hostNameWithProtocol: string, path: string): string {
  return complementRelativeUrl(hostNameWithProtocol, path);
}

/**
 * contextinfoからsiteFullUrlを取得する
 * @param tokenProvider
 * @param customHost ホスト名
 * @param absolutePath contextinfoで取得するリソースの絶対URL
 */
export async function getSiteUrl(
  tokenProvider: TokenProvider,
  customHost: string,
  absolutePath: string,
): Promise<string> {

  const baseUrl = absolutePath.substring(0, absolutePath.lastIndexOf('/'));
  const client = createSpClient(tokenProvider, baseUrl, customHost);
  const endpoint = 'contextinfo';

  if (!client) return Promise.reject(new Error(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  const result = await client
    .api(endpoint)
    .responseType(ResponseType.JSON)
    .post({});

  return result?.WebFullUrl ?? '';
}

/**
 * SPOの相対パスから第一階層のサイトパスを取得する。取得できない場合は空文字列を返す
 * @param relPath SharePointOnline上での相対URL
 */
export function getFirstLevelSiteName(relPath: string): string {
  if (relPath.startsWith(SPO_SITES_DIR)) {
    return relPath.split('/')[2];
  }
  return '';
}

/**
 * relativePathのサイト名を含むbaseUrlを作成する
 * @param hostName ホスト名(プロトコルを含まない)
 * @param relativePath SharePointOnline上での相対URL
 */
export function createBaseUrlForRelativePath(hostName: string, relativePath: string): string {
  const siteName = getFirstLevelSiteName(relativePath);
  const sitePath = siteName ? `${SPO_SITES_DIR}${siteName}` : '';
  return `https://${hostName}${sitePath}`;
}

/**
 * SharePoint上のファイルのBlobを取得
 * @param tokenProvider
 * @param hostNameWithSitePath 環境変数にあるホスト名 + 会社からのお知らせが含まれるサイト名。この関数ではこの値をそのままbaseUrlとはしない
 * @param serverRelativeUrl 取得するファイルの相対パス。この相対パスに含まれるサイトパスをbaseUrlに使う
 * @param report
 * @return reject時にはErrorまたはGraphErrorを返却する
 */
export async function fetchFileBlobImpl(
  tokenProvider: TokenProvider,
  hostNameWithSitePath: string,
  serverRelativeUrl: string,
  report?: EventReporter,
): Promise<Blob> {

  const [customHost, sitePath] = parseSharePointPath(hostNameWithSitePath);
  const endpoint = createFileBlobUrl(serverRelativeUrl);
  // 必須パラメータが存在しない場合はreject
  if (!customHost || !endpoint || !sitePath) {
    return Promise.reject(new Error(
      `${UseSharePointApiError.REQUIRED_PARAM_NOT_AVAILABLE} hostNameWithSitePath: ${hostNameWithSitePath}, serverRelativeUrl: ${serverRelativeUrl}`,
    ));
  }

  const hostName = `https://${customHost}`;
  const absoluteUrl = getAbsoluteUrlForRelativePath(hostName, serverRelativeUrl);
  const baseUrl = await getSiteUrl(tokenProvider, customHost, absoluteUrl)
    .catch((reason: Error | GraphError) => {
      // siteFullUrlの取得に失敗した場合はログを送信
      if (report) {
        report({
          name: 'FETCH_CONTEXT_INFO_FAIL',
          type: EventReportType.SYS_ERROR,
          error: reason,
        });
      }
      // reject時は空文字列にしてフォールバック処理を走らせる
      return Promise.resolve('');
    })
    .then((result) => {
      if (result) {
        return Promise.resolve(result);
      }

      // getSiteUrl失敗時または空文字を返したときのフォールバック処理
      // sitePath(元は環境変数)とURLが部分一致する場合は環境変数のURLをbaseUrlとする
      if (absoluteUrl.includes(`${SPO_SITES_DIR}${sitePath}`)) {
        return hostNameWithSitePath;
      }

      // それ以外の場合は従来ロジックでURLを生成(このケースでは権限が足りない可能性が高い)
      return createBaseUrlForRelativePath(customHost, serverRelativeUrl);
    });

  // tokenProviderが存在しない場合はreject
  const client = createSpClient(tokenProvider, baseUrl, customHost);
  if (!client) return Promise.reject(new Error(UseSharePointApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  return client
    .api(endpoint)
    .responseType(ResponseType.BLOB)
    .get()
    .catch((reason: GraphError) => {
      const errorWithDetail = new GraphError(reason.statusCode);
      errorWithDetail.body = {
        body: reason.body,
        serverRelativeUrl,
        baseUrl,
        absoluteUrl,
        customHost,
        endpoint,
      };
      return Promise.reject(errorWithDetail);
    });
}
export function cancellationIf<TFunc extends(...params: any[]) => any>(
  cancellationRef: React.MutableRefObject<boolean>,
  doAction: TFunc): TFunc {
  if (!cancellationRef.current) {
    return doAction;
  }
  return (() => ({})) as TFunc;
}

/**
 * SharePointAPI通信機能
 * @param tokenProvider
 */
const useSharePointApiAccessor = (
  tokenProvider: TokenProvider,
  cancellationRef: React.MutableRefObject<boolean>,
): UseSharePointApiReturnType => {

  /**
   * fetch SharePoint list without CAML query
   */
  const fetchListItem: FetchSPOList = React.useCallback(async (
    baseUrl: string, listGUID: string, listItemGUID: string[], category?: string,
  ) => cancellationIf(cancellationRef, fetchListItemImpl)(
    tokenProvider,
    baseUrl,
    listGUID,
    listItemGUID,
    category,
  ), [tokenProvider, cancellationRef]);

  /**
   * get the detail of SharePoint post by editLink
   */
  const fetchDetail: FetchSPODetail = React.useCallback(async (
    baseUrl: string, editLink: string, category?: string,
  ) => fetchDetailImpl(tokenProvider, baseUrl, editLink, category), [tokenProvider]);

  /**
   * SharePointファイルのblob取得
   */
  const fetchFileBlob: FetchFileBlob = React.useCallback(async (
    baseUrl: string, serverRelativeUrl: string, report?: EventReporter,
  ) => fetchFileBlobImpl(tokenProvider, baseUrl, serverRelativeUrl, report), [tokenProvider]);

  // tokenProviderが存在するときにだけ関数を返却
  return tokenProvider
    ? [fetchDetail, fetchListItem, fetchFileBlob]
    : [undefined, undefined, undefined];
};

export default useSharePointApiAccessor;
