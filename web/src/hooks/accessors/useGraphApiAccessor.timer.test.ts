import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { createGraphClient } from '@avanade-teams/auth';
import { waitFor } from '@testing-library/react';
import { sendRequests } from './useGraphApiAccessor';
import { WeakTokenProvider } from '../../types/TokenProvider';

/**
 * 取得処理の完了を持たず(awaitしてない)にテストをしているので他のテストスイートに影響を与えないように分割している
 * 本テストのようなテストを新規追加したい場合はファイルを分けて別で実施する
 *  */

dayjs.extend(isBetween);

const MockDefaultTime = 3000;

jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 2,
    REACT_APP_RETRY_DEAFULT_TIME: MockDefaultTime,
    REACT_APP_ADDITIONAL_WAITING_MSEC: 0,
    REACT_APP_BATCH_REQUEST_CHUNK_SIZE: 20,
  },
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));
const mockCreateGraphClient = createGraphClient as jest.Mock;
const mockProvider = jest.mock;

const postMock = jest.fn();
const apiMock = jest.fn();
const mockClient = {
  api: apiMock,
  select: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  post: postMock,
  get: jest.fn(),
};
const cancellationRef = { current: false };

describe('useGraphAPiAccessor', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });
  beforeEach(() => {
    mockCreateGraphClient.mockReturnValue(mockClient);
    mockCreateGraphClient.mockClear();
    Object.values(mockClient).forEach((v) => v.mockClear());
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('retry timer', () => {
    beforeEach(() => {
      jest.setTimeout(10000);
    });

    describe('when retry after is not exists', () => {
      beforeEach(() => {
        jest.setTimeout(10000);
      });
      it('should wait default seconds', async () => {

        apiMock.mockImplementation(() => ({ post: postMock }));
        postMock.mockResolvedValue({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: {},
          }],
        });

        sendRequests(mockProvider as unknown as WeakTokenProvider, [{ id: '1', url: '/me/user', method: 'GET' }], cancellationRef);
        const start = new Date();
        expect(postMock).toHaveBeenCalledTimes(1);
        await waitFor(() => {
          expect(postMock).toHaveBeenCalledTimes(2);
          // 終了時の時間を比較（500ms程度幅を持たせる）
          expect(dayjs().isBetween(dayjs(start).add(MockDefaultTime, 'ms'), dayjs(start).add(MockDefaultTime + 500, 'ms'))).toBeTruthy();
        }, { timeout: MockDefaultTime + 500 });

      });
    });

    describe('when retry after is exists', () => {
      it('should wait retry after seconds', async () => {

        apiMock.mockImplementation(() => ({ post: postMock }));
        postMock.mockResolvedValue({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: {
              'Retry-After': '9',
            },
          },
          {
            body: '',
            id: '2',
            status: 429,
            headers: {
              'Retry-After': '5',
            },
          }],
        });

        sendRequests(mockProvider as unknown as WeakTokenProvider, [{ id: '1', url: '/me/user', method: 'GET' }], cancellationRef);
        const start = new Date();
        expect(postMock).toHaveBeenCalledTimes(1);
        await waitFor(() => {
          expect(postMock).toHaveBeenCalledTimes(1);
          // 終了時の時間を比較
          expect(dayjs().isBetween(dayjs(start).add(MockDefaultTime, 'ms'), dayjs(start).add(MockDefaultTime + 500, 'ms'))).toBeTruthy();
        }, { timeout: MockDefaultTime + 500 });

      });
    });
  });
});
