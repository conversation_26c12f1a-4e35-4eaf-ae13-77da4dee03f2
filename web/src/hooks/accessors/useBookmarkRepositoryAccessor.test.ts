import { renderHook, act } from '@testing-library/react-hooks';
import { openDB } from 'idb';
import {
  clearIdbMocks, dbMock, storeMock,
} from '../../mocks/idb';
import { createReposBookmarkItem, createReposBookmarkQueue } from '../../utilities/test';
import useBookmarkRepositoryAccessor, {
  MAX_BOOKMARKS_COUNT,
  REPOS_CURRENT_DB_VERSION,
  UseRepositoryError,
} from './useBookmarkRepositoryAccessor';
import useIndexedDbAccessor from './useIndexedDbAccessor';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

jest.mock('idb', () => ({
  openDB: jest.fn(),
}));
jest.mock('../../utilities/environment');

// openDBのmock
const openDBMock = (openDB as jest.Mock).mockResolvedValue(dbMock);

describe('useBookmarkRepositoryAccessor', () => {
  async function getHook(initialEntries: ISplitViewListSingle[]) {
    dbMock.getAll.mockResolvedValue(initialEntries);
    const getHookResult = renderHook(
      () => useBookmarkRepositoryAccessor(useIndexedDbAccessor()[0]),
    );
    const { result, waitForNextUpdate } = getHookResult;

    // 初期化前はundefined
    expect(result.current.addBookmark).toBeUndefined();
    expect(result.current.deleteBookmark).toBeUndefined();
    expect(result.current.updateBookmark).toBeUndefined();

    await waitForNextUpdate();

    // openDBが実行されている
    expect(openDBMock).toHaveBeenCalledTimes(2);
    expect(openDBMock).toHaveBeenCalledWith(
      'geranium-attane',
      REPOS_CURRENT_DB_VERSION,
      expect.objectContaining({
        upgrade: expect.anything(),
      }),
    );

    // getAllが呼ばれている
    expect(dbMock.getAll).toHaveBeenCalledTimes(1);
    // closeが呼ばれている
    expect(dbMock.close).toHaveBeenCalledTimes(2);

    // 初期化後はundefinedではない
    expect(result.current.addBookmark).not.toBeUndefined();
    expect(result.current.deleteBookmark).not.toBeUndefined();
    expect(result.current.updateBookmark).not.toBeUndefined();

    openDBMock.mockClear();
    clearIdbMocks();

    return getHookResult;
  }

  beforeEach(() => {
    openDBMock.mockClear();
    clearIdbMocks();
  });

  describe('addBookmark', () => {
    let current = new Date();

    beforeAll(() => {
      jest.useFakeTimers('modern');
      current = new Date();
      jest.setSystemTime(current);
    });

    afterAll(() => {
      jest.useRealTimers();
    });

    describe('when the bookmark count is less than MAX_BOOKMARKS_COUNT', () => {
      it('should resolve the value db.put returned', async () => {
        dbMock.put.mockResolvedValueOnce('put-result');

        const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT - 1)
          .fill(true)
          .map((v, i) => createReposBookmarkItem({ id: String(i) }));

        // 初期化を待機
        const { result } = await getHook(initialEntriesOfMaxCount);
        const { addBookmark } = result.current;
        if (!addBookmark) return;

        await act(async () => {
          await expect(addBookmark({
            id: '123',
            kind: 'SPO',
            title: 'abcd',
            note: 'aaa',
            displayDate: 'displayDate',
            properties: {
              listUrl: 'listUrl',
              siteUrl: 'siteUrl',
              listId: 'listId',
              listName: 'listName',
              createdDate: 'created-date',
              updatedDate: 'updated-date',
              editLink: 'edit-link',
              hasAttachments: true,
            },
          }))
            // putと同じ値を返す
            .resolves.toStrictEqual('put-result');

          // putが呼ばれている
          expect(dbMock.put).toHaveBeenCalledWith(
            'bookmarks',
            // 書き込まれるデータを検証
            {
              id: '123',
              kind: 'SPO',
              title: 'abcd',
              note: 'aaa',
              displayDate: 'displayDate',
              properties: {
                listUrl: 'listUrl',
                siteUrl: 'siteUrl',
                listId: 'listId',
                listName: 'listName',
                createdDate: 'created-date',
                updatedDate: 'updated-date',
                editLink: 'edit-link',
                hasAttachments: true,
              },
              reposCreatedDate: current.toISOString(),
              reposUpdatedDate: current.toISOString(),
            },
            '123',
          );

          // 変更後の再取得のため1回呼ばれる
          expect(dbMock.getAll).toHaveBeenCalledTimes(1);
          // 再取得も含めて2回呼ばれる
          expect(dbMock.close).toHaveBeenCalledTimes(2);
        });
      });
    });

    describe('when the bookmark count is already equal to MAX_BOOKMARKS_COUNT', () => {
      it('should reject with MAX_BOOKMARKS message', async () => {
        const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT)
          .fill(true)
          .map((v, i) => createReposBookmarkItem({ id: String(i) }));

        // 初期化を待機
        const { result } = await getHook(initialEntriesOfMaxCount);
        const { addBookmark } = result.current;
        if (!addBookmark) return;

        await expect(addBookmark).rejects.toMatch(UseRepositoryError.MAX_BOOKMARKS);
      });
    });

    describe('when the bookmark count is more than MAX_BOOKMARKS_COUNT', () => {
      it('should reject with MAX_BOOKMARKS message', async () => {
        const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT + 1)
          .fill(true)
          .map((v, i) => createReposBookmarkItem({ id: String(i) }));

        // 初期化を待機
        const { result } = await getHook(initialEntriesOfMaxCount);
        const { addBookmark } = result.current;
        if (!addBookmark) return;

        await expect(addBookmark).rejects.toMatch(UseRepositoryError.MAX_BOOKMARKS);
      });
    });

    describe('when db.put rejects', () => {
      it('should reject and call db.close', async () => {
        dbMock.put.mockRejectedValueOnce('reject');

        // 初期化を待機
        const { result } = await getHook([]);
        const { addBookmark } = result.current;
        if (!addBookmark) return;

        await act(async () => {
          await expect(addBookmark({
            id: '123',
            kind: 'SPO',
            title: 'abcd',
            note: 'aaa',
            displayDate: 'displayDate',
            properties: {
              listUrl: 'listUrl',
              siteUrl: 'siteUrl',
              listId: 'listId',
              listName: 'listName',
              createdDate: 'created-date',
              updatedDate: 'updated-date',
              editLink: 'edit-link',
              hasAttachments: true,
            },
          }))
            .rejects.toMatch('reject');

          // closeが呼ばれている
          expect(dbMock.close).toHaveBeenCalledTimes(2);
        });
      });
    });
  });

  describe('deleteBookmark', () => {
    it('should resolve the value db.delete returned', async () => {
      dbMock.delete.mockResolvedValueOnce('delete');

      // 初期化を待機
      const { result } = await getHook([]);
      const { deleteBookmark } = result.current;
      if (!deleteBookmark) return;

      await act(async () => {
        // エラーがthrowされていない
        await expect(deleteBookmark('itemId-001'))
          .resolves.not.toThrow();

        // 変更後の再取得のため1回呼ばれる
        expect(dbMock.getAll).toHaveBeenCalledTimes(1);
        // 再取得も含めて2回呼ばれる
        expect(dbMock.close).toHaveBeenCalledTimes(2);
      });
    });

    describe('when db.delete rejects', () => {
      it('should reject and call db.close', async () => {
        dbMock.delete.mockRejectedValueOnce('reject');

        // 初期化を待機
        const { result } = await getHook([]);
        const { deleteBookmark } = result.current;
        if (!deleteBookmark) return;

        await act(async () => {
          await expect(deleteBookmark('itemId-001'))
            .rejects.toMatch('reject');

          // 変更後の再取得のため1回呼ばれる
          expect(dbMock.getAll).toHaveBeenCalledTimes(1);
          // 再取得も含めて2回呼ばれる
          expect(dbMock.close).toHaveBeenCalledTimes(2);
        });
      });
    });
  });

  describe('replaceBookmarks', () => {
    describe('when items.length === 0', () => {
      it('should clear the bookmarks store', async () => {
        const { result } = await getHook([]);
        const { replaceBookmarks } = result.current;
        expect(replaceBookmarks).not.toBeUndefined();
        if (!replaceBookmarks) return;

        dbMock.close.mockClear();

        await act(async () => {
          await expect(replaceBookmarks([])).resolves.toBeUndefined();
          expect(storeMock.clear).toHaveBeenCalledTimes(1);
          expect(storeMock.put).not.toHaveBeenCalled();
        });
      });
    });

    describe('when items have two entries', () => {
      const items = [
        createReposBookmarkItem({ id: 'abc', reposCreatedDate: new Date('2021-01-01').toISOString() }),
        createReposBookmarkItem({ id: 'efg', reposCreatedDate: new Date('2021-01-01').toISOString() }),
      ];

      it('should replace the store with the items', async () => {
        const { result } = await getHook([]);
        const { replaceBookmarks } = result.current;
        expect(replaceBookmarks).not.toBeUndefined();
        if (!replaceBookmarks) return;

        dbMock.close.mockClear();

        await act(async () => {
          await expect(replaceBookmarks(items)).resolves.toBeUndefined();

          expect(dbMock.transaction).toHaveBeenCalledWith(expect.anything(), 'readwrite');
          expect(storeMock.clear).toHaveBeenCalledTimes(1);

          expect(storeMock.put).toHaveBeenCalledTimes(2);
          expect(storeMock.put).toHaveBeenNthCalledWith(
            1,
            createReposBookmarkItem({ id: 'abc', reposCreatedDate: new Date('2021-01-01').toISOString() }),
            'abc',
          );
          expect(storeMock.put).toHaveBeenNthCalledWith(
            2,
            createReposBookmarkItem({ id: 'efg', reposCreatedDate: new Date('2021-01-01').toISOString() }),
            'efg',
          );

          expect(dbMock.close).toHaveBeenCalledTimes(2);
        });
      });
    });
  });

  describe('getBookmarkQueues', () => {
    async function getBookmarkQueuesCase() {
      const { result } = await getHook([]);
      const { getBookmarkQueues } = result.current;
      expect(getBookmarkQueues).not.toBeUndefined();
      if (!getBookmarkQueues) throw new Error('undefined error');
      dbMock.close.mockClear();
      return getBookmarkQueues;
    }

    describe('when getAll returns', () => {
      it('should call openDB', async () => {
        const getBookmarkQueues = await getBookmarkQueuesCase();
        await getBookmarkQueues();
        expect(openDB).toHaveBeenCalledTimes(1);
        expect(openDB).toHaveBeenCalledWith('geranium-attane', REPOS_CURRENT_DB_VERSION);
      });

      it('should return what getAll returns', async () => {
        const getBookmarkQueues = await getBookmarkQueuesCase();
        dbMock.getAll.mockResolvedValue(['abc', '123']);
        await expect(getBookmarkQueues()).resolves.toStrictEqual(['abc', '123']);
      });

      it('should call db.close', async () => {
        const getBookmarkQueues = await getBookmarkQueuesCase();
        await getBookmarkQueues();
        expect(dbMock.close).toHaveBeenCalledTimes(1);
      });
    });

    describe('when getAll rejects', () => {
      it('should reject with what getAll rejects', async () => {
        const getBookmarkQueues = await getBookmarkQueuesCase();
        dbMock.getAll.mockRejectedValueOnce('reject');
        await expect(getBookmarkQueues()).rejects.toMatch('reject');
      });

      it('should call db.close', async () => {
        const getBookmarkQueues = await getBookmarkQueuesCase();
        dbMock.getAll.mockRejectedValueOnce('reject');
        await expect(getBookmarkQueues()).rejects.toMatch('reject');
        expect(dbMock.close).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('addBookmarkQueue', () => {
    async function addBookmarkQueueCase(initialEntries: ISplitViewListSingle[] = []) {
      const { result } = await getHook(initialEntries);
      const { addBookmarkQueue } = result.current;
      expect(addBookmarkQueue).not.toBeUndefined();
      if (!addBookmarkQueue) throw new Error('undefined error');
      dbMock.close.mockClear();
      return addBookmarkQueue;
    }

    const data = createReposBookmarkItem({});

    describe('when the type is "PUT"', () => {
      const type = 'PUT';

      describe('when store.get resolves undefined', () => {
        beforeEach(() => {
          storeMock.get.mockResolvedValue(undefined);
        });

        it('should call openDB', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // finallyのrenewBookmarksも含めて2回呼ばれる
            expect(openDB).toHaveBeenCalledTimes(2);
            expect(openDB).toHaveBeenCalledWith('geranium-attane', REPOS_CURRENT_DB_VERSION);
          });
        });

        it('should resolve undefined', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await expect(addBookmarkQueue(data, type)).resolves.toBeUndefined();
          });
        });

        it('should not call store.delete', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            expect(storeMock.delete).not.toHaveBeenCalled();
          });
        });

        it('should call store.get', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            expect(storeMock.get).toHaveBeenCalledTimes(1);
            expect(storeMock.get).toHaveBeenCalledWith(data.id);
          });
        });

        it('should put a new entry', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // bookmarksとqueueで2回
            expect(storeMock.put).toHaveBeenCalledTimes(2);
            expect(storeMock.put).toHaveBeenCalledWith(
              {
                type: 'PUT',
                data,
                date: expect.anything(),
              },
              data.id,
            );
            expect(storeMock.put).toHaveBeenCalledWith(data, data.id);
          });
        });

        it('should call db.close', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // finallyのrenewBookmarksも含めて2回呼ばれる
            expect(dbMock.close).toHaveBeenCalledTimes(2);
          });
        });

        describe('when allBookmarks.length is equal to MAX_BOOKMARKS_COUNT', () => {
          const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT)
            .fill(true)
            .map((v, i) => createReposBookmarkItem({ id: String(i) }));

          it('should reject with MAX_BOOKMARKS error', async () => {
            const addBookmarkQueue = await addBookmarkQueueCase(initialEntriesOfMaxCount);
            await act(async () => {
              await expect(addBookmarkQueue(data, type))
                .rejects.toMatch(UseRepositoryError.MAX_BOOKMARKS);
            });
          });
        });
      });

      describe('when store.get resolves a queue of "PUT"', () => {
        beforeEach(() => {
          storeMock.get.mockResolvedValue({
            type: 'PUT',
          });
        });

        it('should call openDB', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // finallyのrenewBookmarksも含めて2回呼ばれる
            expect(openDB).toHaveBeenCalledTimes(2);
            expect(openDB).toHaveBeenCalledWith('geranium-attane', REPOS_CURRENT_DB_VERSION);
          });
        });

        it('should resolve undefined', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await expect(addBookmarkQueue(data, type)).resolves.toBeUndefined();
          });
        });

        it('should not call store.delete', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
          });
        });

        it('should call store.get', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            expect(storeMock.get).toHaveBeenCalledTimes(1);
            expect(storeMock.get).toHaveBeenCalledWith(data.id);
          });
        });

        it('should put a new entry', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // bookmarksとqueueの2回呼ばれる
            expect(storeMock.put).toHaveBeenCalledTimes(2);
            expect(storeMock.put).toHaveBeenCalledWith(
              {
                type: 'PUT',
                data,
                date: expect.anything(),
              },
              data.id,
            );
            expect(storeMock.put).toHaveBeenCalledWith(data, data.id);
          });
        });

        it('should call db.close', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // finallyのrenewBookmarksも含めて2回呼ばれる
            expect(dbMock.close).toHaveBeenCalledTimes(2);
          });
        });

        describe('when allBookmarks.length is equal to MAX_BOOKMARKS_COUNT', () => {
          const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT)
            .fill(true)
            .map((v, i) => createReposBookmarkItem({ id: String(i) }));

          it('should reject with MAX_BOOKMARKS error', async () => {
            const addBookmarkQueue = await addBookmarkQueueCase(initialEntriesOfMaxCount);
            await act(async () => {
              await expect(addBookmarkQueue(data, type))
                .rejects.toMatch(UseRepositoryError.MAX_BOOKMARKS);
            });
          });
        });
      });

      describe('when store.get resolves a queue of "DELETE"', () => {
        beforeEach(() => {
          storeMock.get.mockResolvedValue({
            type: 'DELETE',
          });
        });

        it('should resolve undefined', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await expect(addBookmarkQueue(data, type)).resolves.toBeUndefined();
          });
        });

        it('should call store.delete', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // queueの削除のために1回呼ばれる
            expect(storeMock.delete).toHaveBeenCalledTimes(1);
            expect(storeMock.delete).toHaveBeenCalledWith(data.id);
          });
        });

        it('should not call store.put for the queue', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // bookmarksの削除のために1回呼ばれる
            expect(storeMock.put).toHaveBeenCalledTimes(1);
          });
        });

        it('should call db.close', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            // finallyのrenewBookmarksも含めて2回呼ばれる
            expect(dbMock.close).toHaveBeenCalledTimes(2);
          });
        });

        describe('when allBookmarks.length is equal to MAX_BOOKMARKS_COUNT', () => {
          const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT)
            .fill(true)
            .map((v, i) => createReposBookmarkItem({ id: String(i) }));

          it('should reject with MAX_BOOKMARKS error', async () => {
            const addBookmarkQueue = await addBookmarkQueueCase(initialEntriesOfMaxCount);
            await act(async () => {
              await expect(addBookmarkQueue(data, type))
                .rejects.toMatch(UseRepositoryError.MAX_BOOKMARKS);
            });
          });
        });
      });
    });

    describe('when the type is "DELETE"', () => {
      const type = 'DELETE';

      // 差分パターンのみチェック
      describe('when store.get resolves undefined', () => {
        beforeEach(() => {
          storeMock.get.mockResolvedValue(undefined);
        });

        it('should put a new entry', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            expect(storeMock.put).toHaveBeenCalledTimes(1);
            expect(storeMock.put).toHaveBeenCalledWith(
              {
                type: 'DELETE',
                data,
                date: expect.anything(),
              },
              data.id,
            );
          });
        });

        describe('when allBookmarks.length is equal to MAX_BOOKMARKS_COUNT', () => {
          const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT)
            .fill(true)
            .map((v, i) => createReposBookmarkItem({ id: String(i) }));

          it('should put a new entry', async () => {
            const addBookmarkQueue = await addBookmarkQueueCase(initialEntriesOfMaxCount);
            await act(async () => {
              await addBookmarkQueue(data, type);
              expect(storeMock.put).toHaveBeenCalledTimes(1);
              expect(storeMock.put).toHaveBeenCalledWith(
                {
                  type: 'DELETE',
                  data,
                  date: expect.anything(),
                },
                data.id,
              );
            });
          });
        });
      });

      // 差分パターンのみチェック
      describe('when store.get resolve a queue of "PUT"', () => {
        beforeEach(() => {
          storeMock.get.mockResolvedValue({
            type: 'PUT',
          });
        });

        it('should call store.delete', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            expect(storeMock.delete).toHaveBeenCalledTimes(2);
            expect(storeMock.delete).toHaveBeenCalledWith(data.id);
          });
        });

        describe('when allBookmarks.length is equal to MAX_BOOKMARKS_COUNT', () => {
          const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT)
            .fill(true)
            .map((v, i) => createReposBookmarkItem({ id: String(i) }));

          it('should call store.delete', async () => {
            const addBookmarkQueue = await addBookmarkQueueCase(initialEntriesOfMaxCount);
            await act(async () => {
              await addBookmarkQueue(data, type);
              expect(storeMock.delete).toHaveBeenCalledTimes(2);
              expect(storeMock.delete).toHaveBeenCalledWith(data.id);
            });
          });
        });
      });

      // 差分パターンのみチェック
      describe('when store.get resolves a queue of "DELETE"', () => {
        beforeEach(() => {
          storeMock.get.mockResolvedValue({
            type: 'DELETE',
          });
        });

        it('should put a new entry', async () => {
          const addBookmarkQueue = await addBookmarkQueueCase();
          await act(async () => {
            await addBookmarkQueue(data, type);
            expect(storeMock.put).toHaveBeenCalledTimes(1);
            expect(storeMock.put).toHaveBeenCalledWith(
              {
                type: 'DELETE',
                data,
                date: expect.anything(),
              },
              data.id,
            );
          });
        });

        describe('when allBookmarks.length is equal to MAX_BOOKMARKS_COUNT', () => {
          const initialEntriesOfMaxCount = new Array(MAX_BOOKMARKS_COUNT)
            .fill(true)
            .map((v, i) => createReposBookmarkItem({ id: String(i) }));

          it('should put a new entry', async () => {
            const addBookmarkQueue = await addBookmarkQueueCase(initialEntriesOfMaxCount);
            await act(async () => {
              await addBookmarkQueue(data, type);
              expect(storeMock.put).toHaveBeenCalledTimes(1);
              expect(storeMock.put).toHaveBeenCalledWith(
                {
                  type: 'DELETE',
                  data,
                  date: expect.anything(),
                },
                data.id,
              );
            });
          });
        });
      });
    });
  });

  describe('deleteBookmarkQueue', () => {
    async function deleteBookmarkQueueCase() {
      const { result } = await getHook([]);
      const { deleteBookmarkQueue } = result.current;
      expect(deleteBookmarkQueue).not.toBeUndefined();
      if (!deleteBookmarkQueue) throw new Error('undefined error');
      dbMock.close.mockClear();
      return deleteBookmarkQueue;
    }

    const key = 'abc';

    it('should call openDB', async () => {
      const deleteBookmarkQueue = await deleteBookmarkQueueCase();
      await deleteBookmarkQueue(key);
      expect(openDB).toHaveBeenCalledTimes(1);
      expect(openDB).toHaveBeenCalledWith('geranium-attane', REPOS_CURRENT_DB_VERSION);
    });

    it('should resolve undefined', async () => {
      const deleteBookmarkQueue = await deleteBookmarkQueueCase();
      await expect(deleteBookmarkQueue(key)).resolves.toBeUndefined();
    });

    it('should call db.delete', async () => {
      const deleteBookmarkQueue = await deleteBookmarkQueueCase();
      await deleteBookmarkQueue(key);
      expect(dbMock.delete).toHaveBeenCalledTimes(1);
      expect(dbMock.delete).toHaveBeenCalledWith('bookmark_queue', key);
    });

    it('should call db.close', async () => {
      const deleteBookmarkQueue = await deleteBookmarkQueueCase();
      await deleteBookmarkQueue(key);
      expect(dbMock.close).toHaveBeenCalledTimes(1);
    });
  });

  describe('replaceBookmarkQueues', () => {
    describe('when items.length === 0', () => {
      it('should not do anything', async () => {
        const { result } = await getHook([]);
        const { replaceBookmarkQueues } = result.current;
        expect(replaceBookmarkQueues).not.toBeUndefined();
        if (!replaceBookmarkQueues) return;

        dbMock.transaction.mockClear();

        await act(async () => {
          await expect(replaceBookmarkQueues([])).resolves.toBeUndefined();
          expect(dbMock.transaction).not.toHaveBeenCalled();
        });
      });
    });

    describe('when items have two entries', () => {
      const items = [
        createReposBookmarkQueue({ type: 'PUT', data: { id: 'abc' } }),
        createReposBookmarkQueue({ type: 'DELETE', data: { id: 'efg' } }),
      ];

      it('should replace the store with the items', async () => {
        const { result } = await getHook([]);
        const { replaceBookmarkQueues } = result.current;
        expect(replaceBookmarkQueues).not.toBeUndefined();
        if (!replaceBookmarkQueues) return;

        dbMock.close.mockClear();

        await act(async () => {
          await expect(replaceBookmarkQueues(items)).resolves.toBeUndefined();

          expect(dbMock.transaction).toHaveBeenCalledWith(expect.anything(), 'readwrite');
          expect(storeMock.clear).toHaveBeenCalledTimes(1);

          expect(storeMock.put).toHaveBeenCalledTimes(2);
          expect(storeMock.put).toHaveBeenNthCalledWith(
            1,
            createReposBookmarkQueue({ type: 'PUT', data: { id: 'abc' } }),
            'abc',
          );
          expect(storeMock.put).toHaveBeenNthCalledWith(
            2,
            createReposBookmarkQueue({ type: 'DELETE', data: { id: 'efg' } }),
            'efg',
          );

          expect(dbMock.close).toHaveBeenCalledTimes(1);
        });
      });
    });
  });

  describe('updateBookmark', () => {
    describe('when the bookmark is not found in the allBookmarks', () => {
      it('should resolve undefined', async () => {
        const { result } = await getHook([
          createReposBookmarkItem({ id: 'abc' }),
        ]);
        if (!result.current.updateBookmark) return;
        const { updateBookmark } = result.current;

        await expect(updateBookmark(createReposBookmarkItem({ id: '123' }))).resolves.toBeUndefined();
        expect(openDBMock).toHaveBeenCalledTimes(0);
      });
    });

    describe('when there is no change of the updatedDate', () => {
      it('should resolve undefined', async () => {
        const { result } = await getHook([
          createReposBookmarkItem({
            id: 'abc',
            properties: {
              siteUrl: 'siteUrl',
              listUrl: 'listUrl',
              listId: 'listId',
              listName: 'listName',
              editLink: 'abc',
              createdDate: '2021-08-24T03:00:59.000Z',
              updatedDate: 'abc',
              hasAttachments: false,
            },
          }),
        ]);
        if (!result.current.updateBookmark) return;
        const { updateBookmark } = result.current;

        await expect(updateBookmark(createReposBookmarkItem({
          id: 'abc',
          properties: {
            siteUrl: 'siteUrl',
            listUrl: 'listUrl',
            listId: 'listId',
            listName: 'listName',
            editLink: 'abc',
            createdDate: '2021-08-24T03:00:59.000Z',
            updatedDate: 'abc',
            hasAttachments: false,
          },
        }))).resolves.toBeUndefined();
        expect(openDBMock).toHaveBeenCalledTimes(0);
      });
    });

    describe('when the new updatedDate is older than current', () => {
      it('should resolve undefined', async () => {
        const { result } = await getHook([
          createReposBookmarkItem({
            id: 'abc',
            properties: {
              siteUrl: 'siteUrl',
              listUrl: 'listUrl',
              listId: 'listId',
              listName: 'listName',
              editLink: 'abc',
              createdDate: '2021-08-24T03:00:59.000Z',
              updatedDate: '2021-01-01',
              hasAttachments: false,
            },
          }),
        ]);
        if (!result.current.updateBookmark) return;
        const { updateBookmark } = result.current;

        await expect(updateBookmark(createReposBookmarkItem({
          id: 'abc',
          properties: {
            siteUrl: 'siteUrl',
            listUrl: 'listUrl',
            listId: 'listId',
            listName: 'listName',
            editLink: 'abc',
            createdDate: '2021-08-24T03:00:59.000Z',
            updatedDate: '2020-01-01',
            hasAttachments: false,
          },
        }))).resolves.toBeUndefined();
        expect(openDBMock).toHaveBeenCalledTimes(0);
      });
    });

    describe('when the new updatedDate is newer than current', () => {
      it('should resolve the id and should call the db.put', async () => {
        const { result } = await getHook([
          createReposBookmarkItem({
            id: 'abc',
            properties: {
              siteUrl: 'siteUrl',
              listUrl: 'listUrl',
              listId: 'listId',
              listName: 'listName',
              editLink: 'abc',
              createdDate: '2021-08-24T03:00:59.000Z',
              updatedDate: '2021-01-01',
              hasAttachments: false,
            },
          }),
        ]);
        if (!result.current.updateBookmark) return;
        const { updateBookmark } = result.current;

        dbMock.close.mockClear();
        dbMock.put.mockClear();
        dbMock.put.mockResolvedValueOnce('abc');

        await act(async () => {
          await expect(updateBookmark(createReposBookmarkItem({
            id: 'abc',
            title: 'ABC',
            note: 'EFG',
            properties: {
              siteUrl: 'siteUrl',
              listUrl: 'listUrl',
              listId: 'listId',
              listName: 'listName',
              editLink: 'abc',
              createdDate: '2021-08-24T03:00:59.000Z',
              updatedDate: '2022-01-01',
              hasAttachments: false,
            },
          }))).resolves.toBe('abc');

          expect(dbMock.put).toHaveBeenCalledWith(
            expect.anything(),
            {
              ...createReposBookmarkItem({
                id: 'abc',
                title: 'ABC',
                note: 'EFG',
                properties: {
                  siteUrl: 'siteUrl',
                  listUrl: 'listUrl',
                  listId: 'listId',
                  listName: 'listName',
                  editLink: 'abc',
                  createdDate: '2021-08-24T03:00:59.000Z',
                  updatedDate: '2022-01-01',
                  hasAttachments: false,
                },
              }),
              reposUpdatedDate: expect.anything(),
            },
            'abc',
          );

          // 変更後の再取得のため1回呼ばれる
          expect(dbMock.getAll).toHaveBeenCalledTimes(1);
          // 再取得も含めて2回呼ばれる
          expect(dbMock.close).toHaveBeenCalledTimes(2);
        });
      });
    });
  });

  describe('allBookmarks', () => {
    it('should be desc sorted by reposCreatedDate', async () => {
      const { result } = await getHook([
        createReposBookmarkItem({ id: 'a', reposCreatedDate: new Date('2021-01-01').toISOString() }),
        createReposBookmarkItem({ id: 'b', reposCreatedDate: new Date('2021-02-01').toISOString() }),
        createReposBookmarkItem({ id: 'c', reposCreatedDate: new Date('2021-01-02').toISOString() }),
        createReposBookmarkItem({ id: 'd', reposCreatedDate: new Date('2021-01-02').toISOString() }),
      ]);
      const { allBookmarks } = result.current;
      expect(allBookmarks).toStrictEqual([
        createReposBookmarkItem({ id: 'b', reposCreatedDate: new Date('2021-02-01').toISOString() }),
        createReposBookmarkItem({ id: 'c', reposCreatedDate: new Date('2021-01-02').toISOString() }),
        createReposBookmarkItem({ id: 'd', reposCreatedDate: new Date('2021-01-02').toISOString() }),
        createReposBookmarkItem({ id: 'a', reposCreatedDate: new Date('2021-01-01').toISOString() }),
      ]);
    });
  });
});
