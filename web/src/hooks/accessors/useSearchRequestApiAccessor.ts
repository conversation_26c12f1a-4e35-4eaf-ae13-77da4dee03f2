import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import * as React from 'react';
import { IContext, ISortOrder } from '../../types/IContext';
import { ISearchRequestResult } from '../../types/ISearchRequestResult';
import { Reporters } from '../../types/Reporters';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { AtTaneGraphError, fetchUrlRes } from '../../utilities/commonFunction';
import environment from '../../utilities/environment';
import { getUniqueNameByToken } from '../../utilities/token/jwt';
import SearchRequestError from '../../utilities/errors/searchRequestError';

export type PostSearchRequestApi = (condition: string) => Promise<void>;
export type GetSearchRequestApi = () => Promise<ISearchRequestResult | void>
export type CancelSearchRequestApi = () => Promise<string | void>
export type GetPartialSearchRequestApi = (
  reqId: string,
  knownPids: string[]
) => Promise<ISearchRequestResult | void>
export type UpdateRemoteContextApi = (context: IContext) => Promise<void>;
export type UpdateRemoteContextFieldApi = (fieldName: 'sort' | 'filter', sortContext: ISortOrder[]) => Promise<void>;

export type UseSearchRequestApiReturnType = {
  postSearchRequestApi: PostSearchRequestApi | undefined,
  getSearchRequestApi: GetSearchRequestApi | undefined,
  cancelSearchRequestApi: CancelSearchRequestApi | undefined,
  getPartialSearchRequestApi: GetPartialSearchRequestApi | undefined,
  updateRemoteContextApi: UpdateRemoteContextApi | undefined,
  updateRemoteContextFieldApi: UpdateRemoteContextFieldApi | undefined,
};

export const UseSearchRequestApiAccessorError = {
  MISSING_PARAMS: 'MISSING_PARAMS',
  NO_TOKENS: 'NO_TOKENS',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
  NO_CONTENT: 'NO_CONTENT',
};

const PREFIX = environment.REACT_APP_API_URL ?? '/api';

/**
 * SearchRequestResultのGET URLを作成する
 * @param userId
 */
export function createGetUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/search/result`;
}

/**
 * SearchRequestResultのPOST URLを作成する
 * @param userId
 */
export function cancelPostUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/search/cancellation`;
}

/**
 * SearchRequestResultのPOST URLを作成する
 * @param userId
 */
export function createPostUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/search`;
}

/**
 * Contextを更新するためのPUT URLを作成する
 * @param userId
 */
export function createContextPutUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/search/context`;
}

/**
 * Contextの特定のフィールドを更新するためのPUT URLを作成する
 * @param userId
 */
export function createContextFieldPutUrl(userId: string, fieldName: 'sort' | 'filter'): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/search/context/${fieldName}`;
}

function createErrorMessage(response: { [key: string]: unknown }) {
  const conditionError = response['value.condition'] ?? response['Value.condition'];
  if (typeof conditionError === 'string') {
    return new SearchRequestError('検索キーワードに誤りがあります。', { 'value.condition': conditionError });
  }
  if (conditionError instanceof Array) {
    return new SearchRequestError('検索キーワードに誤りがあります。', { 'value.condition': conditionError.join('\n') });
  }
  return new SearchRequestError(
    '検索キーワードに誤りがあります。',
    { 'value.condition': (conditionError as object).toString() },
  );
}

async function postSearchRequestApiImpl(
  tokenProvider: WeakTokenProvider, condition: string, report: EventReporter,
): Promise<void> {

  // the case below will be never executed because we do not publish the function
  // when the tokenProvider is undefined
  if (!tokenProvider) return Promise.reject(new Error(UseSearchRequestApiAccessorError.NO_TOKENS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);
  try {
    const res = await fetchUrlRes(token, 'POST', createPostUrl(uId), JSON.stringify({
      condition,
      context: { timestamp: new Date() } as IContext,
    }));
    if (res.status !== 202) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseSearchRequestApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(res.statusText),
      });
    }
  } catch (e) {
    if ((e as AtTaneGraphError).statusCode === 400) {
      return Promise.reject(createErrorMessage(await (e as AtTaneGraphError).errorResponse.json()));
    }
    return Promise.reject(e);
  }

  return Promise.resolve();
}

async function getSearchRequestApiImpl(
  tokenProvider: WeakTokenProvider, report: EventReporter,
): Promise<ISearchRequestResult | void> {

  // the case below will be never executed because we do not publish the function
  // when the tokenProvider is undefined
  if (!tokenProvider) return Promise.reject(new Error(UseSearchRequestApiAccessorError.NO_TOKENS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);
  const result = await fetchUrlRes(token, 'GET', createGetUrl(uId));
  if (result.status === 204) {
    report({
      type: EventReportType.SYS_EVENT,
      name: UseSearchRequestApiAccessorError.NO_CONTENT,
      customProperties: {
        statusText: result.statusText,
      },
    });
    return Promise.resolve();
  }
  const json = await result.json();
  return Promise.resolve(json);
}

async function cancelSearchRequestApiImpl(
  tokenProvider: WeakTokenProvider, report: EventReporter,
): Promise<string | void> {

  // the case below will be never executed because we do not publish the function
  // when the tokenProvider is undefined
  if (!tokenProvider) return Promise.reject(new Error(UseSearchRequestApiAccessorError.NO_TOKENS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);
  const result = await fetchUrlRes(token, 'POST', cancelPostUrl(uId));
  if (result.status === 204) {
    report({
      type: EventReportType.SYS_EVENT,
      name: UseSearchRequestApiAccessorError.NO_CONTENT,
      customProperties: {
        statusText: result.statusText,
      },
    });
    return Promise.resolve();
  }
  const json = await result.json();
  return Promise.resolve(json);
}

async function getPartialSearchRequestApiImpl(
  tokenProvider: WeakTokenProvider, reqId: string, knownPids: string[], report: EventReporter,
): Promise<ISearchRequestResult | void> {

  // the case below will be never executed because we do not publish the function
  // when the tokenProvider is undefined
  if (!tokenProvider) return Promise.reject(new Error(UseSearchRequestApiAccessorError.NO_TOKENS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  const result = await fetchUrlRes(
    token,
    'POST',
    `${createGetUrl(uId)}/result`,
    JSON.stringify({ reqId, knownPids }),
  );
  if (result.status === 204) {
    report({
      type: EventReportType.SYS_EVENT,
      name: UseSearchRequestApiAccessorError.NO_CONTENT,
      customProperties: {
        statusText: result.statusText,
      },
    });
    return Promise.resolve();
  }
  const json = await result.json();
  return Promise.resolve(json);
}

/**
 * 検索要求に付随するコンテキスト情報(RemoteContext)を更新する
 *
 * @param {WeakTokenProvider} tokenProvider
 * @param {IContext} context
 * @param {EventReporter} report
 * @return {*}  {Promise<void>}
 */
async function updateRemoteContextApiImpl(
  tokenProvider: WeakTokenProvider,
  context: IContext,
  report: EventReporter,
): Promise<void> {

  // the case below will be never executed because we do not publish the function
  // when the tokenProvider is undefined
  if (!tokenProvider) return Promise.reject(new Error(UseSearchRequestApiAccessorError.NO_TOKENS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  // ContextをDictionary<string, string>に変換する
  const escapedJsonString = JSON.stringify(
    Object.fromEntries(
      Object.entries(context).map(
        ([k, v]) => [k, JSON.stringify(v)],
      ),
    ),
  );

  const res = await fetchUrlRes(token, 'PUT', createContextPutUrl(uId), escapedJsonString);
  if (res.status !== 200) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseSearchRequestApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: new Error(res.statusText),
    });
  }
  return Promise.resolve();
}

/**
 * 検索要求に付随するコンテキスト情報(RemoteContext)の特定のフィールドを更新する
 *
 * @param {WeakTokenProvider} tokenProvider
 * @param {('sort' | 'filter')} fieldName
 * @param {ISortOrder[]} sortContext
 * @param {EventReporter} report
 * @return {*}  {Promise<void>}
 */
async function updateRemoteContextFieldApiImpl(
  tokenProvider: WeakTokenProvider,
  fieldName: 'sort' | 'filter',
  sortContext: ISortOrder[],
  report: EventReporter,
): Promise<void> {

  // the case below will be never executed because we do not publish the function
  // when the tokenProvider is undefined
  if (!tokenProvider) return Promise.reject(new Error(UseSearchRequestApiAccessorError.NO_TOKENS));

  const [token, uId] = await getUniqueNameByToken(tokenProvider);
  const escapedJsonString = JSON.stringify(JSON.stringify(sortContext));
  const res = await fetchUrlRes(token, 'PUT', createContextFieldPutUrl(uId, fieldName), escapedJsonString);
  if (res.status !== 200) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseSearchRequestApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: new Error(res.statusText),
    });
  }
  return Promise.resolve();
}

/**
 * SearchRequest API accessor hook
 * @param tokenProvider
 * @param reporters
 */
const useSearchRequestApiAccessor = (
  tokenProvider: WeakTokenProvider, reporters: Reporters,
): UseSearchRequestApiReturnType => {

  const [report] = reporters;
  /**
   * 検索リクエストを送信
   */
  const postSearchRequestApi: PostSearchRequestApi = React.useCallback(
    async (condition: string) => postSearchRequestApiImpl(
      tokenProvider, condition, report,
    ), [tokenProvider, report],
  );

  const getSearchRequestApi: GetSearchRequestApi = React.useCallback(
    async () => getSearchRequestApiImpl(
      tokenProvider, report,
    ), [tokenProvider, report],
  );

  const cancelSearchRequestApi: CancelSearchRequestApi = React.useCallback(
    async () => cancelSearchRequestApiImpl(
      tokenProvider, report,
    ), [tokenProvider, report],
  );

  const getPartialSearchRequestApi: GetPartialSearchRequestApi = React.useCallback(
    async (reqId: string, KnownPids: string[]) => getPartialSearchRequestApiImpl(
      tokenProvider, reqId, KnownPids, report,
    ), [tokenProvider, report],
  );

  const updateRemoteContextApi: UpdateRemoteContextApi = React.useCallback(
    async (context: IContext) => updateRemoteContextApiImpl(
      tokenProvider, context, report,
    ), [tokenProvider, report],
  );

  const updateRemoteContextFieldApi: UpdateRemoteContextFieldApi = React.useCallback(
    async (fieldName: 'sort' | 'filter', sortContext: ISortOrder[]) => updateRemoteContextFieldApiImpl(
      tokenProvider, fieldName, sortContext, report,
    ), [tokenProvider, report],
  );

  return {
    postSearchRequestApi: tokenProvider ? postSearchRequestApi : undefined,
    getSearchRequestApi: tokenProvider ? getSearchRequestApi : undefined,
    cancelSearchRequestApi: tokenProvider ? cancelSearchRequestApi : undefined,
    getPartialSearchRequestApi: tokenProvider ? getPartialSearchRequestApi : undefined,
    updateRemoteContextApi: tokenProvider ? updateRemoteContextApi : undefined,
    updateRemoteContextFieldApi: tokenProvider ? updateRemoteContextFieldApi : undefined,
  };
};

export default useSearchRequestApiAccessor;
