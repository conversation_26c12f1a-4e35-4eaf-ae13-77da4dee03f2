import React from 'react';
import { IContext, ISortOrder } from '../../types/IContext';
import {
  Db<PERSON>rovider,
  ISearchRequestCache,
  ISearchResultsCache,
  CacheStatus,
  CacheStatusType,
  SEARCH_RESULTS_CACHE,
} from '../../types/IGeraniumAttaneDB';
import { ISearchRequestResult, SearchRequestState } from '../../types/ISearchRequestResult';
// import { ISearchResult } from '../../types/ISearchResult';
import { SplitViewDispatch } from '../../components/domains/split-view/split-view-container/reducers/splitViewReducer';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

/**
 * 検索要求キャッシュレコードkey名
 */
const RequestCacheKeyName = 'requestCache';

/**
 * Contextのキャッシュレコードkey名
 */
const CacheContextKeyName = 'context';

/**
 * 検索結果キャッシュレコードKey名のprefix
 */
const ResultsCacheKeyPrefix = 'res-';

export type RetrieveRequest = () => Promise<ISearchRequestCache | undefined>;
export type UpdateRequest = (
  item: ISearchRequestResult, cacheStatus?: CacheStatusType
) => Promise<string | undefined>;
export type RetrieveResults = () => Promise<[ISearchResultsCache[], IContext] | undefined>;
export type AddResults = (pid: string, ids: string[]) => Promise<string | undefined>;
export type UpdateResults = (
  pid: string, item: ISplitViewListSingle[]
) => Promise<string | undefined>;
export type ReplaceCacheContext = (toBe: IContext) => Promise<string | undefined>;
export type ClearCache = () => Promise<void>;

export type UseSearchResultRepositoryReturnType = {
  updateRequest: UpdateRequest | undefined,
  addResults: AddResults | undefined,
  updateResults: UpdateResults | undefined,
  replaceCacheContext: ReplaceCacheContext | undefined,
  clearCache: ClearCache | undefined,
  setSearchRequestCache: React.Dispatch<React.SetStateAction<ISearchRequestCache | undefined>>,
  setSearchResultsCache: React.Dispatch<React.SetStateAction<ISplitViewListSingle[]>>,
  isInitialized: boolean,
  isTransactionPending: boolean,
  searchRequestCache: ISearchRequestCache | undefined,
  searchResultsCache: ISplitViewListSingle[],
  cachedPidsRef: React.MutableRefObject<string[]>,
  sortContexts: ISortOrder[] | undefined,
  cacheContext: IContext | undefined,
}

/**
 * 新規登録/更新用ISearchRequestCacheレコードを作成する
 * @param item
 */
function createSearchRequestCacheEntry(
  item: ISearchRequestResult,
): ISearchRequestCache | undefined {
  // APIの戻りがエラーの場合はキャッシュしない
  if (!item.state || item.state === SearchRequestState.ERROR) return undefined;
  return {
    status: (item.state === SearchRequestState.IN_PROGRESS)
      ? CacheStatus.SEARCH_ON_INTERVAL
      : CacheStatus.REQUEST_IN_PROGRESS,
    result: item,
  };
}

/**
 * 新規登録ISearchResultsCacheレコードを作成する
 * @param ids
 */
function createSearchResultsCacheEntry(
  pid: string,
  ids: string[],
): ISearchResultsCache {
  return {
    pid,
    ids,
    cached: false,
  };
}

const useSearchResultRepositoryAccessor = (
  dispatchSearchResults: SplitViewDispatch,
  openDB?: DbProvider,
): UseSearchResultRepositoryReturnType => {
  const isUnmounted = React.useRef(false);

  // リポジトリの初期化後にtrue
  const [isInitialized, setIsInitialized] = React.useState(false);
  // トランザクション処理中はtrue
  const [isTransactionPending, setIsTransactionPending] = React.useState(false);
  // 検索要求のキャッシュ
  const [searchRequestCache, setSearchRequestCache] = React.useState<ISearchRequestCache>();
  // 検索結果のキャッシュ
  const [searchResultsCache, setSearchResultsCache] = React.useState<ISplitViewListSingle[]>([]);
  // IndexedDBから取得したContextのCache
  const [cacheContext, setCacheContext] = React.useState<IContext>();
  // キャッシュ済検索処理IDのref
  const cachedPidsRef = React.useRef<string[]>([]);

  const sortContexts = React.useMemo(() => cacheContext?.sort, [cacheContext]);

  /**
   * 検索要求キャッシュであるかどうかを判定する
   *
   * @param {(ISearchRequestCache | IContext | ISearchResultsCache)} item
   * @return {*}  {item is ISearchRequestCache}
   */
  const isSearchRequestCache = (
    item: ISearchRequestCache | IContext | ISearchResultsCache,
  ): item is ISearchRequestCache => !!(item as ISearchRequestCache)?.status;

  /**
   * Storeから検索要求キャッシュデータを取得する
   */
  const retrieveRequest: RetrieveRequest = React.useCallback(async () => {
    if (!openDB) return undefined;
    const db = await openDB();
    try {
      const cache = await db.get<typeof SEARCH_RESULTS_CACHE>(
        SEARCH_RESULTS_CACHE,
        RequestCacheKeyName,
      );
      if (!cache) return undefined;
      if (isSearchRequestCache(cache)) return cache;
      return undefined;
    } finally {

      db.close();
    }
  }, [openDB]);

  /**
   * IndexedDBの内容で検索要求キャッシュを生成する
   */
  const initRequestCache = React.useCallback(async () => {
    const requestCache = await retrieveRequest().catch(() => undefined);
    if (isUnmounted.current) return;
    setSearchRequestCache(requestCache);
  }, [retrieveRequest]);

  /**
   * 検索要求レコードが存在する場合は更新、なければ追加する
   */
  const updateRequest: UpdateRequest = React.useCallback(async (
    item: ISearchRequestResult,
    cacheStatus?: CacheStatusType,
  ) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    const db = await openDB();

    try {
      const cache = await db.get<typeof SEARCH_RESULTS_CACHE>(
        SEARCH_RESULTS_CACHE,
        RequestCacheKeyName,
      );

      const requestCache = (cache && isSearchRequestCache(cache)) ? cache : undefined;

      // 全てキャッシュ済、あるいはエラーが発生している場合はSkipする
      if (
        requestCache
        && (
          requestCache.status === CacheStatus.ERROR
          || requestCache.status === CacheStatus.COMPLETED
        )
      ) {
        return undefined;
      }

      const reposEntry = createSearchRequestCacheEntry(
        item,
      );
      if (!reposEntry) return undefined;

      return await db.put<typeof SEARCH_RESULTS_CACHE>(
        SEARCH_RESULTS_CACHE,
        cacheStatus ? {
          ...reposEntry,
          status: cacheStatus,
        } : reposEntry,
        RequestCacheKeyName,
      );
    } finally {
      db.close();
    }
  }, [
    openDB,
  ]);

  /**
   * Storeから検索結果キャッシュデータを取得する
   */
  const retrieveResults: RetrieveResults = React.useCallback(async () => {
    if (!openDB) return undefined;
    const db = await openDB();
    const keyRange = IDBKeyRange.bound(ResultsCacheKeyPrefix, `${ResultsCacheKeyPrefix}|`);
    try {
      const tx = db.transaction(SEARCH_RESULTS_CACHE, 'readwrite');
      const cacheStore = tx.objectStore(SEARCH_RESULTS_CACHE);

      const caches = await Promise.all([
        cacheStore.getAll(keyRange),
        cacheStore.get(CacheContextKeyName),
        tx.done,
      ]).catch(() => undefined);
      if (!caches) return undefined;

      const [results, result] = caches;
      const context = (result as IContext) ?? { filter: [], sort: [] };

      return [
        results as ISearchResultsCache[],
        context,
      ];
    } finally {
      db.close();
    }
  }, [openDB]);

  /**
   * IndexedDBの内容で結果キャッシュを生成する
   */
  const initResultsCache = React.useCallback(async () => {
    const results = await retrieveResults().catch(() => undefined);
    if (!results) return;
    if (isUnmounted.current) return;

    const [retrievedCacheResults, retrievedCacheContext] = results;
    dispatchSearchResults({
      type: 'REPLACE_CONTEXT',
      payload: {
        context: retrievedCacheContext,
      },
    });

    // Contextの初期化
    setCacheContext(retrievedCacheContext);
    // 検索結果キャッシュの初期化
    const cached = retrievedCacheResults.filter((value) => value.cached);
    setSearchResultsCache(cached.flatMap((v) => v.results ?? []));
    cachedPidsRef.current = cached.map((v) => v.pid);
  }, [
    retrieveResults, dispatchSearchResults,
  ]);

  /**
   * 初期化処理
   */
  React.useEffect(() => {
    if (!openDB) return;
    (async () => {
      await initRequestCache();
      await initResultsCache();
      setIsInitialized(true);
    })();
  }, [
    openDB,
    initRequestCache,
    initResultsCache,
  ]);

  /**
   * context(並び替え/フィルター条件)を参照してなければ追加、既に存在すれば差分更新する
   */
  const replaceCacheContext: ReplaceCacheContext = React.useCallback(
    async (toBe: IContext) => {
      if (!openDB) return Promise.reject(new Error('no opendb'));

      const db = await openDB();
      // const reposEntry = createCacheContextReposEntry(toBe, cacheContext);

      setIsTransactionPending(true);
      try {

        return db.put<typeof SEARCH_RESULTS_CACHE>(
          SEARCH_RESULTS_CACHE,
          toBe,
          CacheContextKeyName,
        );
      } finally {
        db.close();
        setCacheContext(toBe);
        setIsTransactionPending(false);
      }
    },
    [
      openDB,
    ],
  );

  /**
   * 渡されたpidをkeyとする検索結果レコードを追加する
   */
  const addResults: AddResults = React.useCallback(async (pid: string, ids: string[]) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    const db = await openDB();

    try {
      // 既に追加されている場合はSkipする
      const resultsCache = await db.get<typeof SEARCH_RESULTS_CACHE>(
        SEARCH_RESULTS_CACHE,
        `${ResultsCacheKeyPrefix}${pid}`,
      );
      if (resultsCache) return undefined;

      const reposEntry = createSearchResultsCacheEntry(pid, ids);
      return db.add<typeof SEARCH_RESULTS_CACHE>(
        SEARCH_RESULTS_CACHE,
        reposEntry,
        `${ResultsCacheKeyPrefix}${pid}`,
      );
    } finally {
      db.close();
    }
  }, [
    openDB,
  ]);

  /**
   * 渡されたpidをkeyとする検索結果レコードを更新する
   */
  const updateResults: UpdateResults = React.useCallback(async (
    pid: string,
    item: ISplitViewListSingle[],
  ) => {
    if (!openDB) return Promise.reject(new Error('no opendb'));

    const db = await openDB();

    try {
      const cache = await db.get<typeof SEARCH_RESULTS_CACHE>(
        SEARCH_RESULTS_CACHE,
        `${ResultsCacheKeyPrefix}${pid}`,
      );

      // 更新対象のレコードが存在しなかった場合Skipする
      if (
        !cache
        || isSearchRequestCache(cache)
      ) return undefined;

      return await db.put<typeof SEARCH_RESULTS_CACHE>(
        SEARCH_RESULTS_CACHE,
        {
          ...cache,
          cached: true,
          results: item,
        } as ISearchResultsCache,
        `${ResultsCacheKeyPrefix}${pid}`,
      );

    } finally {
      db.close();
    }
  }, [
    openDB,
  ]);

  /**
   * ストアに保存されているレコードを全て削除する
   */
  const clearCache: ClearCache = React.useCallback(async () => {
    if (!openDB) return Promise.reject(new Error('no opendb'));
    const db = await openDB();

    try {
      return db.clear(SEARCH_RESULTS_CACHE);
    } finally {
      db.close();
      setCacheContext(undefined);
    }
  }, [
    openDB,
  ]);

  return {
    updateRequest: isInitialized ? updateRequest : undefined,
    addResults: isInitialized ? addResults : undefined,
    updateResults: isInitialized ? updateResults : undefined,
    replaceCacheContext: isInitialized ? replaceCacheContext : undefined,
    clearCache: isInitialized ? clearCache : undefined,
    setSearchRequestCache,
    setSearchResultsCache,
    isInitialized,
    isTransactionPending,
    searchRequestCache,
    searchResultsCache,
    cachedPidsRef,
    sortContexts,
    cacheContext,
  };
};

export default useSearchResultRepositoryAccessor;
