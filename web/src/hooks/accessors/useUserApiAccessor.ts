import React from 'react';
import { WeakTokenProvider } from '../../types/TokenProvider';
import {
  FetchGroupIds,
  FetchUser, FetchUsers, UseGraphApiError, initGraphClient,
} from './useGraphApiAccessor';
import { IGraphUser } from '../../types/IGraphUser';

export interface IUserApiAccessorReturn {
  fetchUser: FetchUser;
  fetchUsers: FetchUsers;
  fetchGroupId?:FetchGroupIds,
  me?: IGraphUser;
}

/**
 * @return reject時にはErrorまたはGraphErrorを返却する
 */
export function fetchUserImpl(
  tokenProvider: WeakTokenProvider,
  oid?: string,
): Promise<IGraphUser> {

  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  const path = oid ? `users/${oid}` : 'me';
  return client
    .api(path)
    .get()
    .then((res) => res);
}

export function fetchUsersImpl(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  tokenProvider: WeakTokenProvider,
  oids: string[],
)
  : Promise<IGraphUser[]> {
  // sendRequests
  // TODO: 実際の処理にはsendRequestsを使う
  const responses = oids.map((oid) => ({ id: oid, displayName: `name: ${oid}` }));
  return Promise.resolve(responses);
}
/**
 * 特定のグループの親グループIDを取得する
 * @param client - Graph APIクライアント
 * @param groupId - 親グループを調べるグループID
 * @returns 親グループIDの配列
 * @throws {Error} - Graph APIのリクエストでエラーが発生した場合にエラーを投げる
 */
async function fetchParentGroupIds(client: any, groupId: string): Promise<string[]> {
  const parentGroupIds: string[] = [];

  const parentResponse = await client
    .api(`/groups/${groupId}/memberOf?$select=id`)
    .get();
  // 直接の親のみ取得する
  if (parentResponse.value && parentResponse.value.length > 0) {
    // 親グループが存在する場合、IDを追加
    parentGroupIds.push(...(parentResponse.value.map((group: any) => group.id)));

    // ページングがある場合は処理
    let nextParentResponse = parentResponse;
    while (nextParentResponse['@odata.nextLink']) {
      // eslint-disable-next-line no-await-in-loop
      nextParentResponse = await client.api(nextParentResponse['@odata.nextLink']).get();
      parentGroupIds.push(...(nextParentResponse.value.map((group: any) => group.id)));
    }
  }
  return parentGroupIds;
}

/**
 * ユーザーが所属している全てのMicrosoft 365グループID一覧をGraph API経由で取得
 *
 * @param tokenProvider - アクセストークンを提供するためのプロバイダー（WeakTokenProvider）。
 * @returns Promise<GraphGroupIdsResponse> - 取得したグループIDの配列を含むPromiseオブジェクト。
 *
 * @throws {Error} - トークンプロバイダーが未指定の場合、またはGraph APIのリクエストでエラーが発生した場合にエラーを投げる
 * Graph API `/me/memberOf` エンドポイントを利用し、ページングがある場合は全ページを自動的に取得
 */
export async function fetchGroupIdImpl(
  tokenProvider: WeakTokenProvider,
): Promise<string[]> {
  if (!tokenProvider) {
    return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  }
  // Graph クライアント準備
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));
  try {
    const groupIds: string[] = [];
    // ユーザーが所属するグループを取得
    let response = await client
      .api('/me/memberOf?$select=id')
      .get();
    // 1ページ目の groupId を追加
    const userGroups = response.value.map((group: any) => group.id);
    groupIds.push(...userGroups);
    // 次ページがあれば繰り返す
    while (response['@odata.nextLink']) {
      // eslint-disable-next-line no-await-in-loop
      response = await client.api(response['@odata.nextLink']).get();
      groupIds.push(...(response.value.map((group: any) => group.id)));
    }
    // 各グループの親グループを取得（Promise.allSettledを使用して並列処理）
    const parentGroupIdsResults = await Promise.allSettled(
      userGroups.map((groupId: string) => fetchParentGroupIds(client, groupId)),
    );

    // 成功した結果のみを取得し、親グループIDの配列を平坦化
    const parentGroupIds = parentGroupIdsResults
      .filter((result): result is PromiseFulfilledResult<string[]> => result.status === 'fulfilled')
      .map((result) => result.value)
      .flat();

    // 重複を除去して全てのグループIDを返す
    return [...new Set([...groupIds, ...parentGroupIds])];
  } catch (e) {
    return Promise.reject(e);
  }
}

const useUserApiAccessor = (tokenProvider: WeakTokenProvider): IUserApiAccessorReturn => {

  /**
   * IDで指定したユーザーの情報を取得
   * IDが指定されていない場合はアプリユーザーの情報を取得
   */
  const fetchUser: FetchUser = React.useCallback(
    (oid) => fetchUserImpl(tokenProvider, oid), [tokenProvider],
  );

  const fetchUsers: FetchUsers = React.useCallback(
    (oids) => fetchUsersImpl(tokenProvider, oids), [tokenProvider],
  );

  const fetchGroupId: FetchGroupIds = React.useCallback(
    () => fetchGroupIdImpl(tokenProvider),
    [tokenProvider],
  );
  const [me, setMe] = React.useState<IGraphUser>();
  React.useEffect(() => {
    (async () => {
      const currentUser = await fetchUser();
      setMe(currentUser);
    })();
  }, [fetchUser, tokenProvider]);

  return {
    fetchUser,
    fetchUsers,
    fetchGroupId,
    me,
  };
};

export default useUserApiAccessor;
