import * as React from 'react';
import { GraphError } from '@microsoft/microsoft-graph-client';
import { fetchUrlRes } from '../../utilities/commonFunction';
import environment from '../../utilities/environment';
import { IBookmarkItemApiResponse } from '../../types/IBookmarkItem';
import { getUniqueNameByToken } from '../../utilities/token/jwt';
import { isChatProperties, isMailProperties, isSpoProperties } from '../../utilities/transform';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { IChatProperties, IMailProperties, SearchResultProperties } from '../../types/ISearchResult';

type TokenProvider = (() => Promise<string>) | undefined;

export type PutBookmarkApi = (item: ISplitViewListSingle) => Promise<void>;
export type DeleteBookmarkApi = (id: string) => Promise<void>;
export type GetBookmarksApi = () => Promise<ISplitViewListSingle[]>;

type UseBookmarksApiReturnType = {
  putBookmarkApi: PutBookmarkApi | undefined,
  deleteBookmarkApi: DeleteBookmarkApi | undefined,
  getBookmarksApi: GetBookmarksApi | undefined,
};

const PREFIX = environment.REACT_APP_API_URL ?? '/api';

/**
 * エンドポイントURLを生成する
 * @param userId
 * @param articleId
 */
export function createBookmarksUrl(userId: string, articleId?: string): string {
  if (!userId) return '';
  const idPath = articleId ? `/${articleId}` : '';
  return `${PREFIX}/users/${userId}/bookmarks${idPath}`;
}

export function pickRequiredProperties(item: ISplitViewListSingle): SearchResultProperties {
  if (item.kind === 'Mail') {
    return {
      updatedDate: item.properties.updatedDate,
    } as IMailProperties;
  } if (item.kind === 'Chat') {
    const properties = item.properties as IChatProperties;
    return {
      teamId: properties.teamId,
      chatId: properties.chatId,
      messageType: properties.messageType,
      updatedDate: item.properties.updatedDate,
    };
  }
  return item.properties;

}

/**
 * putBookmarkの実装 200系以外のときはGraphError型でreject
 * @param tokenProvider
 * @param item
 */
export async function putBookmarkImpl(
  tokenProvider: TokenProvider, item: ISplitViewListSingle,
): Promise<void> {

  // tokenProviderが存在しない時は公開されないので考慮不要
  if (!tokenProvider) return Promise.resolve();

  const [token, uId] = await getUniqueNameByToken(tokenProvider);
  const url = createBookmarksUrl(uId, item.id);
  const bookmarkItem = {
    id: item.id,
    kind: item.kind,
    note: item.kind === 'SPO' ? item.note : '',
    title: item.kind === 'SPO' ? item.title : '',
    displayDate: item.displayDate,
    properties: pickRequiredProperties(item),
    reposCreatedDate: item.reposCreatedDate,
    reposUpdatedDate: item.reposUpdatedDate,
  };

  await fetchUrlRes(token, 'PUT', url, JSON.stringify({
    ...bookmarkItem,
    properties: JSON.stringify(bookmarkItem.properties),
  }));
  return Promise.resolve();
}

/**
 * deleteBookmarkの実装 200系以外のときはGraphError型でreject
 * @param tokenProvider
 * @param spoArticleId
 */
export async function deleteBookmarkImpl(
  tokenProvider: TokenProvider, spoArticleId: string,
): Promise<void> {

  // tokenProviderが存在しない時は公開されないので考慮不要
  if (!tokenProvider) return Promise.resolve();

  const [token, uId] = await getUniqueNameByToken(tokenProvider);
  const url = createBookmarksUrl(uId, spoArticleId);
  await fetchUrlRes(token, 'DELETE', url);
  return Promise.resolve();
}

/**
 * fetchBookmarksの実装 200系以外のとき、204のときはGraphError型でreject
 * @param tokenProvider
 */
export async function getBookmarksImpl(
  tokenProvider: TokenProvider,
): Promise<ISplitViewListSingle[]> {

  // tokenProviderが存在しない時は公開されないので考慮不要
  if (!tokenProvider) return Promise.resolve([]);

  const [token, uId] = await getUniqueNameByToken(tokenProvider);
  const url = createBookmarksUrl(uId);
  const res = await fetchUrlRes(token, 'GET', url);

  // 204ステータスのときはエラー扱いにしてreject
  if (res.status === 204) {
    return Promise.reject(new GraphError(204));
  }
  const json = await res.json() as IBookmarkItemApiResponse[] ?? [];
  const bookmarks = json.map<ISplitViewListSingle>((val) => {
    const properties = JSON.parse(val.properties);
    return {
      id: val.id,
      kind: val.kind,
      title: val.title,
      note: val.note,
      displayDate: val.displayDate,
      properties: {
        ...(isSpoProperties(properties) ? {
          siteUrl: properties.siteUrl,
          listUrl: properties.listUrl,
          listId: properties.listId,
          listName: properties.listName,
          editLink: properties?.editLink,
          categoryKeyName: properties.categoryKeyName,
          createdDate: properties?.createdDate,
        } : {}),
        ...(isMailProperties(properties) ? {
          from: properties.from,
          sender: properties.sender,
          toRecipients: properties.toRecipients,
          ccRecipients: properties.ccRecipients,
          bccRecipients: properties.bccRecipients,
          sentDateTime: properties.sentDateTime,
          receivedDateTime: properties.receivedDateTime,
          webLink: properties.webLink,
        } : {}),
        ...(isChatProperties(properties) ? {
          ...properties,
        } : {}),
        hasAttachments: properties.hasAttachments,
        editLink: properties.editLink,
        createdDate: properties.createdDate,
        updatedDate: properties.updatedDate,
      },
      reposUpdatedDate: val.reposUpdatedDate,
      reposCreatedDate: val.reposCreatedDate,
    };
  });
  return Promise.resolve(bookmarks);
}

/**
 * useBookmarksApi機能
 * バックエンドAPIと通信してお気に入りの追加を行う(取得や削除は今後追加予定)
 */
const useBookmarksApiAccessor = (
  tokenProvider: TokenProvider,
): UseBookmarksApiReturnType => {

  const putBookmarkApi = React.useCallback(
    (item: ISplitViewListSingle) => putBookmarkImpl(tokenProvider, item),
    [tokenProvider],
  );

  const deleteBookmarkApi = React.useCallback(
    (spoArticleId: string) => deleteBookmarkImpl(tokenProvider, spoArticleId),
    [tokenProvider],
  );

  const getBookmarksApi = React.useCallback(
    () => getBookmarksImpl(tokenProvider),
    [tokenProvider],
  );

  return {
    putBookmarkApi: tokenProvider ? putBookmarkApi : undefined,
    deleteBookmarkApi: tokenProvider ? deleteBookmarkApi : undefined,
    getBookmarksApi: tokenProvider ? getBookmarksApi : undefined,
  };
};

export default useBookmarksApiAccessor;
