import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { createGraphClient } from '@avanade-teams/auth';
import { waitFor } from '@testing-library/react';
import { sendRequests } from './useGraphApiAccessor';
import { WeakTokenProvider } from '../../types/TokenProvider';

dayjs.extend(isBetween);

const MockDefaultTime = 3000;

jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 2,
    REACT_APP_RETRY_DEAFULT_TIME: MockDefaultTime,
    REACT_APP_BATCH_REQUEST_CHUNK_SIZE: 20,
  },
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));
const mockCreateGraphClient = createGraphClient as jest.Mock;
const mockProvider = jest.mock;
const cancellationRefMock = { current: false };

const postMock = jest.fn();
const apiMock = jest.fn();
const mockClient = {
  api: apiMock,
  select: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  post: postMock,
  get: jest.fn(),
};

describe('useGraphAPiAccessor', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });
  beforeEach(() => {
    mockCreateGraphClient.mockReturnValue(mockClient);
    mockCreateGraphClient.mockClear();
    Object.values(mockClient).forEach((v) => v.mockClear());
    apiMock.mockClear();
    postMock.mockClear();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('should retry in defined times', async () => {
    apiMock.mockImplementation(() => ({ post: postMock }));
    postMock.mockResolvedValueOnce({
      responses: [
        {
          body: '',
          id: '1',
          status: 429,
          headers: { 'Retry-After': '1' },
        },
        {
          body: '',
          id: '2',
          status: 409,
          headers: {},
        },
        {
          body: '',
          id: '3',
          status: 412,
          headers: {},
        },
      ],
    });
    postMock.mockResolvedValueOnce({
      responses: [
        {
          body: '',
          id: '1',
          status: 200,
          headers: {},
        },
        {
          body: '',
          id: '2',
          status: 400,
          headers: {},
        },
        {
          body: '',
          id: '3',
          status: 400,
          headers: {},
        },
      ],
    });

    const task = sendRequests(mockProvider as unknown as WeakTokenProvider, [
      { id: '1', url: '/me/user', method: 'GET' },
      { id: '2', url: '/me/user', method: 'GET' },
      { id: '3', url: '/me/user', method: 'GET' },
    ], cancellationRefMock);
    expect(postMock).toHaveBeenCalledTimes(1);
    await waitFor(async () => {
      expect(postMock).toHaveBeenCalledTimes(2);
      await task.then((result) => {
        expect(result).toStrictEqual({
          responses: [''],
          recoverable: [],
          errors: [
            {
              body: '',
              id: '2',
              status: 400,
              headers: {},
            },
            {
              body: '',
              id: '3',
              status: 400,
              headers: {},
            },
          ],
          totalTooManyRequests: 1,
          tooManyRequests: [{
            count: 1,
            averageRetryAfter: 1,
            maxRetryAfter: 1,
          }],
        });
      });
    }, { timeout: MockDefaultTime + 500 });
  });

  it('should calculate average waiting time', async () => {
    apiMock.mockImplementation(() => ({ post: postMock }));
    postMock.mockResolvedValueOnce({
      responses: [
        {
          body: '',
          id: '1',
          status: 429,
          headers: { 'Retry-After': '1' },
        },
        {
          body: '',
          id: '2',
          status: 429,
          headers: { 'Retry-After': '3' },
        },
        {
          body: '',
          id: '3',
          status: 412,
          headers: {},
        },
      ],
    });
    postMock.mockResolvedValueOnce({
      responses: [
        {
          body: '',
          id: '1',
          status: 200,
          headers: {},
        },
        {
          body: '',
          id: '2',
          status: 400,
          headers: {},
        },
        {
          body: '',
          id: '3',
          status: 400,
          headers: {},
        },
      ],
    });

    const task = sendRequests(mockProvider as unknown as WeakTokenProvider, [
      { id: '1', url: '/me/user', method: 'GET' },
      { id: '2', url: '/me/user', method: 'GET' },
      { id: '3', url: '/me/user', method: 'GET' },
    ], cancellationRefMock);
    expect(postMock).toHaveBeenCalledTimes(1);
    await waitFor(async () => {
      expect(postMock).toHaveBeenCalledTimes(2);
      await task.then((result) => {
        expect(result).toStrictEqual({
          responses: [''],
          recoverable: [],
          errors: [
            {
              body: '',
              id: '2',
              status: 400,
              headers: {},
            },
            {
              body: '',
              id: '3',
              status: 400,
              headers: {},
            },
          ],
          totalTooManyRequests: 2,
          tooManyRequests: [{
            count: 2,
            averageRetryAfter: 2,
            maxRetryAfter: 3,
          }],
        });
      });
    }, { timeout: MockDefaultTime + 500 });
  });

  it('should not calculate  waiting time', async () => {
    apiMock.mockImplementation(() => ({ post: postMock }));
    postMock.mockResolvedValueOnce({
      responses: [
        {
          body: '',
          id: '1',
          status: 409,
          headers: {},
        },
        {
          body: '',
          id: '2',
          status: 412,
          headers: {},
        },
        {
          body: '',
          id: '3',
          status: 412,
          headers: {},
        },
      ],
    });
    postMock.mockResolvedValueOnce({
      responses: [
        {
          body: '',
          id: '1',
          status: 200,
          headers: {},
        },
        {
          body: '',
          id: '2',
          status: 400,
          headers: {},
        },
        {
          body: '',
          id: '3',
          status: 400,
          headers: {},
        },
      ],
    });

    const task = sendRequests(mockProvider as unknown as WeakTokenProvider, [
      { id: '1', url: '/me/user', method: 'GET' },
      { id: '2', url: '/me/user', method: 'GET' },
      { id: '3', url: '/me/user', method: 'GET' },
    ], cancellationRefMock);
    expect(postMock).toHaveBeenCalledTimes(1);
    await waitFor(async () => {
      expect(postMock).toHaveBeenCalledTimes(2);
      await task.then((result) => {
        expect(result).toStrictEqual({
          responses: [''],
          recoverable: [],
          errors: [
            {
              body: '',
              id: '2',
              status: 400,
              headers: {},
            },
            {
              body: '',
              id: '3',
              status: 400,
              headers: {},
            },
          ],
          totalTooManyRequests: 0,
          tooManyRequests: [{
            count: 0,
            averageRetryAfter: 0,
            maxRetryAfter: 0,
          }],
        });
      });
    }, { timeout: MockDefaultTime + 500 });
  });

  it('should retry only defined times', async () => {
    apiMock.mockImplementation(() => ({ post: postMock }));
    postMock.mockResolvedValue({
      responses: [{
        body: '',
        id: '1',
        status: 429,
        headers: { 'Retry-After': '1' },
      }],
    });

    const task = sendRequests(mockProvider as unknown as WeakTokenProvider, [
      { id: '1', url: '/me/user', method: 'GET' },
    ], cancellationRefMock);
    expect(postMock).toHaveBeenCalledTimes(1);
    await waitFor(async () => {
      expect(postMock).toHaveBeenCalledTimes(2);
      await task.then((result) => {
        expect(result).toStrictEqual({
          responses: [],
          recoverable: [{
            body: '',
            id: '1',
            status: 429,
            headers: { 'Retry-After': '1' },
          }],
          errors: [],
          totalTooManyRequests: 2,
          tooManyRequests: [
            {
              maxRetryAfter: 1,
              averageRetryAfter: 1,
              count: 1,
            },
            {
              maxRetryAfter: 1,
              averageRetryAfter: 1,
              count: 1,
            },
          ],
        });
      });
    }, { timeout: MockDefaultTime + 500 });
  });

  it('should callback success responses', async () => {
    apiMock.mockImplementation(() => ({ post: postMock }));
    const callbackFn = jest.fn();
    postMock.mockResolvedValueOnce({
      responses: [
        {
          body: 'success: 1',
          id: '1',
          status: 200,
          headers: {},
        },
        {
          body: '',
          id: '2',
          status: 429,
          headers: { 'Retry-After': 1 },
        },
        {
          body: '',
          id: '3',
          status: 400,
          headers: {},
        },
      ],
    });
    postMock.mockResolvedValueOnce({
      responses: [{
        body: 'success: 2',
        id: '2',
        status: 200,
        headers: {},
      }],
    });

    const task = sendRequests(mockProvider as unknown as WeakTokenProvider, [
      { id: '1', url: '/me/user', method: 'GET' },
      { id: '2', url: '/me/user', method: 'GET' },
      { id: '3', url: '/me/user', method: 'GET' },
    ], cancellationRefMock, callbackFn);
    await waitFor(async () => {
      await task.then(() => {
        expect(callbackFn).toHaveBeenCalledTimes(2);
        expect(callbackFn).toHaveBeenCalledWith(['success: 1']);
        expect(callbackFn).toHaveBeenCalledWith(['success: 2']);
      });
    }, { timeout: MockDefaultTime + 500 });
  });

  it('should retry recoverable responses only', async () => {
    apiMock.mockImplementation(() => ({ post: postMock }));
    postMock.mockResolvedValueOnce(Promise.resolve({
      responses: [
        {
          body: 'success: 1',
          id: '1',
          status: 200,
          headers: {},
        },
        {
          body: '',
          id: '3',
          status: 429,
          headers: { 'Retry-After': 1 },
        },
        {
          body: '',
          id: '2',
          status: 409,
          headers: {},
        },
        {
          body: '',
          id: '4',
          status: 404,
          headers: {},
        },
      ],
    }));
    postMock.mockResolvedValueOnce(Promise.resolve({
      responses: [
        {
          body: 'success: 2',
          id: '2',
          status: 200,
          headers: {},
        },
        {
          body: 'success: 3',
          id: '3',
          status: 200,
          headers: {},
        },
      ],
    }));

    const task = sendRequests(mockProvider as unknown as WeakTokenProvider, [
      { id: '1', url: '/me/user', method: 'GET' },
      { id: '2', url: '/me/user', method: 'GET' },
      { id: '3', url: '/me/user', method: 'GET' },
      { id: '4', url: '/me/user', method: 'GET' },
    ], cancellationRefMock);
    await waitFor(async () => {
      await task.then(() => {
        expect(postMock).toHaveBeenCalledTimes(2);
        expect(postMock).toHaveBeenCalledWith({
          requests: [
            { id: '1', url: '/me/user', method: 'GET' },
            { id: '2', url: '/me/user', method: 'GET' },
            { id: '3', url: '/me/user', method: 'GET' },
            { id: '4', url: '/me/user', method: 'GET' },
          ],
        });
        expect(postMock).toHaveBeenCalledWith({
          requests: [
            { id: '3', url: '/me/user', method: 'GET' },
            { id: '2', url: '/me/user', method: 'GET' },
          ],
        });
      });
    }, { timeout: MockDefaultTime + 500 });
  });

  describe('cancellation', () => {
    const cancelled = { current: true };

    it('should stop api calling when cancellation flag is true', async () => {
      apiMock.mockImplementation(() => ({ post: postMock }));
      postMock.mockResolvedValueOnce({
        responses: [
          {
            body: '',
            id: '1',
            status: 429,
            headers: { 'Retry-After': '1' },
          },
          {
            body: '',
            id: '2',
            status: 409,
            headers: {},
          },
          {
            body: '',
            id: '3',
            status: 412,
            headers: {},
          },
        ],
      });
      postMock.mockResolvedValueOnce({
        responses: [
          {
            body: '',
            id: '1',
            status: 200,
            headers: {},
          },
          {
            body: '',
            id: '2',
            status: 400,
            headers: {},
          },
          {
            body: '',
            id: '3',
            status: 400,
            headers: {},
          },
        ],
      });

      await sendRequests(mockProvider as unknown as WeakTokenProvider, [
        { id: '1', url: '/me/user', method: 'GET' },
        { id: '2', url: '/me/user', method: 'GET' },
        { id: '3', url: '/me/user', method: 'GET' },
      ], cancelled);
      expect(postMock).not.toHaveBeenCalled();
    });
  });
});
