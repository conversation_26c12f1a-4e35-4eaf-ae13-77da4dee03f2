import { renderHook, act } from '@testing-library/react-hooks';
import { openDB } from 'idb';
import {
  clearIdbMocks, dbMock,
} from '../../mocks/idb';
import { IContext } from '../../types/IContext';
import { INDEX_DB_VERSION, ISearchRequestCache, ISearchResultsCache } from '../../types/IGeraniumAttaneDB';
import useIndexedDbAccessor from './useIndexedDbAccessor';
import useSearchResultRepositoryAccessor from './useSearchResultRepositoryAccessor';

jest.mock('idb', () => ({
  openDB: jest.fn(),
}));
jest.mock('../../utilities/environment');

// openDBのmock
const openDBMock = (openDB as jest.Mock).mockResolvedValue(dbMock);
const current = new Date(2023, 3, 1);

describe('useSearchResultRepositoryAccessor', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
    jest.setSystemTime(current);
  });

  afterAll(() => {
    jest.useRealTimers();
  });
  const mockDispatcher = jest.fn();
  async function getHook(
    initialEntries: ISearchRequestCache | IContext | ISearchResultsCache[],
  ) {
    dbMock.getAll.mockResolvedValue(initialEntries);
    const getHookResult = renderHook(
      () => useSearchResultRepositoryAccessor(
        mockDispatcher,
        useIndexedDbAccessor()[0],
      ),
    );
    const { result, waitForNextUpdate } = getHookResult;

    // 初期化前はundefined
    expect(result.current.updateRequest).toBeUndefined();
    expect(result.current.addResults).toBeUndefined();
    expect(result.current.updateResults).toBeUndefined();
    expect(result.current.replaceCacheContext).toBeUndefined();
    expect(result.current.clearCache).toBeUndefined();

    await waitForNextUpdate();

    // openDBが実行されている
    expect(openDBMock).toHaveBeenCalledTimes(3);
    expect(openDBMock).toHaveBeenCalledWith(
      'geranium-attane',
      INDEX_DB_VERSION,
      expect.objectContaining({
        upgrade: expect.anything(),
      }),
    );

    // closeが呼ばれている
    expect(dbMock.close).toHaveBeenCalledTimes(2);

    // 初期化後はundefinedではない
    expect(result.current.updateRequest).not.toBeUndefined();
    expect(result.current.addResults).not.toBeUndefined();
    expect(result.current.updateResults).not.toBeUndefined();
    expect(result.current.replaceCacheContext).not.toBeUndefined();
    expect(result.current.clearCache).not.toBeUndefined();

    openDBMock.mockClear();
    clearIdbMocks();

    return getHookResult;
  }

  beforeEach(() => {
    openDBMock.mockClear();
    clearIdbMocks();
  });

  describe('replaceContexts', () => {
    describe('when no context record exists', () => {
      it('should resolve the value db.put returned', async () => {
        dbMock.put.mockResolvedValueOnce('put-result');

        // 初期化を待機
        const { result } = await getHook({});
        const { replaceCacheContext: ReplaceCacheContext } = result.current;
        if (!ReplaceCacheContext) return;

        await act(async () => {
          // エラーがthrowされていない
          await expect(ReplaceCacheContext({
            sort: [{ key: 'displayDate', order: 'asc', priority: 1 }],
            filter: [],
            timestamp: current,
          }))
            .resolves.not.toThrow();

          // putが呼ばれている;
          expect(dbMock.put).toHaveBeenCalledWith(
            'search_results_cache',
            // 書き込まれるデータを検証
            {
              sort: [
                {
                  key: 'displayDate',
                  order: 'asc',
                  priority: 1,
                },
              ],
              filter: [],
              timestamp: current,
            },
            'context',
          );
        });
      });
    });

    describe('when db.put rejects', () => {
      it('should reject and call db.close', async () => {
        dbMock.put.mockRejectedValueOnce('reject');

        // 初期化を待機
        const { result } = await getHook({});
        const { replaceCacheContext: ReplaceCacheContext } = result.current;
        if (!ReplaceCacheContext) return;

        await act(async () => {
          await expect(ReplaceCacheContext({
            sort: [{ key: 'displayDate', order: 'asc', priority: 1 }],
            filter: [],
            timestamp: current,
          }))
            .rejects.toMatch('reject');

          // closeが呼ばれている
          expect(dbMock.close).toHaveBeenCalledTimes(1);
        });
      });
    });
  });
});
