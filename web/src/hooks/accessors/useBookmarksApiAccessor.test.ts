import '@testing-library/jest-dom';
import { renderHook } from '@testing-library/react-hooks';
import { GraphError } from '@microsoft/microsoft-graph-client';
import useBookmarksApiAccessor, { createBookmarksUrl, pickRequiredProperties } from './useBookmarksApiAccessor';
import mockFetch from '../../mocks/fetch';
import { DataSourceKind } from '../../types/DataSourceKind';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

jest.mock('../../utilities/environment');

beforeEach(() => {
  mockFetch.mockClear();
});

describe('createBookmarksUrl', () => {
  describe('when userId = ""', () => {
    const userId = '';

    it('should return blank', () => {
      expect(createBookmarksUrl(userId)).toBe('');
    });
  });

  describe('when userId = "a"', () => {
    const userId = 'a';

    describe('when articleId is falsy', () => {
      it('should return "https://localhost:5001/users/a/bookmarks"', () => {
        const expected = 'https://localhost:5001/users/a/bookmarks';
        expect(createBookmarksUrl(userId)).toBe(expected);
        expect(createBookmarksUrl(userId, '')).toBe(expected);
      });
    });

    describe('when articleId is "123"', () => {
      const articleId = '123';

      it('should return "https://localhost:5001/users/a/bookmarks/123"', () => {
        const expected = 'https://localhost:5001/users/a/bookmarks/123';
        expect(createBookmarksUrl(userId, articleId)).toBe(expected);
      });
    });
  });
});

describe('useBookmarksApiAccessor', () => {
  describe('when tokenProvider = undefined', () => {
    const tokenProvider = undefined;

    it('should return undefined', () => {
      const { result } = renderHook(() => useBookmarksApiAccessor(tokenProvider));
      expect(result.current.putBookmarkApi).toBeUndefined();
      expect(result.current.deleteBookmarkApi).toBeUndefined();
      expect(result.current.getBookmarksApi).toBeUndefined();
    });
  });

  describe('when tokenProvider resolves', () => {
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************.-xgNUazh7BVnEE86hvd2FMyIyH4-0Oy9A_tpKoXnEbs';
    const tokenProvider = jest.fn().mockResolvedValue(token);

    describe('putBookmarkApi', () => {
      const item1: ISplitViewListSingle = {
        id: '123',
        kind: DataSourceKind.SPO,
        note: '経営部',
        title: 'Title',
        displayDate: new Date('2022-01-01').toISOString(),
        properties: {
          listUrl: 'listUrl',
          siteUrl: 'siteUrl',
          listId: 'listId',
          listName: 'listName',
          createdDate: new Date('2022-01-01').toISOString(),
          updatedDate: new Date('2022-01-01').toISOString(),
          editLink: 'abcdefg',
          hasAttachments: true,
        },
        reposCreatedDate: new Date('2022-01-01').toISOString(),
        reposUpdatedDate: new Date('2022-01-01').toISOString(),
      };
      const item2: ISplitViewListSingle = {
        id: '456',
        kind: DataSourceKind.Mail,
        note: '',
        title: '',
        displayDate: new Date('2022-01-01').toISOString(),
        properties: {},
        reposCreatedDate: new Date('2022-01-01').toISOString(),
        reposUpdatedDate: new Date('2022-01-01').toISOString(),
      };
      const item3: ISplitViewListSingle = {
        id: '789',
        kind: DataSourceKind.Chat,
        note: '',
        title: '',
        displayDate: new Date('2022-01-01').toISOString(),
        properties: {
          teamId: 'team123',
          chatId: 'chat456',
          messageType: 'text',
        },
        reposCreatedDate: new Date('2022-01-01').toISOString(),
        reposUpdatedDate: new Date('2022-01-01').toISOString(),
      };
      describe('putBookmarkproperties', () => {
        it('should return an empty object for Mail', () => {
          const result = pickRequiredProperties(item2);
          expect(result).toEqual({});
        });

        it('should return properties for Chat', () => {
          const result = pickRequiredProperties(item3);
          expect(result).toEqual({
            teamId: 'team123',
            chatId: 'chat456',
            messageType: 'text',
          });
        });

        it('should return item.properties for other kinds', () => {
          const result = pickRequiredProperties(item1);
          expect(result).toEqual(item1.properties);
        });
      });

      describe('when fetch resolves', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            status: 200,
          });
        });

        it('should resolve void', async () => {
          const { result } = renderHook(() => useBookmarksApiAccessor(tokenProvider));
          await expect(result.current.putBookmarkApi?.(item1)).resolves.toBe(undefined);

          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            'https://localhost:5001/users/abc/bookmarks/123',
            {
              method: 'PUT',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                ...item1,
                properties: JSON.stringify(item1.properties),
              }),
            },
          );
        });

        it('when kind is Mail, resolved', async () => {
          const { result } = renderHook(() => useBookmarksApiAccessor(tokenProvider));
          await expect(result.current.putBookmarkApi?.(item2)).resolves.toBe(undefined);
          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            'https://localhost:5001/users/abc/bookmarks/456',
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              method: 'PUT',
              body: JSON.stringify({
                ...item2,
                properties: JSON.stringify(item2.properties),
              }),
            },
          );
        });
      });
    });

    describe('deleteBookmarkApi', () => {
      describe('when fetch resolves', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
          });
        });

        it('should resolve void', async () => {
          const { result } = renderHook(() => useBookmarksApiAccessor(tokenProvider));
          await expect(result.current.deleteBookmarkApi?.('efg')).resolves.toBe(undefined);

          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            'https://localhost:5001/users/abc/bookmarks/efg',
            {
              method: 'DELETE',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: undefined,
            },
          );
        });
      });
    });

    describe('getBookmarks', () => {
      const item = {
        id: '123',
        kind: 'SPO',
        title: 'Title',
        note: 'note',
        displayDate: 'displayDate',
        properties: JSON.stringify(
          {
            siteUrl: 'siteUrl',
            listUrl: 'listUrl',
            listId: 'listId',
            listName: 'listName',
            createdDate: new Date('2022-01-01').toISOString(),
            updatedDate: new Date('2022-01-02').toISOString(),
            editLink: 'abcdefg',
            hasAttachments: true,
            categoryKeyName: 'category1',
          },
        ),
        reposCreatedDate: new Date('2022-01-03').toISOString(),
        reposUpdatedDate: new Date('2022-01-04').toISOString(),
        timeStamp: new Date('2022-01-05').toISOString(), // テスト用不要データ
      };

      const expected: ISplitViewListSingle = {
        id: '123',
        kind: 'SPO',
        title: 'Title',
        note: 'note',
        displayDate: 'displayDate',
        properties: {
          siteUrl: 'siteUrl',
          listUrl: 'listUrl',
          listId: 'listId',
          listName: 'listName',
          createdDate: new Date('2022-01-01').toISOString(),
          updatedDate: new Date('2022-01-02').toISOString(),
          editLink: 'abcdefg',
          hasAttachments: true,
          categoryKeyName: 'category1',
        },
        reposCreatedDate: new Date('2022-01-03').toISOString(),
        reposUpdatedDate: new Date('2022-01-04').toISOString(),
      };

      describe('when fetch resolves data with extra keys', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            json: () => Promise.resolve([item]),
          });
        });

        it('should resolve ISplitViewListSingle array', async () => {
          const { result } = renderHook(() => useBookmarksApiAccessor(tokenProvider));
          await expect(result.current.getBookmarksApi?.()).resolves.toStrictEqual([expected]);

          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            'https://localhost:5001/users/abc/bookmarks',
            {
              method: 'GET',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: undefined,
            },
          );
        });

        describe('when fetch resolves response with 204 status', () => {
          beforeEach(() => {
            mockFetch.mockResolvedValue({
              ok: true,
              json: () => Promise.resolve('abc'),
              status: 204,
            });
          });

          it('should reject GraphError', async () => {
            const { result } = renderHook(() => useBookmarksApiAccessor(tokenProvider));
            await expect(
              result.current.getBookmarksApi?.(),
            ).rejects.toStrictEqual(new GraphError(204));
          });
        });
      });
    });
  });
});
