import React from 'react';
import environment from '../../utilities/environment';

const PREFIX = environment.REACT_APP_API_URL ?? '/api';

/**
 * Azure OpenAI API を呼び出し、検索結果の一覧を取得
 *
 * @param input - 検索クエリとして送信するユーザー入力文字列
 * @param userId - 現在のユーザーを識別するための ID
 * @param groupId - 現在のユーザーを識別するためのグループID
 * @param dateFilter - （省略可）絞り込み対象とする日付フィルター（ISO 8601 形式文字列）
 * @param sourceFilter - （省略可）絞り込み対象とするソースフィルター
 * @throws {Error} レスポンスステータスが OK でない場合、エラー情報を含む Error をスロー
 */
const fetchResultFromAIImpl = async (
  input: string,
  userId?: string,
  groupIds?: string[],
  from?: string,
  to?: string,
  sourceFilter?: string,
) => {
  try {
    const response = await fetch(`${PREFIX}/openai/aisearch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        Input: input,
        UserId: userId,
        GroupIds: groupIds,
        From: from,
        To: to,
        SourceFilter: sourceFilter,
      }),
    });

    // レスポンスのContent-Typeをチェック
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error(`Expected JSON response but got ${contentType}`);
    }
    // エラーが返ってきたらText変換(json、HTMLにも対応できる)
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error (${response.status}): ${errorText}`);
    }
    // 中身が正しいことを確認したらjson化
    const data = await response.json();
    // 軽量なランダム生成（検索速度への影響はほぼゼロ）
    const randomSummary = Math.floor(Math.random() * 100000).toString();
    return {
      reply: randomSummary,
      list: data.mergedResult,
    };
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

const useOpenAIApiAccessor = () => {
  /**
   * 検索文字からクエリを生成
   */
  const fetchResultFromAI = React.useCallback(
    (searchInputValue: string,
      userId: string,
      groupIds?: string[],
      from?: string,
      to?: string,
      sourceFilter?: string) => fetchResultFromAIImpl(
      searchInputValue,
      userId,
      groupIds,
      from,
      to,
      sourceFilter,
    ),
    [],
  );
  return {
    fetchResultFromAI,
  };
};

export default useOpenAIApiAccessor;
