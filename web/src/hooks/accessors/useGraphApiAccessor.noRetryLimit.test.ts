import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { createGraphClient } from '@avanade-teams/auth';
import { waitFor } from '@testing-library/react';
import { sendRequests } from './useGraphApiAccessor';
import { WeakTokenProvider } from '../../types/TokenProvider';

/**
 * 取得処理の完了を持たず(awaitしてない)にテストをしているので他のテストスイートに影響を与えないように分割している
 * 本テストのようなテストを新規追加したい場合はファイルを分けて別で実施する
 *  */

dayjs.extend(isBetween);

const MockDefaultTime = 3000;

jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: -1,
    REACT_APP_RETRY_DEAFULT_TIME: MockDefaultTime,
    REACT_APP_ADDITIONAL_WAITING_MSEC: 0,
    REACT_APP_BATCH_REQUEST_CHUNK_SIZE: 20,
  },
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));
const mockCreateGraphClient = createGraphClient as jest.Mock;
const mockProvider = jest.mock;

const postMock = jest.fn();
const apiMock = jest.fn();
const mockClient = {
  api: apiMock,
  select: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  post: postMock,
  get: jest.fn(),
};
const cancellationRef = { current: false };

describe('useGraphAPiAccessor', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });
  beforeEach(() => {
    mockCreateGraphClient.mockReturnValue(mockClient);
    mockCreateGraphClient.mockClear();
    Object.values(mockClient).forEach((v) => v.mockClear());
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('retry timer', () => {
    beforeEach(() => {
      jest.setTimeout(10000);
    });

    describe('when REACT_APP_RETRY_COUNTS is -1', () => {
      it('should done infinity', async () => {

        apiMock.mockImplementation(() => ({ post: postMock }));
        postMock.mockResolvedValue({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: {
              'Retry-After': '1',
            },
          }],
        });
        postMock.mockResolvedValueOnce({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: {
              'Retry-After': '1',
            },
          }],
        });
        postMock.mockResolvedValueOnce({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: {
              'Retry-After': '1',
            },
          }],
        });
        postMock.mockResolvedValueOnce({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: {
              'Retry-After': '1',
            },
          }],
        });
        postMock.mockResolvedValueOnce({
          responses: [{
            body: '',
            id: '1',
            status: 429,
            headers: {
              'Retry-After': '1',
            },
          }],
        });
        sendRequests(mockProvider as unknown as WeakTokenProvider, [{ id: '1', url: '/me/user', method: 'GET' }], cancellationRef);
        expect(postMock).toHaveBeenCalledTimes(1);
        await waitFor(async () => {
          expect(postMock).toHaveBeenCalledTimes(2);
        }, { timeout: MockDefaultTime + 500 });
        await waitFor(async () => {
          expect(postMock).toHaveBeenCalledTimes(3);
        }, { timeout: MockDefaultTime + 500 });
        await waitFor(async () => {
          expect(postMock).toHaveBeenCalledTimes(4);
        }, { timeout: MockDefaultTime + 500 });
        await waitFor(async () => {
          expect(postMock).toHaveBeenCalledTimes(5);
        }, { timeout: MockDefaultTime + 500 });

      });
    });
  });
});
