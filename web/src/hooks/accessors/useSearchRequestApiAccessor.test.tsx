import '@testing-library/jest-dom';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { GraphError } from '@microsoft/microsoft-graph-client';
import { renderHook } from '@testing-library/react-hooks';
import mockFetch from '../../mocks/fetch';
import { Reporters } from '../../types/Reporters';
import useSearchRequestApiAccessor, {
  createGetUrl, createPostUrl, createContextPutUrl, createContextFieldPutUrl,
} from './useSearchRequestApiAccessor';
import { ISearchRequestResult } from '../../types/ISearchRequestResult';
import { IContext, ISortOrder } from '../../types/IContext';
import { AtTaneGraphError } from '../../utilities/commonFunction';
import SearchRequestError from '../../utilities/errors/searchRequestError';

jest.mock('../../utilities/environment');

beforeEach(() => {
  mockFetch.mockClear();
});

describe('createGetUrl', () => {
  describe('when userId = ""', () => {
    const userId = '';

    it('should return blank', () => {
      expect(createGetUrl(userId)).toBe('');
    });
  });

  describe('when userId = "a"', () => {
    const userId = 'a';
    it('should return "https://localhost:5001/users/a/search/result"', () => {
      const expected = 'https://localhost:5001/users/a/search/result';
      expect(createGetUrl(userId)).toBe(expected);
    });
  });
});

describe('createPostUrl', () => {
  describe('when userId = ""', () => {
    const userId = '';

    it('should return blank', () => {
      expect(createPostUrl(userId)).toBe('');
    });
  });

  describe('when userId = "a"', () => {
    const userId = 'a';
    it('should return "https://localhost:5001/users/a/search"', () => {
      const expected = 'https://localhost:5001/users/a/search';
      expect(createPostUrl(userId)).toBe(expected);
    });
  });
});

describe('createContextPutUrl', () => {
  describe('when userId = ""', () => {
    const userId = '';

    it('should return blank', () => {
      expect(createContextPutUrl(userId)).toBe('');
    });
  });

  describe('when userId = "a"', () => {
    const userId = 'a';
    it('should return "https://localhost:5001/users/a/search/context"', () => {
      const expected = 'https://localhost:5001/users/a/search/context';
      expect(createContextPutUrl(userId)).toBe(expected);
    });
  });
});

describe('createContextFieldPutUrl', () => {
  const fieldName = 'sort';
  describe('when userId = ""', () => {
    const userId = '';

    it('should return blank', () => {
      expect(createContextFieldPutUrl(userId, fieldName)).toBe('');
    });
  });

  describe('when userId = "a"', () => {
    const userId = 'a';
    it('should return "https://localhost:5001/users/a/search/context/sort"', () => {
      const expected = 'https://localhost:5001/users/a/search/context/sort';
      expect(createContextFieldPutUrl(userId, fieldName)).toBe(expected);
    });
  });
});

describe('useSearchRequestApiAccessor', () => {
  const current = new Date(2023, 3, 1);
  beforeAll(() => {
    jest.useFakeTimers('modern');
    jest.setSystemTime(current);
  });
  const mockReporters = [jest.fn(), jest.fn()];
  const reporters = mockReporters as unknown as Reporters;
  beforeEach(() => {
    mockReporters[0].mockClear();
    mockReporters[1].mockClear();
  });
  afterAll(() => {
    jest.useRealTimers();
  });

  describe('when the tokenProvider is undefined', () => {
    it('should return undefined', () => {
      const { result } = renderHook(() => useSearchRequestApiAccessor(undefined, reporters));
      expect(result.current.postSearchRequestApi).toBeUndefined();
      expect(result.current.getSearchRequestApi).toBeUndefined();
    });
  });

  describe('when the tokenProvider is available', () => {
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************.-xgNUazh7BVnEE86hvd2FMyIyH4-0Oy9A_tpKoXnEbs';
    const tokenProvider = jest.fn().mockResolvedValue(token);
    it('should return the function', () => {
      const { result } = renderHook(() => useSearchRequestApiAccessor(tokenProvider, reporters));
      expect(result.current.postSearchRequestApi).toBeInstanceOf(Function);
      expect(result.current.getSearchRequestApi).toBeInstanceOf(Function);
    });

    describe('postSearchRequestApi', () => {
      describe('when fetch resolves', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            code: 202,
          });
        });

        it('should resolve void', async () => {
          const condition = 'aaa';
          const uId = 'abc';
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.postSearchRequestApi?.(condition)).resolves.toBeUndefined();
          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            `https://localhost:5001/users/${uId}/search`,
            {
              method: 'POST',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                condition: 'aaa',
                context: {
                  timestamp: current,
                } as IContext,
              }),
            },
          );
        });
      });

      describe('when fetch resolves response other than 202 status', () => {
        const condition = 'aaa';
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            status: 204,
          });
        });

        it('should report SYS_ERROR', async () => {
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.postSearchRequestApi?.(condition)).resolves.toBeUndefined();
          expect(reporters[0]).toHaveBeenCalledTimes(1);
          expect(reporters[0]).toHaveBeenCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'IS_OFFLINE_OR_SOMETHING_WRONG',
            error: new GraphError(500),
          });
        });
      });

      describe('when fetcch resolves response client error status', () => {

        it.each([
          { status: 404, statusText: 'NotFound' },
          { status: 403, statusText: 'Forbidden' },
          { status: 500, statusText: 'InternalServerError' },
          { status: 503, statusText: 'TemporaryUnavailable' },
        ])('failed with status code $status', async ({ status, statusText }) => {
          const condition = 'OR aaa';
          mockFetch.mockRejectedValueOnce(
            new AtTaneGraphError({
              status,
              statusText,
            } as unknown as any, status),
          );
          const {
            result,
          } = renderHook(() => useSearchRequestApiAccessor(tokenProvider, reporters));
          try {
            await result.current.postSearchRequestApi?.(condition);
            fail();
          } catch (e) {
            expect(e).not.toBeNull();
            expect((e as AtTaneGraphError).errorResponse.status).toBe(status);
          }
        });
        it('failed with status code 400', async () => {
          const condition = 'OR aaa';
          mockFetch.mockRejectedValueOnce(
            {
              statusCode: 400,
              errorResponse: {
                json: () => Promise.resolve({ 'value.condition': 'ORの左辺がありません' }),
              },
            } as unknown as AtTaneGraphError,
          );
          const {
            result,
          } = renderHook(() => useSearchRequestApiAccessor(tokenProvider, reporters));
          try {
            await result.current.postSearchRequestApi?.(condition);
            fail();
          } catch (e) {
            expect(e).not.toBeNull();
            expect((e as SearchRequestError).details['value.condition']).toContain('ORの左辺がありません');
          }
        });
      });
    });

    describe('getSearchRequestApi', () => {
      describe('when fetch resolves', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            code: 200,
            json: jest.fn().mockResolvedValue(
              {
                conditionKeywords: 'test',
                state: 'Completed',
                results: [
                  {
                    reqId: '82e678ab-3b01-419a-9e70-7ec2452f1b19',
                    pid: '0a0ac3a2-f363-42a0-bae0-486d9396424e',
                    dataSource: {
                      kind: 'SPO',
                      properties: {
                        category: 'category1',
                        list: 'A6EEDE04-AE01-4AD6-A934-EA3C74B230E0',
                        listName: '会社からのお知らせ',
                        listUrl: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/sub-project/Lists/List/DispForm.aspx',
                        site: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/sub-project/',
                      },
                    },
                  },
                ],
              },
            ),
          });
        });

        const expected: ISearchRequestResult = {
          conditionKeywords: 'test',
          state: 'Completed',
          results: [
            {
              reqId: '82e678ab-3b01-419a-9e70-7ec2452f1b19',
              pid: '0a0ac3a2-f363-42a0-bae0-486d9396424e',
              dataSource: {
                kind: 'SPO',
                properties: {
                  category: 'category1',
                  list: 'A6EEDE04-AE01-4AD6-A934-EA3C74B230E0',
                  listName: '会社からのお知らせ',
                  listUrl: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/sub-project/Lists/List/DispForm.aspx',
                  site: 'https://projectgeranium.sharepoint.com/sites/projectgeranium/sub-project/',
                },
              },
            },
          ],
        };

        it('should resolve the ISearchRequestResult', async () => {
          const uId = 'abc';
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.getSearchRequestApi?.()).resolves.toStrictEqual(expected);
          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            `https://localhost:5001/users/${uId}/search/result`,
            {
              method: 'GET',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: undefined,
            },
          );
        });
      });

      describe('when fetch resolves response with 204 status', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            json: () => Promise.resolve(),
            status: 204,
          });
        });

        it('should report SYS_EVENT', async () => {
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(
            result.current.getSearchRequestApi?.(),
          ).resolves.toBeUndefined();
          expect(reporters[0]).toHaveBeenCalledTimes(1);
          expect(reporters[0]).toHaveBeenCalledWith({
            type: EventReportType.SYS_EVENT,
            name: 'NO_CONTENT',
            customProperties: {
              statusText: undefined,
            },
          });
        });
      });
    });

    describe('cancelSearchRequestApi', () => {
      describe('when fetch resolves', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            status: 200,
            json: jest.fn().mockResolvedValue(
              {
                string: 'Cancelled',
              },
            ),
          });
        });

        const expected = {
          string: 'Cancelled',
        };

        it('should resolve the stirng', async () => {
          const uId = 'abc';
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.cancelSearchRequestApi?.()).resolves.toStrictEqual(expected);
          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            `https://localhost:5001/users/${uId}/search/cancellation`,
            {
              method: 'POST',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: undefined,
            },
          );
        });
      });

      describe('when fetch resolves response with 204 status', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            json: () => Promise.resolve(),
            status: 204,
          });
        });

        it('should report SYS_EVENT', async () => {
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(
            result.current.cancelSearchRequestApi?.(),
          ).resolves.toBeUndefined();
          expect(reporters[0]).toHaveBeenCalledTimes(1);
          expect(reporters[0]).toHaveBeenCalledWith({
            type: EventReportType.SYS_EVENT,
            name: 'NO_CONTENT',
            customProperties: {
              statusText: undefined,
            },
          });
        });
      });
    });

    describe('updateRemoteContextApi', () => {
      describe('when fetch resolves', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            code: 200,
          });
        });

        it('should resolve void', async () => {
          const context = {
            sort: [{
              key: 'displayDate',
              order: 'desc',
            }],
            timestamp: current,
          } as IContext;
          const uId = 'abc';
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.updateRemoteContextApi?.(context)).resolves.toBeUndefined();
          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            `https://localhost:5001/users/${uId}/search/context`,
            {
              method: 'PUT',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: '{"sort":"[{\\"key\\":\\"displayDate\\",\\"order\\":\\"desc\\"}]","timestamp":"\\"2023-03-31T15:00:00.000Z\\""}',
            },
          );
        });
      });

      describe('when fetch resolves response other than 200 status', () => {
        const context = {
          sort: [{
            key: 'displayDate',
            order: 'desc',
          }],
          timestamp: current,
        } as IContext;
        beforeEach(() => {
          mockFetch.mockResolvedValueOnce({
            ok: true,
            status: 500,
          });
        });

        it('should report SYS_ERROR', async () => {
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.updateRemoteContextApi?.(context)).resolves.toBeUndefined();
          expect(reporters[0]).toHaveBeenCalledTimes(1);
          expect(reporters[0]).toHaveBeenCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'IS_OFFLINE_OR_SOMETHING_WRONG',
            error: new GraphError(500),
          });
        });
      });
    });

    describe('updateContextFieldApi', () => {
      describe('when fetch resolves', () => {
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            code: 200,
          });
        });

        it('should resolve void', async () => {
          const context = [{
            key: 'displayDate',
            order: 'desc',
          }] as ISortOrder[];
          const uId = 'abc';
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.updateRemoteContextFieldApi?.('sort', context)).resolves.toBeUndefined();
          expect(mockFetch).toBeCalledTimes(1);
          expect(mockFetch).toBeCalledWith(
            `https://localhost:5001/users/${uId}/search/context/sort`,
            {
              method: 'PUT',
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
              body: '"[{\\"key\\":\\"displayDate\\",\\"order\\":\\"desc\\"}]"',
            },
          );
        });
      });

      describe('when fetch resolves response other than 200 status', () => {
        const context = [{
          key: 'displayDate',
          order: 'desc',
        }] as ISortOrder[];
        beforeEach(() => {
          mockFetch.mockResolvedValue({
            ok: true,
            status: 500,
          });
        });

        it('should report SYS_ERROR', async () => {
          const { result } = renderHook(
            () => useSearchRequestApiAccessor(tokenProvider, reporters),
          );
          await expect(result.current.updateRemoteContextFieldApi?.('sort', context)).resolves.toBeUndefined();
          expect(reporters[0]).toHaveBeenCalledTimes(1);
          expect(reporters[0]).toHaveBeenCalledWith({
            type: EventReportType.SYS_ERROR,
            name: 'IS_OFFLINE_OR_SOMETHING_WRONG',
            error: new GraphError(500),
          });
        });
      });
    });
  });
});
