import { createGraphClient } from '@avanade-teams/auth';
import { renderHook } from '@testing-library/react-hooks';
import useChatApiAccessor from './useChatApiAccessor';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { IChatReaction } from '../../types/IChatResponse';

// mock environment.ts
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 3,
    REACT_APP_RETRY_DEAFULT_TIME: 5000,
    REACT_APP_BATCH_REQUEST_CHUNK_SIZE: 20,
  },
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));
const mockCreateGraphClient = createGraphClient as jest.Mock;
const mockProvider = jest.mock;

// mock the client of microsoft-graph-client
const getMock = jest.fn();
const postMock = jest.fn();
const apiMock = jest.fn().mockReturnThis();
const mockClient = {
  api: apiMock,
  select: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  expand: jest.fn().mockReturnThis(),
  get: getMock,
  post: postMock,
};

const callbackFn = jest.fn();

type RenderHookProps = {
  tokenProvider: WeakTokenProvider,
};

function getHook(props: RenderHookProps) {
  return renderHook((p) => useChatApiAccessor(p.tokenProvider), {
    initialProps: props,
  });
}

describe('useChatApiAccessor', () => {

  beforeEach(() => {
    mockCreateGraphClient.mockReturnValue(mockClient);
    mockCreateGraphClient.mockClear();
    Object.values(mockClient).forEach((v) => v.mockClear());
    callbackFn.mockClear();
    mockClient.api.mockClear();
  });

  describe('fetchchatDetail', () => {
    describe('when the client.get resolves response', () => {
      beforeEach(() => {
        mockCreateGraphClient.mockReturnValue(mockClient);
        mockClient.api.mockReturnValue({ get: getMock });
        getMock.mockResolvedValue(Promise.resolve({
          value: [{ id: 'abc' }],
        }));
      });

      it('should resolve what client.get resolves', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatDetail } = result.current;

        const expected = { value: [{ id: 'abc' }] };
        await expect(fetchChatDetail?.('chatId', 'messageId'))
          .resolves.toStrictEqual(expected);
      });

      it('should call client.api with the expected parameters', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatDetail } = result.current;
        await fetchChatDetail?.('chatId', 'messageId');

        expect(mockClient.api).toHaveBeenCalledTimes(1);
        const expectedEndpoint = 'me/chats/chatId/messages/messageId';
        expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
      });
    });
  });

  describe('fetchChatAttachment', () => {
    const tokenProvider = jest.fn();
    beforeEach(() => {
      getMock.mockImplementation(() => Promise.resolve('blob content'));
    });
    afterEach(() => {
      getMock.mockClear();
    });
    it('should not called content', async () => {

      const { result } = getHook({ tokenProvider });
      const input1 = 'https://example.com/v1.0/test/$value';
      const input2 = 'https://example.com/beta/test/$value';
      expect(result.current.fetchChatAttachment?.(input1)).rejects.toThrow('required parameters are not provided');
      expect(result.current.fetchChatAttachment?.(input2)).rejects.toThrow('required parameters are not provided');
      expect(getMock).toHaveBeenCalledTimes(0);
    });

    it('should called content', async () => {

      const { result } = getHook({ tokenProvider });
      const input1 = 'https://graph.microsoft.com/v1.0/test/$value';
      const input2 = 'https://graph.microsoft.com/beta/test/$value';
      await result.current.fetchChatAttachment?.(input1);
      await result.current.fetchChatAttachment?.(input2);
      expect(getMock).toHaveBeenCalledTimes(2);
      expect(apiMock).toHaveBeenCalledWith('test/$value');
    });
  });

  describe('fetchChatChannelName', () => {
    describe('when the client.get resolves response', () => {
      beforeEach(() => {
        mockCreateGraphClient.mockReturnValue(mockClient);
        mockClient.api.mockReturnValue({ get: getMock });
        getMock.mockResolvedValue(Promise.resolve({
          value: [{ id: 'abc' }],
        }));
      });

      it('should resolve what client.get resolves', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatChannelName } = result.current;

        const expected = { value: [{ id: 'abc' }] };
        await expect(fetchChatChannelName?.('teamId')).resolves.toStrictEqual(expected);
      });

      it('should call client.api with the expected parameters', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatChannelName } = result.current;
        await fetchChatChannelName?.('teamId');

        expect(mockClient.api).toHaveBeenCalledTimes(1);
        const expectedEndpoint = 'teams/teamId';
        expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
      });
    });
  });

  describe('fetchChatTeamName', () => {
    describe('when the client.get resolves response', () => {
      beforeEach(() => {
        mockCreateGraphClient.mockReturnValue(mockClient);
        mockClient.api.mockReturnValue({ get: getMock });
        getMock.mockResolvedValue(Promise.resolve({
          value: [{ id: 'abc' }],
        }));
      });

      it('should resolve what client.get resolves', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatTeamName } = result.current;

        const expected = { value: [{ id: 'abc' }] };
        await expect(fetchChatTeamName?.('teamId', 'chatId')).resolves.toStrictEqual(expected);
      });

      it('should call client.api with the expected parameters', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatTeamName } = result.current;
        await fetchChatTeamName?.('teamId', 'chatId');

        expect(mockClient.api).toHaveBeenCalledTimes(1);
        const expectedEndpoint = 'teams/teamId/channels/chatId';
        expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
      });
    });
  });

  describe('fetchChatThreadTitle', () => {
    describe('when the client.get resolves response', () => {
      beforeEach(() => {
        mockCreateGraphClient.mockReturnValue(mockClient);
        mockClient.api.mockReturnValue({ get: getMock });
        getMock.mockResolvedValue(Promise.resolve({
          value: [{ id: 'abc' }],
        }));
      });

      it('should resolve what client.get resolves', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatThreadTitle } = result.current;

        const expected = { value: [{ id: 'abc' }] };
        await expect(fetchChatThreadTitle?.('teamId', 'chatId', 'messageId')).resolves.toStrictEqual(expected);
      });

      it('should call client.api with the expected parameters', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatThreadTitle } = result.current;
        await fetchChatThreadTitle?.('teamId', 'chatId', 'messageId');

        expect(mockClient.api).toHaveBeenCalledTimes(1);
        const expectedEndpoint = 'teams/teamId/channels/chatId/messages/messageId?$extends=members';
        expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
      });

      it('should call get thread items of self-chat', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatMember } = result.current;
        getMock.mockReturnValueOnce({ '@odata.context': 'https://graph.microsoft.com/v1.0/$metadata#chats', '@odata.count': 1, value: [{ id: 'chatId' }] });
        await fetchChatMember?.('chatId');

        expect(mockClient.api).toHaveBeenCalledTimes(2);
        const checkChatRoom = 'me/chats?$filter=id eq \'chatId\'';
        expect(mockClient.api).toHaveBeenCalledWith(checkChatRoom);
        const getMessage = 'me/chats/chatId?$expand=members';
        expect(mockClient.api).toHaveBeenCalledWith(getMessage);
      });

      it('should call get thread items of self-chat', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatMember } = result.current;
        getMock.mockReturnValueOnce({ '@odata.context': 'https://graph.microsoft.com/v1.0/$metadata#chats', '@odata.count': 0, value: [] });
        await fetchChatMember?.('chatId');

        expect(mockClient.api).toHaveBeenCalledTimes(1);
        const checkChatRoom = 'me/chats?$filter=id eq \'chatId\'';
        expect(mockClient.api).toHaveBeenCalledWith(checkChatRoom);
        const getMessage = 'me/chats/chatId?$expand=members';
        expect(mockClient.api).not.toHaveBeenCalledWith(getMessage);
      });
    });
  });

  describe('fetchChatChannelName', () => {
    describe('when the client.get resolves response', () => {
      beforeEach(() => {
        mockCreateGraphClient.mockReturnValue(mockClient);
        mockClient.api.mockReturnValue({ get: getMock });
        getMock.mockResolvedValue(Promise.resolve({
          value: [{ id: 'abc' }],
        }));
      });

      it('should resolve what client.get resolves', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatChannelName } = result.current;

        const expected = { value: [{ id: 'abc' }] };
        await expect(fetchChatChannelName?.('teamId')).resolves.toStrictEqual(expected);
      });

      it('should call client.api with the expected parameters', async () => {
        const { result } = getHook({ tokenProvider: mockProvider as unknown as WeakTokenProvider });
        const { fetchChatChannelName } = result.current;
        await fetchChatChannelName?.('teamId');

        expect(mockClient.api).toHaveBeenCalledTimes(1);
        const expectedEndpoint = 'teams/teamId';
        expect(mockClient.api).toHaveBeenCalledWith(expectedEndpoint);
      });
    });
  });

  describe('fetchReactionUserNames', () => {
    it('should not send blank requests', async () => {
      getHook({
        tokenProvider: mockProvider as unknown as WeakTokenProvider,
      }).result.current?.fetchReactionUserNames?.([]);
      expect(apiMock).not.toHaveBeenCalled();
    });

    it('should call chat apis as batch request', async () => {
      const reactionsMock: IChatReaction[] = [
        {
          reactionType: 'like',
          creationDate: 'creationDate',
          user: {
            user: {
              id: 'testUserId1',
              displayName: 'displayName',
              userIdentityType: 'userIdentityType',
            },
            application: null,
            device: null,
          },
        },
        {
          reactionType: 'heart',
          creationDate: 'creationDate',
          user: {
            user: {
              id: 'testUserId2',
              displayName: 'displayName',
              userIdentityType: 'userIdentityType',
            },
            application: null,
            device: null,
          },
        },
      ];
      apiMock.mockImplementation(() => ({ post: postMock }));
      postMock.mockImplementation(
        ({ requests }: { requests: { id: string }[] }) => Promise.resolve({
          responses: requests.map(({ id }, index: number) => ({
            id,
            status: 200,
            body: {
              value: [
                { displayName: `displayName${index + 1}`, id: `testUserId${index + 1}` },
                { displayName: `displayName${index + 2}`, id: `testUserId${index + 2}` },
              ],
            },
          })),
        }),
      );
      const hook = getHook({
        tokenProvider: mockProvider as unknown as WeakTokenProvider,
      }).result.current;
      const result = await hook.fetchReactionUserNames?.(reactionsMock);
      expect(postMock).toHaveBeenCalledWith({
        requests: [
          {
            url: "users?$filter=id in ('testUserId1','testUserId2')&$select=displayName,id",
            id: '0',
            method: 'GET',
          },
        ],
      });
      expect(result).toStrictEqual({
        errors: [],
        recoverable: [],
        responses: [{
          id: 'testUserId1',
          displayName: 'displayName1',
        },
        {
          id: 'testUserId2',
          displayName: 'displayName2',
        }],
        tooManyRequests: [],
        totalTooManyRequests: 0,
      });
    });
  });
});
