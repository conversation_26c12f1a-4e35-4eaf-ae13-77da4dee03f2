import * as React from 'react';
import { ListModeType } from '../../components/domains/split-view/types/ListMode';

/**
 * イベントリスナを登録後、解除するまで保持するための配列の型定義
 */
type ListenerCache<T extends HTMLElement> = {
  $el: T,
  listener: (e: MouseEvent) => void,
};

/**
 * innerHTML内部の<a>要素にイベントリスナを付与する
 *
 * @param {string} query DOM内からイベントリスナを付与する要素を探すためのquerySelectorのクエリ文字列
 * @param {boolean} trigger このフラグの状態を監視し、trueのときにイベントリスナを登録する
 * @param {ListModeType} listMode リスト切替時にトリガーさせるためdependenciesに追加
 * @param {(e: MouseEvent, $el: T) => void} onClick DOM内の要素をクリックしたときに実行されるcallback
 * @return {*}  {[ref: React.RefObject<T>]} DOMへの参照
 * @example
 *   const [$innerClick] = useInnerHtmlClickBehavior<HTMLLinkElement>('.link', isActive, onClick);
 *   <div ref="$innerClick"><a className="link">abc</a></div>
 */
const useInnerHtmlClickBehavior = <T extends HTMLElement>(
  query: string,
  trigger: boolean,
  listMode: ListModeType,
  onClick: (e: MouseEvent, $el: T) => void,
): [ref: React.RefObject<T>] => {

  // 初期引数からquery文字列を固定
  const [q] = React.useState(query);

  // DOMへの参照を渡すためのRefObject
  const $elemRef = React.useRef<T>(null);

  // イベントリスナを保持するための配列
  const listenerCache = React.useRef<ListenerCache<T>[]>([]);

  // イベントリスナ削除関数
  const removeListeners = React.useCallback(() => {
    listenerCache.current.forEach((cache) => {
      cache.$el.removeEventListener<'click'>('click', cache.listener);
    });
    listenerCache.current = [];
  }, []);

  // イベントリスナ追加関数
  const attachListeners = React.useCallback(() => {
    if (!$elemRef.current) return;

    const $result = $elemRef.current.querySelectorAll<T>(q);
    if ($result.length === 0) return;

    $result.forEach(($el) => {
      const listener = (e: MouseEvent) => {
        onClick(e, $el);
      };
      $el.addEventListener<'click'>('click', listener);
      listenerCache.current.push({ $el, listener });
    });

  }, [q, onClick]);

  // innerHTMLのイベントリスナ登録／解除処理
  React.useEffect(() => {
    if (!trigger) return removeListeners;

    // triggerがtrueの場合にイベンリスナを登録
    removeListeners();
    attachListeners();

    // アンマウント時にリスナを削除する
    return removeListeners;
  }, [trigger, attachListeners, removeListeners, listMode]);

  return [$elemRef];
};

export default useInnerHtmlClickBehavior;
