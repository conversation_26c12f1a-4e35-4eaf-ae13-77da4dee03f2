/* eslint-disable no-param-reassign */
import * as React from 'react';
import type { SplitViewDispatch } from '../../components/domains/split-view/split-view-container/reducers/splitViewReducer';
import { SplitViewListView } from '../../components/domains/split-view/split-view-list/SplitViewList';
import { CancelSearchRequestApi } from '../accessors/useSearchRequestApiAccessor';
import { PerformanceMetrics } from '../../types/PerformanceMetrics';

interface CancellationButtonBehaviorProps {
  dispatch: SplitViewDispatch;
  cancelSearchRequestApi?: CancelSearchRequestApi;
  cancellationRef: React.MutableRefObject<boolean>;
  setPerformanceMetrics: (override: Partial<PerformanceMetrics> | null) => void;
}

const useCancellationButtonBehavior = ({
  dispatch,
  cancelSearchRequestApi,
  cancellationRef,
  setPerformanceMetrics,
}: CancellationButtonBehaviorProps) => {

  const onCancelSubmit = React.useCallback(async () => {
    if (!cancelSearchRequestApi) return;
    cancellationRef.current = true;

    setPerformanceMetrics({
      cancellation: performance.now(),
    });

    dispatch({
      type: 'SET_DATA',
      payload: {
        listView: SplitViewListView.SEARCH_COMPLETED,
      },
    });

    await cancelSearchRequestApi();
  }, [cancelSearchRequestApi, cancellationRef, setPerformanceMetrics, dispatch]);
  return { onCancelSubmit };
};
export default useCancellationButtonBehavior;
