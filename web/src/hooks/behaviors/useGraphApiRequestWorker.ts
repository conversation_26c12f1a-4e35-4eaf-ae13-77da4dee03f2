import * as React from 'react';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { RequestQueueItem } from '../../types/RequestQueueItem';
import {
  IRequestPayload, sendRequests,
} from '../accessors/useGraphApiAccessor';
import { IMailResponse } from '../../types/IMailResponse';
import { IChatResponse } from '../../types/IChatResponse';

function blank() {
  return {
    responses: [],
    recoverable: [],
    errors: [],
    totalTooManyRequests: 0,
    tooManyRequests: [],
    drops: [],
  };
}

export function composeMailRequest(id: string, index: number): IRequestPayload {
  return {
    method: 'GET',
    url: `/me/messages/${id}?$select=receivedDateTime,sentDateTime,hasAttachments,subject,lastModifiedDateTime,id,sender,from,toRecipients,ccRecipients,bccRecipients`,
    id: `${id}|${index}`,
  };
}

export function composeChatRequest(id: string, index: number): IRequestPayload {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_type, _teamId, channelId, resourceId] = id.split('|');
  return {
    method: 'GET',
    url: `me/chats/${channelId}/messages/${resourceId}`,
    id: `${id}|${index}`,
  } as IRequestPayload;
}

function composeSearchRequest(id: string, kind: string, index: number): IRequestPayload {
  switch (kind) {
    case 'Mail':
      return composeMailRequest(id, index);
    case 'Chat':
      return composeChatRequest(id, index);
    default:
      throw Error(`unknown kind: ${kind}`);
  }
}

const useGraphApiRequestWorker = (
  cancellationRef: React.MutableRefObject<boolean>,
  tokenProvider: WeakTokenProvider,
) => {
  const queueRef = React.useRef<RequestQueueItem[]>([]);
  const searchingRef = React.useRef<boolean>(false);

  const enqueue = React.useCallback((item: RequestQueueItem) => {
    queueRef.current.push(item);
  }, []);

  const dequeue = React.useCallback(() => queueRef.current.shift(), []);

  const search = React.useCallback(async (
    item: RequestQueueItem,
  ) => {
    const requests = item.ids.map((id, index) => composeSearchRequest(id, item.kind, index));
    const results = await sendRequests<IMailResponse | IChatResponse>(
      tokenProvider, requests, cancellationRef, item.appendItems, item.converter,
    );
    if (results) {
      item.resolve(results);
    } else {
      // TODO: 重大な理由で失敗したときはrejectできるようにする
      item.resolve(blank());
    }
  }, [cancellationRef, tokenProvider]);

  const start = React.useCallback(async () => {
    if (searchingRef.current) {
      return;
    }
    searchingRef.current = true;

    // eslint-disable-next-line no-constant-condition
    while (true) {
      const item = dequeue();
      if (!item) break;
      if (cancellationRef.current) {
        item.resolve(blank());
      } else {
        // 直列処理するため
        // eslint-disable-next-line no-await-in-loop
        await search(item);
      }
    }
    searchingRef.current = false;
  }, [cancellationRef, dequeue, search]);

  return {
    queue: {
      enqueue,
    },
    search,
    start,
  };
};

export default useGraphApiRequestWorker;
