import { renderHook, act } from '@testing-library/react-hooks';
import useCancellationButtonBehavior from './useCancellationButtonBehavior';

describe('useCancellationButtonBehavior', () => {
  it('should update cancellationRef after calling cancelSearchRequestApi', async () => {
    const dispatchMock = jest.fn();
    const cancelSearchRequestApiMock = jest.fn();
    const cancellationRefMock = { current: false };
    const setPerformanceMetricsMock = jest.fn();

    const { result } = renderHook(() => useCancellationButtonBehavior({
      dispatch: dispatchMock,
      cancelSearchRequestApi: cancelSearchRequestApiMock,
      cancellationRef: cancellationRefMock,
      setPerformanceMetrics: setPerformanceMetricsMock,
    }));

    await act(async () => {
      await result.current.onCancelSubmit();
    });

    expect(dispatchMock).toHaveBeenCalledWith({
      type: 'SET_DATA',
      payload: {
        listView: 'searchCompleted',
      },
    });
    expect(cancelSearchRequestApiMock).toHaveBeenCalled();
    expect(cancellationRefMock.current).toBe(true);
  });
});
