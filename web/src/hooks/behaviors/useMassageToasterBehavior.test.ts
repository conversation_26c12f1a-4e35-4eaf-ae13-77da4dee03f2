import { renderHook, act } from '@testing-library/react-hooks';
import { ToasterMessage } from '../../components/commons/molecules/message-toaster/MessageToaster';
import useMessageToasterBehavior from './useMessageToasterBehavior';

describe('useMessageToasterBehavior', () => {
  beforeAll(() => {
    jest.useFakeTimers('modern');
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('when useMessageToasterBehavior initialized', () => {
    it('should return default values', () => {
      const { result } = renderHook(() => useMessageToasterBehavior(1000));
      const [isPopupShown, popupMessage, extendPopupTimer] = result.current;
      expect(isPopupShown).toBe(false);
      expect(popupMessage).toBe(ToasterMessage.BLANK);
      expect(extendPopupTimer).toBeInstanceOf(Function);
    });
  });

  describe('when extendPopupTimer(true) called', () => {
    it('should change return values to show a bookmarked popup and should invert the flag after the timer duration', async () => {
      const TIMER_DURATION = 1000;

      const { result, waitForNextUpdate, unmount } = renderHook(
        () => useMessageToasterBehavior(TIMER_DURATION),
      );
      const [,, extendPopupTimer] = result.current;

      await act(async () => {
        // trueで実行
        extendPopupTimer(true);

        await waitForNextUpdate();
        expect(result.current[0]).toBe(true);
        expect(result.current[1]).toBe(ToasterMessage.BOOKMARKED);

        // 値が変わるまで待機
        jest.advanceTimersByTime(TIMER_DURATION);

        expect(result.current[0]).toBe(false);

        unmount();
      });
    });
  });

  describe('when extendPopupTimer(false) called', () => {
    it('should change return values to show a un-bookmarked popup and should invert the flag after the timer duration', async () => {
      const TIMER_DURATION = 1000;

      const { result, waitForNextUpdate, unmount } = renderHook(
        () => useMessageToasterBehavior(TIMER_DURATION),
      );
      const [,, extendPopupTimer] = result.current;

      await act(async () => {
        // falseで実行
        extendPopupTimer(false);

        await waitForNextUpdate();
        expect(result.current[0]).toBe(true);
        expect(result.current[1]).toBe(ToasterMessage.UN_BOOKMARKED);

        // 値が変わるまで待機
        jest.advanceTimersByTime(TIMER_DURATION);

        expect(result.current[0]).toBe(false);

        unmount();
      });
    });
  });
});
