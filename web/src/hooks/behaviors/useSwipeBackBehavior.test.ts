import { act, renderHook } from '@testing-library/react-hooks';
import { SyntheticEvent } from 'react';
import { defineGetSelectionMock, getSelectionMock, restoreGetSelectionMock } from '../../mocks/get-selection';
import useSwipeBackBehavior, {
  calculateTouchLength, createInitialTouchInfo,
  createTouchInfoOnMove, createTouchInfoOnStart, detectAxis, isRangeSelected,
  resetTouchLengthImpl, TouchInfo, updateTouchLength,
} from './useSwipeBackBehavior';

beforeAll(() => {
  defineGetSelectionMock();
});

beforeEach(() => {
  getSelectionMock.mockClear();
});

afterAll(() => {
  restoreGetSelectionMock();
});

describe('isRangeSelected', () => {
  describe('when selection is null', () => {
    beforeEach(() => {
      getSelectionMock.mockReturnValue(null);
    });

    it('should return false', () => {
      expect(isRangeSelected()).toBe(false);
    });
  });

  describe('when selection.type is not "Range"', () => {
    beforeEach(() => {
      getSelectionMock.mockReturnValue({
        type: 'None',
      } as Selection);
    });

    it('should return false', () => {
      expect(isRangeSelected()).toBe(false);
    });
  });

  describe('when selection.type is "Range"', () => {

    beforeEach(() => {
      getSelectionMock.mockReturnValue({
        type: 'Range',
        toString: () => '',
      } as Selection);
    });

    it('should return true', () => {
      expect(isRangeSelected()).toBe(true);
    });
  });
});

function createTouchEventMock(clientX: number) {
  return {
    nativeEvent: new TouchEvent('touchMove', {
      touches: [{ clientX, clientY: 0 }] as Touch[],
    }),
    preventDefault: jest.fn(),
  } as unknown as SyntheticEvent<HTMLElement, TouchEvent>;
}

function createMouseEventMock(clientX: number) {
  return {
    nativeEvent: new MouseEvent('mouseMove', {
      clientX,
      clientY: 0,
    }),
    preventDefault: jest.fn(),
  } as unknown as SyntheticEvent<HTMLElement, MouseEvent>;
}

describe('calculateTouchLength', () => {
  describe('when window.innerWidth = 1000', () => {
    beforeEach(() => {
      Object.defineProperty(global.window, 'innerWidth', {
        value: 1000,
      });
    });

    describe('when swipeDir = "x"', () => {
      const swipeDir = 'x';

      describe('when xStart = 100, xCurrent = 200, isCapturing = 1', () => {
        const tInfo: TouchInfo = {
          xStart: 100, yStart: 0, xCurrent: 200, yCurrent: 0, isCapturing: 1,
        };

        it('should return 2', () => {
          expect(calculateTouchLength(tInfo, swipeDir)).toBe(2);
        });
      });

      describe('when xStart = 100, xCurrent = 150, isCapturing = 1', () => {
        const tInfo: TouchInfo = {
          xStart: 100, yStart: 0, xCurrent: 150, yCurrent: 0, isCapturing: 1,
        };

        it('should return 0', () => {
          expect(calculateTouchLength(tInfo, swipeDir)).toBe(0);
        });
      });
    });

    describe('when swipeDir = "y"', () => {
      const swipeDir = 'y';

      describe('when yStart = 100, yCurrent = 200, isCapturing = 1', () => {
        const tInfo: TouchInfo = {
          xStart: 0, yStart: 100, xCurrent: 0, yCurrent: 200, isCapturing: 1,
        };

        it('should return 2', () => {
          expect(calculateTouchLength(tInfo, swipeDir)).toBe(2);
        });
      });

      describe('when yStart = 100, yCurrent = 150, isCapturing = 1', () => {
        const tInfo: TouchInfo = {
          xStart: 0, yStart: 100, xCurrent: 0, yCurrent: 150, isCapturing: 1,
        };

        it('should return 0', () => {
          expect(calculateTouchLength(tInfo, swipeDir)).toBe(0);
        });
      });
    });
  });
});

describe('updateTouchLength', () => {
  const setTouchLengthMock = jest.fn();
  beforeEach(() => { setTouchLengthMock.mockClear(); });

  describe('when touchLength <= 0', () => {
    const tLength = 0;

    it('should not call setTouchLength', () => {
      updateTouchLength(tLength, setTouchLengthMock);
      expect(setTouchLengthMock).toBeCalledTimes(0);
    });
  });

  describe('when touchLength > 0', () => {
    const tLength = 1;

    it('should call setTouchLength with touchLength', () => {
      updateTouchLength(tLength, setTouchLengthMock);
      expect(setTouchLengthMock).toBeCalledTimes(1);
      expect(setTouchLengthMock).toBeCalledWith(tLength);
    });
  });
});

describe('createTouchInfoOnStart', () => {
  describe('when window.getSelection returns text selection', () => {
    const e = createTouchEventMock(100);

    beforeEach(() => {
      getSelectionMock.mockReturnValue({
        type: 'Range',
        toString: () => 'a',
      } as Selection);
    });

    it('should return an initial state of TouchInfo', () => {
      expect(createTouchInfoOnStart(e)).toStrictEqual({
        xStart: 0, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: false,
      });
    });
  });

  describe('when window.getSelection returns no text selections', () => {
    beforeEach(() => {
      getSelectionMock.mockReturnValue(null);
    });

    describe('when the event is TouchEvent', () => {
      const e = createTouchEventMock(100);

      it('should return a TouchInfo with clientX', () => {
        expect(createTouchInfoOnStart(e)).toStrictEqual({
          xStart: 100, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: true,
        });
      });
    });

    describe('when the event is MouseEvent', () => {
      const e = createMouseEventMock(100);

      it('should return a TouchInfo with clientX', () => {
        expect(createTouchInfoOnStart(e)).toStrictEqual({
          xStart: 100, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: true,
        });
      });
    });
  });
});

describe('createTouchInfoOnEnd', () => {
  it('should return an initial TouchInfo', () => {
    expect(createInitialTouchInfo()).toStrictEqual({
      xStart: 0, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: false,
    });
  });
});

describe('createTouchInfoOnStart', () => {
  describe('when touchInfo.isCapturing is not true', () => {
    it('should return the touchInfo as is', () => {
      expect(detectAxis({ isCapturing: false } as TouchInfo)).toStrictEqual({ isCapturing: false });
      expect(detectAxis({ isCapturing: 1 } as TouchInfo)).toStrictEqual({ isCapturing: 1 });
      expect(detectAxis({ isCapturing: 2 } as TouchInfo)).toStrictEqual({ isCapturing: 2 });
    });
  });

  describe('when touchInfo.isCapturing is true', () => {
    const isCapturing = true;

    describe('when diffY >= 25', () => {
      const yCurrent = 25;
      const yStart = 0;

      it('should return { isCapturing: 2 }', () => {
        const tInfo = { yStart, yCurrent, isCapturing } as TouchInfo;

        expect(detectAxis(tInfo)).toStrictEqual({
          ...tInfo,
          isCapturing: 2,
        });
      });
    });

    describe('when diffY < 25', () => {
      const yCurrent = 24;
      const yStart = 0;

      describe('when diffX >= 25', () => {
        const xCurrent = 25;
        const xStart = 0;

        it('should return { isCapturing: 1 }', () => {
          const tInfo = {
            xStart, xCurrent, yStart, yCurrent, isCapturing,
          } as TouchInfo;

          expect(detectAxis(tInfo)).toStrictEqual({
            ...tInfo,
            isCapturing: 1,
          });
        });
      });

      describe('when diffX < 25', () => {
        const xCurrent = 24;
        const xStart = 0;

        it('should return the touchInfo as is', () => {
          const tInfo = {
            xStart, xCurrent, yStart, yCurrent, isCapturing,
          } as TouchInfo;

          expect(detectAxis(tInfo)).toStrictEqual(tInfo);
        });
      });
    });
  });
});

describe('createTouchInfoOnMove', () => {
  const setTouchLengthMock = jest.fn();
  const onSwipeBackMock = jest.fn();

  beforeEach(() => {
    setTouchLengthMock.mockClear();
  });

  describe('when window.getSelection returns text selection', () => {
    beforeEach(() => {
      getSelectionMock.mockReturnValue({
        type: 'Range',
        toString: () => 'a',
      } as Selection);
    });

    it('should return an initial state of TouchInfo', () => {
      expect(createTouchInfoOnMove(
        createTouchEventMock(0),
        {
          xStart: 1, yStart: 0, xCurrent: 2, yCurrent: 0, isCapturing: true,
        },
        0,
        setTouchLengthMock,
      )).toStrictEqual(
        {
          xStart: 0, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: false,
        },
      );
      expect(setTouchLengthMock).toBeCalledTimes(1);
      expect(setTouchLengthMock).toBeCalledWith(0);
    });
  });

  describe('when window.getSelection returns no text selections', () => {
    beforeEach(() => {
      getSelectionMock.mockReturnValue(null);
    });

    describe('when the touchInfo.isCapturing is false', () => {
      it('should return touchInfo as is', () => {
        expect(
          createTouchInfoOnMove(
            createTouchEventMock(0),
            {
              xStart: 1, yStart: 0, xCurrent: 2, yCurrent: 0, isCapturing: false,
            },
            0,
            setTouchLengthMock,
          ),
        ).toStrictEqual(
          {
            xStart: 1, yStart: 0, xCurrent: 2, yCurrent: 0, isCapturing: false,
          },
        );
        expect(setTouchLengthMock).toBeCalledTimes(0);
        expect(onSwipeBackMock).toBeCalledTimes(0);
      });
    });

    describe('when the touchInfo.isCapturing is true', () => {
      const tInfo = {
        xStart: 0, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: true,
      };

      describe('when window.innerWidth = 1000', () => {
        beforeEach(() => {
          Object.defineProperty(global.window, 'innerWidth', {
            value: 1000,
          });
        });

        // touchEventとMouseEventでそれぞれ同じテストを実施する
        [
          { fn: createTouchEventMock, name: 'TouchEvent' },
          { fn: createMouseEventMock, name: 'MouseEvent' },
        ].forEach((mocker) => {

          describe(`when the "e" is ${mocker.name}`, () => {
            let e = mocker.fn(100);
            beforeEach(() => { e = createTouchEventMock(100); });

            describe('when touchLength > 0', () => {
              const tLength = 1;

              it('should return a touchInfo whose xCurrent is updated', () => {
                expect(createTouchInfoOnMove(e, tInfo, tLength, setTouchLengthMock))
                  .toStrictEqual({
                    ...tInfo,
                    xCurrent: 100,
                    isCapturing: 1,
                  });
              });

              it('should not call e.preventDefault', () => {
                createTouchInfoOnMove(e, tInfo, tLength, setTouchLengthMock);
                expect(e.preventDefault).toBeCalledTimes(0);
              });

              it('should call setTouchLength with 2', () => {
                createTouchInfoOnMove(e, tInfo, tLength, setTouchLengthMock);
                expect(setTouchLengthMock).toBeCalledTimes(1);
                expect(setTouchLengthMock).toBeCalledWith(2);
              });
            });

            describe('when touchLength <= 0', () => {
              const tLength = 0;

              it('should return a touchInfo whose xCurrent is updated', () => {
                expect(createTouchInfoOnMove(e, tInfo, tLength, setTouchLengthMock))
                  .toStrictEqual({
                    ...tInfo,
                    xCurrent: 100,
                    isCapturing: 1,
                  });
              });

              it('should not call e.preventDefault', () => {
                createTouchInfoOnMove(e, tInfo, tLength, setTouchLengthMock);
                expect(e.preventDefault).toBeCalledTimes(0);
              });

              it('should call setTouchLength with 2', () => {
                createTouchInfoOnMove(e, tInfo, tLength, setTouchLengthMock);
                expect(setTouchLengthMock).toBeCalledTimes(1);
                expect(setTouchLengthMock).toBeCalledWith(2);
              });
            });
          });
        });

      });

    });
  });
});

describe('resetTouchLengthImpl', () => {
  const onSwipeBackMock = jest.fn();
  const setTouchLengthMock = jest.fn();

  beforeEach(() => {
    onSwipeBackMock.mockClear();
    setTouchLengthMock.mockClear();
  });

  function resetTouchLengthBasicCase(
    tLength: number, threshold: number, onSwipeBack?: () => void,
  ) {
    setTouchLengthMock.mockClear();
    expect(resetTouchLengthImpl(
      tLength,
      threshold,
      setTouchLengthMock,
      onSwipeBack,
    )).toStrictEqual({
      xStart: 0, yStart: 0, xCurrent: 0, yCurrent: 0, isCapturing: false,
    });
    expect(setTouchLengthMock).toBeCalledTimes(1);
    expect(setTouchLengthMock).toBeCalledWith(0);
  }

  describe('when onSwipeBack is unavailable', () => {
    it('should call setTouchLength and should return an initial TouchInfo', () => {
      resetTouchLengthBasicCase(0, 0, undefined);
    });
  });

  describe('when onSwipeBack is assigned', () => {

    describe('when touchLength <= threshold', () => {
      it('should not call onSwipeBack', () => {
        resetTouchLengthBasicCase(1, 1, onSwipeBackMock);
        resetTouchLengthBasicCase(1, 2, onSwipeBackMock);
        expect(onSwipeBackMock).toBeCalledTimes(0);
      });
    });

    describe('when touchLength > threshold', () => {
      it('should call onSwipeBack', () => {
        resetTouchLengthBasicCase(2, 1, onSwipeBackMock);
        expect(onSwipeBackMock).toBeCalledTimes(1);
      });
    });

  });
});

describe('useSwipeBackBehavior', () => {
  const onSwipeBackMock = jest.fn();

  beforeAll(() => {
    jest.useFakeTimers('legacy');
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  beforeEach(() => {
    onSwipeBackMock.mockClear();
  });

  describe('when window.innerWidth = 1000', () => {
    beforeEach(() => {
      Object.defineProperty(global.window, 'innerWidth', {
        value: 1000,
      });
    });

    async function useSwipeBackDirXCase(
      mocker: (n: number) => SyntheticEvent<HTMLElement, MouseEvent | TouchEvent>,
      threshold: number,
      startX: number,
      step1: { x: number, lengthToBe: number },
      step2: { x: number, lengthToBe: number },
    ) {
      const { result, waitForNextUpdate } = renderHook(
        // テスト用
        // eslint-disable-next-line react-hooks/rules-of-hooks
        () => useSwipeBackBehavior(threshold, onSwipeBackMock, 'x'),
      );
      const [, onTouchStart, onTouchMove] = result.current;
      const THROTTLE_MS = 10;

      expect(result.current[0]).toBe(0);

      await act(async () => {
        onTouchStart(mocker(startX));
        onTouchMove(mocker(step1.x));
        jest.advanceTimersByTime(THROTTLE_MS);
        expect(result.current[0]).toBe(step1.lengthToBe);

        onTouchMove(mocker(step2.x));

        const getSelectionResult = window.getSelection();

        if (getSelectionResult === null) {
          // テキスト選択中でない場合は状態が変わるので待機する
          await waitForNextUpdate();
        }

        jest.advanceTimersByTime(THROTTLE_MS);
        expect(result.current[0]).toBe(step2.lengthToBe);

        const [,,, onTouchEnd] = result.current;
        onTouchEnd(mocker(step2.x));

        jest.advanceTimersByTime(THROTTLE_MS);
        expect(result.current[0]).toBe(0);
      });
    }

    // touchEventとMouseEventでそれぞれ同じテストを実施する
    [
      { fn: createTouchEventMock, name: 'TouchEvent' },
      { fn: createMouseEventMock, name: 'MouseEvent' },
    ].forEach((mocker) => {
      describe(`when ${mocker.name}`, () => {

        describe('when window.getSelection returns text selection', () => {
          beforeEach(() => {
            getSelectionMock.mockReturnValue({
              type: 'Range',
              toString: () => 'a',
            } as Selection);
          });

          describe('when a user moves more than a length of the threshold', () => {
            it('should not call onSwipeBack', async () => {
              await useSwipeBackDirXCase(
                mocker.fn, 20, 100, { x: 200, lengthToBe: 0 }, { x: 390, lengthToBe: 0 },
              );
              expect(onSwipeBackMock).toBeCalledTimes(0);
            });
          });

        });

        describe('when window.getSelection returns no text selections', () => {
          beforeEach(() => {
            getSelectionMock.mockReturnValue(null);
          });

          describe('when a user moves less than or equal to a length of the threshold', () => {
            it('should not call onSwipeBack', async () => {
              await useSwipeBackDirXCase(
                mocker.fn, 20, 100, { x: 200, lengthToBe: 2 }, { x: 380, lengthToBe: 20 },
              );
              await useSwipeBackDirXCase(
                mocker.fn, 21, 100, { x: 200, lengthToBe: 2 }, { x: 390, lengthToBe: 21 },
              );
              expect(onSwipeBackMock).toBeCalledTimes(0);
            });
          });

          describe('when a user moves more than a length of the threshold', () => {
            it('should call onSwipeBack', async () => {
              await useSwipeBackDirXCase(
                mocker.fn, 20, 100, { x: 200, lengthToBe: 2 }, { x: 390, lengthToBe: 21 },
              );
              expect(onSwipeBackMock).toBeCalledTimes(1);
            });
          });
        });

      });
    });
  });
});
