import Mark, { MarkOptions } from 'mark.js';
import * as React from 'react';
import { removeDupMembers } from '../../utilities/array';

type ShowHighlighter = () => void;
type HideHighlighter = () => Promise<void>;

type UseHighlighterReturnType = [
  showHighlighter: ShowHighlighter,
  HideHighlighter: HideHighlighter,
];

/**
 * デフォルトのmark.jsオプション
 */
export const useHighlighterDefaultMarkOptions: MarkOptions = {
  // ハイライトされた要素に適用されるCSSクラス名
  className: 'highlight-search-words',
  // スペースがある場合別の単語として認識
  separateWordSearch: true,
  // 段落が異なる<p>タグに分かれている場合でも、単語が見つかればハイライト
  acrossElements: true,
};

/**
 * ISearchWordsから検索キーワードの配列を生成する
 * @param words
 * @param includeSynonyms
 */
export function createKeywords(words: string): string {
  // split(' ')で検索文字列を空白で分割し、単語の配列にする
  // join(' ')で除去された重複を含まない単語の配列を、再び文字列に結合する
  // replaceで全角スペースを半角スペースに置換する
  // eslint-disable-next-line no-irregular-whitespace
  return removeDupMembers(words.split(' ')).join(' ').replace(/　/g, ' ');
}

/**
 * useHighlighter機能
 * @param selector ハイライトの対象となる領域のCSSセレクタ
 * @param words 検索するキーワード (ISearchWords型)
 * @param includeSynonyms trueでwords.synonymsをキーワードに含む
 * @param [markJsOptions] mark.jsオプション デフォルト設定値と結合される
 */
const useHighlighterBehavior = (
  selector: string,
  words: string | undefined,
  includeSynonyms: boolean,
  markJsOptions?: MarkOptions,
): UseHighlighterReturnType => {

  // デフォルト設定とマージ
  const [options] = React.useState({
    ...useHighlighterDefaultMarkOptions,
    ...markJsOptions,
  });

  // インスタンスを生成
  const [mark] = React.useState(new Mark(selector));

  /**
   * ハイライトを表示
   */
  const showHighlight = React.useCallback(() => {
    if (!words) return;
    // AND,OR,括弧(),がハイライトされないように空白として返す
    const cleanedWords = words.replace(/(AND|OR|\(|\))/g, ' ');
    const keywords = createKeywords(cleanedWords);
    if (keywords.length === 0) return;
    // TODO:半角がハイライトされないということはここで半角が外れている
    mark.mark(keywords, options);
  }, [mark, options, words]);

  /**
   * ハイライトを隠す
   */
  const hideHighlight = React.useCallback(() => new Promise<void>((resolve) => {
    mark.unmark({
      done: () => {
        resolve();
      },
    });
  }), [mark]);

  return [showHighlight, hideHighlight];
};

export default useHighlighterBehavior;
