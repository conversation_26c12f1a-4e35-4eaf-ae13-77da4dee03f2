import * as React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ListMode } from '../../components/domains/split-view/types/ListMode';
import useInnerHtmlClickBehavior from './useInnerHtmlClickBehavior';

describe('useInnerHtmlClickBehavior', () => {

  const onClickMock = jest.fn();

  // テスト用コードなので定義不要
  /* eslint-disable react/prop-types */
  const TestComponent: React.FC<{
    query: string,
    trigger: boolean,
    html: string,
  }> = (props) => {
    const {
      query,
      trigger,
      html,
    } = props;
    const [listMode, setListMode] = React.useState(ListMode.INITIAL_DISPLAY);
    const handleListMode = React.useCallback(() => {
      setListMode(ListMode.BOOKMARKS);
    }, []);
    const [$ref] = useInnerHtmlClickBehavior<HTMLDivElement>(query, trigger, listMode, (e, $el) => {
      e.preventDefault();
      const $e = $el as unknown as HTMLLinkElement;
      onClickMock($e.href);
    });
    return (
      <div>
        {/* innerHTMLを用いる機能なので必要 */}
        {/* eslint-disable-next-line react/no-danger */}
        <div ref={$ref} dangerouslySetInnerHTML={{ __html: html }} />
        <input type="button" onClick={handleListMode} />
      </div>
    );
  };

  function renderComponent(
    html: string, query: string, trigger: boolean,
  ) {
    return render(<TestComponent
      html={html}
      query={query}
      trigger={trigger}
    />);
  }

  beforeEach(() => {
    onClickMock.mockClear();
  });

  it('should trigger onClick for each innerHTML links', async () => {
    const { container } = renderComponent(
      '<a href="#test1" data-testid="test-1">test1</a><br /><a href="#test2" data-testid="test-2">test2</a>',
      'a',
      true,
    );
    expect(await screen.findByTestId('test-1')).toBeInTheDocument();

    const $links = container.querySelectorAll('div a');
    expect($links.length).toBe(2);

    if (!$links[0]) return;
    fireEvent($links[0], new MouseEvent('click'));
    expect(onClickMock).toBeCalledTimes(1);
    expect(onClickMock).toHaveBeenLastCalledWith('http://localhost/#test1');

    if (!$links[1]) return;
    fireEvent($links[1], new MouseEvent('click'));
    await screen.findByTestId('test-2');
    expect(onClickMock).toBeCalledTimes(2);
    expect(onClickMock).toHaveBeenLastCalledWith('http://localhost/#test2');
  });

  it('should not fire onClick if the trigger is false', async () => {
    const { container } = renderComponent(
      '<a href="#test1" data-testid="test-1">test1</a>',
      'a',
      false,
    );
    expect(await screen.findByTestId('test-1')).toBeInTheDocument();

    const $links = container.querySelectorAll('div a');
    expect($links.length).toBe(1);

    if (!$links[0]) return;
    fireEvent($links[0], new MouseEvent('click'));
    expect(onClickMock).toBeCalledTimes(0);
  });

  it('should remove listeners after unmounting', async () => {
    const { container, unmount } = renderComponent(
      '<a href="#test1" data-testid="test-1">test1</a>',
      'a',
      true,
    );
    expect(await screen.findByTestId('test-1')).toBeInTheDocument();

    const $links = container.querySelectorAll('div a');
    expect($links.length).toBe(1);
    if (!$links[0]) return;

    // クリック直前でアンマウント
    unmount();

    // アンマウント後にはリスナが削除されているので発火しない
    fireEvent($links[0], new MouseEvent('click'));
    expect(onClickMock).toBeCalledTimes(0);
  });

  it('should trigger when switching list mode', async () => {
    const { container, unmount } = renderComponent(
      '<a href="#test1" data-testid="test-1">test1</a>',
      'a',
      true,
    );
    expect(await screen.findByTestId('test-1')).toBeInTheDocument();

    const $links = container.querySelectorAll('div a');
    expect($links.length).toBe(1);

    if (!$links[0]) return;
    fireEvent($links[0], new MouseEvent('click'));
    expect(onClickMock).toBeCalledTimes(1);
    expect(onClickMock).toHaveBeenLastCalledWith('http://localhost/#test1');

    // 一度アンマウントする
    unmount();
    fireEvent($links[0], new MouseEvent('click'));
    expect(onClickMock).toBeCalledTimes(1);

    const $button = container.querySelectorAll('div input');
    if (!$button[0]) return;
    fireEvent($button[0], new MouseEvent('click'));
    fireEvent($links[0], new MouseEvent('click'));
    expect(onClickMock).toBeCalledTimes(2);
    expect(onClickMock).toHaveBeenLastCalledWith('http://localhost/#test1');
  });

});
