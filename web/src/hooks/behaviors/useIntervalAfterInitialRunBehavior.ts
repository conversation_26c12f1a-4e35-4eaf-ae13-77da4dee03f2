import * as React from 'react';
import { useInterval } from 'react-use';

/**
 * 初回実行とインターバル実行のためのカスタムフック
 * @param interval 初回取得以降のインターバル(msec)/nullのときは動作しない
 * @param initialRunTrigger 状態を監視してtrueのときにだけ初回実行
 * @param callback 初回実行/インターバル実行されるcallback
 */
const useIntervalAfterInitialRunBehavior = (
  interval: number | null,
  initialRunTrigger: boolean,
  callback: (onSuccess?: () => void) => void,
): void => {

  const [intervalDelay, setIntervalDelay] = React.useState<number | null>(null);

  // インターバル取得
  // intervalDelayがnullのときは動作しない
  useInterval(callback, intervalDelay);

  // 初回取得処理が既に実行されているかどうか
  const isAlreadyInitialRunExecuted = React.useMemo(() => intervalDelay != null, [intervalDelay]);

  // 初回取得処理の実行
  React.useEffect(() => {
    if (!interval) return;
    if (!initialRunTrigger || isAlreadyInitialRunExecuted) return;

    // 初回取得成功後にインターバル処理を開始
    callback(() => setIntervalDelay(interval));
  }, [callback, initialRunTrigger, isAlreadyInitialRunExecuted, interval]);

  // インターバル取得の時間が変更された場合の処理
  React.useEffect(() => {
    if (interval === intervalDelay || !isAlreadyInitialRunExecuted) return;

    // 取得タイミングを上書く
    setIntervalDelay(interval);
  }, [interval, intervalDelay, isAlreadyInitialRunExecuted]);
};

export default useIntervalAfterInitialRunBehavior;
