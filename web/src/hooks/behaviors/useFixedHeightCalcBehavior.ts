import * as React from 'react';
import { useThrottle } from 'react-use';
import { isPC, isSP } from '../../utilities/mediaQuery';

type UseFixedHeightCalcReturnType = [
  $componentRef: React.Ref<HTMLDivElement>,
  $fixedElemRef: React.Ref<HTMLDivElement>,
  $staticElemRef: React.Ref<HTMLDivElement>,
  isFixedHeader: boolean,
];

/**
 * 見出しエリアの高さに応じて固定／非固定の制御をする機能
 * @param threshold
 * @param watchValueToInit
 */
const useFixedHeightCalcBehavior = (
  threshold: number,
  watchValueToInit: boolean,
): UseFixedHeightCalcReturnType => {

  // trueで固定状態になる
  // PCのときはデフォルト値を固定するためにtrueをセット
  // SPのときはデフォルト値を非固定にするfalseをセット
  const [isFixedHeader, setIsFixedHeader] = React.useState(isPC());
  const isFixedHeaderRef = React.useRef(true);

  // windowサイズが変更されたら更新される値
  const [winSize, setWinSize] = React.useState(`${window.innerHeight}${window.innerWidth}` ?? '');

  // 固定判定する倍率
  const multiply = React.useRef(threshold);

  // $fixedHeaderRefの高さ > ($componentRef * multiply) のときにヘッダー非固定へ切り替え
  // $staticHeaderRefの高さ < ($componentRef * multiply) のときにヘッダー固定へ切り替え
  const $componentRef = React.useRef<HTMLDivElement>(null);
  const $fixedHeaderRef = React.useRef<HTMLDivElement>(null);
  const $staticHeaderRef = React.useRef<HTMLDivElement>(null);

  const switchLayoutByHeaderHeight = React.useCallback(() => {
    const componentHeight = $componentRef.current?.offsetHeight;

    if (!componentHeight) return;

    // 固定レイアウト → 非固定レイアウトへの判定
    if (isFixedHeaderRef.current) {
      if (!$fixedHeaderRef.current) return;

      const headerHeight = $fixedHeaderRef.current.offsetHeight;

      // スマホサイズのときは常に非固定 || $fixedHeaderRefの高さ > ($componentRef * multiply) のときにヘッダー非固定へ切り替え
      if (isSP() || headerHeight > componentHeight * multiply.current) {
        setIsFixedHeader(false);
        isFixedHeaderRef.current = false;
      }

    // 非固定レイアウト → 固定レイアウトへの判定
    } else {
      // スマホサイズのときは常に非固定を維持したいのでreturn
      if (isSP() || !$staticHeaderRef.current) return;

      const headerHeight = $staticHeaderRef.current.offsetHeight;

      // $staticHeaderRefの高さ < ($componentRef * multiply) のときにヘッダー固定へ切り替え
      if (headerHeight < componentHeight * multiply.current) {
        setIsFixedHeader(true);
        isFixedHeaderRef.current = true;
      }
    }
  }, []);

  const onResize = React.useCallback(() => {
    setWinSize(`${window.innerHeight}${window.innerWidth}`);
  }, []);

  // 初回実行
  React.useEffect(() => {
    if (!watchValueToInit) return;
    switchLayoutByHeaderHeight();
  }, [switchLayoutByHeaderHeight, watchValueToInit]);

  // ウィンドウリサイズ監視を付与
  const isListenerAttached = React.useRef(false);
  React.useEffect(() => {
    if (isListenerAttached.current) return;
    window.addEventListener('resize', onResize);
    isListenerAttached.current = true;
  }, [onResize]);

  // winSizeが変化したら1秒起きに再実行
  const throttledWinSize = useThrottle(winSize, 1000);
  const skipFirstRun = React.useRef(true);
  React.useEffect(() => {
    if (skipFirstRun.current) {
      skipFirstRun.current = false;
      return;
    }
    switchLayoutByHeaderHeight();
  }, [throttledWinSize, switchLayoutByHeaderHeight]);

  // アンマウント時処理
  const onUnmount = React.useCallback(() => {
    isListenerAttached.current = false;
    window.removeEventListener('resize', onResize);
  }, [onResize]);
  React.useEffect(() => onUnmount, [onUnmount]);

  return [$componentRef, $fixedHeaderRef, $staticHeaderRef, isFixedHeader];
};

export default useFixedHeightCalcBehavior;
