import { act, renderHook } from '@testing-library/react-hooks';
import dayjs from 'dayjs';
import useGraphApiRequestWorker, { composeChatRequest, composeMailRequest } from './useGraphApiRequestWorker';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { RequestQueueItem } from '../../types/RequestQueueItem';
// mock environment.ts
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 3,
    REACT_APP_RETRY_DEAFULT_TIME: 5000,
  },
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));

const mockProvider = jest.mock;

const sendRequestMock = jest.fn();

jest.mock('../accessors/useGraphApiAccessor', () => ({
  __esModule: true,
  sendRequests: (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _tokenProvider: WeakTokenProvider,
    _item: RequestQueueItem,
    _cancellationRef: React.MutableRefObject<boolean>,
  ) => (sendRequestMock()),
}));
const defaultMockValue = {
  responses: [],
  recoverable: [],
  errors: [],
  totalTooManyRequests: 0,
  tooManyRequests: [],
};
function mockDefaultResponse() {
  sendRequestMock.mockReturnValue(defaultMockValue);
}

describe('useGraphApiRequestWorker', () => {
  beforeEach(() => {
    sendRequestMock.mockClear();
  });

  it('should resolved', async () => {
    const resolveMock = jest.fn();
    const appendItemsMock = jest.fn();
    mockDefaultResponse();
    const requestObject = {
      pid: 'id',
      ids: ['id1'],
      kind: 'Mail',
      resolve: resolveMock,
      appendItems: appendItemsMock,
    };
    const { result } = renderHook(
      () => useGraphApiRequestWorker(
        { current: false },
        mockProvider as unknown as WeakTokenProvider,
      ),
    );
    await result.current.search(requestObject);

    expect(resolveMock).toHaveBeenCalled();
  });

  it('should guard search task ignition while searching', async () => {
    const resolveMock = jest.fn();
    const appendItemsMock = jest.fn();
    sendRequestMock.mockImplementation(
      () => new Promise(
        (resolve) => { setTimeout(() => { resolve(defaultMockValue); }, 1000); },
      ),
    );
    const requestObject = [1, 2].map((id) => ({
      pid: `id${id}`,
      ids: ['id1'],
      kind: 'Mail',
      resolve: resolveMock,
      appendItems: appendItemsMock,
    }));
    const { result } = await renderHook(
      () => useGraphApiRequestWorker(
        { current: false },
        mockProvider as unknown as WeakTokenProvider,
      ),
    );
    const { current } = result;
    await act(async () => {

      requestObject.forEach((item) => current.queue.enqueue(item));
      const before = new Date();
      const task = current.start();
      await current.start();
      const first = new Date();
      await task;
      const second = new Date();

      const skipped = (dayjs(first).diff(before).valueOf());
      const completed = (dayjs(second).diff(before).valueOf());
      expect(resolveMock).toHaveBeenCalledTimes(2);
      // 2回目がSkipされていれば1回目実行と2回目実行の間で1秒以上差が存在する
      expect(completed - skipped).toBeGreaterThan(1800);
    });
  });

  it('should not call resolve when cancellationRef is true', async () => {
    const resolveMock = jest.fn();
    mockDefaultResponse();
    const cancellationRef = { current: true }; // キャンセルフラグをtrueに設定

    const { result } = renderHook(
      () => useGraphApiRequestWorker(
        cancellationRef,
        mockProvider as unknown as WeakTokenProvider,
      ),
    );

    await act(async () => {
      await result.current.start(); // startを呼び出す
    });

    expect(resolveMock).not.toHaveBeenCalled(); // resolveが呼ばれないことを確認
  });

  describe('composeMailRequest', () => {
    it('should mail request id is unique in batch request set', () => {
      const request = composeMailRequest('mailId1', 1);
      expect(request).toStrictEqual({
        method: 'GET',
        url: '/me/messages/mailId1?$select=receivedDateTime,sentDateTime,hasAttachments,subject,lastModifiedDateTime,id,sender,from,toRecipients,ccRecipients,bccRecipients',
        id: 'mailId1|1',
      });
    });
  });

  describe('composeChatRequest', () => {
    it('should chat request id is unique in batch request set', () => {
      const request = composeChatRequest('teams|teamId1|chatId1|messageId1', 1);
      expect(request).toStrictEqual({
        method: 'GET',
        url: 'me/chats/chatId1/messages/messageId1',
        id: 'teams|teamId1|chatId1|messageId1|1',
      });
    });
  });
});
