import { onClickBookmarkSwitchButtonImpl } from './useBookmarkSwitchButtonBehavior';
import mockMatchMedia from '../../mocks/match-media';

jest.mock('../../utilities/environment');

// matchMediaのmock
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const matchMediaMock = mockMatchMedia();
describe('useBookmarkSwitchButtonBehavior', () => {
  describe('onClickBookmarkSwitchButtonImpl', () => {
    const dispatchMock = jest.fn();

    beforeEach(() => {
      dispatchMock.mockClear();
    });

    it('should be sort bookmark list', async () => {
      await expect(onClickBookmarkSwitchButtonImpl(
        [
          {
            id: 'ef8f1072-30bb-4492-a0b9-b0b4edb17ca8',
            kind: 'SPO',
            title: 'test-title01',
            note: 'category-1',
            displayDate: '2021-08-23T03:01:08.000Z',
            properties: {
              listUrl: 'listUrl',
              siteUrl: 'siteUrl',
              listId: 'listId',
              listName: 'listName',
              createdDate: 'created-date',
              updatedDate: 'updated-date',
              editLink: 'edit-link',
              hasAttachments: true,
            },
            reposUpdatedDate: '2021-08-23T03:01:08.000Z',
            reposCreatedDate: '2021-08-23T03:00:59.000Z',
          },
        ],
        dispatchMock,
      )).resolves.toBeUndefined();
      expect(dispatchMock).toBeCalledTimes(1);
    });
  });
});
