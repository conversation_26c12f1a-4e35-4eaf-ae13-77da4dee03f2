import { renderHook } from '@testing-library/react-hooks';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import useChatReactionBehavior from './useChatReactionBehavior';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { IChatReaction } from '../../types/IChatResponse';

// mock environment.ts
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_RETRY_COUNTS: 3,
    REACT_APP_RETRY_DEAFULT_TIME: 5000,
  },
}));

const fetchReactionUserNamesMock = jest.fn();

jest.mock('../accessors/useChatApiAccessor', () => ({
  __esModule: true,
  default: () => ({
    fetchReactionUserNames: fetchReactionUserNamesMock,
  }),
}));

// mock @avanade-teams/auth
jest.mock('@avanade-teams/auth', () => ({
  createGraphClient: jest.fn(),
}));
const mockProvider = jest.mock;
const reportEventMock = jest.fn();
const cancellationRefMock = { current: false };

describe('useChatReactionBehavior', () => {
  function getHook() {
    return renderHook(
      () => useChatReactionBehavior(
        mockProvider as unknown as WeakTokenProvider,
        cancellationRefMock,
        reportEventMock,
      ),
    );
  }

  beforeEach(() => {
    fetchReactionUserNamesMock.mockClear();
    reportEventMock.mockClear();
  });

  describe('groupUsersByReaction', () => {

    it('should group users by reactions', () => {
      const hook = getHook().result.current;

      const users = [
        { id: 'id-1', displayName: 'user1' }, { id: 'id-2', displayName: 'user2' }, { id: 'id-3', displayName: 'user3' },
      ];

      const reactions = [
        {
          reactionType: 'like',
          creationDate: '',
          user: {
            user: {
              id: 'id-1',
            },
          },
        },
        {
          reactionType: 'like',
          creationDate: '',
          user: {
            user: {
              id: 'id-2',
            },
          },
        },
        {
          reactionType: '💯',
          creationDate: '',
          user: {
            user: {
              id: 'id-3',
            },
          },
        },
      ] as IChatReaction[];

      const result = hook.groupUsersByReaction(users, reactions);
      expect(result).toStrictEqual([
        { reactionType: 'like', users: [{ id: 'id-1', displayName: 'user1' }, { id: 'id-2', displayName: 'user2' }] },
        { reactionType: '💯', users: [{ id: 'id-3', displayName: 'user3' }] },
      ]);
      expect(reportEventMock).not.toHaveBeenCalled();
    });

    it('should omit users who was not found', () => {
      const hook = getHook().result.current;

      const users = [
        { id: 'id-2', displayName: 'user2' }, { id: 'id-3', displayName: 'user3' },
      ];

      const reactions = [
        {
          reactionType: 'like',
          creationDate: '',
          user: {
            user: {
              id: 'id-1',
            },
          },
        },
        {
          reactionType: 'like',
          creationDate: '',
          user: {
            user: {
              id: 'id-2',
            },
          },
        },
        {
          reactionType: '💯',
          creationDate: '',
          user: {
            user: {
              id: 'id-3',
            },
          },
        },
      ] as IChatReaction[];

      const result = hook.groupUsersByReaction(users, reactions);
      expect(result).toStrictEqual([
        { reactionType: 'like', users: [{ id: 'id-2', displayName: 'user2' }] },
        { reactionType: '💯', users: [{ id: 'id-3', displayName: 'user3' }] },
      ]);
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'REACTION_USERS_NOT_FOUND',
        customProperties: {
          notFoundUserIds: [{
            reactionType: 'like',
            userId: 'id-1',
          }],
        },
      });
    });
  });

  describe('fetchDisplayNames', () => {
    function createMockReaction(id: string, reactionType = 'like'): IChatReaction {
      return {
        reactionType,
        creationDate: new Date().toISOString(),
        user: {
          application: null,
          device: null,
          user: { id, displayName: null, userIdentityType: 'aadUser' },
        },
      };
    }

    it('should get users', async () => {
      fetchReactionUserNamesMock.mockImplementation(
        (reactions: IChatReaction[]) => Promise.resolve({
          responses: reactions.map((reaction, index) => ({ id: reaction.user.user.id, displayName: `user${index + 1}` })),
          errors: [],
          recoverable: [],
        }),
      );
      const hook = getHook().result.current;

      const result = await hook.fetchDisplayNames([createMockReaction('id-1'), createMockReaction('id-2')]);

      expect(fetchReactionUserNamesMock).toHaveBeenCalled();
      expect(result).toStrictEqual([{ id: 'id-1', displayName: 'user1' }, { id: 'id-2', displayName: 'user2' }]);
      expect(reportEventMock).not.toHaveBeenCalled();
    });

    it('should report batch request error to fetch users', async () => {
      fetchReactionUserNamesMock.mockReturnValue(
        Promise.resolve({
          responses: [],
          errors: [{ id: '1', status: 404 }],
          recoverable: [],
        }),
      );
      const hook = getHook().result.current;

      const result = await hook.fetchDisplayNames([createMockReaction('id-1'), createMockReaction('id-2')]);

      expect(fetchReactionUserNamesMock).toHaveBeenCalled();
      expect(result).toStrictEqual([]);
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'FETCH_REACTION_USERS_FAILED',
        customProperties: {
          kind: 'Chat',
          ids: [{ id: '1', status: 404 }],
          count: 1,
        },
      });
    });

    it('should report fetch limit exceeded', async () => {
      fetchReactionUserNamesMock.mockReturnValue(
        Promise.resolve({
          responses: [],
          errors: [],
          recoverable: [{ id: '1', status: 429 }],
        }),
      );
      const hook = getHook().result.current;

      const result = await hook.fetchDisplayNames([createMockReaction('id-1'), createMockReaction('id-2')]);

      expect(fetchReactionUserNamesMock).toHaveBeenCalled();
      expect(result).toStrictEqual([]);
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'FETCH_REACTION_USERS_RETRY_EXCEEDED',
        customProperties: {
          kind: 'Chat',
          ids: [{ id: '1', status: 429 }],
          count: 1,
        },
      });
    });

    it('should report fetch error', async () => {
      const error = new Error('some logical error has occurred');
      fetchReactionUserNamesMock.mockReturnValue(Promise.reject(error));
      const hook = getHook().result.current;

      const result = await hook.fetchDisplayNames([createMockReaction('id-1'), createMockReaction('id-2')]);

      expect(fetchReactionUserNamesMock).toHaveBeenCalled();
      expect(result).toStrictEqual(undefined);
      expect(reportEventMock).toHaveBeenCalledWith({
        type: EventReportType.SYS_ERROR,
        name: 'FETCH_REACTION_USERS_ERROR',
        error,
      });
    });
  });
});
