/* eslint-disable @typescript-eslint/no-unused-vars */
import * as React from 'react';
import { ISpecificResult } from '../../types/ISearchRequestResult';
import { IBatchResponseStatus, bulkResponseConverter } from '../accessors/useGraphApiAccessor';
import { IMailResponse } from '../../types/IMailResponse';
import { IChatResponse } from '../../types/IChatResponse';
import useGraphApiRequestWorker from './useGraphApiRequestWorker';
import { RequestQueueItem } from '../../types/RequestQueueItem';
import { WeakTokenProvider } from '../../types/TokenProvider';

export function convertToRequestObject(
  results: ISpecificResult,
  resolve: (value: IBatchResponseStatus<IMailResponse | IChatResponse>) => void,
  appendItems: ((items: (IChatResponse)[]) => void) | ((items: (IMailResponse)[]) => void),
  converter?: bulkResponseConverter<IChatResponse> | bulkResponseConverter<IMailResponse>,
): void | RequestQueueItem {
  if (!results.dataSource?.kind || !results.ids) return undefined;

  return {
    kind: results.dataSource.kind,
    pid: results.pid,
    ids: results.ids,
    resolve,
    appendItems,
    converter,
  } as RequestQueueItem;
}

/**
 * idsのセットを受け取ってリクエストオブジェクトを作成してエンキューする
 * 窓口になるフック
 */
const useGraphApiRequestQueue = (
  cancellationRef: React.MutableRefObject<boolean>,
  tokenProvider: WeakTokenProvider,
) => {
  const {
    queue,
    start,
  } = useGraphApiRequestWorker(cancellationRef, tokenProvider);
  const process = React.useCallback((
    results: ISpecificResult,
    appendItems: ((items: (IChatResponse)[]) => void) | ((items: (IMailResponse)[]) => void),
    converter?: bulkResponseConverter<IChatResponse> | bulkResponseConverter<IMailResponse>,
  ) => new Promise(
    (resolve: (value: IBatchResponseStatus<IMailResponse | IChatResponse>) => void) => {
      const requests = convertToRequestObject(results, resolve, appendItems, converter);
      if (!requests) return;
      queue.enqueue(requests);
      // tokenProviderが初期化されていない段階で動き出すタイミングがある
      if (tokenProvider) {
        start();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
  ), [tokenProvider]);

  return {
    process,
  };
};

export default useGraphApiRequestQueue;
