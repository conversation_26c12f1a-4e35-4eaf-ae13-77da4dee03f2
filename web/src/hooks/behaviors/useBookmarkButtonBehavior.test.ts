import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { UseRepositoryError } from '../accessors/useBookmarkRepositoryAccessor';
import { onClickBookmarkImpl } from './useBookmarkButtonBehavior';
import { DataSourceKind } from '../../types/DataSourceKind';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

jest.mock('../../utilities/environment');

describe('useBookmarkButtonBehavior', () => {
  describe('onClickBookmarkImpl', () => {
    const addBookmarkMock = jest.fn();
    const deleteBookmarkMock = jest.fn();
    const onReportMock = jest.fn();
    const extendPopupTimerMock = jest.fn();

    beforeEach(() => {
      addBookmarkMock.mockClear();
      deleteBookmarkMock.mockClear();
      onReportMock.mockClear();
      extendPopupTimerMock.mockClear();
    });

    const bookmarkItemMock = {
      id: '123',
      kind: DataSourceKind.SPO,
      title: 'Title',
      note: '経営部',
      displayDate: new Date('2022-01-01').toISOString(),
      properties: {
        listUrl: 'listUrl',
        siteUrl: 'siteUrl',
        listId: 'listId',
        listName: 'listName',
        createdDate: new Date('2022-01-01').toISOString(),
        updatedDate: new Date('2022-01-01').toISOString(),
        hasAttachments: true,
        editLink: 'abcdefg',
      },
    };

    describe('when the toBe is true', () => {
      describe('addBookmark', () => {
        describe('when the addBookmark is unavailable', () => {
          it('should do nothing', async () => {
            await expect(onClickBookmarkImpl(
              undefined,
              deleteBookmarkMock,
              extendPopupTimerMock,
              onReportMock,
              bookmarkItemMock,
              true,
            )).resolves.toBeUndefined();
            expect(onReportMock).toBeCalledTimes(0);
            expect(extendPopupTimerMock).toBeCalledTimes(0);
          });
        });

        describe('when the addBookmark resolves', () => {
          it('should call addBookmark', async () => {
            addBookmarkMock.mockResolvedValueOnce('123');

            await expect(onClickBookmarkImpl(
              addBookmarkMock,
              deleteBookmarkMock,
              extendPopupTimerMock,
              onReportMock,
              bookmarkItemMock,
              true,
            )).resolves.toBeUndefined();

            expect(addBookmarkMock).toBeCalled();
            expect(onReportMock).toBeCalledWith({
              type: EventReportType.USER_EVENT,
              name: 'ADD_BOOKMARK',
              customProperties: expect.objectContaining({
                detail: expect.objectContaining({
                  id: '123',
                  kind: DataSourceKind.SPO,
                  properties: {
                    listUrl: 'listUrl',
                    siteUrl: 'siteUrl',
                    listId: 'listId',
                  },
                }),
              }),
            });
            expect(extendPopupTimerMock).toBeCalledTimes(1);
          });
        });

        describe('when the addBookmark rejects with MAX_BOOKMARKS error', () => {
          it('should report and should call extendPopupTimer', async () => {
            addBookmarkMock.mockRejectedValue(UseRepositoryError.MAX_BOOKMARKS);
            await expect(onClickBookmarkImpl(
              addBookmarkMock,
              deleteBookmarkMock,
              extendPopupTimerMock,
              onReportMock,
              bookmarkItemMock,
              true,
            )).resolves.toBeUndefined();
            expect(onReportMock).toBeCalledTimes(1);
            expect(onReportMock).toBeCalledWith(expect.objectContaining({
              name: 'MAX_BOOKMARKS',
            }));
            expect(extendPopupTimerMock).toBeCalledTimes(1);
          });
        });

        describe('when the addBookmark rejects', () => {
          it('should do nothing', async () => {
            addBookmarkMock.mockRejectedValue(new Error());
            await expect(onClickBookmarkImpl(
              addBookmarkMock,
              deleteBookmarkMock,
              extendPopupTimerMock,
              onReportMock,
              bookmarkItemMock,
              true,
            )).resolves.toBeUndefined();
            expect(onReportMock).not.toBeCalled();
            expect(extendPopupTimerMock).toBeCalledTimes(0);
          });
        });
      });
    });

    describe('when the tobe is false', () => {
      const itemId = 'itemId-001';

      describe('when the deleteBookmark is unavailable', () => {
        it('should do nothing', async () => {
          await expect(onClickBookmarkImpl(
            addBookmarkMock,
            undefined,
            extendPopupTimerMock,
            onReportMock,
            { id: itemId } as ISplitViewListSingle,
            false,
          )).resolves.toBeUndefined();

          expect(onReportMock).toBeCalledTimes(0);
          expect(extendPopupTimerMock).toBeCalledTimes(0);
        });
      });
      describe('when the deleteBookmark function resolves', () => {
        it('should call deleteBookmark and should resolve undefined', async () => {
          deleteBookmarkMock.mockReturnValueOnce(Promise.resolve());

          await expect(onClickBookmarkImpl(
            addBookmarkMock,
            deleteBookmarkMock,
            extendPopupTimerMock,
            onReportMock,
            { id: itemId } as ISplitViewListSingle,
            false,
          )).resolves.toBeUndefined();

          expect(deleteBookmarkMock).toBeCalledTimes(1);
          expect(extendPopupTimerMock).toBeCalledTimes(1);
        });
      });

      describe('when the deleteBookmark function rejects', () => {
        it('should do nothing', async () => {
          deleteBookmarkMock.mockRejectedValue(new Error());

          await expect(onClickBookmarkImpl(
            addBookmarkMock,
            deleteBookmarkMock,
            extendPopupTimerMock,
            onReportMock,
            { id: itemId } as ISplitViewListSingle,
            false,
          )).resolves.toBeUndefined();

          expect(extendPopupTimerMock).toBeCalledTimes(0);
        });
      });
    });
  });
});
