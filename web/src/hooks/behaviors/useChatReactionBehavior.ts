import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import React from 'react';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { IChatReaction } from '../../types/IChatResponse';
import { IGraphUser } from '../../types/IGraphUser';
import useChatApiAccessor from '../accessors/useChatApiAccessor';

const useChatReactionBehavior = (
  tokenProvider: WeakTokenProvider,
  cancellationRef: React.MutableRefObject<boolean>,
  reportEvent: EventReporter,
) => {
  const { fetchReactionUserNames } = useChatApiAccessor(tokenProvider);

  const fetchDisplayNames = React.useCallback(
    async (reactions: IChatReaction[]) => {
      if (!fetchReactionUserNames) return undefined;
      const result = await fetchReactionUserNames(reactions).catch((error) => {
        reportEvent({
          error,
          type: EventReportType.SYS_ERROR,
          name: 'FETCH_REACTION_USERS_ERROR',
        });
      });

      if (!result) return undefined;

      if (result.recoverable.length > 0) {
        const { recoverable } = result;
        reportEvent({
          type: EventReportType.SYS_ERROR,
          name: 'FETCH_REACTION_USERS_RETRY_EXCEEDED',
          customProperties: {
            kind: 'Chat',
            ids: recoverable.map((item) => ({
              id: item.id,
              status: item.status,
            })),
            count: recoverable.length,
          },
        });
      }
      if (result.errors.length > 0) {
        const { errors } = result;
        reportEvent({
          type: EventReportType.SYS_ERROR,
          name: 'FETCH_REACTION_USERS_FAILED',
          customProperties: {
            kind: 'Chat',
            ids: errors.map((item) => ({
              id: item.id,
              status: item.status,
            })),
            count: errors.length,
          },
        });
      }
      return result.responses as IGraphUser[];
    },
    [
      fetchReactionUserNames,
      reportEvent,
    ],
  );

  const groupUsersByReaction = React.useCallback(
    (users: IGraphUser[], reactions: IChatReaction[]) => {
      const notFoundUserIds: { userId: string, reactionType: string; }[] = [];
      const grouped = Object.entries(
        reactions.reduce((origin, reaction) => {
          const reactionsDictionary = { ...origin };
          const user = users.find(({ id }) => id === reaction.user.user.id);
          if (!user) {
            notFoundUserIds.push(
              { reactionType: reaction.reactionType, userId: reaction.user.user.id },
            );
            return reactionsDictionary;
          }
          if (!reactionsDictionary[reaction.reactionType]) {
            reactionsDictionary[reaction.reactionType] = [user];
          } else {
            reactionsDictionary[reaction.reactionType].push(user);
          }
          return reactionsDictionary;
        },
          {} as { [key: string]: IGraphUser[] }),
      )
        .map(([reactionType, u]) => ({ reactionType, users: u }));
      if (notFoundUserIds.length > 0) {
        reportEvent({
          type: EventReportType.SYS_ERROR,
          name: 'REACTION_USERS_NOT_FOUND',
          customProperties: {
            notFoundUserIds,
          },
        });
      }
      return grouped;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [],
  );

  return {
    fetchDisplayNames,
    groupUsersByReaction,
  };
};

export default useChatReactionBehavior;
