import { RenderResult, renderHook } from '@testing-library/react-hooks';
import useFilterOptionBehavior, { UseFilterOptionBehaviorReturn } from './useFilterOptionBehavior';
import { SplitViewListMessage, SplitViewListView } from '../../components/domains/split-view/split-view-list/SplitViewList';
import { SplitViewDetailMessage, SplitViewDetailView } from '../../components/domains/split-view/split-view-detail/SplitViewDetail';

jest.mock('../../utilities/environment');

describe('useFilterOptionBehavior', () => {
  const state = {
    listView: SplitViewListView.LOADING,
    listMessage: SplitViewListMessage.BLANK,
    list: [],
    activeId: '',
    detailView: SplitViewDetailView.LOADING,
    detailMessage: SplitViewDetailMessage.BLANK,
    detail: undefined,
    context: {
      sort: [],
      filter: [],
    },
    inlineMailAttachments: [],
    chatAttachments: [],
  };
  const dispatch = jest.fn();
  function getHook() {
    return renderHook(() => useFilterOptionBehavior(state, dispatch));
  }

  let result: RenderResult<UseFilterOptionBehaviorReturn>;

  beforeEach(() => {
    result = getHook().result;
    dispatch.mockReset();
  });

  it('should dispatch filter options', () => {
    result.current.onSelectOption({
      key: 'displayDate',
      option: 2,
    });

    expect(dispatch).toHaveBeenCalledTimes(1);
    expect(dispatch).toHaveBeenCalledWith({
      type: 'SET_FILTER',
      payload: {
        filter: [
          { key: 'displayDate', option: 2 },
        ],
      },
    });
  });
});
