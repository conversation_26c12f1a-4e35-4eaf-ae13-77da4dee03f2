import * as React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { ListMode } from '../../components/domains/split-view/types/ListMode';
import { queryElem } from '../../utilities/test';
import { trimRestrictedPathPrefixWithParam } from '../../utilities/text';
import { FetchFileBlob } from '../accessors/useSharePointApiAccessor';
import useSpoImgLoaderFeature, {
  fetchFile, isActiveIdChanged,
  onUnmount,
  processImage,
  processImages,
  setErrorClass,
  setLoadingClass, setSrc,
  setSrcIfCacheAvailable, unsetLoadingClass,
} from './useSpoImgLoaderFeature';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import environment from '../../utilities/environment';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { ObjectUrlCache } from '../../types/ObjectUrlCache';

jest.mock('../../utilities/environment');

const sharePointSiteUrl = 'https://example.sharepoint.com/sites/abcd';

// classList.add/remove, dataset, setAttributeのmock
const classList = {
  add: jest.fn(),
  remove: jest.fn(),
};
const setAttributeMock = jest.fn();
const datasetMock: { [k: string]: string } = {};
const $el = {
  classList, setAttribute: setAttributeMock, dataset: datasetMock,
} as unknown as HTMLImageElement;

// create/revokeObjectURLのmock
const createObjectUrlMock = jest.fn();
URL.createObjectURL = createObjectUrlMock;
const revokeObjectUrlMock = jest.fn();
URL.revokeObjectURL = revokeObjectUrlMock;

beforeEach(() => {
  classList.add.mockClear();
  classList.remove.mockClear();
  setAttributeMock.mockClear();
  createObjectUrlMock.mockClear();
  revokeObjectUrlMock.mockClear();
});

describe('setLoadingClass', () => {
  it('should call $el.classList.add("is-loading")', () => {
    setLoadingClass($el);
    expect(classList.add).toBeCalledTimes(1);
    expect(classList.add).toBeCalledWith('is-loading');
  });
});

describe('unsetLoadingClass', () => {
  it('should call $el.classList.remove("is-loading")', () => {
    unsetLoadingClass($el);
    expect(classList.remove).toBeCalledTimes(1);
    expect(classList.remove).toBeCalledWith('is-loading');
  });
});

describe('setErrorClass', () => {
  it('should call $el.classList.add("is-error")', () => {
    setErrorClass($el);
    expect(classList.add).toBeCalledTimes(1);
    expect(classList.add).toBeCalledWith('is-error');
  });

  it('should call $el.classList.remove("is-loading")', () => {
    setErrorClass($el);
    expect(classList.remove).toBeCalledTimes(1);
    expect(classList.remove).toBeCalledWith('is-loading');
  });
});

describe('setSrc', () => {
  describe('when the src is "abc"', () => {
    const src = 'abc';

    it('should call $el.setAttribute("src", "abc")', () => {
      setSrc($el, src);
      expect(setAttributeMock).toBeCalledTimes(1);
      expect(setAttributeMock).toBeCalledWith('src', 'abc');
    });
  });
});

describe('isActiveIdChanged', () => {
  describe('when cache.activeId === activeIdRef.current', () => {
    it('should return false', () => {
      expect(isActiveIdChanged({ activeId: 'abc', objUrl: '' }, { current: 'abc' })).toBe(false);
      expect(isActiveIdChanged({ activeId: '', objUrl: '' }, { current: '' })).toBe(false);
    });
  });

  describe('when cache.activeId !== activeIdRef.current', () => {
    it('should return true', () => {
      expect(isActiveIdChanged({ activeId: 'abc', objUrl: '' }, { current: '' })).toBe(true);
      expect(isActiveIdChanged({ activeId: '', objUrl: '' }, { current: 'abc' })).toBe(true);
    });
  });
});

describe('setSrcIfCacheAvailable', () => {
  describe('when the cache is undefined', () => {
    it('should return false', () => {
      expect(setSrcIfCacheAvailable($el, undefined)).toBe(false);
      expect(setAttributeMock).toBeCalledTimes(0);
    });
  });

  describe('when the cache.objUrl is blank', () => {
    it('should return false', () => {
      expect(setSrcIfCacheAvailable($el, { objUrl: '', activeId: '' })).toBe(false);
      expect(setAttributeMock).toBeCalledTimes(0);
    });
  });

  describe('when the cache.objUrl is available', () => {
    it('should return false', () => {
      expect(setSrcIfCacheAvailable($el, { objUrl: 'abc', activeId: '' })).toBe(true);
      expect(setAttributeMock).toBeCalledTimes(1);
      expect(setAttributeMock).toBeCalledWith('src', 'abc');
    });
  });
});

describe('fetchFile', () => {
  const reportEvent = jest.fn();
  const spoSrc = '123';
  const fetchFileBlob = jest.fn();

  beforeEach(() => {
    reportEvent.mockClear();
    fetchFileBlob.mockClear();
    // environment.REACT_APP_SHAREPOINT_URL = 'https://example.sharepoint.com/sites/abcd';
  });

  describe('when the fetchFileBlob rejects', () => {
    function fetchFileBlobRejectCase() {
      fetchFileBlob.mockRejectedValue(new Error('abc'));
      return expect(
        fetchFile(
          fetchFileBlob,
          reportEvent,
          $el,
          spoSrc,
          { current: {} },
          { current: 'hij' },
          { current: sharePointSiteUrl },
        ),
      ).resolves.toBe(undefined);
    }

    it('should resolve undefined', async () => {
      await fetchFileBlobRejectCase();
    });

    it('should call reportEvent', async () => {
      await fetchFileBlobRejectCase();
      expect(reportEvent).toBeCalledTimes(1);
      expect(reportEvent).toBeCalledWith(expect.objectContaining({
        name: 'FETCH_FILE_BLOB_FAIL',
        type: EventReportType.SYS_EVENT,
        error: new Error('abc'),
        customProperties: {
          error: new Error('abc'),
          request: {
            kind: 'SPO',
            itemId: 'hij',
            siteUrl: 'https://example.sharepoint.com/sites/abcd',
            sourceUrl: '123',
          },
        },
      }));
    });

    it('should remove the "is-loading" and add the "is-error"', async () => {
      await fetchFileBlobRejectCase();

      expect(classList.remove).toBeCalledWith('is-loading');
      expect(classList.remove).toBeCalledTimes(1);

      expect(classList.add).toBeCalledWith('is-error');
      expect(classList.add).toBeCalledTimes(1);
    });

    it('should not change the src', async () => {
      await fetchFileBlobRejectCase();
      expect($el.setAttribute).toBeCalledTimes(0);
    });
  });

  describe('when the fetchFileBlob resolves', () => {
    function fetchFileBlobResolveCase(
      cachedActiveId: string,
      currentActiveId: string,
      returns: string | undefined,
    ) {
      fetchFileBlob.mockResolvedValue('mock-blob');

      const objectUrlCache: React.MutableRefObject<ObjectUrlCache> = {
        current: {
          [spoSrc]: { activeId: cachedActiveId, objUrl: '' },
        },
      };

      const activeIdCache = { current: currentActiveId };
      const sharePointSiteUrlCache = { current: sharePointSiteUrl };

      return expect(
        fetchFile(
          fetchFileBlob,
          reportEvent,
          $el,
          spoSrc,
          objectUrlCache,
          activeIdCache,
          sharePointSiteUrlCache,
        ),
      ).resolves.toBe(returns);
    }

    describe('when the activeId is changed', () => {
      const cachedActiveId = 'efg';
      const currentActiveId = '123';

      it('should return undefined', async () => {
        await expect(fetchFileBlobResolveCase(cachedActiveId, currentActiveId, undefined))
          .resolves.toBe(undefined);
      });

      it('should not change the src', async () => {
        await fetchFileBlobResolveCase(cachedActiveId, currentActiveId, undefined);
        expect($el.setAttribute).toBeCalledTimes(0);
      });

      it('should not change the className', async () => {
        await fetchFileBlobResolveCase(cachedActiveId, currentActiveId, undefined);
        expect(classList.remove).toBeCalledTimes(0);
        expect(classList.add).toBeCalledTimes(0);
      });
    });

    describe('when the activeId is not changed', () => {
      const cachedActiveId = 'efg';
      const currentActiveId = 'efg';

      beforeAll(() => {
        createObjectUrlMock.mockReturnValue('https://localhsot/#abcd');
      });

      it('should return what URL.createObjectURL returns', async () => {
        await fetchFileBlobResolveCase(cachedActiveId, currentActiveId, 'https://localhsot/#abcd');
      });

      it('should change the src with the object url', async () => {
        await fetchFileBlobResolveCase(cachedActiveId, currentActiveId, 'https://localhsot/#abcd');
        expect($el.setAttribute).toBeCalledTimes(1);
        expect($el.setAttribute).toBeCalledWith('src', 'https://localhsot/#abcd');
      });

      it('should remove the "is-loading" and not add the "is-error"', async () => {
        await fetchFileBlobResolveCase(cachedActiveId, currentActiveId, 'https://localhsot/#abcd');
        expect(classList.remove).toBeCalledWith('is-loading');
        expect(classList.remove).toBeCalledTimes(1);
        expect(classList.add).toBeCalledTimes(0);
      });
    });
  });
});

describe('processImage', () => {
  const fetchFileBlob = jest.fn();
  const activeId = 'abc';
  const cachedActiveId = 'abc';
  const currentActiveId = 'abc';
  const addObjUrlCacheEntry = jest.fn();
  const setObjUrlCache = jest.fn();

  beforeEach(() => {
    fetchFileBlob.mockClear();
    addObjUrlCacheEntry.mockClear();
    setObjUrlCache.mockClear();
  });

  async function processImageCase(dataValue: string, cachedObjUrl: string) {
    datasetMock.spoSrc = dataValue;

    const objectUrlCache: React.MutableRefObject<ObjectUrlCache> = {
      current: {
        [trimRestrictedPathPrefixWithParam(dataValue)]: {
          activeId: cachedActiveId, objUrl: cachedObjUrl,
        },
      },
    };
    const activeIdCache = { current: currentActiveId };
    const sharePointSiteUrlCache = { current: sharePointSiteUrl };

    await expect(processImage(
      $el,
      fetchFileBlob,
      jest.fn(),
      activeId,
      activeIdCache,
      sharePointSiteUrlCache,
      objectUrlCache,
      addObjUrlCacheEntry,
      setObjUrlCache,
    )).resolves.toBeUndefined();
  }

  describe('when the $el.dataset.spoSrc is falsy', () => {
    const dataValue = '';
    const cachedObjUrl = '';

    it('should resoles undefined', async () => {
      await processImageCase(dataValue, cachedObjUrl);
    });

    it('should not call $el.setAttribute', async () => {
      await processImageCase(dataValue, cachedObjUrl);
      expect($el.setAttribute).toBeCalledTimes(0);
    });
  });

  describe('when the $el.dataset.spoSrc has a value', () => {
    describe('when dataValue is normal', () => {
      const dataValue = 'abc';

      describe('when the objectUrlCache has an objectUrl already', () => {
        const cachedObjUrl = 'efg';

        it('should resolve undefined', async () => {
          await processImageCase(dataValue, cachedObjUrl);
        });

        it('should call $el.setAttribute with the object url', async () => {
          await processImageCase(dataValue, cachedObjUrl);
          expect($el.setAttribute).toBeCalledTimes(1);
          expect($el.setAttribute).toBeCalledWith('src', 'efg');
        });

        it('should not call addObjUrlCacheEntry', async () => {
          await processImageCase(dataValue, cachedObjUrl);
          expect(addObjUrlCacheEntry).toBeCalledTimes(0);
        });
      });

      describe('when the objectUrlCache does not have an objectUrl', () => {
        const cachedObjUrl = '';

        describe('when the fetchFile rejects', () => {
          beforeEach(() => {
            fetchFileBlob.mockRejectedValue(new Error('abc'));
          });

          it('should call addObjUrlCacheEntry', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(addObjUrlCacheEntry).toBeCalledTimes(1);
            expect(addObjUrlCacheEntry).toBeCalledWith(dataValue, activeId);
          });

          it('should set the "is-loading" class', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(classList.add).toBeCalledWith('is-loading');
          });

          it('should resolve undefined', async () => {
            await processImageCase(dataValue, cachedObjUrl);
          });

          it('should not call setObjUrlCache', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(setObjUrlCache).toBeCalledTimes(0);
          });
        });

        describe('when the fetchFile resolves string', () => {
          beforeEach(() => {
            fetchFileBlob.mockResolvedValue('mock-blob');
            createObjectUrlMock.mockReturnValue('abc');
          });

          it('should set the "is-loading" class', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(classList.add).toBeCalledWith('is-loading');
          });

          it('should resolve undefined', async () => {
            await processImageCase(dataValue, cachedObjUrl);
          });

          it('should call setObjUrlCache', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(setObjUrlCache).toBeCalledTimes(1);
            expect(setObjUrlCache).toBeCalledWith(dataValue, 'abc');
          });
        });
      });
    });

    describe('when dataValue is restricted url', () => {
      const dataValue = '/:i:/r/abc?cfs=123';

      describe('when the objectUrlCache has an objectUrl already', () => {
        const cachedObjUrl = 'efg';

        it('should resolve undefined', async () => {
          await processImageCase(dataValue, cachedObjUrl);
        });

        it('should call $el.setAttribute with the object url', async () => {
          await processImageCase(dataValue, cachedObjUrl);
          expect($el.setAttribute).toBeCalledTimes(1);
          expect($el.setAttribute).toBeCalledWith('src', 'efg');
        });

        it('should not call addObjUrlCacheEntry', async () => {
          await processImageCase(dataValue, cachedObjUrl);
          expect(addObjUrlCacheEntry).toBeCalledTimes(0);
        });
      });

      describe('when the objectUrlCache does not have an objectUrl', () => {
        const cachedObjUrl = '';

        describe('when the fetchFile rejects', () => {
          beforeEach(() => {
            fetchFileBlob.mockRejectedValue(new Error('abc'));
          });

          it('should call addObjUrlCacheEntry', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(addObjUrlCacheEntry).toBeCalledTimes(1);
            expect(addObjUrlCacheEntry).toBeCalledWith('/abc', activeId);
          });

          it('should set the "is-loading" class', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(classList.add).toBeCalledWith('is-loading');
          });

          it('should resolve undefined', async () => {
            await processImageCase('/abc', cachedObjUrl);
          });

          it('should not call setObjUrlCache', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(setObjUrlCache).toBeCalledTimes(0);
          });
        });

        describe('when the fetchFile resolves string', () => {
          beforeEach(() => {
            fetchFileBlob.mockResolvedValue('mock-blob');
            createObjectUrlMock.mockReturnValue('abc');
          });

          it('should set the "is-loading" class', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(classList.add).toBeCalledWith('is-loading');
          });

          it('should resolve undefined', async () => {
            await processImageCase(dataValue, cachedObjUrl);
          });

          it('should call setObjUrlCache', async () => {
            await processImageCase(dataValue, cachedObjUrl);
            expect(setObjUrlCache).toBeCalledTimes(1);
            expect(setObjUrlCache).toBeCalledWith('/abc', 'abc');
          });
        });
      });
    });
  });
});

describe('processImages', () => {
  const reportEvent = jest.fn();
  const fetchFileMock = jest.fn().mockRejectedValue(new Error('abc'));

  function processImagesCase(
    spoSrc: string,
    activeId: string,
    fetchFileBlob: FetchFileBlob | undefined,
    $element: null | HTMLElement,
    query: string,
    trigger: boolean,
  ) {
    datasetMock.spoSrc = spoSrc;

    const objectUrlCache: React.MutableRefObject<ObjectUrlCache> = {
      current: {
        [spoSrc]: { activeId, objUrl: '' },
      },
    };
    const activeIdCache = { current: activeId };
    const sharePointSiteUrlCache = { current: sharePointSiteUrl };

    return processImages(
      { current: $element },
      query,
      fetchFileBlob,
      reportEvent,
      trigger,
      activeId,
      activeIdCache,
      sharePointSiteUrlCache,
      objectUrlCache,
      jest.fn(),
      jest.fn(),
    );
  }

  describe('when the fetchFileBlob, trigger, or $elementToQuery.current is falsy', () => {
    it('should return []', () => {
      const mockElement = {} as HTMLElement;
      expect(processImagesCase('abc', '123', undefined, mockElement, 'abc', true)).toStrictEqual([]);
      expect(processImagesCase('abc', '123', fetchFileMock, null, 'abc', true)).toStrictEqual([]);
      expect(processImagesCase('abc', '123', fetchFileMock, mockElement, 'abc', false)).toStrictEqual([]);
    });
  });

  describe('when the querySelectorAll returns 3 elements', () => {
    it('should return an array which length is 3', () => {
      const mockElement = {
        querySelectorAll: jest.fn().mockReturnValue({
          0: $el, 1: $el, 2: $el, length: 3,
        }),
      } as unknown as HTMLElement;
      expect(processImagesCase('abc', '123', fetchFileMock, mockElement, 'abc', true)).toHaveLength(3);
    });
  });
});

describe('onUnmount', () => {
  describe('when cache has some objUrl', () => {
    const cache: React.MutableRefObject<ObjectUrlCache> = {
      current: {
        abc: {
          objUrl: '',
          activeId: 'ABC',
        },
        efg: {
          objUrl: '123',
          activeId: 'EFG',
        },
        hij: {
          objUrl: '456',
          activeId: 'HIJ',
        },
      },
    };

    it('should call revokeObjectURL', () => {
      onUnmount(cache);
      expect(revokeObjectUrlMock).toBeCalledTimes(2);
      expect(revokeObjectUrlMock).toBeCalledWith('123');
      expect(revokeObjectUrlMock).toBeCalledWith('456');
    });
  });
});

describe('useSpoImgLoaderFeature', () => {
  const fetchFileMock = jest.fn();
  const eventReportMock = jest.fn();
  createObjectUrlMock.mockReturnValue('/mock-url.jpg');

  const TestComponent = () => {
    const activeItem = {
      id: 'id1',
      kind: 'SPO',
      displayDate: '2021-08-24T03:01:08.000Z',
      title: 'title',
      note: 'category-1',
      properties: {
        listName: '会社からのお知らせ',
        editLink: 'abc',
        listUrl: 'aaa',
        siteUrl: 'bbb',
        listId: 'ccc',
        createdDate: '2021-08-24T03:00:59.000Z',
        updatedDate: '2021-08-24T03:01:08.000Z',
        hasAttachments: false,
      },
    } as ISplitViewListSingle;
    const $div = React.useRef<HTMLDivElement>(null);
    const [listMode, setListMode] = React.useState(ListMode.INITIAL_DISPLAY);
    const handleListMode = React.useCallback(() => {
      setListMode(ListMode.BOOKMARKS);
    }, []);
    useSpoImgLoaderFeature(
      $div,
      'img',
      fetchFileMock,
      eventReportMock,
      true,
      activeItem,
      'https://example.sharepoint.com/sites/abcd',
      listMode,
    );

    return (
      <div ref={$div}>
        <img src="abc.png" data-spo-src="/sites/image.png" alt="" />
        <input type="button" onClick={handleListMode} />
      </div>
    );
  };

  beforeEach(() => {
    fetchFileMock.mockClear();
    eventReportMock.mockClear();
  });

  describe('when the fetch resolves', () => {
    beforeEach(() => {
      fetchFileMock.mockResolvedValue('mock-blob');
    });

    // 結果が不安定なので現状skip, 単体で走ると動くが、通しで走らせると動かない
    it.skip('should change the src with object-url', async () => {
      const { container } = render(<TestComponent />);
      await waitFor(() => expect(queryElem(container, 'img')).toHaveClass('is-loading'));
      await waitFor(() => expect(queryElem(container, 'img')).not.toHaveClass('is-loading'));
      expect(queryElem(container, 'img')).toContainHTML('mock-url.jpg');
    });

    it('should trigger when switching list mode', async () => {
      createObjectUrlMock.mockReturnValueOnce('/mock-url-latest.jpg');
      const { container, unmount } = render(<TestComponent />);
      await waitFor(() => expect(queryElem(container, 'img')).toHaveClass('is-loading'));
      await waitFor(() => expect(queryElem(container, 'img')).not.toHaveClass('is-loading'));
      expect(queryElem(container, 'img')).toContainHTML('mock-url-latest.jpg');

      // 一度アンマウントする
      unmount();
      expect(revokeObjectUrlMock).toBeCalledTimes(1);

      const $button = container.querySelectorAll('div input');
      if (!$button[0]) return;
      fireEvent($button[0], new MouseEvent('click'));

      await waitFor(() => expect(queryElem(container, 'img')).toHaveClass('is-loading'));
      await waitFor(() => expect(queryElem(container, 'img')).not.toHaveClass('is-loading'));
      expect(queryElem(container, 'img')).toContainHTML('mock-url.jpg');
    });

    describe('when unmounted', () => {
      it('should call revokeObjectURL', async () => {
        const { container, unmount } = render(<TestComponent />);
        await waitFor(() => expect(queryElem(container, 'img')).toHaveClass('is-loading'));
        await waitFor(() => expect(queryElem(container, 'img')).not.toHaveClass('is-loading'));
        unmount();
        expect(revokeObjectUrlMock).toBeCalledTimes(1);
      });
    });
  });

  describe('when the fetch rejects', () => {
    beforeEach(() => {
      fetchFileMock.mockRejectedValue(new Error('abc'));
    });

    it('should set the "is-error" class', async () => {
      const { container } = render(<TestComponent />);
      await waitFor(() => expect(queryElem(container, 'img')).toHaveClass('is-error'));
    });

    it('should call the eventReporter', async () => {
      const { container } = render(<TestComponent />);
      await waitFor(() => expect(queryElem(container, 'img')).toHaveClass('is-error'));
      expect(eventReportMock).toHaveBeenCalledTimes(1);
      expect(eventReportMock).toHaveBeenCalledWith(expect.objectContaining({
        type: EventReportType.SYS_EVENT,
        name: 'FETCH_FILE_BLOB_FAIL',
        error: new Error('abc'),
        customProperties: {
          error: new Error('abc'),
          request: {
            kind: 'SPO',
            itemId: 'id1',
            siteUrl: 'https://example.sharepoint.com/sites/abcd',
            sourceUrl: '/sites/image.png',
          },
        },
      }));
    });
  });
});
