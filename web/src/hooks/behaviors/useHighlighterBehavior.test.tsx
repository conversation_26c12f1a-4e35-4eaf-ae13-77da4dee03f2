import { MarkOptions } from 'mark.js';
import * as React from 'react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import { Simulate } from 'react-dom/test-utils';
import { queryElem } from '../../utilities/test';
import useHighlighterBehavior, { useHighlighterDefaultMarkOptions } from './useHighlighterBehavior';

describe('useHighlighterDefaultMarkOptions', () => {
  // デフォルト設定値の検証

  it('should have className "highlight-search-words"', () => {
    expect(useHighlighterDefaultMarkOptions.className).toBe('highlight-search-words');
  });

  it('should have separateWordSearch: true', () => {
    expect(useHighlighterDefaultMarkOptions.separateWordSearch).toBe(true);
  });

  it('should have acrossElements: true', () => {
    expect(useHighlighterDefaultMarkOptions.acrossElements).toBe(true);
  });
});

describe('useHighlighterBehavior', () => {
  interface TestProps {
    words: string | undefined;
    includeSynonyms: boolean;
    markJsOptions?: MarkOptions;
  }

  // テスト用コンポーネントなので不要
  /* eslint-disable react/prop-types */
  const TestComponent: React.FC<TestProps> = (props) => {
    const {
      words, includeSynonyms, markJsOptions, children,
    } = props;

    const [show, hide] = useHighlighterBehavior(
      '#test-ground', words, includeSynonyms, markJsOptions,
    );

    return (
      <div>
        <button id="button-show" onClick={show} type="button">show</button>
        <button id="button-hide" onClick={hide} type="button">hide</button>
        <div id="test-ground">
          { children }
        </div>
      </div>
    );
  };

  function renderComponent(props: TestProps) {
    return render(<TestComponent {...props} />);
  }

  const children = (
    <>
      <span>abc</span>
      <span>efg</span>
    </>
  );

  describe('when words == undefined', () => {
    const words = undefined;
    const includeSynonyms = true;

    describe('when showHighlight is called', () => {
      it('should not do anything', () => {
        const props = { words, includeSynonyms, children };
        const { container } = renderComponent(props);
        Simulate.click(queryElem(container, '#button-show'));
        expect(() => queryElem(container, 'mark')).toThrow();
      });
    });

    describe('when hideHighlight is called', () => {
      it('should not do anything', () => {
        const props = { words, includeSynonyms, children };
        const { container } = renderComponent(props);
        Simulate.click(queryElem(container, '#button-hide'));
        expect(() => queryElem(container, 'mark')).toThrow();
      });
    });
  });

  describe('when words != undefined', () => {
    const words = 'abc';

    describe('when includeSynonyms == false', () => {
      const includeSynonyms = false;

      describe('when showHighlight is called', () => {
        const props = { words, includeSynonyms, children };

        it('should show markers with the default className', async () => {
          const { container } = renderComponent(props);
          Simulate.click(queryElem(container, '#button-show'));
          const marker = queryElem(container, 'mark');
          expect(marker).toBeInTheDocument();
          expect(marker).toHaveClass('highlight-search-words');
        });

        describe('when hideHighlight is called', () => {
          it('should remove marker elements', () => {
            const { container } = renderComponent(props);
            Simulate.click(queryElem(container, '#button-show'));
            expect(queryElem(container, 'mark')).toBeInTheDocument();

            Simulate.click(queryElem(container, '#button-hide'));
            expect(() => queryElem(container, 'mark')).toThrow();
          });
        });
      });
    });
  });

});
