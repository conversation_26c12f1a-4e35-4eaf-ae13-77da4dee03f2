import { renderHook } from '@testing-library/react-hooks';
import useIntervalAfterInitialRunBehavior from './useIntervalAfterInitialRunBehavior';

describe('useIntervalAfterInitialRunBehavior', () => {

  const callbackMock = jest.fn().mockImplementation(
    (onSuccess?: () => void) => onSuccess?.(),
  );

  function getRenderedHook(interval: number, initialRunTrigger: boolean) {
    const { result } = renderHook(() => useIntervalAfterInitialRunBehavior(
      interval,
      initialRunTrigger,
      callbackMock,
    ));
    return result.current;
  }

  beforeAll(() => {
    jest.useFakeTimers('legacy');
  });

  beforeEach(() => {
    callbackMock.mockClear();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('when initialRunTrigger is false', () => {
    it('should not exec callback', () => {
      getRenderedHook(30000, false);
      expect(callbackMock).toBeCalledTimes(0);
    });
  });

  describe('when initialRunTrigger is true', () => {
    it('should exec exec the first run', () => {
      getRenderedHook(30000, true);
      expect(callbackMock).toBeCalledTimes(1);
    });

    it('should exec second run after the interval', () => {
      getRenderedHook(30000, true);
      expect(callbackMock).toBeCalledTimes(1);

      // 時間をすすめる
      jest.advanceTimersByTime(30000);

      expect(callbackMock).toBeCalledTimes(2);
    });
  });
});
