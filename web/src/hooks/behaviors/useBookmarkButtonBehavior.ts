import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import * as React from 'react';
import {
  ExtendPopupTimer,
  UseMessageToasterBehaviorReturn,
} from './useMessageToasterBehavior';
import useComponentInitUtility from '../utilities/useComponentInitUtility';
import useRemoteBookmarkFeature, {
  AddRemoteBookmark, DeleteRemoteBookmark,
} from '../features/useRemoteBookmarkFeature';
import { UseRepositoryError } from '../accessors/useBookmarkRepositoryAccessor';
import { attachReposDateToISplitViewListSingle } from '../../utilities/transform';
import { composeAdditionalProperties } from '../../components/domains/utilities/logging/composeAdditionalProperties';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

type UseBookmarkButtonBehaviorReturn = [
  onClickBookmark: (item: ISplitViewListSingle, toBe: boolean) => Promise<void>,
];

/**
 * お気に入りを追加する
 * 同時にキューを作成してリモート送信を試みる
 *
 * @param {ISplitViewListSingle} item
 * @param {(AddBookmark | undefined)} addRemoteBookmark
 * @param extendPopupTimer
 * @param {EventReporter} reportEvent
 * @return {*}  {Promise<void>}
 */
export async function handleAddBookmark(
  item: ISplitViewListSingle,
  addRemoteBookmark: AddRemoteBookmark | undefined,
  extendPopupTimer: ExtendPopupTimer,
  reportEvent: EventReporter,
): Promise<void> {
  if (!addRemoteBookmark) return Promise.resolve(undefined);
  if (!item.id) return Promise.resolve(undefined);

  return addRemoteBookmark(item)
    // 異常系処理
    .catch((reason) => {
      if (reason === UseRepositoryError.MAX_BOOKMARKS) {
        // お気に入り最大件数エラー
        reportEvent({
          type: EventReportType.USER_EVENT,
          name: 'MAX_BOOKMARKS',
        });
        extendPopupTimer(null);
      }
      return null;
    })

    // 正常系処理
    .then((result) => {
      if (result === null) return undefined;

      reportEvent({
        type: EventReportType.USER_EVENT,
        name: 'ADD_BOOKMARK',
        customProperties: {
          bookmarkCreatedDate: new Date().toISOString(),
          detail: {
            id: item.id,
            kind: item.kind,
            properties: composeAdditionalProperties(item),
          },
        },
      });
      extendPopupTimer(true);
      return undefined;
    });
}

/**
 * お気に入りを削除する
 * 同時にキューを作成してリモート送信を試みる
 *
 * @param item
 * @param {(DeleteBookmark | undefined)} deleteRemoteBookmark
 * @param extendPopupTimer
 * @return {*}  {Promise<void>}
 */
export async function handleDeleteBookmark(
  item: ISplitViewListSingle,
  deleteRemoteBookmark: DeleteRemoteBookmark | undefined,
  extendPopupTimer: ExtendPopupTimer,
): Promise<void> {

  if (!deleteRemoteBookmark) return;

  try {
    await deleteRemoteBookmark(item);
  } catch {
    return;
  }

  // ポップアップを表示
  extendPopupTimer(false);
}

/**
 * お気に入りボタンクリック時のコールバック
 * @param addRemoteBookmark
 * @param deleteRemoteBookmark
 * @param extendPopupTimer
 * @param reportEvent
 * @param item
 * @param toBe trueで追加/falseで削除
 */
export async function onClickBookmarkImpl(
  addRemoteBookmark: AddRemoteBookmark | undefined,
  deleteRemoteBookmark: DeleteRemoteBookmark | undefined,
  extendPopupTimer: ExtendPopupTimer,
  reportEvent: EventReporter,
  item: ISplitViewListSingle,
  toBe: boolean,
): Promise<void> {

  const itemDateAttached = attachReposDateToISplitViewListSingle(item);

  if (toBe) {
    return handleAddBookmark(
      itemDateAttached,
      addRemoteBookmark,
      extendPopupTimer,
      reportEvent,
    );
  }

  return handleDeleteBookmark(
    itemDateAttached,
    deleteRemoteBookmark,
    extendPopupTimer,
  );
}

/**
 * handle the behavior on click the bookmark button
 * @param useComponentInitReturn
 * @param useRemoteBookmarkReturn
 * @param useMessageToasterBehavior
 */
const useBookmarkButtonBehavior = (
  useComponentInitReturn: ReturnType<typeof useComponentInitUtility>,
  useRemoteBookmarkReturn: ReturnType<typeof useRemoteBookmarkFeature>,
  useMessageToasterBehavior: UseMessageToasterBehaviorReturn,
): UseBookmarkButtonBehaviorReturn => {

  // pick an event reporter from the utility hook
  const [, [reportEvent]] = useComponentInitReturn;

  // pick the toaster function
  const [, , extendPopupTimer] = useMessageToasterBehavior;

  // pick bookmark add/remove business logics
  const { addRemoteBookmark, deleteRemoteBookmark } = useRemoteBookmarkReturn;

  // お気に入りアイコンクリック時のアクション
  const onClickBookmark = React.useCallback(
    async (item: ISplitViewListSingle, toBe: boolean) => onClickBookmarkImpl(
      addRemoteBookmark, deleteRemoteBookmark, extendPopupTimer, reportEvent, item, toBe,
    ), [addRemoteBookmark, deleteRemoteBookmark, extendPopupTimer, reportEvent],
  );

  return [onClickBookmark];
};

export default useBookmarkButtonBehavior;
