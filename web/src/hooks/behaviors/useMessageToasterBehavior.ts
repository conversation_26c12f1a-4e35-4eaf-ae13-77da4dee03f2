import * as React from 'react';
import { ToasterMessage, ToasterMessageType } from '../../components/commons/molecules/message-toaster/MessageToaster';
import { makePositiveInt } from '../../utilities/number';

export type ExtendPopupTimer = (
  toBeBookmarkedOrMessageType: boolean | null | ToasterMessageType,
) => void;

/**
 * return value of useBookmarkPopup
 *   isPopupShown: トースターを表示するためのフラグ
 *   toasterMessage: トースターに表示するメッセージ
 *   extendPopupTimer: 何秒間トースターを表示するかの関数
 */
export type UseMessageToasterBehaviorReturn = [
  isPopupShown: boolean,
  toasterMessage: ToasterMessageType,
  extendPopupTimer: ExtendPopupTimer,
];

/**
 * toBeBookmarkedの値に応じてメッセージの種類を返却する
 * @param toBeBookmarked trueでお気に入り済メッセージを返却 nullで最大件数エラーを返却
 */
function setBookmarkMessage(toBeBookmarked: boolean | null): ToasterMessageType {
  if (toBeBookmarked === null) {
    return ToasterMessage.MAX_BOOKMARKS;
  }
  return toBeBookmarked ? ToasterMessage.BOOKMARKED : ToasterMessage.UN_BOOKMARKED;
}

/**
 * お気に入り追加／削除時にメッセージをポップアップするとき、表示表示をタイマーで制御する機能
 * @param popupDuration 何ミリ秒トースターを表示するか
 */
const useMessageToasterBehavior = (popupDuration: number): UseMessageToasterBehaviorReturn => {
  const [isPopupShown, setToasterShown] = React.useState(false);
  const [toasterMessage, setToasterMessage] = React.useState(ToasterMessage.BLANK);
  const popupTimerRef = React.useRef<ReturnType<typeof setTimeout> | null>(null);
  const duration = React.useRef(makePositiveInt(popupDuration));

  /**
   * もしpopupTimerRefが存在していたらNullにする
   */
  const clearPopupTimer = React.useCallback(() => {
    if (!popupTimerRef.current) return;
    clearTimeout(popupTimerRef.current);
    popupTimerRef.current = null;
  }, []);

  /**
   * メッセージを設定し、表示タイマーをリセットする
   * toBeBookmarkedOrMessageType: trueの場合、ブックマークされたメッセージを表示
   * それ以外の場合は、メッセージタイプを直接設定します。
   */
  const extendPopupTimer: ExtendPopupTimer = React.useCallback((
    toBeBookmarkedOrMessageType,
  ) => {
    // reset the timer if it exists
    if (popupTimerRef.current != null) {
      clearPopupTimer();
    }

    // messageTypeが設定されている場合、thaの値でメッセージを上書き
    if (typeof toBeBookmarkedOrMessageType !== 'boolean' && toBeBookmarkedOrMessageType !== null) {
      setToasterMessage(toBeBookmarkedOrMessageType);
    } else {
      // boolean値からブックマークメッセージを設定する
      setToasterMessage(setBookmarkMessage(toBeBookmarkedOrMessageType));
    }

    // トースター表示
    setToasterShown(true);

    // タイマーを設定し、タイマーが終了したら隠す。
    popupTimerRef.current = setTimeout(() => {
      setToasterShown(false);
      clearPopupTimer();
    }, duration.current);

  }, [clearPopupTimer]);

  // アンマウント時にタイマーを外す
  React.useEffect(() => clearPopupTimer, [clearPopupTimer]);

  return [
    isPopupShown,
    toasterMessage,
    extendPopupTimer,
  ];
};

export default useMessageToasterBehavior;
