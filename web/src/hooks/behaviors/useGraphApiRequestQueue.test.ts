import { renderHook } from '@testing-library/react-hooks';
import React from 'react';
import useGraphApiRequestQueue from './useGraphApiRequestQueue';
import { ISpecificResult } from '../../types/ISearchRequestResult';
import { WeakTokenProvider } from '../../types/TokenProvider';

const enqueueMock = jest.fn();
const startMock = jest.fn();
jest.mock('./useGraphApiRequestWorker', () => ({
  __esModule: true,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  default: (
    _cancellationRef: React.MutableRefObject<boolean>,
    _tokenProvider: WeakTokenProvider,
  ) => ({
    queue: {
      enqueue: enqueueMock,
    },
    start: startMock,
  }),
}));
describe('useGraphApiRequestQueue', () => {
  const cancellationRefMock = { current: false };
  const appendItemsMock = jest.fn();
  beforeEach(() => {
    enqueueMock.mockClear();
    startMock.mockClear();
  });
  it('should start task if tokenProvider is initialized', async () => {
    enqueueMock.mockImplementationOnce((item) => setTimeout(() => item.resolve(), 1000));
    const { result } = renderHook(() => useGraphApiRequestQueue(cancellationRefMock, () => Promise.resolve('token')));
    result.current.process({ dataSource: { kind: 'Mail' }, ids: [] } as unknown as ISpecificResult, appendItemsMock);
    await new Promise<void>((resolve) => { setTimeout(() => { resolve(); }, 1000); });
    expect(enqueueMock).toHaveBeenCalled();
    expect(startMock).toHaveBeenCalled();
  });

  it('should not start task if tokenProvider is not initialized', async () => {
    enqueueMock.mockImplementationOnce((item) => setTimeout(() => item.resolve(), 1000));
    const { result } = renderHook(() => useGraphApiRequestQueue(cancellationRefMock, undefined));
    result.current.process({ dataSource: { kind: 'Mail' }, ids: [] } as unknown as ISpecificResult, appendItemsMock);
    await new Promise<void>((resolve) => { setTimeout(() => { resolve(); }, 1000); });
    expect(enqueueMock).toHaveBeenCalled();
    expect(startMock).not.toHaveBeenCalled();
  });
});
