import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import { GraphError } from '@microsoft/microsoft-graph-client';
import * as React from 'react';
import { ListModeType } from '../../components/domains/split-view/types/ListMode';
import { trimRestrictedPathPrefixWithParam } from '../../utilities/text';
import { FetchFileBlob } from '../accessors/useSharePointApiAccessor';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { DataSourceKind } from '../../types/DataSourceKind';
import { ObjectUrlCache, ObjectUrlCacheEntry } from '../../types/ObjectUrlCache';

/**
 * .is-loadingを付与
 * @param $el
 */
export function setLoadingClass($el: HTMLElement): void {
  $el.classList.add('is-loading');
}

/**
 * .is-loadingを削除
 * @param $el
 */
export function unsetLoadingClass($el: HTMLElement): void {
  $el.classList.remove('is-loading');
}

/**
 * .is-loadingを削除してis-errorを付与
 * @param $el
 */
export function setErrorClass($el: HTMLElement): void {
  unsetLoadingClass($el);
  $el.classList.add('is-error');
}

/**
 * アンマウント後の後処理
 * @param cache 格納されているobjectURLをrevokeする
 */
export function onUnmount(cache: React.MutableRefObject<ObjectUrlCache>): void {
  Object.values(cache.current).forEach((entry) => {
    if (!entry.objUrl) return;
    URL.revokeObjectURL(entry.objUrl);
  });
}

/**
 * $elのsrcをセット
 * @param $el
 * @param src
 */
export function setSrc($el: HTMLImageElement, src: string): void {
  $el.setAttribute('src', src);
}

/**
 * activeIdがactiveIdRef.currentと一致していないときにtrue
 * @param cache
 * @param activeIdRef
 */
export function isActiveIdChanged(
  cache: ObjectUrlCacheEntry, activeIdRef: React.MutableRefObject<string>,
): boolean {
  return cache.activeId !== activeIdRef.current;
}

/**
 * キャッシュが存在した場合はsrcにセット
 * @param $el
 * @param cache
 * @return キャッシュが存在した場合はtrue
 */
export function setSrcIfCacheAvailable(
  $el: HTMLImageElement, cache?: ObjectUrlCacheEntry,
): boolean {
  if (!cache || !cache.objUrl) return false;
  setSrc($el, cache.objUrl);
  return true;
}

/**
 * 画像データのfetch処理
 * @param $el
 * @param fetchFileBlob
 * @param reportEvent
 * @param spoSrc SPO画像URLの相対パス
 * @param objectUrlCache
 * @param activeIdCache
 */
export function fetchFile(
  fetchFileBlob: FetchFileBlob,
  reportEvent: EventReporter,
  $el: HTMLImageElement,
  spoSrc: string,
  objectUrlCache: React.MutableRefObject<ObjectUrlCache>,
  activeIdCache: React.MutableRefObject<string>,
  sharePointSiteUrlCache: React.MutableRefObject<string>,
): Promise<string | undefined> {

  return fetchFileBlob(sharePointSiteUrlCache.current, spoSrc)
    // 異常系処理
    .catch((error: Error | GraphError) => {
      setErrorClass($el);
      const request = {
        kind: 'SPO',
        itemId: activeIdCache.current,
        siteUrl: sharePointSiteUrlCache.current,
        sourceUrl: spoSrc,
      };
      reportEvent({
        name: 'FETCH_FILE_BLOB_FAIL',
        type: EventReportType.SYS_EVENT,
        error,
        customProperties: {
          error,
          request,
        },
      });
      return undefined;
    })

    // 正常系処理
    .then((result) => {
      if (!result) return undefined;

      // activeIdが変更されていた場合は終了
      if (isActiveIdChanged(objectUrlCache.current[spoSrc], activeIdCache)) {
        return undefined;
      }
      // objectURLを生成して要素にセット
      const objUrl = URL.createObjectURL(result);
      setSrc($el, objUrl);
      unsetLoadingClass($el);

      return objUrl;
    });
}

/**
 * 画像1件を処理する
 * @param $el
 * @param fetchFileBlob
 * @param reportEvent
 * @param activeId
 * @param activeIdCache
 * @param sharePointSiteUrlCache
 * @param objectUrlCache
 * @param addObjUrlCacheEntry
 * @param setObjUrlCache
 */
export function processImage(
  $el: HTMLImageElement,
  fetchFileBlob: FetchFileBlob,
  reportEvent: EventReporter,
  activeId: string,
  activeIdCache: React.MutableRefObject<string>,
  sharePointSiteUrlCache: React.MutableRefObject<string>,
  objectUrlCache: React.MutableRefObject<ObjectUrlCache>,
  addObjUrlCacheEntry: (spoSrc: string, actId: string) => void,
  setObjUrlCache: (spoSrc: string, objUrl: string) => void,
): Promise<undefined> {

  // :i:/r を除去
  const spoSrc = trimRestrictedPathPrefixWithParam($el.dataset?.spoSrc ?? '');
  if (!spoSrc) return Promise.resolve(undefined);

  // 既に取得済の場合は終了
  if (setSrcIfCacheAvailable($el, objectUrlCache.current[spoSrc])) {
    return Promise.resolve(undefined);
  }

  // キャッシュエントリを新規作成
  // 処理開始時点でのactiveIdでエントリを作成したいのでrefのactiveIdは使わない
  addObjUrlCacheEntry(spoSrc, activeId);
  setLoadingClass($el);

  return fetchFile(
    fetchFileBlob,
    reportEvent,
    $el,
    spoSrc,
    objectUrlCache,
    activeIdCache,
    sharePointSiteUrlCache,
  ).then((result) => {
    // 成功時にはobjectUrlをキャッシュ
    if (result) setObjUrlCache(spoSrc, result);
    return undefined;
  });
}

/**
 * img要素を取得して全件を処理する
 * @param $elementToQuery
 * @param querySelectorString
 * @param fetchFileBlob
 * @param reportEvent
 * @param trigger
 * @param activeId
 * @param activeIdCache
 * @param sharePointSiteUrlCache
 * @param objectUrlCache
 * @param addObjUrlCacheEntry
 * @param setObjUrlCache
 */
export function processImages(
  $elementToQuery: React.RefObject<HTMLElement>,
  querySelectorString: string,
  fetchFileBlob: FetchFileBlob | undefined,
  reportEvent: EventReporter,
  trigger: boolean,
  activeId: string,
  activeIdCache: React.MutableRefObject<string>,
  sharePointSiteUrlCache: React.MutableRefObject<string>,
  objectUrlCache: React.MutableRefObject<ObjectUrlCache>,
  addObjUrlCacheEntry: (spoSrc: string, actId: string) => void,
  setObjUrlCache: (spoSrc: string, objUrl: string) => void,
): Promise<void>[] {

  if (!fetchFileBlob || !trigger || !$elementToQuery.current) return [];

  return Array
    .from($elementToQuery.current.querySelectorAll<HTMLImageElement>(querySelectorString))
    .map(($el) => processImage(
      $el,
      fetchFileBlob,
      reportEvent,
      activeId,
      activeIdCache,
      sharePointSiteUrlCache,
      objectUrlCache,
      addObjUrlCacheEntry,
      setObjUrlCache,
    ));
}

/**
 * SharePointの投稿本文に含まれるimg要素をAPI経由で取得する機能
 *
 * @param {React.RefObject<T>} $elementToQuery DOMへの参照
 * @param {string} querySelectorString img要素を取得するためのカスタムdata属性セレクター
 * @param {(FetchFileBlob | undefined)} fetchFileBlob
 * @param {EventReporter} reportEvent
 * @param {boolean} trigger このフラグの状態を監視し、trueのときに処理を進める
 * @param {string} activeId 選択中の記事ID
 * @param {string} sharePointSiteUrl 選択中の記事が格納されているSharePointサイトURL
 * @param {ListModeType} listMode リスト切替時にprocessImagesをトリガーさせるためdependenciesに追加
 */
const useSpoImgLoaderFeature = <T extends HTMLElement>(
  $elementToQuery: React.RefObject<T>,
  querySelectorString: string,
  fetchFileBlob: FetchFileBlob | undefined,
  reportEvent: EventReporter,
  trigger: boolean,
  activeId: ISplitViewListSingle | undefined,
  sharePointSiteUrl: string,
  listMode: ListModeType,
): void => {

  // 初期引数からquery文字列を固定
  const [queryString] = React.useState(querySelectorString);

  // objectUrlを保持するための配列
  const objectUrlCache = React.useRef<ObjectUrlCache>({});

  // activeIdをcacheに保存する
  const activeIdCache = React.useRef(activeId?.id ?? '');
  React.useEffect(() => {
    activeIdCache.current = activeId?.id ?? '';
  }, [activeId]);

  // sharePointSiteUrlをcacheに保存する
  const sharePointSiteUrlCache = React.useRef(sharePointSiteUrl);
  React.useEffect(() => {
    sharePointSiteUrlCache.current = sharePointSiteUrl;
  }, [sharePointSiteUrl]);

  // objectUrlCacheに新しいエントリを追加
  const addObjUrlCacheEntry = React.useCallback((spoSrc: string, actId: string) => {
    objectUrlCache.current[spoSrc] = { objUrl: '', activeId: actId };
  }, []);

  // objectUrlCacheの既存エントリにobjUrlを保存
  const setObjUrlCache = React.useCallback((spoSrc: string, objUrl: string) => {
    const entry = objectUrlCache.current[spoSrc];
    if (!entry) return;
    entry.objUrl = objUrl;
  }, []);

  // innerHTMLのイベントリスナ登録／解除処理
  React.useEffect(() => {
    if (activeId?.kind !== DataSourceKind.SPO) return;

    processImages(
      $elementToQuery,
      queryString,
      fetchFileBlob,
      reportEvent,
      trigger,
      activeId?.id ?? '',
      activeIdCache,
      sharePointSiteUrlCache,
      objectUrlCache,
      addObjUrlCacheEntry,
      setObjUrlCache,
    );
  }, [
    fetchFileBlob,
    reportEvent,
    trigger,
    $elementToQuery,
    activeId,
    sharePointSiteUrl,
    queryString,
    listMode,
    addObjUrlCacheEntry,
    setObjUrlCache,
  ]);

  // アンマウント時の後処理
  React.useEffect(() => () => onUnmount(objectUrlCache), []);
};

export default useSpoImgLoaderFeature;
