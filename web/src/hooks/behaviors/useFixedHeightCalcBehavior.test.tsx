import * as React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useThrottle } from 'react-use';
import { MediaQueryStrings } from '../../utilities/mediaQuery';
import useFixedHeightCalcBehavior from './useFixedHeightCalcBehavior';
import mockMatchMedia from '../../mocks/match-media';

// matchMediaのモック
const matchMediaMock = mockMatchMedia();

// useThrottleが即座に値を反映するようにモック化
jest.mock('react-use', () => ({
  useThrottle: jest.fn(),
}));
const useThrottleMock = useThrottle as jest.Mock;
useThrottleMock.mockImplementation((size: string) => size);

describe('useFixedHeightCalcBehavior', () => {

  beforeAll(() => {
    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
      configurable: true,
      value: 100,
    });
  });

  beforeEach(() => {
    useThrottleMock.mockClear();

    // ウィンドウサイズを変更するテストのために上書き
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    global.window.innerWidth = 1024;
  });

  // テスト用コードなので定義不要
  /* eslint-disable react/prop-types */
  const TestComponent: React.FC<{
    threshold: number,
    watchValue: boolean,
  }> = (props) => {

    const {
      threshold,
      watchValue,
    } = props;

    const [
      $ref1,
      $ref2,
      $ref3,
      isFixed,
    ] = useFixedHeightCalcBehavior(threshold, watchValue);

    return (
      <div ref={$ref1} id="ref1">
        <div ref={$ref2} id="ref2" />
        <div ref={$ref3} id="ref3" />
        <p data-testid="is-fixed">{ isFixed ? 'isFixed: true' : 'isFixed: false' }</p>
      </div>
    );
  };

  function renderComponent(config: {
   threshold: number,
   watchValue: boolean,
  }) {
    return render(<TestComponent {...config} />);
  }

  describe('watchValue is false', () => {

    describe('when the isPC() returns true', () => {
      beforeAll(() => {
        matchMediaMock.mockReturnValue({ matches: true });
      });

      it('should keep that isFixed is true', async () => {
        const container1 = renderComponent({
          threshold: 0.5,
          watchValue: false,
        }).container;

        const container2 = renderComponent({
          threshold: 2,
          watchValue: false,
        }).container;

        expect(container1.querySelector('[data-testid]')?.textContent).toBe('isFixed: true');
        expect(container2.querySelector('[data-testid]')?.textContent).toBe('isFixed: true');
      });
    });

    describe('when the isPC() returns false', () => {
      beforeAll(() => {
        matchMediaMock.mockReturnValue({ matches: false });
      });

      it('should keep that isFixed is false', async () => {
        const container1 = renderComponent({
          threshold: 0.5,
          watchValue: false,
        }).container;

        const container2 = renderComponent({
          threshold: 2,
          watchValue: false,
        }).container;

        expect(container1.querySelector('[data-testid]')?.textContent).toBe('isFixed: false');
        expect(container2.querySelector('[data-testid]')?.textContent).toBe('isFixed: false');
      });
    });
  });

  describe('watchValue is true', () => {

    describe('when the isPC() returns true, the isSP() returns false', () => {
      beforeAll(() => {
        matchMediaMock
          .mockImplementation((text: string) => ({ matches: text === MediaQueryStrings.PC }));
      });

      describe('componentHeight * threshold < fixedElemHeight', () => {
        it('should switch isFixed to be false', async () => {
          renderComponent({
            threshold: 0.5,
            watchValue: true,
          });
          expect(await screen.findByText('isFixed: false')).toBeInTheDocument();
        });

        describe('when window size changed', () => {
          describe('componentHeight * threshold > fixedElemHeight', () => {
            it('should switch isFixed to be true', async () => {
              const { container } = renderComponent({
                threshold: 0.5,
                watchValue: true,
              });

              // 初期状態はfalse
              expect(await screen.findByText('isFixed: false')).toBeInTheDocument();

              // $ref3のoffsetHeightを変更
              const $ref3 = container.querySelector<HTMLDivElement>('#ref3');
              expect($ref3).not.toBeNull();
              if (!$ref3) return;
              Object.defineProperty($ref3, 'offsetHeight', { value: 5 });

              // ウィンドウサイズを変更するテストのために上書き
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              global.window.innerWidth = 500;

              // windowリサイズを発火
              fireEvent.resize(global.window);

              // サイズが変わったことが検知されフラグも反転する
              expect(await screen.findByText('isFixed: true')).toBeInTheDocument();
            });
          });
        });
      });

      describe('componentHeight * threshold > fixedElemHeight', () => {
        it('should keep that isFixed is true', async () => {
          renderComponent({
            threshold: 2,
            watchValue: true,
          });
          expect(await screen.findByText('isFixed: true')).toBeInTheDocument();
        });

        describe('when window size changed', () => {
          describe('componentHeight * threshold < fixedElemHeight', () => {
            it('should switch isFixed to be false', async () => {
              const { container } = renderComponent({
                threshold: 2,
                watchValue: true,
              });

              // 初期状態はtrue
              expect(await screen.findByText('isFixed: true')).toBeInTheDocument();

              // $ref2のoffsetHeightを変更
              const $ref2 = container.querySelector<HTMLDivElement>('#ref2');
              expect($ref2).not.toBeNull();
              if (!$ref2) return;
              Object.defineProperty($ref2, 'offsetHeight', { value: 500 });

              // ウィンドウサイズを変更するテストのために上書き
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              global.window.innerWidth = 500;

              // windowリサイズを発火
              fireEvent.resize(global.window);

              // サイズが変わったことが検知されフラグも反転する
              expect(await screen.findByText('isFixed: false')).toBeInTheDocument();
            });
          });
        });
      });

    });

    describe('when the isPC() returns false, the isSP() returns true', () => {
      beforeAll(() => {
        matchMediaMock
          .mockImplementation((text: string) => ({ matches: text === MediaQueryStrings.SP }));
      });

      describe('componentHeight * threshold < fixedElemHeight', () => {
        it('should keep that isFixed is false', async () => {
          renderComponent({
            threshold: 0.5,
            watchValue: true,
          });
          expect(await screen.findByText('isFixed: false')).toBeInTheDocument();
        });

        describe('when window size changed', () => {
          describe('componentHeight * threshold > fixedElemHeight', () => {
            it('should keep that isFixed is false', async () => {
              const { container } = renderComponent({
                threshold: 0.5,
                watchValue: true,
              });

              // 初期状態はfalse
              expect(await screen.findByText('isFixed: false')).toBeInTheDocument();

              // $ref3のoffsetHeightを変更
              const $ref3 = container.querySelector<HTMLDivElement>('#ref3');
              expect($ref3).not.toBeNull();
              if (!$ref3) return;
              Object.defineProperty($ref3, 'offsetHeight', { value: 5 });

              // ウィンドウサイズを変更するテストのために上書き
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              global.window.innerWidth = 500;

              // windowリサイズを発火
              fireEvent.resize(global.window);

              // サイズが変わったことが検知されるがフラグはfalseのまま
              expect(await screen.findByText('isFixed: false')).toBeInTheDocument();
            });
          });
        });
      });

      describe('componentHeight * threshold > fixedElemHeight', () => {
        it('should keep that isFixed is false', async () => {
          renderComponent({
            threshold: 2,
            watchValue: true,
          });
          expect(await screen.findByText('isFixed: false')).toBeInTheDocument();
        });

        describe('when window size changed', () => {
          describe('componentHeight * threshold < fixedElemHeight', () => {
            it('should keep that isFixed is false', async () => {
              const { container } = renderComponent({
                threshold: 2,
                watchValue: true,
              });

              // 初期状態はfalse
              expect(await screen.findByText('isFixed: false')).toBeInTheDocument();

              // $ref2のoffsetHeightを変更
              const $ref2 = container.querySelector<HTMLDivElement>('#ref2');
              expect($ref2).not.toBeNull();
              if (!$ref2) return;
              Object.defineProperty($ref2, 'offsetHeight', { value: 500 });

              // ウィンドウサイズを変更するテストのために上書き
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              global.window.innerWidth = 500;

              // windowリサイズを発火
              fireEvent.resize(global.window);

              // サイズが変わったことが検知されるがフラグはfalseのまま
              expect(await screen.findByText('isFixed: false')).toBeInTheDocument();
            });
          });
        });
      });

    });
  });

});
