import '@testing-library/jest-dom';
import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { GraphError } from '@microsoft/microsoft-graph-client';
import { IRepositoryBookmarkQueue } from '../../types/IGeraniumAttaneDB';
import { createReposBookmarkItem, createReposBookmarkQueue, createSplitViewListSingle } from '../../utilities/test';
import {
  addRemoteBookmarkImpl,
  applyQueueToBookmarks,
  deleteRemoteBookmarkImpl,
  fetchRemoteImpl,
  mergeLocalRemoteBookmarks,
  replaceQueuesByBookmarks,
  filterBookmarksWithMissingRequiredValues,
  sendQueue, sendQueues, UseRemoteBookmarkError, renewBookmarkData, mergeReposTimestamp,
} from './useRemoteBookmarkFeature';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

describe('applyQueueToBookmarks', () => {
  const bookmarks: ISplitViewListSingle[] = [
    createReposBookmarkItem({ id: 'abc' }),
    createReposBookmarkItem({ id: 'efg' }),
    createReposBookmarkItem({ id: 'hij' }),
  ];

  const queues: IRepositoryBookmarkQueue[] = [
    {
      type: 'PUT',
      data: createReposBookmarkItem({ id: '123' }),
      date: new Date(),
    },
    {
      type: 'DELETE',
      data: createReposBookmarkItem({ id: 'abc' }),
      date: new Date(),
    },
    {
      type: 'PUT',
      data: createReposBookmarkItem({ id: '456' }),
      date: new Date(),
    },
    {
      type: 'DELETE',
      // bookmarksに存在しないキー
      data: createReposBookmarkItem({ id: 'XYZ' }),
      date: new Date(),
    },
  ];

  it('should return merged bookmarks by the queues', () => {
    expect(
      applyQueueToBookmarks(bookmarks, queues),
    ).toStrictEqual([
      createReposBookmarkItem({ id: 'efg' }),
      createReposBookmarkItem({ id: 'hij' }),
      createReposBookmarkItem({ id: '123' }),
      createReposBookmarkItem({ id: '456' }),
    ]);
  });
});

describe('mergeLocalRemoteBookmarks', () => {
  const remoteBookmarks: ISplitViewListSingle[] = [
    createReposBookmarkItem({ id: '111' }),
    createReposBookmarkItem({ id: '222' }),
    createReposBookmarkItem({ id: '333' }),
    createReposBookmarkItem({ id: '444' }),
  ];

  const allBookmarks: ISplitViewListSingle[] = [
    // Remoteよりも新しいので上書きされる
    createReposBookmarkItem({
      id: '222',
      title: 'List Api Entry SHOULD UPDATED',
      properties: {
        siteUrl: 'siteUrl',
        listUrl: 'listUrl',
        listId: 'listId',
        listName: 'listName',
        editLink: 'abc',
        createdDate: '2021-08-24T03:00:59.000Z',
        updatedDate: '2020-12-03T02:39:48.000Z',
        hasAttachments: false,
      },
    }),
    // Remoteと同時なので上書きされる
    createReposBookmarkItem({
      id: '333',
      title: 'List Api Entry SHOULD UPDATED',
      properties: {
        siteUrl: 'siteUrl',
        listUrl: 'listUrl',
        listId: 'listId',
        listName: 'listName',
        editLink: 'abc',
        createdDate: '2021-08-24T03:00:59.000Z',
        updatedDate: '2020-12-02T02:39:48.000Z',
        hasAttachments: false,
      },
    }),
    // Remoteよりも古いので上書きされない
    createReposBookmarkItem({
      id: '444',
      title: 'List Api Entry SHOULD NOT UPDATED',
      properties: {
        siteUrl: 'siteUrl',
        listUrl: 'listUrl',
        listId: 'listId',
        listName: 'listName',
        editLink: 'abc',
        createdDate: '2021-08-24T03:00:59.000Z',
        updatedDate: '2020-12-01T02:39:48.000Z',
        hasAttachments: false,
      },
    }),
  ];

  const bookmarkDict = {
    222: true,
    333: true,
    444: true,
  };

  describe('when localQueues is available', () => {
    it('should return the merged result', () => {
      const localQueues: IRepositoryBookmarkQueue[] = [
        {
          type: 'PUT',
          data: createReposBookmarkItem({ id: '123' }),
          date: new Date(),
        },
        {
          type: 'DELETE',
          data: createReposBookmarkItem({ id: '111' }),
          date: new Date(),
        },
      ];

      expect(mergeLocalRemoteBookmarks(
        remoteBookmarks, localQueues, allBookmarks, bookmarkDict,
      )).toStrictEqual([
        createReposBookmarkItem({
          id: '222',
          title: 'List Api Entry SHOULD UPDATED',
          properties: {
            siteUrl: 'siteUrl',
            listUrl: 'listUrl',
            listId: 'listId',
            listName: 'listName',
            editLink: 'abc',
            createdDate: '2021-08-24T03:00:59.000Z',
            updatedDate: '2020-12-03T02:39:48.000Z',
            hasAttachments: false,
          },
        }),
        createReposBookmarkItem({
          id: '333',
          title: 'List Api Entry SHOULD UPDATED',
          properties: {
            siteUrl: 'siteUrl',
            listUrl: 'listUrl',
            listId: 'listId',
            listName: 'listName',
            editLink: 'abc',
            createdDate: '2021-08-24T03:00:59.000Z',
            updatedDate: '2020-12-02T02:39:48.000Z',
            hasAttachments: false,
          },
        }),
        createReposBookmarkItem({ id: '444' }),
        createReposBookmarkItem({ id: '123' }),
      ]);
    });
  });

  describe('when localQueue is not available', () => {
    it('should return the merged result', () => {
      const expected = [
        createReposBookmarkItem({ id: '111' }),
        createReposBookmarkItem({
          id: '222',
          title: 'List Api Entry SHOULD UPDATED',
          properties: {
            siteUrl: 'siteUrl',
            listUrl: 'listUrl',
            listId: 'listId',
            listName: 'listName',
            editLink: 'abc',
            createdDate: '2021-08-24T03:00:59.000Z',
            updatedDate: '2020-12-03T02:39:48.000Z',
            hasAttachments: false,
          },
        }),
        createReposBookmarkItem({
          id: '333',
          title: 'List Api Entry SHOULD UPDATED',
          properties: {
            siteUrl: 'siteUrl',
            listUrl: 'listUrl',
            listId: 'listId',
            listName: 'listName',
            editLink: 'abc',
            createdDate: '2021-08-24T03:00:59.000Z',
            updatedDate: '2020-12-02T02:39:48.000Z',
            hasAttachments: false,
          },
        }),
        createReposBookmarkItem({ id: '444' }),
      ];

      expect(mergeLocalRemoteBookmarks(
        remoteBookmarks, [], allBookmarks, bookmarkDict,
      )).toStrictEqual(expected);

      expect(mergeLocalRemoteBookmarks(
        remoteBookmarks, null, allBookmarks, bookmarkDict,
      )).toStrictEqual(expected);
    });

  });

});

describe('sendQueue', () => {
  const deleteBookmarkQueue = jest.fn().mockResolvedValue(undefined);

  beforeEach(() => {
    deleteBookmarkQueue.mockClear();
  });

  describe('when type = "PUT"', () => {
    const type = 'PUT';
    const deleteBookmarkApi = jest.fn().mockRejectedValue(undefined);

    beforeEach(() => {
      deleteBookmarkApi.mockClear();
    });

    describe('when putBookmarkApi resolves', () => {
      const putBookmarkApi = jest.fn().mockResolvedValue(undefined);

      beforeEach(() => {
        putBookmarkApi.mockClear();
      });

      it('should resolve undefined', async () => {
        await expect(
          sendQueue(
            createReposBookmarkQueue({ type }),
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
          ),
        ).resolves.toBeUndefined();
      });

      it('should call putBookmarkApi', async () => {
        await sendQueue(
          createReposBookmarkQueue({ type }),
          putBookmarkApi,
          deleteBookmarkApi,
          deleteBookmarkQueue,
        );
        expect(putBookmarkApi).toHaveBeenCalledTimes(1);
        expect(putBookmarkApi).toHaveBeenCalledWith(createReposBookmarkQueue({ type }).data);
      });

      it('should call deleteBookmarkQueue', async () => {
        await sendQueue(
          createReposBookmarkQueue({ type, data: { id: 'abc' } }),
          putBookmarkApi,
          deleteBookmarkApi,
          deleteBookmarkQueue,
        );
        expect(deleteBookmarkQueue).toHaveBeenCalledTimes(1);
        expect(deleteBookmarkQueue).toHaveBeenCalledWith('abc');
      });
    });

    describe('when putBookmarkApi rejects GraphError', () => {
      const putBookmarkApi = jest.fn().mockRejectedValue(new GraphError(404));

      beforeEach(() => {
        putBookmarkApi.mockClear();
      });

      it('should reject what putBookmarkApi rejects', async () => {
        await expect(
          sendQueue(
            createReposBookmarkQueue({ type }),
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
          ),
        ).rejects.toThrow(new GraphError(404));
      });

      it('should not call deleteBookmarkQueue', async () => {
        await expect(
          sendQueue(
            createReposBookmarkQueue({ type, data: { id: 'abc' } }),
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
          ),
        ).rejects.toThrow();
        expect(deleteBookmarkQueue).not.toHaveBeenCalled();
      });
    });
  });

  describe('when type = "DELETE"', () => {
    const type = 'DELETE';
    const putBookmarkApi = jest.fn().mockResolvedValue(undefined);

    beforeEach(() => {
      putBookmarkApi.mockClear();
    });

    describe('when deleteBookmarkApi resolves', () => {
      const deleteBookmarkApi = jest.fn().mockResolvedValue(undefined);

      beforeEach(() => {
        deleteBookmarkApi.mockClear();
      });

      it('should resolve undefined', async () => {
        await expect(
          sendQueue(
            createReposBookmarkQueue({ type }),
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
          ),
        ).resolves.toBeUndefined();
      });

      it('should call deleteBookmarkApi', async () => {
        await sendQueue(
          createReposBookmarkQueue({ type }),
          putBookmarkApi,
          deleteBookmarkApi,
          deleteBookmarkQueue,
        );
        expect(deleteBookmarkApi).toHaveBeenCalledTimes(1);
        expect(deleteBookmarkApi).toHaveBeenCalledWith(createReposBookmarkQueue({ type }).data.id);
      });

      it('should call deleteBookmarkQueue', async () => {
        await sendQueue(
          createReposBookmarkQueue({ type, data: { id: 'abc' } }),
          putBookmarkApi,
          deleteBookmarkApi,
          deleteBookmarkQueue,
        );
        expect(deleteBookmarkQueue).toHaveBeenCalledTimes(1);
        expect(deleteBookmarkQueue).toHaveBeenCalledWith('abc');
      });

      it('should not call putBookmarkApi', async () => {
        await sendQueue(
          createReposBookmarkQueue({ type }),
          putBookmarkApi,
          deleteBookmarkApi,
          deleteBookmarkQueue,
        );
        expect(putBookmarkApi).not.toHaveBeenCalled();
      });
    });

    describe('when deleteBookmarkApi rejects GraphError', () => {
      const deleteBookmarkApi = jest.fn().mockRejectedValue(new GraphError(404));

      beforeEach(() => {
        deleteBookmarkApi.mockClear();
      });

      it('should reject what deleteBookmarkApi rejects', async () => {
        await expect(
          sendQueue(
            createReposBookmarkQueue({ type }),
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
          ),
        ).rejects.toThrow(new GraphError(404));
      });

      it('should not call deleteBookmarkQueue', async () => {
        await expect(
          sendQueue(
            createReposBookmarkQueue({ type }),
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
          ),
        ).rejects.toThrow();
        expect(deleteBookmarkQueue).not.toHaveBeenCalled();
      });
    });
  });
});

describe('sendQueues', () => {

  const putBookmarkApi = jest.fn().mockResolvedValue(undefined);
  const deleteBookmarkApi = jest.fn().mockResolvedValue(undefined);
  const deleteBookmarkQueue = jest.fn().mockResolvedValue(undefined);
  const eventReporter = jest.fn();

  beforeEach(() => {
    putBookmarkApi.mockClear();
    deleteBookmarkApi.mockClear();
    deleteBookmarkQueue.mockClear();
  });

  describe('when one of the required functions is undefined', () => {
    it('should not do anything', () => {
      const mock = jest.fn();
      const queues = [createReposBookmarkQueue({})];

      expect(
        sendQueues(queues, undefined, mock, mock, eventReporter),
      ).resolves.toBeUndefined();
      expect(
        sendQueues(queues, mock, undefined, mock, eventReporter),
      ).resolves.toBeUndefined();
      expect(
        sendQueues(queues, mock, mock, undefined, eventReporter),
      ).resolves.toBeUndefined();

      expect(mock).not.toHaveBeenCalled();
    });
  });

  describe('when the queue is empty', () => {
    it('should not do anything', () => {
      const mock = jest.fn();

      expect(
        sendQueues([], mock, mock, mock, eventReporter),
      ).resolves.toBeUndefined();

      expect(
        sendQueues(null, mock, mock, mock, eventReporter),
      ).resolves.toBeUndefined();

      expect(mock).not.toHaveBeenCalled();
    });
  });

  describe('when the api always resolves', () => {
    describe('when queues have two entries', () => {
      it('should call the api twice', async () => {
        const queues: IRepositoryBookmarkQueue[] = [
          createReposBookmarkQueue({ type: 'PUT', data: { id: 'abc' } }),
          createReposBookmarkQueue({ type: 'DELETE', data: { id: 'efg' } }),
        ];

        await sendQueues(
          queues,
          putBookmarkApi,
          deleteBookmarkApi,
          deleteBookmarkQueue,
          eventReporter,
        );

        expect(putBookmarkApi).toHaveBeenCalledTimes(1);
        expect(putBookmarkApi).toHaveBeenCalledWith(createReposBookmarkQueue({ type: 'PUT', data: { id: 'abc' } }).data);
        expect(deleteBookmarkApi).toHaveBeenCalledTimes(1);
        expect(deleteBookmarkApi).toHaveBeenCalledWith('efg');
        expect(deleteBookmarkQueue).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('when the api resolves partially', () => {
    const rejectValueCases = [
      { value: new GraphError(404), desc: 'GraphError', log: new GraphError(404) },
      { value: new Error('abc'), desc: 'Error', log: new Error('abc') },
      { value: 'abc', desc: 'string', log: new Error(UseRemoteBookmarkError.UNKNOWN_ERROR) },
    ];

    rejectValueCases.forEach((test) => {
      describe(`the api reject with ${test.desc}`, () => {

        const queues: IRepositoryBookmarkQueue[] = [
          createReposBookmarkQueue({ type: 'PUT', data: { id: 'abc' } }),
          createReposBookmarkQueue({ type: 'DELETE', data: { id: 'efg' } }),
          createReposBookmarkQueue({ type: 'PUT', data: { id: 'hij' } }),
          createReposBookmarkQueue({ type: 'DELETE', data: { id: 'klm' } }),
          createReposBookmarkQueue({ type: 'PUT', data: { id: 'nop' } }),
        ];

        it('should call the api until it rejects', async () => {
          eventReporter.mockClear();
          putBookmarkApi
            .mockClear()
            // PUT1回目は成功する
            .mockResolvedValueOnce(undefined)
            // PUT2回目には失敗する
            .mockRejectedValueOnce(test.value);

          await sendQueues(
            queues,
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
            eventReporter,
          );

          // 3回目以降のPUTは呼ばれない
          expect(putBookmarkApi).toHaveBeenCalledTimes(2);
          // 2回目以降のDELETEは呼ばれない
          expect(deleteBookmarkApi).toHaveBeenCalledTimes(1);
          // 成功した最初の2つのキューの数だけ削除される
          expect(deleteBookmarkQueue).toHaveBeenCalledTimes(2);
        });

        it('should call eventReporter', async () => {
          await sendQueues(
            queues,
            putBookmarkApi,
            deleteBookmarkApi,
            deleteBookmarkQueue,
            eventReporter,
          );
          expect(eventReporter).toHaveBeenCalledTimes(1);
          expect(eventReporter).toHaveBeenCalledWith({
            type: EventReportType.SYS_ERROR,
            name: UseRemoteBookmarkError.QUEUE_SEND_FAIL,
            error: test.log,
            customProperties: {
              failedAt: 2,
              succeededQueues: [
                createReposBookmarkQueue({ type: 'PUT', data: { id: 'abc' } }),
                createReposBookmarkQueue({ type: 'DELETE', data: { id: 'efg' } }),
              ],
              remainingQueues: [
                createReposBookmarkQueue({ type: 'PUT', data: { id: 'hij' } }),
                createReposBookmarkQueue({ type: 'DELETE', data: { id: 'klm' } }),
                createReposBookmarkQueue({ type: 'PUT', data: { id: 'nop' } }),
              ],
            },
          });
        });
      });
    });
  });
});

describe('addRemoteBookmarkImpl', () => {
  const item = createReposBookmarkItem({});
  const eventReporter = jest.fn();

  describe('when one of the required functions is undefined', () => {
    it('should not do anything', async () => {
      const mock = jest.fn();

      await expect(addRemoteBookmarkImpl(
        undefined,
        mock,
        mock,
        mock,
        mock,
        item,
        eventReporter,
      )).resolves.toBeUndefined();

      await expect(addRemoteBookmarkImpl(
        mock,
        undefined,
        mock,
        mock,
        mock,
        item,
        eventReporter,
      )).resolves.toBeUndefined();

      expect(mock).not.toHaveBeenCalled();
    });

  });

  describe('when the required functions are available', () => {
    const addBookmarkQueue = jest.fn().mockResolvedValue(undefined);
    const getBookmarkQueues = jest.fn().mockResolvedValue(undefined);
    const deleteBookmarkQueue = jest.fn().mockResolvedValue(undefined);
    const deleteBookmarkApi = jest.fn().mockResolvedValue(undefined);
    const putBookmarkApi = jest.fn().mockResolvedValue(undefined);

    describe('when getBookmarkQueues resolved PUT and DELETE queues', () => {
      beforeEach(() => {
        getBookmarkQueues.mockResolvedValue([
          createReposBookmarkQueue({ type: 'PUT' }),
          createReposBookmarkQueue({ type: 'DELETE' }),
        ]);
      });

      it('should addBookmarkQueue and call put and delete apis', async () => {
        await expect(
          addRemoteBookmarkImpl(
            addBookmarkQueue,
            getBookmarkQueues,
            deleteBookmarkQueue,
            deleteBookmarkApi,
            putBookmarkApi,
            item,
            eventReporter,
          ),
        ).resolves.toBeUndefined();
        expect(addBookmarkQueue).toHaveBeenCalledTimes(1);
        expect(addBookmarkQueue).toHaveBeenCalledWith(item, 'PUT');
        expect(putBookmarkApi).toHaveBeenCalledTimes(1);
        expect(deleteBookmarkApi).toHaveBeenCalledTimes(1);
      });
    });
  });
});

describe('deleteRemoteBookmarkImpl', () => {
  const item = createReposBookmarkItem({});
  const eventReporter = jest.fn();

  describe('when one of the required functions is undefined', () => {
    it('should not do anything', async () => {
      const mock = jest.fn();

      // addBookmarkQueueがundefined
      await expect(deleteRemoteBookmarkImpl(
        undefined,
        mock,
        mock,
        mock,
        mock,
        item,
        eventReporter,
      )).resolves.toBeUndefined();

      // getBookmarkQueuesがundefined
      await expect(deleteRemoteBookmarkImpl(
        mock,
        undefined,
        mock,
        mock,
        mock,
        item,
        eventReporter,
      )).resolves.toBeUndefined();

      expect(mock).not.toHaveBeenCalled();
    });

  });

  describe('when the required functions are available', () => {
    const addBookmarkQueue = jest.fn().mockResolvedValue(undefined);
    const getBookmarkQueues = jest.fn().mockResolvedValue(undefined);
    const deleteBookmarkQueue = jest.fn().mockResolvedValue(undefined);
    const deleteBookmarkApi = jest.fn().mockResolvedValue(undefined);
    const putBookmarkApi = jest.fn().mockResolvedValue(undefined);

    describe('when getBookmarkQueues resolved PUT and DELETE queues', () => {
      beforeEach(() => {
        getBookmarkQueues.mockResolvedValue([
          createReposBookmarkQueue({ type: 'PUT' }),
          createReposBookmarkQueue({ type: 'DELETE' }),
        ]);
      });

      it('should addBookmarkQueue and call put and delete apis', async () => {
        await expect(
          deleteRemoteBookmarkImpl(
            addBookmarkQueue,
            getBookmarkQueues,
            deleteBookmarkQueue,
            deleteBookmarkApi,
            putBookmarkApi,
            item,
            eventReporter,
          ),
        ).resolves.toBeUndefined();
        expect(addBookmarkQueue).toHaveBeenCalledTimes(1);
        expect(addBookmarkQueue).toHaveBeenCalledWith(item, 'DELETE');
        expect(putBookmarkApi).toHaveBeenCalledTimes(1);
        expect(deleteBookmarkApi).toHaveBeenCalledTimes(1);
      });
    });
  });
});

describe('replaceQueuesByBookmarks', () => {
  it('should call replaceBookmarkQueues', async () => {
    const bookmarks = [
      createReposBookmarkItem({ id: 'abc' }),
      createReposBookmarkItem({ id: 'efg' }),
    ];
    const replaceBookmarkQueues = jest.fn().mockResolvedValue(undefined);

    await expect(
      replaceQueuesByBookmarks(bookmarks, replaceBookmarkQueues),
    ).resolves.toBeUndefined();

    expect(replaceBookmarkQueues).toBeCalledWith([
      {
        type: 'PUT',
        data: createReposBookmarkItem({ id: 'abc' }),
        date: expect.anything(),
      },
      {
        type: 'PUT',
        data: createReposBookmarkItem({ id: 'efg' }),
        date: expect.anything(),
      },
    ]);
  });
});

describe('filterBookmarksWithMissingRequiredValues', () => {
  it('should only return items with no required value', async () => {
    const bookmarks: ISplitViewListSingle[] = [
      createReposBookmarkItem({ id: 's1', kind: 'SPO' }),
      createReposBookmarkItem({ id: 's2', kind: 'SPO', title: '' }),
      createReposBookmarkItem({ id: 's3', kind: 'SPO', note: '' }),
      createReposBookmarkItem({
        id: 's4', kind: 'SPO', title: '', note: '',
      }),
      createReposBookmarkItem({ id: 'm1', kind: 'Mail' }),
      createReposBookmarkItem({ id: 'm2', kind: 'Mail', title: '' }),
      createReposBookmarkItem({ id: 'm3', kind: 'Mail', note: '' }),
      createReposBookmarkItem({
        id: 'm4', kind: 'Mail', title: '', note: '',
      }),
      createReposBookmarkItem({ id: 'c1', kind: 'Chat' }),
      createReposBookmarkItem({ id: 'c2', kind: 'Chat', title: '' }),
      createReposBookmarkItem({ id: 'c3', kind: 'Chat', note: '' }),
      createReposBookmarkItem({
        id: 'c4', kind: 'Chat', title: '', note: '',
      }),
    ];
    const result = filterBookmarksWithMissingRequiredValues(bookmarks);

    expect(result).not.toStrictEqual({
      mail: [
        expect.objectContaining({ id: 'm1' }),
      ],
      chat: [
        expect.objectContaining({ id: 'c1' }),
      ],
    });

    expect(result).toStrictEqual({
      mail: [
        expect.objectContaining({ id: 'm2' }),
        expect.objectContaining({ id: 'm3' }),
        expect.objectContaining({ id: 'm4' }),
      ],
      chat: [
        expect.objectContaining({ id: 'c2' }),
        expect.objectContaining({ id: 'c3' }),
        expect.objectContaining({ id: 'c4' }),
      ],
    });
  });
});

describe('fetchRemoteImpl', () => {
  const setIsInitialized = jest.fn();
  const eventReporter = jest.fn();

  beforeEach(() => {
    setIsInitialized.mockClear();
    eventReporter.mockClear();
  });

  describe('when one of the required functions is undefined', () => {
    it('should not do anything', () => {
      const mock = jest.fn();

      expect(fetchRemoteImpl(
        setIsInitialized,
        undefined,
        mock,
        mock,
        mock,
        mock,
        mock,
        mock,
        mock,
        mock,
        [],
        {},
        eventReporter,
      )).resolves.toBeUndefined();

      expect(fetchRemoteImpl(
        setIsInitialized,
        mock,
        mock,
        mock,
        undefined,
        mock,
        mock,
        mock,
        mock,
        mock,
        [],
        {},
        eventReporter,
      )).resolves.toBeUndefined();

      expect(fetchRemoteImpl(
        setIsInitialized,
        mock,
        mock,
        mock,
        mock,
        undefined,
        mock,
        mock,
        mock,
        mock,
        [],
        {},
        eventReporter,
      )).resolves.toBeUndefined();

      expect(fetchRemoteImpl(
        setIsInitialized,
        mock,
        mock,
        mock,
        mock,
        mock,
        mock,
        mock,
        undefined,
        mock,
        [],
        {},
        eventReporter,
      )).resolves.toBeUndefined();

      expect(setIsInitialized).not.toHaveBeenCalled();
      expect(mock).not.toHaveBeenCalled();
    });
  });

  describe('when getBookmarks rejects 404 GraphError', () => {
    const getBookmarks = jest.fn().mockRejectedValue(new GraphError(404));

    describe('when getBookmarkQueues resolves []', () => {
      const getBookmarkQueues = jest.fn().mockResolvedValue([]);
      const mock = jest.fn();

      beforeEach(() => {
        mock.mockClear();
      });

      it('should not change bookmarks and queues', async () => {
        await expect(fetchRemoteImpl(
          setIsInitialized,
          getBookmarks,
          mock,
          mock,
          mock,
          mock,
          getBookmarkQueues,
          mock,
          mock,
          mock,
          [],
          {},
          eventReporter,
        )).resolves.toBeUndefined();

        expect(setIsInitialized).toHaveBeenCalledWith('PENDING');
        expect(mock).not.toHaveBeenCalled();
        expect(setIsInitialized).toHaveBeenLastCalledWith('DONE');
      });

      it('should call eventReporter', async () => {
        await expect(fetchRemoteImpl(
          setIsInitialized,
          getBookmarks,
          mock,
          mock,
          mock,
          mock,
          getBookmarkQueues,
          mock,
          mock,
          mock,
          [],
          {},
          eventReporter,
        )).resolves.toBeUndefined();

        expect(eventReporter).toHaveBeenCalledTimes(1);
        expect(eventReporter).toHaveBeenCalledWith({
          type: EventReportType.SYS_ERROR,
          name: UseRemoteBookmarkError.IS_OFFLINE_OR_SOMETHING_WRONG,
          error: new GraphError(404),
        });
      });
    });
  });

  describe('when getBookmarks rejects 204 GraphError', () => {
    const getBookmarks = jest.fn().mockRejectedValue(new GraphError(204));

    describe('when getBookmarkQueues resolves an array with a member', () => {
      const getBookmarkQueues = jest.fn().mockResolvedValue(['a']);

      it('should not change bookmarks and queues', async () => {
        const mock = jest.fn();

        await expect(fetchRemoteImpl(
          setIsInitialized,
          getBookmarks,
          mock,
          mock,
          mock,
          mock,
          getBookmarkQueues,
          mock,
          mock,
          mock,
          [],
          {},
          eventReporter,
        )).resolves.toBeUndefined();

        expect(setIsInitialized).toHaveBeenCalledWith('PENDING');
        expect(mock).not.toHaveBeenCalled();
        expect(setIsInitialized).toHaveBeenLastCalledWith('DONE');
      });
    });

    // 初回登録処理のパターン
    describe('when getBookmarkQueues resolves []', () => {
      const getBookmarkQueues = jest.fn().mockResolvedValue([]);
      const putBookmarkApi = jest.fn().mockResolvedValue(undefined);
      const replaceBookmarkQueues = jest.fn().mockResolvedValue(undefined);
      const mock = jest.fn().mockResolvedValue(undefined);
      const allBookmarks = [
        createReposBookmarkItem({ id: 'abc' }),
        createReposBookmarkItem({ id: 'efg' }),
      ];
      const bookmarkDict = { abc: true, efg: true };

      beforeEach(() => {
        getBookmarkQueues.mockClear()
          .mockResolvedValueOnce([])
          .mockResolvedValueOnce([
            createReposBookmarkQueue({ type: 'PUT' }),
          ]);
        putBookmarkApi.mockClear();
        replaceBookmarkQueues.mockClear();
        mock.mockClear();
      });

      it('it should call replaceBookmarkQueues and call the put api', async () => {
        await expect(fetchRemoteImpl(
          setIsInitialized,
          getBookmarks,
          putBookmarkApi,
          mock,
          mock,
          mock,
          getBookmarkQueues,
          mock,
          replaceBookmarkQueues,
          mock,
          allBookmarks,
          bookmarkDict,
          eventReporter,
        )).resolves.toBeUndefined();

        expect(setIsInitialized).toHaveBeenCalledWith('PENDING');
        expect(replaceBookmarkQueues).toHaveBeenCalledTimes(1);
        expect(putBookmarkApi).toHaveBeenCalledTimes(1);
        expect(setIsInitialized).toHaveBeenLastCalledWith('DONE');
      });

      it('should call eventReporter', async () => {
        await expect(fetchRemoteImpl(
          setIsInitialized,
          getBookmarks,
          putBookmarkApi,
          mock,
          mock,
          mock,
          getBookmarkQueues,
          mock,
          replaceBookmarkQueues,
          mock,
          allBookmarks,
          bookmarkDict,
          eventReporter,
        )).resolves.toBeUndefined();

        expect(eventReporter).toHaveBeenCalledTimes(1);
        expect(eventReporter).toHaveBeenCalledWith({
          type: EventReportType.SYS_EVENT,
          name: UseRemoteBookmarkError.IS_FIRST_TIME_SYNC,
          customProperties: {
            bookmarksLength: 2,
            createdQueueLength: 1,
          },
        });
      });
    });
  });

  describe('when getBookmarks resolves', () => {
    const bookmarkDict = { efg: true };
    const allBookmarks = [
      // オリジナルよりも新しいので上書きされる
      createReposBookmarkItem({
        id: 'efg',
        title: 'List Api Entry SHOULD UPDATED',
        properties: {
          siteUrl: 'siteUrl',
          listUrl: 'listUrl',
          listId: 'listId',
          listName: 'listName',
          editLink: 'abc',
          createdDate: '2021-08-24T03:00:59.000Z',
          updatedDate: '2020-12-03T02:39:48Z',
          hasAttachments: false,
        },
      }),
    ];

    const remoteBookmarks = [
      createReposBookmarkItem({ id: 'abc' }),
      createReposBookmarkItem({ id: 'efg' }),
      createReposBookmarkItem({ id: 'hij' }),
    ];

    const localQueues: IRepositoryBookmarkQueue[] = [
      {
        type: 'PUT',
        data: createReposBookmarkItem({ id: '123' }),
        date: new Date(),
      },
      {
        type: 'DELETE',
        data: createReposBookmarkItem({ id: 'abc' }),
        date: new Date(),
      },
    ];

    const expectedResult = [
      createReposBookmarkItem({
        id: 'efg',
        title: 'List Api Entry SHOULD UPDATED',
        properties: {
          siteUrl: 'siteUrl',
          listUrl: 'listUrl',
          listId: 'listId',
          listName: 'listName',
          editLink: 'abc',
          createdDate: '2021-08-24T03:00:59.000Z',
          updatedDate: '2020-12-03T02:39:48Z',
          hasAttachments: false,
        },
      }),
      createReposBookmarkItem({ id: 'hij' }),
      createReposBookmarkItem({ id: '123' }),
    ];

    const getBookmarks = jest.fn().mockResolvedValue(remoteBookmarks);

    describe('when getBookmarkQueues resolves', () => {
      const getBookmarkQueues = jest.fn().mockResolvedValue(localQueues);

      it('should call replaceBookmarks and call the put/delete api', async () => {
        const mock = jest.fn().mockResolvedValue(undefined);
        const putBookmarkApi = jest.fn().mockResolvedValue(undefined);
        const replaceBookmarks = jest.fn().mockResolvedValue(undefined);
        const deleteBookmarkApi = jest.fn().mockResolvedValue(undefined);

        await expect(fetchRemoteImpl(
          setIsInitialized,
          getBookmarks,
          putBookmarkApi,
          deleteBookmarkApi,
          replaceBookmarks,
          mock,
          getBookmarkQueues,
          mock,
          mock,
          mock,
          allBookmarks,
          bookmarkDict,
          eventReporter,
        )).resolves.toBeUndefined();

        expect(setIsInitialized).toHaveBeenCalledWith('PENDING');
        expect(replaceBookmarks).toHaveBeenCalledTimes(1);
        expect(replaceBookmarks).toHaveBeenCalledWith(expectedResult);
        expect(putBookmarkApi).toHaveBeenCalledTimes(1);
        expect(deleteBookmarkApi).toHaveBeenCalledTimes(1);
        expect(setIsInitialized).toHaveBeenLastCalledWith('DONE');
      });
    });
  });

  describe('renewBookmarkData', () => {
    const fetchItemsMock = jest.fn();
    beforeEach(() => {
      fetchItemsMock.mockClear();
    });
    describe('there are no targets', () => {
      it('should not call api', async () => {
        fetchItemsMock.mockReturnValue({ responses: [] });
        await renewBookmarkData({ mail: [], chat: [] }, fetchItemsMock);
        expect(fetchItemsMock).not.toHaveBeenCalled();
      });
      it.each([
        { mail: [createSplitViewListSingle({ kind: 'Mail' })], chat: [], kind: 'Mail' },
        { mail: [], chat: [createSplitViewListSingle({ kind: 'Chat' })], kind: 'Chat' },
      ])('should call api just once, $kind', async ({ mail, chat }) => {
        fetchItemsMock.mockReturnValue({ responses: [] });
        await renewBookmarkData({ mail, chat }, fetchItemsMock);
        expect(fetchItemsMock).toHaveBeenCalledTimes(1);
      });
    });

  });
  describe('mergeReposTimestamp', () => {
    it('should keep repos timestamp', () => {
      const oldItems = [
        createSplitViewListSingle({ id: '1', reposCreatedDate: '2023-09-01T00:00:00Z', reposUpdatedDate: '2023-09-01T00:00:00Z' }),
        createSplitViewListSingle({ id: '2', reposCreatedDate: '2023-09-10T00:00:00Z', reposUpdatedDate: '2023-09-10T00:00:00Z' }),
      ];
      const newItem = createSplitViewListSingle({ id: '1', reposCreatedDate: undefined, reposUpdatedDate: undefined });
      const result = mergeReposTimestamp(oldItems)(newItem);
      expect(result.reposCreatedDate).toBe('2023-09-01T00:00:00Z');
      expect(result.reposUpdatedDate).toBe('2023-09-01T00:00:00Z');
    });
  });
});
