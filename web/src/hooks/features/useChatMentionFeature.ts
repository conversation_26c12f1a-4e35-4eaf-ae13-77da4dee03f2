import React from 'react';
import { ListModeType } from '../../components/domains/split-view/types/ListMode';
import { DataSourceKind } from '../../types/DataSourceKind';
import { IChatProperties } from '../../types/ISearchResult';
import { ISplitViewDetail } from '../../components/domains/split-view/types/ISplitViewDetail';

export function decorate<T extends HTMLElement>(
  $elementToQuery: React.RefObject<T>,
  queryString: string,
  detail: ISplitViewDetail,
  oid: string,
) {
  const mentionTags = Array.from(
    $elementToQuery.current?.querySelectorAll(queryString).values() ?? [],
  );
  const mentionIds = (detail.properties as IChatProperties)?.mentions ?? [];
  mentionTags.forEach((mention) => {
    const mentionId = mention.getAttribute('data-chat-mention-id');
    if (!mentionId) return;
    const isMentionedToUser = mentionIds.find(
      (m) => m.id.toString() === mentionId,
    )?.mentioned?.user?.id === oid;
    if (isMentionedToUser) {
      mention.classList.add('me');
    }
  });
}

const useChatMentionFeature = <T extends HTMLElement>(
  $elementToQuery: React.RefObject<T>,
  querySelectorString: string,
  oid: string,
  detail: ISplitViewDetail | undefined,
  listMode: ListModeType,
): void => {

  // 初期引数からquery文字列を固定
  const [queryString] = React.useState(querySelectorString);

  React.useEffect(() => {
    if (detail?.kind !== DataSourceKind.Chat) return;
    if (!detail.id) return;

    decorate(
      $elementToQuery,
      queryString,
      detail,
      oid,
    );
  }, [
    $elementToQuery,
    queryString,
    detail,
    oid,
    listMode, // リスト切替時にprocessImagesをトリガーさせるためdependenciesに追加
  ]);
};

export default useChatMentionFeature;
