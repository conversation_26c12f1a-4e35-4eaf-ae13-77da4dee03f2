import React from 'react';
import { GraphError } from '@microsoft/microsoft-graph-client';
import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import useMailApiAccessor, {
  FetchMailAttachment,
} from '../accessors/useMailApiAccessor';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { DataSourceKind } from '../../types/DataSourceKind';
import { ListModeType } from '../../components/domains/split-view/types/ListMode';
import {
  isActiveIdChanged, onUnmount, setSrc, setSrcIfCacheAvailable,
} from '../behaviors/useSpoImgLoaderFeature';
import { ObjectUrlCache } from '../../types/ObjectUrlCache';
import { IMailAttachment } from '../../components/domains/split-view/types/IMailAttachment';

export async function processImage(
  $el: HTMLImageElement,
  fetchAttachment: FetchMailAttachment,
  activeItem: ISplitViewListSingle | undefined,
  activeIdCache: React.MutableRefObject<string>,
  inlineMailAttachment: IMailAttachment | undefined,
  objectUrlCache: React.MutableRefObject<ObjectUrlCache>,
  addObjUrlCacheEntry: (contentId: string, activeId: string,) => void,
  setObjUrlCache: (contentId: string, objUrl: string) => void,
  reportEvent: EventReporter,
): Promise<void> {
  const mailId = activeItem?.id;
  const id = inlineMailAttachment?.id;
  const contentId = inlineMailAttachment?.contentId;
  if (!mailId || !id || !contentId) return undefined;

  // 既に取得済の場合は終了
  if (setSrcIfCacheAvailable($el, objectUrlCache.current[contentId])) {
    return Promise.resolve(undefined);
  }

  // キャッシュエントリを新規作成
  // 処理開始時点でのactiveIdでエントリを作成したいのでrefのactiveIdは使わない
  addObjUrlCacheEntry(contentId, activeItem.id ?? '');

  // TODO: fetchAttachmentAPIを呼ぶのにcontentIdは不要なのでMailAttachmentParams型を修正する
  const result = await fetchAttachment({ id, mailId, contentId })
    .catch((error: Error | GraphError) => {
      const request = {
        kind: 'Mail',
        mailId,
        attachmentId: id,
      };
      reportEvent({
        name: 'FETCH_FILE_BLOB_FAIL',
        type: EventReportType.SYS_EVENT,
        error,
        customProperties: {
          error,
          request,
        },
      });
      return undefined;
    });

  if (!result) return undefined;

  // activeIdが変更されていた場合は終了
  if (isActiveIdChanged(objectUrlCache.current[contentId], activeIdCache)) {
    return Promise.resolve(undefined);
  }

  // objectURLを生成して要素にセット
  const objUrl = URL.createObjectURL(result);
  setSrc($el, objUrl);
  setObjUrlCache(contentId, objUrl);

  return Promise.resolve(undefined);
}

export function processImages(
  $elementToQuery: React.RefObject<HTMLElement>,
  querySelectorString: string,
  fetchAttachment: FetchMailAttachment | undefined,
  innerHtmlUpdateTrigger: boolean,
  activeItem: ISplitViewListSingle | undefined,
  activeIdCache: React.MutableRefObject<string>,
  inlineMailAttachments: IMailAttachment[],
  objectUrlCache: React.MutableRefObject<ObjectUrlCache>,
  addObjUrlCacheEntry: (contentId: string, activeId: string,) => void,
  setObjUrlCache: (contentId: string, objUrl: string) => void,
  reportEvent: EventReporter,
): Promise<void>[] {
  if (!fetchAttachment || !innerHtmlUpdateTrigger || !$elementToQuery.current) return [];

  return Array
    .from($elementToQuery.current.querySelectorAll<HTMLImageElement>(querySelectorString))
    .map(($el) => {
      const srcAtr = $el.getAttribute('src')?.substring(4);
      const targetAttachment = inlineMailAttachments.find(
        (attachment) => attachment.contentId === srcAtr,
      );
      return processImage(
        $el,
        fetchAttachment,
        activeItem,
        activeIdCache,
        targetAttachment,
        objectUrlCache,
        addObjUrlCacheEntry,
        setObjUrlCache,
        reportEvent,
      );
    });
}

const useMailLoadingFeature = <T extends HTMLElement>(
  useGraphApiReturn: ReturnType<typeof useMailApiAccessor>,
  $elementToQuery: React.RefObject<T>,
  querySelectorString: string,
  innerHtmlUpdateTrigger: boolean,
  activeItem: ISplitViewListSingle | undefined,
  inlineMailAttachments: IMailAttachment[],
  listMode: ListModeType,
  reportEvent: EventReporter,
): void => {
  // 画像取得中にユーザーが詳細記事を切り替えた時に処理中断するためactiveIdをrefに保存する
  const activeIdCache = React.useRef(activeItem?.id ?? '');
  React.useEffect(() => {
    activeIdCache.current = activeItem?.id ?? '';
  }, [activeItem]);

  // 初期引数からquery文字列を固定
  const [queryString] = React.useState(querySelectorString);

  const { fetchMailAttachment: fetchAttachment } = useGraphApiReturn;

  // objectUrlを保持するための配列
  const objectUrlCache = React.useRef<ObjectUrlCache>({});

  // objectUrlCacheに新しいエントリを追加
  const addObjUrlCacheEntry = React.useCallback(
    (contentId: string, activeId: string) => {
      objectUrlCache.current[contentId] = { objUrl: '', activeId };
    }, [],
  );

  // objectUrlCacheの既存エントリにobjUrlを保存
  const setObjUrlCache = React.useCallback((contentId: string, objUrl: string) => {
    const entry = objectUrlCache.current[contentId];
    if (!entry) return;
    entry.objUrl = objUrl;
  }, []);

  React.useEffect(() => {
    if (activeItem?.kind !== DataSourceKind.Mail) return;

    processImages(
      $elementToQuery,
      queryString,
      fetchAttachment,
      innerHtmlUpdateTrigger,
      activeItem,
      activeIdCache,
      inlineMailAttachments,
      objectUrlCache,
      addObjUrlCacheEntry,
      setObjUrlCache,
      reportEvent,
    );
  }, [
    $elementToQuery,
    queryString,
    fetchAttachment,
    reportEvent,
    innerHtmlUpdateTrigger,
    activeItem,
    inlineMailAttachments,
    objectUrlCache,
    addObjUrlCacheEntry,
    setObjUrlCache,
    listMode, // リスト切替時にprocessImagesをトリガーさせるためdependenciesに追加
  ]);

  // アンマウント時の後処理
  React.useEffect(() => () => onUnmount(objectUrlCache), []);
};

export default useMailLoadingFeature;
