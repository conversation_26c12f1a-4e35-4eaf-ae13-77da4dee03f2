import * as React from 'react';
import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import dayjs from 'dayjs';
import useBookmarksApiAccessor, { DeleteBookmark<PERSON>pi, <PERSON><PERSON><PERSON>marksApi, PutBookmarkApi } from '../accessors/useBookmarksApiAccessor';
import useBookmarkRepositoryAccessor, {
  AddBookmarkQueue, DeleteBookmarkQueue, GetBookmarkQueues, ReplaceBookmarkQueues, ReplaceBookmarks,
} from '../accessors/useBookmarkRepositoryAccessor';
import { IRepositoryBookmarkQueue } from '../../types/IGeraniumAttaneDB';
import { IBookmarkDict } from '../../types/IBookmarkDict';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { DataSourceKind } from '../../types/DataSourceKind';
import { IChatProperties } from '../../types/ISearchResult';
import {
  bulkResponseConverter, IBatchResponseStatus, IRequestPayload,
} from '../accessors/useGraphApiAccessor';
import chatItemBatchResponseConverter from '../../utilities/chatItemBatchResponseConverter';
import { convertChatResponseToSplitViewSingle, convertMailResponseToSplitViewListSingle } from '../../utilities/transform';
import { IMailResponse } from '../../types/IMailResponse';
import { IChatResponse } from '../../types/IChatResponse';

export type AddRemoteBookmark = (data: ISplitViewListSingle) => Promise<void>;
export type DeleteRemoteBookmark = (data: ISplitViewListSingle) => Promise<void>;
export type FetchRemoteBookmarks = () => Promise<void>;
type UseRemoteBookmarkFeatureReturnType = {
  addRemoteBookmark: AddRemoteBookmark | undefined,
  deleteRemoteBookmark: DeleteRemoteBookmark | undefined,
  fetchRemoteBookmarks: FetchRemoteBookmarks | undefined,
};

export const UseRemoteBookmarkError = {
  IS_FIRST_TIME_SYNC: 'IS_FIRST_TIME_SYNC',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
  QUEUE_SEND_FAIL: 'QUEUE_SEND_FAIL',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

/**
 * bookmarksにqueuesの内容を反映する
 * @param bookmarks
 * @param queues
 */
export function applyQueueToBookmarks(
  bookmarks: ISplitViewListSingle[],
  queues: IRepositoryBookmarkQueue[],
): ISplitViewListSingle[] {

  const putQueues = queues
    .filter((q) => q.type === 'PUT');

  // 追加キューを反映
  putQueues.forEach((q) => {
    bookmarks.push(q.data);
  });

  const deleteQueuesId = queues
    .filter((q) => q.type === 'DELETE')
    .map((q) => q.data.id);

  // 削除キューを反映
  // 削除対象とIDが一致する場合はfilterで削除
  return bookmarks
    .filter((bookmark) => !deleteQueuesId.includes(bookmark.id));
}

/**
 * remoteBookmarksにlocalQueuesとより新しいallBookmarksの項目をマージする
 * @param remoteBookmarks
 * @param localQueues
 * @param localBookmarks
 * @param localBookmarkDict
 */
export function mergeLocalRemoteBookmarks(
  remoteBookmarks: ISplitViewListSingle[],
  localQueues: IRepositoryBookmarkQueue[] | null,
  localBookmarks: ISplitViewListSingle[],
  localBookmarkDict: IBookmarkDict,
): ISplitViewListSingle[] {

  // リモートお気に入りにローカルの差分をマージ
  const mergedRemoteBookmarks = remoteBookmarks.map((remoteItem) => {
    const hasLocalItem = localBookmarkDict[remoteItem.id];

    // ローカルに重複レコードが存在しない場合は終了
    if (!hasLocalItem) return remoteItem;

    const localItem = localBookmarks.find((local) => local.id === remoteItem.id);

    // ローカルに重複レコードが存在しない場合は終了
    if (!localItem) return remoteItem;

    // リモートの日付が新しい場合のみリモートを採用する
    const isRemoteNew = dayjs(localItem.properties.updatedDate)
      .isBefore(remoteItem.properties.updatedDate);
    return isRemoteNew ? remoteItem : localItem;
  });

  // 未送信のキューが溜まっている場合
  if (localQueues && localQueues.length > 0) {
    // リモートお気に入りにqueueの変更をマージ
    return applyQueueToBookmarks(mergedRemoteBookmarks, localQueues);
  }

  return mergedRemoteBookmarks;
}

/**
 * 1件づつQueueを送信し、成功したらqueueから消す
 * 失敗したらそれ以上は送信しない
 * @param q
 * @param putBookmarkApi
 * @param deleteBookmarkApi
 * @param deleteBookmarkQueue
 */
export function sendQueue(
  q: IRepositoryBookmarkQueue,
  putBookmarkApi: PutBookmarkApi,
  deleteBookmarkApi: DeleteBookmarkApi,
  deleteBookmarkQueue: DeleteBookmarkQueue,
): Promise<void> {

  // PUTの場合
  if (q.type === 'PUT') {
    return putBookmarkApi(q.data)
      .then(() => deleteBookmarkQueue(q.data.id));
  }

  // DELETEの場合
  return deleteBookmarkApi(q.data.id)
    .then(() => deleteBookmarkQueue(q.data.id));
}

/**
 * キューを直列で逐次送信し、成功したキューは削除する
 * 途中で失敗した場合はキューを保持し、送信を止める
 * @param queues
 * @param putBookmarkApi
 * @param deleteBookmarkApi
 * @param deleteBookmarkQueue
 * @param eventReporter
 */
export async function sendQueues(
  queues: IRepositoryBookmarkQueue[] | null,
  putBookmarkApi: PutBookmarkApi | undefined,
  deleteBookmarkApi: DeleteBookmarkApi | undefined,
  deleteBookmarkQueue: DeleteBookmarkQueue | undefined,
  eventReporter: EventReporter,
): Promise<void> {

  if (!queues || !putBookmarkApi || !deleteBookmarkApi || !deleteBookmarkQueue) {
    return Promise.resolve();
  }

  // 0件時はwhileを回さずに終了
  if (queues.length === 0) {
    return Promise.resolve();
  }

  // whileループを開始
  let isContinue = true;
  let counter = 0;

  while (isContinue) {
    const q = queues[counter];
    if (!q) {
      isContinue = false;
      break;
    }

    // 直列処理したいのでPromise.allにしない
    // キューの中身を逐次APIに投げ、失敗したらそこで止める
    // eslint-disable-next-line no-await-in-loop
    const result = await sendQueue(q, putBookmarkApi, deleteBookmarkApi, deleteBookmarkQueue)
      // Error型でcatchできるはずだが、whileを抜けられるように必ずError型を返させる
      .catch((reason: Error | unknown) => (reason instanceof Error
        ? reason
        : new Error(UseRemoteBookmarkError.UNKNOWN_ERROR)));

    if (result instanceof Error) {
      // 前の処理が失敗した場合は継続しない
      isContinue = false;

      // 成功キュー、失敗キュー、残りキューをログとして送信
      eventReporter({
        type: EventReportType.SYS_ERROR,
        name: UseRemoteBookmarkError.QUEUE_SEND_FAIL,
        error: result,
        customProperties: {
          failedAt: counter,
          succeededQueues: queues.slice(0, counter),
          remainingQueues: queues.slice(counter),
        },
      });
    } else {
      counter += 1;
    }
  }

  return Promise.resolve();
}

/**
 * ローカルとリモートの両方にお気に入りを追加する機能の実装
 * @param addBookmarkQueue
 * @param getBookmarkQueues
 * @param deleteBookmarkQueue
 * @param deleteBookmarkApi
 * @param putBookmarkApi
 * @param item
 * @param eventReporter
 */
export async function addRemoteBookmarkImpl(
  addBookmarkQueue: AddBookmarkQueue | undefined,
  getBookmarkQueues: GetBookmarkQueues | undefined,
  deleteBookmarkQueue: DeleteBookmarkQueue | undefined,
  deleteBookmarkApi: DeleteBookmarkApi | undefined,
  putBookmarkApi: PutBookmarkApi | undefined,
  item: ISplitViewListSingle,
  eventReporter: EventReporter,
): Promise<void> {

  if (
    !getBookmarkQueues
    || !addBookmarkQueue) {
    return Promise.resolve();
  }

  // キューとお気に入りの両方に追加
  await addBookmarkQueue(item, 'PUT');

  // キュー送信を試行
  return sendQueues(
    await getBookmarkQueues(),
    putBookmarkApi,
    deleteBookmarkApi,
    deleteBookmarkQueue,
    eventReporter,
  );
}

/**
 * ローカルとリモートの両方からお気に入りを削除する機能の実装
 * @param addBookmarkQueue
 * @param getBookmarkQueues
 * @param deleteBookmarkQueue
 * @param deleteBookmarkApi
 * @param putBookmarkApi
 * @param item
 * @param eventReporter
 */
export async function deleteRemoteBookmarkImpl(
  addBookmarkQueue: AddBookmarkQueue | undefined,
  getBookmarkQueues: GetBookmarkQueues | undefined,
  deleteBookmarkQueue: DeleteBookmarkQueue | undefined,
  deleteBookmarkApi: DeleteBookmarkApi | undefined,
  putBookmarkApi: PutBookmarkApi | undefined,
  item: ISplitViewListSingle,
  eventReporter: EventReporter,
): Promise<void> {

  if (
    !getBookmarkQueues
    || !addBookmarkQueue
  ) {
    return Promise.resolve();
  }

  // 削除キューの追加とお気に入り削除
  await addBookmarkQueue(item, 'DELETE');

  // キュー送信を試行
  return sendQueues(
    await getBookmarkQueues(),
    putBookmarkApi,
    deleteBookmarkApi,
    deleteBookmarkQueue,
    eventReporter,
  );
}

/**
 * queueをbookmarksのPUTで全件置き換える
 * @param bookmarks
 * @param replaceBookmarkQueues
 */
export function replaceQueuesByBookmarks(
  bookmarks: ISplitViewListSingle[], replaceBookmarkQueues: ReplaceBookmarkQueues,
): Promise<void> {

  const queues: IRepositoryBookmarkQueue[] = bookmarks.map((bookmark) => ({
    type: 'PUT',
    data: bookmark,
    date: new Date(),
  }));

  return replaceBookmarkQueues(queues);
}

export function filterBookmarksWithMissingRequiredValues(
  bookmarks: ISplitViewListSingle[],
) {
  return bookmarks
    // ??はNull 合体演算子でitem.titleがnull or undefined の場合""を返す
    // item.titleとitem.noteが空欄のものを選出
    .filter((item) => (item.title ?? '') === '' || (item.note ?? '') === '')
    .reduce((acc, item) => {
      if (item.kind === DataSourceKind.Mail) {
        acc.mail.push(item);
      } else if (item.kind === DataSourceKind.Chat) {
        acc.chat.push(item);
      }
      return acc;
    }, { mail: [] as ISplitViewListSingle[], chat: [] as ISplitViewListSingle[] });
}

export function composeMailRequest({ id }: Omit<ISplitViewListSingle, 'title' | 'note'>): IRequestPayload {
  return {
    method: 'GET',
    url: `/me/messages/${id}?$select=receivedDateTime,sentDateTime,hasAttachments,subject,lastModifiedDateTime,id,sender,from,toRecipients,ccRecipients,bccRecipients`,
    id,
  };
}

export function composeChatRequest(item: Omit<ISplitViewListSingle, 'title' | 'note'>): IRequestPayload {
  const properties = item.properties as IChatProperties;
  return {
    method: 'GET',
    url: `me/chats/${properties.chatId}/messages/${item.id}`,
    id: `${properties.messageType}|${properties.teamId ?? ''}|${properties.chatId}|${item.id}`,
  };
}

export function mergeReposTimestamp(items: Pick<ISplitViewListSingle, 'id' | 'reposCreatedDate' | 'reposUpdatedDate'>[]) {
  return (target: ISplitViewListSingle): ISplitViewListSingle => {
    const { reposCreatedDate, reposUpdatedDate } = items.find(({ id }) => target.id === id) ?? {};
    return {
      ...target,
      reposCreatedDate,
      reposUpdatedDate,
    };
  };
}

export async function renewBookmarkData(
  { mail, chat }: {
    mail: Omit<ISplitViewListSingle, 'title' | 'note'>[],
    chat: Omit<ISplitViewListSingle, 'title' | 'note'>[]
  },
  fetchItems: <T>(
    items: IRequestPayload[],
    converter?: bulkResponseConverter<T>
  ) => Promise<IBatchResponseStatus<T>> | undefined,
): Promise<ISplitViewListSingle[]> {
  const mailRequests = mail.map((item) => composeMailRequest(item));
  const chatRequests = chat.map((item) => composeChatRequest(item));
  const mailResults = mail.length > 0
    ? await fetchItems<IMailResponse>(mailRequests) : { responses: [] };
  const chatResults = chat.length > 0
    ? await fetchItems<IChatResponse>(chatRequests, chatItemBatchResponseConverter)
    : { responses: [] };

  if (!mailResults || !chatResults) {
    return [];
  }

  return [
    ...convertMailResponseToSplitViewListSingle(mailResults.responses, { kind: 'Mail' }).map(mergeReposTimestamp(mail)),
    ...convertChatResponseToSplitViewSingle(chatResults.responses, { kind: 'Chat' }, []).map(mergeReposTimestamp(chat))];
}

/**
 * 取得〜マージ処理の実装
 * @param setIsInitialized
 * @param getBookmarks
 * @param putBookmarkApi
 * @param deleteBookmarkApi
 * @param getBookmarkQueues
 * @param replaceBookmarks
 * @param addBookmarkQueue
 * @param deleteBookmarkQueue
 * @param replaceBookmarkQueues
 * @param allBookmarks
 * @param bookmarkDict
 * @param eventReporter
 */
export async function fetchRemoteImpl(
  setIsInitialized: (isInitialized: 'INITIAL' | 'PENDING' | 'DONE') => void,
  getBookmarks: GetBookmarksApi | undefined,
  putBookmarkApi: PutBookmarkApi | undefined,
  deleteBookmarkApi: DeleteBookmarkApi | undefined,
  replaceBookmarks: ReplaceBookmarks | undefined,
  addBookmarkQueue: AddBookmarkQueue | undefined,
  getBookmarkQueues: GetBookmarkQueues | undefined,
  deleteBookmarkQueue: DeleteBookmarkQueue | undefined,
  replaceBookmarkQueues: ReplaceBookmarkQueues | undefined,
  fetchItems: <T>(
    items: IRequestPayload[],
    converter?: bulkResponseConverter<T>
  ) => Promise<IBatchResponseStatus<T>> | undefined,
  allBookmarks: ISplitViewListSingle[],
  bookmarkDict: IBookmarkDict,
  eventReporter: EventReporter,
): Promise<void> {

  if (
    !getBookmarks
    || !getBookmarkQueues
    || !replaceBookmarks
    || !addBookmarkQueue
    || !replaceBookmarkQueues
  ) return Promise.resolve();

  // この後の処理の途中でuseEffectのdependenciesも更新されるため、
  // 初期処理が完了する前に何度も実行される場合がある
  // それを防ぐため処理中のフラグを立てる
  setIsInitialized('PENDING');

  // 状態判定の定数
  const IS_FIRST_TIME_SYNC = 'IS_ONLINE_BUT_NO_CONTENT' as const;
  const IS_OFFLINE_OR_SOMETHING_WRONG = 'IS_OFFLINE_OR_SOMETHING_WRONG' as const;

  // リモートお気に入りとローカルキューを取得する
  const [remoteBookmarks, localQueues] = await Promise.all([
    getBookmarks().catch((reason) => {
      // API取得エラー時の戻り値
      if (reason?.statusCode === 204) {
        // リモート側がゼロ件の場合
        // (単なる通信エラーとは区別する必要がある)
        return IS_FIRST_TIME_SYNC;
      }

      // それ以外のエラー時は異常動作なのでロギング
      eventReporter({
        type: EventReportType.SYS_ERROR,
        name: UseRemoteBookmarkError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: reason,
      });

      // 定数を返却
      return IS_OFFLINE_OR_SOMETHING_WRONG;

    }),
    // ローカルキュー取得
    getBookmarkQueues(),
  ]);

  // オフライン時はスキップ
  if (remoteBookmarks === IS_OFFLINE_OR_SOMETHING_WRONG) {
    setIsInitialized('DONE');
    return Promise.resolve();
  }

  // サーバには接続できているが、まだ一度も同期できない場合
  // まだ一度もremoteに記事が無い場合は初回登録処理を開始
  if (remoteBookmarks === IS_FIRST_TIME_SYNC) {

    // すでにキューがある場合は処理をスキップして終了
    if (localQueues.length > 0) {
      setIsInitialized('DONE');
      return Promise.resolve();
    }

    try {
      // 既存お気に入りをキューに登録
      await replaceQueuesByBookmarks(allBookmarks, replaceBookmarkQueues);
      const updatedQueues = await getBookmarkQueues();

      // ログ送信
      eventReporter({
        type: EventReportType.SYS_EVENT,
        name: UseRemoteBookmarkError.IS_FIRST_TIME_SYNC,
        customProperties: {
          // 通常は2つとも同数になる
          bookmarksLength: allBookmarks.length,
          createdQueueLength: updatedQueues.length,
        },
      });

      // queueの送信を試みる
      await sendQueues(
        updatedQueues, putBookmarkApi, deleteBookmarkApi, deleteBookmarkQueue, eventReporter,
      );
      return Promise.resolve();

    } finally {
      setIsInitialized('DONE');
    }
  }

  // 通常のマージ処理を開始
  const result = mergeLocalRemoteBookmarks(
    remoteBookmarks,
    localQueues,
    allBookmarks,
    bookmarkDict,
  );

  // メール/チャットのTitle,Noteが存在しないものを補完する
  const targets = filterBookmarksWithMissingRequiredValues(result);
  const targetCount = Object.values(targets).reduce((acc, arr) => acc + arr.length, 0);
  const renewedResult = await renewBookmarkData(targets, fetchItems);
  const updatedResult = result.map(
    (item) => renewedResult.find((newItem) => item.id === newItem.id) ?? item,
  );
  // 補完できない場合は補完前の値を使用する
  const isRenew = targetCount !== 0 && renewedResult.length !== 0;
  try {
    // bookmarkを上書く
    await replaceBookmarks(isRenew ? updatedResult : result);
    // queueの送信を試みる
    await sendQueues(
      localQueues, putBookmarkApi, deleteBookmarkApi, deleteBookmarkQueue, eventReporter,
    );
    return Promise.resolve();

  } finally {
    setIsInitialized('DONE');
  }
}

/**
 * リモート上のAPIとブラウザローカルのお気に入りデータの追加/削除/同期処理をする機能
 */
const useRemoteBookmarkFeature = (
  useRepositoryReturn: ReturnType<typeof useBookmarkRepositoryAccessor>,
  useBookmarksApiReturn: ReturnType<typeof useBookmarksApiAccessor>,
  syncBookmarkStateReturn: [boolean, (start: boolean) => void],
  fetchItems: <T>(
    items: IRequestPayload[],
    converter?: bulkResponseConverter<T>
  ) => Promise<IBatchResponseStatus<T>> | undefined,
  eventReporter: EventReporter,
): UseRemoteBookmarkFeatureReturnType => {

  // useRepositoryからメンバ取得
  const {
    replaceBookmarks,
    getBookmarkQueues,
    addBookmarkQueue,
    deleteBookmarkQueue,
    replaceBookmarkQueues,
    bookmarkDict,
    allBookmarks,
  } = useRepositoryReturn;

  // useBookmarksApiからメンバ取得
  const { putBookmarkApi, deleteBookmarkApi, getBookmarksApi } = useBookmarksApiReturn;

  // お気に入り同期開始フラグ
  const [syncBookmark, setSyncBookmark] = syncBookmarkStateReturn;
  const initialRunTrigger = React.useRef<boolean>(false);

  // 初期化完了済フラグ
  const [isInitialized, setIsInitialized] = React.useState<'INITIAL' | 'PENDING' | 'DONE'>('INITIAL');

  /**
   * リモート取得処理の実装
   */
  const fetchRemoteBookmarks = React.useCallback(async (onSuccess?: () => void) => {
    if (isInitialized !== 'PENDING') {
      await fetchRemoteImpl(
        setIsInitialized,
        getBookmarksApi,
        putBookmarkApi,
        deleteBookmarkApi,
        replaceBookmarks,
        addBookmarkQueue,
        getBookmarkQueues,
        deleteBookmarkQueue,
        replaceBookmarkQueues,
        fetchItems,
        allBookmarks,
        bookmarkDict,
        eventReporter,
      );
      if (onSuccess) {
        onSuccess();
      }
    }
  }, [
    isInitialized,
    getBookmarksApi,
    putBookmarkApi,
    deleteBookmarkApi,
    replaceBookmarks,
    addBookmarkQueue,
    getBookmarkQueues,
    deleteBookmarkQueue,
    replaceBookmarkQueues,
    fetchItems,
    allBookmarks,
    bookmarkDict,
    eventReporter,
  ]);

  /**
   * ローカルとリモートの両方にお気に入り追加
   */
  const addRemoteBookmark: AddRemoteBookmark = React.useCallback(
    async (item: ISplitViewListSingle) => addRemoteBookmarkImpl(
      addBookmarkQueue,
      getBookmarkQueues,
      deleteBookmarkQueue,
      deleteBookmarkApi,
      putBookmarkApi,
      item,
      eventReporter,
    ),
    [
      addBookmarkQueue,
      deleteBookmarkQueue,
      getBookmarkQueues,
      putBookmarkApi,
      deleteBookmarkApi,
      eventReporter,
    ],
  );

  /**
   * ローカルとリモートの両方からお気に入り削除
   */
  const deleteRemoteBookmark: DeleteRemoteBookmark = React.useCallback(
    async (item: ISplitViewListSingle) => deleteRemoteBookmarkImpl(
      addBookmarkQueue,
      getBookmarkQueues,
      deleteBookmarkQueue,
      deleteBookmarkApi,
      putBookmarkApi,
      item,
      eventReporter,
    ),
    [
      addBookmarkQueue,
      getBookmarkQueues,
      deleteBookmarkQueue,
      deleteBookmarkApi,
      putBookmarkApi,
      eventReporter,
    ],
  );

  // 初回同期処理の実行
  React.useEffect(() => {
    if (
      !getBookmarksApi
      || !getBookmarkQueues
      || !replaceBookmarks
      || !addBookmarkQueue
      || !replaceBookmarkQueues
    ) return;

    if (initialRunTrigger.current) return;

    initialRunTrigger.current = true;
    fetchRemoteBookmarks();
  }, [
    fetchRemoteBookmarks,
    getBookmarksApi,
    addBookmarkQueue,
    getBookmarkQueues,
    replaceBookmarkQueues,
    replaceBookmarks,
  ]);

  // 同期処理の実行
  React.useEffect(() => {
    if (!syncBookmark) return;
    fetchRemoteBookmarks(() => {
      setSyncBookmark(false);
    });
  }, [fetchRemoteBookmarks, setSyncBookmark, syncBookmark]);

  return {
    addRemoteBookmark,
    deleteRemoteBookmark,
    fetchRemoteBookmarks,
  };
};

export default useRemoteBookmarkFeature;
