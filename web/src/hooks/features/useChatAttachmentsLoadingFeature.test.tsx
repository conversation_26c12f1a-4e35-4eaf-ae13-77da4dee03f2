import * as React from 'react';
import { render } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';
import useChatAttachmentsLoadingFeature, { processAttachments, setReplyCard } from './useChatAttachmentsLoadingFeature';
import { ListMode } from '../../components/domains/split-view/types/ListMode';
import { IChatAttachment } from '../../components/domains/split-view/types/IChatAttachment';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { createSplitViewListSingle } from '../../utilities/test';

jest.mock('../../utilities/environment');

const classList = {
  add: jest.fn(),
  remove: jest.fn(),
  replace: jest.fn(),
};
const setAttributeMock = jest.fn();
const getAttributeMock = jest.fn();
const removeAttributeMock = jest.fn();
const appendChildMock = jest.fn();
const datasetMock: { [k: string]: string } = {};
const $el = {
  classList,
  setAttribute: setAttributeMock,
  getAttribute: getAttributeMock,
  removeAttribute: removeAttributeMock,
  appendChild: appendChildMock,
  dataset: datasetMock,
} as unknown as HTMLDivElement;

describe('setReplyCard', () => {
  beforeEach(() => {
    getAttributeMock.mockClear();
    appendChildMock.mockClear();
  });

  describe('when messageSender is user', () => {
    const attachment: IChatAttachment = {
      id: '111',
      name: 'name',
      contentType: 'contentType',
      contentUrl: 'contentUrl',
      content: '{"messageId":"111","messagePreview":"Test Message","messageSender":{"application":null,"device":null,"user":{"userIdentityType":"aadUser","id":"test-user-id","displayName":"テスト太郎"}}}',
    };

    it('should call $el.appendChild with messageSender.user', () => {
      const { asFragment } = render(
        <>
          <div className="attachment-sender-name">テスト太郎</div>
          <div className="attachment-content-preview">Test Message</div>
        </>,
      );
      setReplyCard($el, attachment);
      expect(classList.add).toHaveBeenCalledWith('attachment-reply-card');
      expect(appendChildMock).toBeCalledTimes(1);
      expect(appendChildMock).toBeCalledWith(asFragment());
    });
  });

  describe('when messageSender is bot', () => {
    const attachment: IChatAttachment = {
      id: '111',
      name: 'name',
      contentType: 'contentType',
      contentUrl: 'contentUrl',
      content: '{"messageId":"111","messagePreview":"Test Message","messageSender":{"application":{"applicationIdentityType":"bot","id":"test-user-id","displayName":"Bot"},"device":null,"user":null}}',
    };

    it('should call $el.appendChild with messageSender.application', () => {
      const { asFragment } = render(
        <>
          <div className="attachment-sender-name">Bot</div>
          <div className="attachment-content-preview">Test Message</div>
        </>,
      );
      setReplyCard($el, attachment);
      expect(classList.add).toHaveBeenCalledWith('attachment-reply-card');
      expect(appendChildMock).toBeCalledTimes(1);
      expect(appendChildMock).toBeCalledWith(asFragment());
    });
  });
});

describe('processAttachments', () => {
  beforeEach(() => {
    getAttributeMock.mockClear();
    appendChildMock.mockClear();
  });

  function processAttachmentsCase(
    $element: null | HTMLElement,
    querySelectorString: string,
    innerHtmlUpdateTrigger: boolean,
    chatAttachments: IChatAttachment[],
  ) {

    return processAttachments(
      { current: $element },
      querySelectorString,
      innerHtmlUpdateTrigger,
      chatAttachments,
    );
  }

  describe('when innerHtmlUpdateTrigger, or $elementToQuery.current is falsy', () => {
    it('should not call `getAttribute`', () => {
      const attachments: IChatAttachment[] = [{
        id: '111',
        name: 'name',
        contentType: 'contentType',
        contentUrl: 'contentUrl',
        content: '{"messageId":"111","messagePreview":"Test Message","messageSender":{"application":null,"device":null,"user":{"userIdentityType":"aadUser","id":"test-user-id","displayName":"テスト太郎"}}}',
      }];
      const mockElement = {} as HTMLElement;
      processAttachmentsCase(null, 'abc', true, attachments);
      processAttachmentsCase(mockElement, 'abc', false, attachments);
      expect(getAttributeMock).not.toBeCalled();
    });
  });

  describe('when the querySelectorAll returns 3 elements', () => {
    it('should return an array which length is 3', () => {
      const attachments: IChatAttachment[] = [{
        id: '111',
        name: 'name',
        contentType: 'contentType',
        contentUrl: 'contentUrl',
        content: '{"messageId":"111","messagePreview":"Test Message","messageSender":{"application":null,"device":null,"user":{"userIdentityType":"aadUser","id":"test-user-id","displayName":"テスト太郎"}}}',
      }];
      const mockElement = {
        querySelectorAll: jest.fn().mockReturnValue({
          0: $el, 1: $el, 2: $el, length: 3,
        }),
      } as unknown as HTMLElement;
      processAttachmentsCase(mockElement, 'abc', true, attachments);
      expect(getAttributeMock).toBeCalledTimes(3);
    });

    it('should only be called appendChild when it matches the condition', () => {
      getAttributeMock.mockReturnValueOnce('111');
      getAttributeMock.mockReturnValue('222');
      const attachments: IChatAttachment[] = [{
        id: '111',
        name: 'name',
        contentType: 'messageReference',
        contentUrl: 'contentUrl',
        content: '{"messageId":"111","messagePreview":"Test Message","messageSender":{"application":null,"device":null,"user":{"userIdentityType":"aadUser","id":"test-user-id","displayName":"テスト太郎"}}}',
      },
      {
        id: '222',
        name: 'name',
        contentType: 'other',
        contentUrl: 'contentUrl',
        content: '{"messageId":"111","messagePreview":"Test Message","messageSender":{"application":null,"device":null,"user":{"userIdentityType":"aadUser","id":"test-user-id","displayName":"テスト太郎"}}}',
      },
      ];
      const mockElement = {
        querySelectorAll: jest.fn().mockReturnValue({
          0: $el, 1: $el, 2: $el, length: 3,
        }),
      } as unknown as HTMLElement;
      processAttachmentsCase(mockElement, 'abc', true, attachments);
      expect(getAttributeMock).toBeCalledTimes(3);
      // リプライカードは最初の1件のみ
      expect(appendChildMock).toBeCalledTimes(1);
    });
  });
});

describe('useChatAttachmentsLoadingFeature', () => {

  beforeEach(() => {
    getAttributeMock.mockClear();
    appendChildMock.mockClear();
  });

  const getHook = (activeItem: ISplitViewListSingle) => {
    const mockElement = {
      querySelectorAll: jest.fn().mockReturnValue({
        0: $el, 1: $el, 2: $el, length: 3,
      }),
    } as unknown as HTMLElement;

    return renderHook(() => useChatAttachmentsLoadingFeature(
      { current: mockElement },
      'div',
      true,
      activeItem,
      [],
      ListMode.SEARCH,
    ));
  };

  describe('when kind is Chat', () => {
    const activeItem = {
      id: 'test2',
      title: 'botDisplayName',
      kind: 'Chat',
      body: '<p>test text |XXXXXXXXkeywordXXXXXXXXXXXXXXXXXXXXXX|</p>',
      displayDate: '2023-05-25T19:00:00.000Z',
      note: 'search result note',
      properties: {
        bookmarkNote: 'bookmark note',
        chatAttachments: [
          {
            id: 'attachment1',
            name: 'attachment.jpg',
            contentType: 'messageReference',
            contentUrl: 'https://examle.com/image.jpg',
            content: 'test content',
          },
        ],
        from: {
          id: 'botId',
          displayName: 'botDisplayName',
          applicationIdentityType: 'bot',
        },
        updatedDate: '2023-05-26T06:05:00+09:00',
        lastModifiedDateTime: '2023-05-26T06:05:00+09:00',
        hasAttachments: true,
        chatId: 'chatId2',
        teamId: undefined,
        contentType: 'html',
        messageType: 'chat',
      },
    } as ISplitViewListSingle;

    it('should invoke the hook function', () => {
      getHook(activeItem);
      expect(getAttributeMock).toBeCalled();
    });

  });
  describe('when kind is not Chat', () => {
    it.each([createSplitViewListSingle({
      kind: 'Mail',
      id: '1',
      note: '<EMAIL>',
      title: 'mail subject',
    }),
    createSplitViewListSingle({
      kind: 'SPO',
      id: '1',
      note: 'document subject',
      title: 'document title',
    }),
    ])('should not invoke the hook function', (activeItem) => {
      getHook(activeItem);
      expect(getAttributeMock).not.toBeCalled();
    });

  });
});
