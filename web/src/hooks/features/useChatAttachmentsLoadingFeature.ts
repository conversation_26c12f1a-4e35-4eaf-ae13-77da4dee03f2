import React from 'react';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { DataSourceKind } from '../../types/DataSourceKind';
import { ListModeType } from '../../components/domains/split-view/types/ListMode';
import { DATA_ATTR_CHAT_ATTACHMENT_ID } from '../../utilities/sanitize';
import { ITeamworkApplicationIdentity, ITeamworkUserIdentity } from '../../types/IChatResponse';
import { IChatAttachment } from '../../components/domains/split-view/types/IChatAttachment';

export interface IMessageReference {
  messageId: string;
  messagePreview: string;
  messageSender: {
    user: ITeamworkUserIdentity | null,
    application: ITeamworkApplicationIdentity | null,
  };
}

export function setReplyCard(
  $el: HTMLElement,
  attachment: IChatAttachment,
): void {
  const messageReference: IMessageReference = JSON.parse(attachment.content ?? '');

  const fragment = document.createDocumentFragment();

  $el.classList.add('attachment-reply-card');

  const senderName = document.createElement('div');
  senderName.textContent = messageReference.messageSender?.user?.displayName
    ?? messageReference.messageSender?.application?.displayName ?? '';
  senderName.classList.add('attachment-sender-name');
  fragment.appendChild(senderName);

  const contentPreview = document.createElement('div');
  contentPreview.textContent = messageReference.messagePreview;
  contentPreview.classList.add('attachment-content-preview');
  fragment.appendChild(contentPreview);

  $el.appendChild(fragment);
}

export function processAttachments(
  $elementToQuery: React.RefObject<HTMLElement>,
  querySelectorString: string,
  innerHtmlUpdateTrigger: boolean,
  chatAttachments: IChatAttachment[],
): void {
  if (!innerHtmlUpdateTrigger || !$elementToQuery.current) return;

  Array.from($elementToQuery.current.querySelectorAll<HTMLDivElement>(querySelectorString))
    .forEach(($el) => {
      const attachmentId = $el.getAttribute(DATA_ATTR_CHAT_ATTACHMENT_ID) ?? '';

      // TODO: Bug38821に関連して複数回処理が呼び出されてしまうためカスタムデータ属性を削除することで重複表示されないようにする
      // 複数回呼ばれることがおかしいのでBug修正後は不要になる(ただ残しても害はない)
      $el.removeAttribute(DATA_ATTR_CHAT_ATTACHMENT_ID);

      const target = chatAttachments.find((attachment) => attachment.id === attachmentId);
      // attachmentがリプライカードだった場合
      if (!target || target.contentType !== 'messageReference') return;
      setReplyCard($el, target);
    });
}

const useChatAttachmentsLoadingFeature = <T extends HTMLElement>(
  $elementToQuery: React.RefObject<T>,
  querySelectorString: string,
  innerHtmlUpdateTrigger: boolean,
  activeItem: ISplitViewListSingle | undefined,
  chatAttachments: IChatAttachment[],
  listMode: ListModeType,
): void => {

  // 初期引数からquery文字列を固定
  const [queryString] = React.useState(querySelectorString);

  React.useEffect(() => {
    if (activeItem?.kind !== DataSourceKind.Chat) return;

    processAttachments(
      $elementToQuery,
      queryString,
      innerHtmlUpdateTrigger,
      chatAttachments,
    );
  }, [
    $elementToQuery,
    queryString,
    innerHtmlUpdateTrigger,
    chatAttachments,
    activeItem,
    listMode, // リスト切替時にトリガーさせるためdependenciesに追加
  ]);

};

export default useChatAttachmentsLoadingFeature;
