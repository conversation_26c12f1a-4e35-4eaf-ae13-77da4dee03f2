import { EventReportType } from '@avanade-teams/app-insights-reporter';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { ObjectUrlCache } from '../../types/ObjectUrlCache';
import { UseGraphApiError } from '../accessors/useGraphApiAccessor';
import { FetchMailAttachment } from '../accessors/useMailApiAccessor';
import { processImage, processImages } from './useMailLoadingFeature';
import { IMailAttachment } from '../../components/domains/split-view/types/IMailAttachment';

jest.mock('../../utilities/environment');

const classList = {
  add: jest.fn(),
  remove: jest.fn(),
};
const setAttributeMock = jest.fn();
const getAttributeMock = jest.fn();
const datasetMock: { [k: string]: string } = {};
const $el = {
  classList,
  setAttribute: setAttributeMock,
  getAttribute: getAttributeMock,
  dataset: datasetMock,
} as unknown as HTMLImageElement;
describe('processImages', () => {
  const reportEvent = jest.fn();
  const fetchAttachmentMock = jest.fn().mockRejectedValue(new Error('abc'));

  function processImagesCase(
    contentId: string,
    activeItem: ISplitViewListSingle | undefined,
    fetchAttachment: FetchMailAttachment | undefined,
    $element: null | HTMLElement,
    querySelectorString: string,
    innerHtmlUpdateTrigger: boolean,
    inlineMailAttachments: IMailAttachment[],
  ) {
    datasetMock.contentId = contentId;

    const objectUrlCache: React.MutableRefObject<ObjectUrlCache> = {
      current: {
        [contentId]: { activeId: activeItem?.id ?? '', objUrl: '' },
      },
    };
    const activeIdCache = { current: activeItem?.id ?? '' };

    return processImages(
      { current: $element },
      querySelectorString,
      fetchAttachment,
      innerHtmlUpdateTrigger,
      activeItem,
      activeIdCache,
      inlineMailAttachments,
      objectUrlCache,
      jest.fn(),
      jest.fn(),
      reportEvent,
    );
  }

  describe('when the fetchAttachment, innerHtmlUpdateTrigger, or $elementToQuery.current is falsy', () => {
    it('should return []', () => {
      const activeItem = {
        id: '123',
        kind: 'Mail',
      } as ISplitViewListSingle;
      const inlineMailAttachments = [{
        id: '123',
        name: 'name',
        contentType: 'type',
        size: 100,
        isInline: true,
        contentId: 'abc',
      }] as IMailAttachment[];
      const mockElement = {} as HTMLElement;
      expect(processImagesCase('abc', activeItem, undefined, mockElement, 'abc', true, inlineMailAttachments)).toStrictEqual([]);
      expect(processImagesCase('abc', activeItem, fetchAttachmentMock, null, 'abc', true, inlineMailAttachments)).toStrictEqual([]);
      expect(processImagesCase('abc', activeItem, fetchAttachmentMock, mockElement, 'abc', false, inlineMailAttachments)).toStrictEqual([]);
    });
  });

  describe('when the querySelectorAll returns 3 elements', () => {
    it('should return an array which length is 3', () => {
      const activeItem = {
        id: '123',
        kind: 'Mail',
      } as ISplitViewListSingle;
      const inlineMailAttachments = [{
        id: '123',
        name: 'name',
        contentType: 'type',
        size: 100,
        isInline: true,
        contentId: 'abc',
      }] as IMailAttachment[];
      const mockElement = {
        querySelectorAll: jest.fn().mockReturnValue({
          0: $el, 1: $el, 2: $el, length: 3,
        }),
      } as unknown as HTMLElement;
      expect(processImagesCase('abc', activeItem, fetchAttachmentMock, mockElement, 'abc', true, inlineMailAttachments)).toHaveLength(3);
    });

    describe('logging FETCH_FILE_BLOB_FAIL', () => {
      const reportEventMock = jest.fn();
      const setObjUrlCacheMock = jest.fn();
      const addObjUrlCacheEntryMock = jest.fn();
      const fetchMock = jest.fn();

      beforeEach(() => {
        fetchMock.mockClear();
        reportEventMock.mockClear();
        setObjUrlCacheMock.mockClear();
        addObjUrlCacheEntryMock.mockClear();
      });

      it('should log FETCH_FILE_BLOB_FAIL when fetch attachment failed', async () => {
        const error = new Error(UseGraphApiError.REQUIRED_PARAM_NOT_AVAILABLE);
        fetchMock.mockRejectedValue(error);
        const activeItemMock = {
          id: 'mail-id1',
          kind: 'Mail',
          title: 'title',
          note: 'note',
          displayDate: '2023-04-04T02:02:02.000Z',
          properties: {},
        } as ISplitViewListSingle;
        const attachment = {
          id: '1',
          name: 'attachment.jpg',
          contentType: 'image/jpeg',
          contentId: 'cid-1',
          size: 2341231231,
          isInline: true,
        };

        const activeIdCache = { current: '1' };
        const objectUrlCache = {
          current: {
            1: {
              activeId: '',
              objUrl: 'https://img.url',
            },
          },
        };
        await processImage(
          $el,
          fetchMock,
          activeItemMock,
          activeIdCache,
          attachment,
          objectUrlCache,
          addObjUrlCacheEntryMock,
          setObjUrlCacheMock,
          reportEventMock,
        );

        expect(reportEventMock).toHaveBeenCalledWith({
          name: 'FETCH_FILE_BLOB_FAIL',
          type: EventReportType.SYS_EVENT,
          error,
          customProperties: {
            error,
            request: {
              kind: 'Mail',
              mailId: 'mail-id1',
              attachmentId: '1',
            },
          },
        });
      });

      it('should log FETCH_FILE_BLOB_FAIL when fetch attachment failed', async () => {
        fetchMock.mockResolvedValue(undefined);
        const activeItemMock = {
          id: 'mail-id1',
          kind: 'Mail',
          title: 'title',
          note: 'note',
          displayDate: '2023-04-04T02:02:02.000Z',
          properties: {},
        } as ISplitViewListSingle;
        const attachment = {
          id: '1',
          name: 'attachment.jpg',
          contentType: 'image/jpeg',
          contentId: 'cid-1',
          size: 2341231231,
          isInline: true,
        };

        const activeIdCache = { current: '1' };
        const objectUrlCache = {
          current: {
            1: {
              activeId: '',
              objUrl: 'https://img.url',
            },
          },
        };
        await processImage(
          $el,
          fetchMock,
          activeItemMock,
          activeIdCache,
          attachment,
          objectUrlCache,
          addObjUrlCacheEntryMock,
          setObjUrlCacheMock,
          reportEventMock,
        );

        expect(reportEventMock).not.toHaveBeenCalled();
      });
    });
  });
});
