import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';
import { DataSourceKindType } from '../../types/DataSourceKind';
import { ObjectUrlCache } from '../../types/ObjectUrlCache';
import { FetchChatAttachment } from '../accessors/useChatApiAccessor';
import { UseGraphApiError } from '../accessors/useGraphApiAccessor';
import { processImage, processImages } from './useChatImgLoadingFeature';

jest.mock('../../utilities/environment');

const classList = {
  add: jest.fn(),
  remove: jest.fn(),
};
const setAttributeMock = jest.fn();
const getAttributeMock = jest.fn();
const datasetMock: { [k: string]: string } = {};
const $el = {
  classList,
  setAttribute: setAttributeMock,
  getAttribute: getAttributeMock,
  dataset: datasetMock,
} as unknown as HTMLImageElement;
describe('processImages', () => {
  const reportEvent = jest.fn();
  const fetchAttachmentMock = jest.fn().mockRejectedValue(new Error('abc'));

  function processImagesCase(
    contentId: string,
    activeItem: ISplitViewListSingle | undefined,
    fetchChatAttachment: FetchChatAttachment | undefined,
    $element: null | HTMLElement,
    querySelectorString: string,
    innerHtmlUpdateTrigger: boolean,
  ) {
    datasetMock.contentId = contentId;

    const objectUrlCache: React.MutableRefObject<ObjectUrlCache> = {
      current: {
        [contentId]: { activeId: activeItem?.id ?? '', objUrl: '' },
      },
    };
    const activeIdCache = { current: activeItem?.id ?? '' };

    return processImages(
      { current: $element },
      querySelectorString,
      fetchChatAttachment,
      innerHtmlUpdateTrigger,
      activeItem,
      activeIdCache,
      objectUrlCache,
      jest.fn(),
      jest.fn(),
      reportEvent,
    );
  }

  describe('when the fetchAttachment, innerHtmlUpdateTrigger, or $elementToQuery.current is falsy', () => {
    it('should return []', () => {
      const activeItem = {
        id: '123',
        kind: 'Chat',
      } as ISplitViewListSingle;
      //      const inlineChatAttachments = [{
      //        id: 'abc',
      //        name: 'string',
      //        contentType: 'def',
      //        contentUrl: 'ghi',
      //        content: 'jkl',
      //      }] as IChatAttachment[];
      const mockElement = {} as HTMLElement;
      expect(processImagesCase('abc', activeItem, undefined, mockElement, 'abc', true)).toStrictEqual([]);
      expect(processImagesCase('abc', activeItem, fetchAttachmentMock, null, 'abc', true)).toStrictEqual([]);
      expect(processImagesCase('abc', activeItem, fetchAttachmentMock, mockElement, 'abc', false)).toStrictEqual([]);
    });
  });

  describe('when the querySelectorAll returns 3 elements', () => {
    it('should return an array which length is 3', () => {
      const activeItem = {
        id: '123',
        kind: 'Chat',
      } as ISplitViewListSingle;
      // const inlineMailAttachments = [{
      //  id: 'abc',
      //  name: 'string',
      //  contentType: 'def',
      //  contentUrl: 'ghi',
      //  content: 'jkl',
      // }] as IChatAttachment[];
      const mockElement = {
        querySelectorAll: jest.fn().mockReturnValue({
          0: $el, 1: $el, 2: $el, length: 3,
        }),
      } as unknown as HTMLElement;
      expect(processImagesCase('abc', activeItem, fetchAttachmentMock, mockElement, 'abc', true)).toHaveLength(3);
    });
  });

  describe('should log FETCH_FILE_BLOB_FAIL when fetch attachment failed', () => {
    const reportEventMock = jest.fn();
    const setObjUrlCacheMock = jest.fn();
    const addObjUrlCacheEntryMock = jest.fn();
    const fetchMock = jest.fn();

    beforeEach(() => {
      fetchMock.mockClear();
      reportEventMock.mockClear();
      setObjUrlCacheMock.mockClear();
      addObjUrlCacheEntryMock.mockClear();
    });
    it('should log FETCH_FILE_BLOB_FAIL when fetch attachment failed', async () => {
      fetchMock.mockResolvedValue(undefined);
      const activeItemMock = {
        id: 'chat-id1',
        note: '田中',
        displayDate: '20230405',
        properties: {
          chatId: '',
          messageType: '',
        },
        kind: 'Chat' as DataSourceKindType,
        title: 'qwerty',
      };
      const srcAtr = 'tgbjgbkhgbhjkjuhjkj';

      const activeIdCache = { current: '1' };
      const objectUrlCache = {
        current: {
          1: {
            activeId: '',
            objUrl: 'https://img.url',
          },
        },
      };
      await processImage(
        $el,
        fetchMock,
        srcAtr,
        activeItemMock,
        activeIdCache,
        objectUrlCache,
        addObjUrlCacheEntryMock,
        setObjUrlCacheMock,
        reportEventMock,
      );

      expect(reportEventMock).not.toHaveBeenCalled();
    });

    it('should log FETCH_FILE_BLOB_FAIL when fetch attachment', async () => {
      const error = new Error(UseGraphApiError.REQUIRED_PARAM_NOT_AVAILABLE);
      fetchMock.mockRejectedValue(error);
      const activeItemMock = {
        id: 'chat-id2',
        note: '佐藤',
        displayDate: '20230405',
        properties: {
          teamId: '123',
          chatId: '456',
          messageType: 'chat',
        },
        kind: 'Chat' as DataSourceKindType,
        title: 'qwerty',
      };
      const srcAtr = 'tgbjgbkhgbhjkjuhjkj';

      const activeIdCache = { current: '1' };
      const objectUrlCache = {
        current: {
          1: {
            activeId: '',
            objUrl: 'https://img.url',
          },
        },
      };
      await processImage(
        $el,
        fetchMock,
        srcAtr,
        activeItemMock,
        activeIdCache,
        objectUrlCache,
        addObjUrlCacheEntryMock,
        setObjUrlCacheMock,
        reportEventMock,
      );

      expect(reportEventMock).toHaveBeenCalledTimes(1);
    });
  });
});
