import { RefObject } from 'react';
import { renderHook } from '@testing-library/react-hooks';
import { IChatProperties } from '../../types/ISearchResult';
import useChatMentionFeature, { decorate } from './useChatMentionFeature';
import { ListMode } from '../../components/domains/split-view/types/ListMode';
import { ISplitViewDetail } from '../../components/domains/split-view/types/ISplitViewDetail';

describe('decorate', () => {
  const getAttributeMock = jest.fn();
  const otherGetAttributeMock = jest.fn();
  const addClassMock = jest.fn();
  const querySelectorAllMock = jest.fn();
  const $mockTarget = {
    getAttribute: getAttributeMock,
    classList: {
      add: addClassMock,
    },
  };
  const $otherElement = {
    getAttribute: otherGetAttributeMock,
    classList: {
      add: addClassMock,
    },
  };

  const $bodyRef = {
    current:
    {
      querySelectorAll: querySelectorAllMock,
    },
  };

  beforeEach(() => {
    querySelectorAllMock.mockClear();
    getAttributeMock.mockClear();
    otherGetAttributeMock.mockClear();
    addClassMock.mockClear();
  });

  it('should append .me class', () => {
    querySelectorAllMock.mockImplementation(() => [$mockTarget, $otherElement]);
    getAttributeMock.mockReturnValue('0');
    otherGetAttributeMock.mockReturnValue('1');
    decorate<HTMLElement>(
      $bodyRef as unknown as RefObject<HTMLElement>,
      'span.chat-mention',
      {
        id: 'chat-id1',
        properties: {
          mentions: [
            {
              id: 0,
              mentioned: {
                user: {
                  id: 'user-id1',
                },
              },
            },
            {
              id: 1,
              mentioned: {
                user: {
                  id: 'user-id1',
                },
              },
            },
          ],
        } as IChatProperties,
        kind: 'Chat',
        title: 'opqr',
      },
      'user-id1',
    );
    expect(querySelectorAllMock).toHaveBeenCalledWith('span.chat-mention');
    expect(addClassMock).toHaveBeenCalledTimes(2);
    expect(addClassMock).toHaveBeenCalledWith('me');
  });

  it('should append .me class only one time', () => {
    querySelectorAllMock.mockImplementation(() => [$mockTarget, $otherElement]);
    getAttributeMock.mockReturnValue('0');
    otherGetAttributeMock.mockReturnValue('1');
    decorate<HTMLElement>(
      $bodyRef as unknown as RefObject<HTMLElement>,
      'span.chat-mention',
      {
        id: 'chat-id1',
        properties: {
          mentions: [
            {
              id: 0,
              mentioned: {
                user: {
                  id: 'user-id1',
                },
              },
            },
            {
              id: 1,
              mentioned: {
                user: {
                  id: 'user-id2',
                },
              },
            },
          ],
        } as IChatProperties,
        kind: 'Chat',
        title: 'xyz',
      },
      'user-id1',
    );
    expect(querySelectorAllMock).toHaveBeenCalledWith('span.chat-mention');
    expect(getAttributeMock).toHaveBeenCalledWith('data-chat-mention-id');
    expect(otherGetAttributeMock).toHaveBeenCalledWith('data-chat-mention-id');
    expect(addClassMock).toHaveBeenCalledTimes(1);
    expect(addClassMock).toHaveBeenCalledWith('me');
  });

  it('should not append .me class', () => {
    querySelectorAllMock.mockImplementation(() => [$mockTarget, $otherElement]);
    getAttributeMock.mockReturnValue('0');
    otherGetAttributeMock.mockReturnValue('1');
    decorate<HTMLElement>(
      $bodyRef as unknown as RefObject<HTMLElement>,
      'span.chat-mention',
      {
        id: 'chat-id1',
        properties: {
          mentions: [
            {
              id: 0,
              mentioned: {
                user: {
                  id: 'user-id2',
                },
              },
            },
            {
              id: 0,
              mentioned: {
                user: {
                  id: 'user-id3',
                },
              },
            },
          ],
        } as IChatProperties,
        kind: 'Chat',
        title: 'abcde',
      },
      'user-id1',
    );
    expect(addClassMock).not.toHaveBeenCalled();
  });
  describe('useChatMentionFeature', () => {

    function getHook(detail: ISplitViewDetail) {
      return renderHook(() => useChatMentionFeature($bodyRef as unknown as RefObject<HTMLElement>, '.chat-mention', 'user-id1', detail, ListMode.SEARCH));
    }
    it('should process mentions when chat item is selected', () => {
      querySelectorAllMock.mockImplementation(() => [$mockTarget, $otherElement]);
      getAttributeMock.mockReturnValue('0');
      otherGetAttributeMock.mockReturnValue('1');
      const hook = getHook({
        kind: 'Chat',
        id: '1',
        note: 'chat note',
        title: 'User1',
        properties: {
          mentions: [
            {
              id: 0,
              mentioned: {
                user: {
                  id: 'user-id1',
                },
              },
            },
            {
              id: 1,
              mentioned: {
                user: {
                  id: 'user-id1',
                },
              },
            },
          ],
        } as IChatProperties,
      });
      expect(hook.result.error).toBeFalsy();
      expect(addClassMock).toHaveBeenCalled();
    });

    it.each([{
      kind: 'Mail',
      id: '1',
      note: '<EMAIL>',
      title: 'mail subject',
    },
    {
      kind: 'SPO',
      id: '1',
      note: 'document subject',
      title: 'document title',
    },
    ] as ISplitViewDetail[])('should do nothing when $kind item is selected', (detail) => {
      querySelectorAllMock.mockImplementation(() => [$mockTarget, $otherElement]);
      getAttributeMock.mockReturnValue('0');
      otherGetAttributeMock.mockReturnValue('1');
      const hook = getHook(detail);
      expect(hook.result.error).toBeFalsy();
      expect(addClassMock).not.toHaveBeenCalled();
    });
  });
});
