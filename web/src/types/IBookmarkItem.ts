import { ISplitViewListSingle } from '../components/domains/split-view/types/ISplitViewListSingle';
import { DataSourceKindType } from './DataSourceKind';

// お気に入りされた記事のAPI側レコードの型定義
export interface IBookmarkItemApiResponse {
  id: string,
  kind: DataSourceKindType,
  title: string,
  note: string,
  displayDate: string,
  properties: string,
  reposUpdatedDate?: string,
  reposCreatedDate?: string,
}

export type IbookmarkItem = Omit<ISplitViewListSingle, 'title' | 'note'>;
