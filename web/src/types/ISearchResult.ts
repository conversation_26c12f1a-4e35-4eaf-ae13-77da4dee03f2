import { IChatAttachment } from '../components/domains/split-view/types/IChatAttachment';
import { IMailAttachment } from '../components/domains/split-view/types/IMailAttachment';
import { ISpoAttachment } from '../components/domains/split-view/types/ISpoAttachment';
import { DataSourceKindType } from './DataSourceKind';
import {
  IChatMention, IChatReaction, ITeamworkApplicationIdentity, ITeamworkUserIdentity,
} from './IChatResponse';
import { IEmailAddress } from './IMailResponse';

export type SearchResultProperties = ISpoProperties | IMailProperties | IChatProperties;

export interface CommonProperties {
  hasAttachments?: boolean,
  // お気に入りのマージ処理で利用する
  updatedDate?: string,
}

/**
 * SPOリストアイテムデータのプロパティ
*/
export interface ISpoProperties extends CommonProperties {
  siteUrl?: string,
  listUrl?: string,
  listId?: string,
  listName?: string,
  editLink?: string,
  createdDate?: string,
  categoryKeyName?: string,
  spoAttachments?: ISpoAttachment[],
}

/**
 * メールアイテムデータのプロパティ
 */
export interface IMailProperties extends CommonProperties {
  mailAttachments?: IMailAttachment[],
  from?: IEmailAddress,
  sender?: IEmailAddress,
  toRecipients?: IEmailAddress[],
  ccRecipients?: IEmailAddress[],
  bccRecipients?: IEmailAddress[],
  sentDateTime?: string,
  receivedDateTime?: string,
  contentType?: string,
  webLink?: string,
  isDraft?: boolean,
}

/**
 * チャットアイテムデータのプロパティ
 */
export interface IChatProperties extends CommonProperties {
  teamId?: string,
  chatId: string, // resource.chatId or channelIdentity.channelId
  chatAttachments?: IChatAttachment[],
  from?: ITeamworkUserIdentity | ITeamworkApplicationIdentity | null,
  createdDateTime?: string,
  lastModifiedDateTime?: string,
  contentType?: string,
  messageType: string,
  subject?: string;
  // TODO: [OBSOLETE] チャットの検索はbodyを持ち回っているので、登録時にbodyから加工してお気に入りのnoteを作るようにすればこのプロパティはおそらく必要ない
  bookmarkNote?: string;
  detailedTitle?: string;
  mentions?: IChatMention[];
  reactions?: IChatReaction[],
  replyToId?: string,
  teamChatType?: string,
}

export interface ISearchResult {
  id: string,
  kind: DataSourceKindType,
  title: string,
  note: string,
  displayDate: string,
  properties: SearchResultProperties,
}
