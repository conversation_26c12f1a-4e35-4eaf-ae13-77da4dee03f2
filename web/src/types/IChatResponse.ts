export interface IUserIdentity {
  displayName: string | null;
  id: string;
  userIdentityType: string;
}

export interface IChatResponseBody {
  contentType: string,
  content: string,
}
/**
 * #microsoft.graph.chatMessageAttachment
 */
export interface IChatAttachmentResponse {
  id: string;
  name: string;
  contentType: string | null;
  contentUrl: string | null;
  content: string | null;
}

export interface IMentionTarget {
  application: null;
  conversation: null;
  device: null;
  tag: null;
  user: IUserIdentity
}

export interface IChatMention {
  id: number;
  mentionText: string;
  mentioned: IMentionTarget;
}

/**
 * #microsoft.graph.identity
 */
export interface IGraphIdentity {
  id: string,
  displayName: string,
}

/**
 * #microsoft.graph.teamworkUserIdentity
 */
export interface ITeamworkUserIdentity extends IGraphIdentity {
  tenantId: string,
}

/**
 * #microsoft.graph.teamworkApplicationIdentity"
 */
export interface ITeamworkApplicationIdentity extends IGraphIdentity {
  applicationIdentityType: string, // botの場合'bot'という値が返ってくる
}

export type TeamsMessageType = 'team' | 'chat'

export interface IChatReaction {
  reactionType: string,
  creationDate: string,
  user: {
    application: null,
    device: null,
    user: IUserIdentity,
  }
}

/**
 * https://graph.microsoft.com/v1.0/me/chats/{chat-id}/messages/{chatMessage-id}
 */
export interface IChatResponse {
  id: string,
  createdDateTime: string,
  lastModifiedDateTime: string,
  chatId: string,
  body: IChatResponseBody,
  from: {
    user: ITeamworkUserIdentity | null,
    application: ITeamworkApplicationIdentity | null,
  },
  attachments: IChatAttachmentResponse[],
  messageType: TeamsMessageType,
  teamId?: string,
  subject?: string,
  mentions?: IChatMention[],
  reactions: IChatReaction[],
  replyToId?: string,
  teamChatType?: string
  // TODO mentions/reactions等必要に応じて追加
}
