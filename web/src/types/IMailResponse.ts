export interface IMailResponseBody {
  contentType?: string,
  content?: string,
}
/**
 *  https://graph.microsoft.com/beta/me/messages/{hitId}/attachment/attachmentId
*/
export interface IMailAttachmentResponse {
  id: string;
  name: string;
  contentType: string | null;
  size: number;
  isInline: boolean,
  contentId?: string,
  contentBytes?:string
}

export interface IEmailAddress {
  emailAddress?: {
    name?: string,
    address?: string,
  }
}
/**
 * https://graph.microsoft.com/beta/me/messages/{hitId}
 */
export interface IMailResponse {
  'odata.type'?: string | null,
  'odata.etag'?: string | null,
  id?: string | null,
  attachments: IMailAttachmentResponse[],
  createdDateTime?: string | null,
  lastModifiedDateTime?: string | null,
  receivedDateTime?: string | null,
  sentDateTime?: string | null,
  hasAttachments?: boolean | null,
  subject?: string | null,
  webLink?: string | null,
  body?: IMailResponseBody,
  sender?: IEmailAddress,
  from?: IEmailAddress,
  toRecipients?: IEmailAddress[],
  ccRecipients?: IEmailAddress[],
  bccRecipients?: IEmailAddress[],
  isDraft?: boolean,
}
