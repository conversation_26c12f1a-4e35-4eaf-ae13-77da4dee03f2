/**
 * リストの状態
 * StateのContext/CacheContextで共通利用
 */
export interface IContext {
  sort: ISortOrder[],
  filter: FilterOption[],
  timestamp?: Date
  displayIds?: string[],
}

/**
 * 並び替え状況
 */
export interface ISortOrder {
  key: 'displayDate' | 'score',
  order: 'asc' | 'desc',
  priority?: number,
}

export interface IDisplayDateFilterOption {
  key: 'displayDate';
  option: number;
  from?: string;
  to?: string;
}
export interface IKindFilterOption {
  key: 'kind';
  option: string[];
}
export type FilterOption = IDisplayDateFilterOption | IKindFilterOption;
