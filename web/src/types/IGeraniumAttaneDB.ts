import { DBSchema, IDBPDatabase } from 'idb';
import { ValueOf } from '../utilities/type';
import { IContext } from './IContext';
import { ISearchRequestResult } from './ISearchRequestResult';
// import { ISearchResult } from './ISearchResult';
import { ISplitViewListSingle } from '../components/domains/split-view/types/ISplitViewListSingle';

// ブックマーク関連
export const BOOKMARKS_STORE = 'bookmarks';
export const BOOKMARK_QUEUE_STORE = 'bookmark_queue';
export type BookmarkQueueType = 'PUT' | 'DELETE';

/**
 * ブックマークのPUT/DELETEキュー
 */
export interface IRepositoryBookmarkQueue {
  date: Date;
  type: BookmarkQueueType;
  data: ISplitViewListSingle,
}

export const SEARCH_RESULTS_CACHE = 'search_results_cache';
export const CacheStatus = {
  REQUEST_IN_PROGRESS: 'RequestInProgress', // APIから検索要求を取得中
  SEARCH_ON_INTERVAL: 'SearchOnInterval', // Web側で検索を実施中
  COMPLETED: 'Completed',
  ERROR: 'Error',
};
export type CacheStatusType = ValueOf<typeof CacheStatus>;

/**
 * 検索要求のキャッシュ
 */
export interface ISearchRequestCache {
  status?: CacheStatusType,
  result?: ISearchRequestResult
}

/**
 * 検索結果のキャッシュ
 */
export interface ISearchResultsCache {
  pid: string,
  ids: string[],
  cached: boolean,
  results?: ISplitViewListSingle[]
}

// DB名
export const INDEXED_DB_NAME = 'geranium-attane';
// DBバージョン
export const INDEX_DB_VERSION = 3;

/**
 * IndexedDB型定義
 */
export interface IGeraniumAttaneDB extends DBSchema {
  // v1
  [BOOKMARKS_STORE]: {
    key: string,
    value: ISplitViewListSingle,
  },
  [BOOKMARK_QUEUE_STORE]: {
    key: string,
    value: IRepositoryBookmarkQueue,
  },
  // v2
  [SEARCH_RESULTS_CACHE]: {
    key: 'requestCache' | 'context' | string,
    value: ISearchRequestCache | IContext | ISearchResultsCache,
  }
}

export type DbProvider = () => Promise<IDBPDatabase<IGeraniumAttaneDB>>;
