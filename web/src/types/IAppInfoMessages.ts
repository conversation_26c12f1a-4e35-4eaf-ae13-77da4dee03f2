import PropTypes from 'prop-types';

/**
 * atTaneのインフォメーションメッセージ
 */
export interface IAppInfoMessages {
  overview?: string[] | null,
  searchCategory?: string[] | null,
  searchableItems?: string[] | null,
  searchCriteria?: string[] | null,
  sortOrder?: string[] | null,
  contactUs?: string[] | null,
}
export const IAppInfoMessagesPropTypesShape = {
  overview: PropTypes.arrayOf(PropTypes.string),
  searchCategory: PropTypes.arrayOf(PropTypes.string),
  searchableItems: PropTypes.arrayOf(PropTypes.string),
  searchCriteria: PropTypes.arrayOf(PropTypes.string),
  sortOrder: PropTypes.arrayOf(PropTypes.string),
  contactUs: PropTypes.arrayOf(PropTypes.string),
};
