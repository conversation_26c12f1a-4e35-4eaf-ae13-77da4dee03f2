export interface PerformanceMetric {
  start: number;
  /**
   * customMetricsに設定するプロパティ
   */
  customProperties: { [key: string]: unknown };
}

export interface AllSearchCompleted extends PerformanceMetric {
  /**
   * 処理開始時刻
   */
  references: {
    intervalExecutionStartTime: number,
    apiIdsRetrievalStartTime: number,
    spoSearchStartTime: number,
    mailSearchStartTime: number,
    chatSearchStartTime: number,
  },
  customProperties: {
    /**
     * 新規検索開始からインターバル処理開始までにかかった時間
     */
    intervalProcessingTimeElapsed: number;
    /**
     * インターバル処理開始から終了までにかかった時間
     */
    intervalExecutionTimeElapsed: number;
    /**
     * API検索要求取得開始から全Idが返ってくるまでにかかった時間
     */
    apiIdsRetrievalTimeElapsed: number;
    /**
     * Web側でSPOアイテムの検索にかかった時間(合計)
     */
    spoSearchTimeElapsed: number;
    /**
     * Web側で検索したSPOアイテムの件数
     */
    spoSearchItemCount: number;
    /**
     * Web側でチャットアイテムの検索にかかった時間(合計)
     */
    chatSearchTimeElapsed: number;
    /**
     * Web側で検索したチャットアイテムの件数
     */
    chatSearchItemCount: number;
    /**
     * Web側でメールアイテムの検索にかかった時間(合計)
     */
    mailSearchTimeElapsed: number;
    /**
     * Web側で検索したメールアイテムの件数
     */
    mailSearchItemCount: number;
  };
}

export interface ViewDetailsFromCache extends PerformanceMetric {
  /**
   * 初期表示時にキャッシュが存在しない場合false
   */
  isAvailable: boolean,
  isLogged: boolean,
  /**
   * 処理開始時刻
   */
  references: {
    apiDetailPreRequestStartTime: number,
    apiDetailRequestStartTime: number,
    apiDetailPostRequestStartTime: number,
  },
  customProperties: {
    /**
     * キャッシュにより一覧が表示されるまでにかかった時間
     */
    cacheListDisplayTimeElapsed: number;
    /**
     * APIで詳細を取得する前の処理にかかった時間
     */
    apiDetailPreRequestTimeElapsed: number;
    /**
     * APIで詳細を取得するのにかかった時間
     */
    apiDetailRequestTimeElapsed: number;
    /**
     * APIで詳細を取得完了後の処理にかかった時間
     */
    apiDetailPostRequestTimeElapsed: number;
  };
}

/**
 * パフォーマンス計測用Refオブジェクト定義
 */
export interface PerformanceMetrics {
  /**
   * 新規検索開始からすべての検索処理が完了して画面に表示されるまでの時間
   */
  allSearchCompleted: AllSearchCompleted
  /**
   * 新規検索開始から最初の1件が画面に表示されるまでの時間
   */
  showFirstSearchResults: PerformanceMetric
  /**
   * 検索結果キャッシュによって一覧が表示されてから詳細アイテムが画面に表示されるまでの時間
   */
  viewDetailsFromCache: ViewDetailsFromCache
  /**
   * ユーザーが中断ボタンをクリックした時刻
   */
  cancellation: number,
}

/**
 * PerformanceMetricsRef初期値
 */
export const initialPerformanceMetrics = {
  allSearchCompleted: {
    start: 0,
    references: {
      intervalExecutionStartTime: 0,
      apiIdsRetrievalStartTime: 0,
      spoSearchStartTime: 0,
      mailSearchStartTime: 0,
      chatSearchStartTime: 0,
    },
    customProperties: {
      intervalProcessingTimeElapsed: 0,
      intervalExecutionTimeElapsed: 0,
      apiIdsRetrievalTimeElapsed: 0,
      spoSearchTimeElapsed: 0,
      spoSearchItemCount: 0,
      chatSearchTimeElapsed: 0,
      chatSearchItemCount: 0,
      mailSearchTimeElapsed: 0,
      mailSearchItemCount: 0,
    },
  },
  showFirstSearchResults: {
    start: 0,
    customProperties: {},
  },
  viewDetailsFromCache: {
    start: 0,
    references: {
      apiDetailPreRequestStartTime: 0,
      apiDetailRequestStartTime: 0,
      apiDetailPostRequestStartTime: 0,
    },
    customProperties: {
      cacheListDisplayTimeElapsed: 0,
      apiDetailRequestTimeElapsed: 0,
      apiDetailPreRequestTimeElapsed: 0,
      apiDetailPostRequestTimeElapsed: 0,
    },
  },
  cancellation: 0,
} as PerformanceMetrics;
