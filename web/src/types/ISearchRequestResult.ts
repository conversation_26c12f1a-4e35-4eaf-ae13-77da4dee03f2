import { ValueOf } from '../utilities/type';
import { DataSourceKindType } from './DataSourceKind';
import { IRemoteContext } from './IRemoteContext';

export const SearchRequestState = {
  NOT_REGISTERED: 'NotRegistered',
  COMPLETED: 'Completed',
  IN_PROGRESS: 'InProgress',
  ERROR: 'Error',
  CANCELLED: 'Cancelled',
};

export type SearchRequestStateType = ValueOf<typeof SearchRequestState>;

/**
 * APIから返却されるSPOリスト固有プロパティ
 */
export interface IApiSpoProperties {
  category?: string,
  list?: string,
  listUrl?: string,
  site?: string,
  listName?: string,
  filterByPresentPeriod?: boolean,
  timezoneOffset?: string,
}
/**
 * APIから返却される検索結果レコードのデータソース情報
 */
export interface IApiDataSource {
  kind?: DataSourceKindType,
  properties?: IApiSpoProperties,
}

export interface ISpecificResult {
  ids?: string[],
  reqId?: string,
  pid: string,
  dataSource?: IApiDataSource,
}

export interface IMailResult extends ISpecificResult {
  dataSource?: {
    kind: 'Mail',
    properties?: IApiSpoProperties
  }
}

export interface ISPOResult extends ISpecificResult {
  dataSource?: {
    kind: 'SPO',
    properties: IApiSpoProperties
  }
}

export type Operand = string | SearchConditionTree;
export interface SearchConditionTree {
  operator: string,
  operand: Operand[],
}

export type SpecificResult = IMailResult | ISPOResult | ISpecificResult;

export interface ISearchRequestResult {
  condition?: string,
  conditionKeywords?: string | SearchConditionTree,
  userId?: string,
  reqId?: string,
  state?: string,
  results?: ISpecificResult[],
  context?: IRemoteContext,
}
