import { ValueOf } from '../utilities/type';
import { ICompanyNewsCustomColumns } from './ICompanyNewsCustomColumns';

// 公開ステータスの種類
export const ReleaseState = {
  PUBLISHED: '公開',
  DRAFT: '下書き',
};
export type ReleaseStateType = ValueOf<typeof ReleaseState>;

/**
 * SharePointリストのレスポンスデータの1行分
 */
export interface ISharePointListsSingleResponse extends ICompanyNewsCustomColumns {
  'odata.type'?: string | null;
  'odata.id'?: string | null;
  'odata.etag'?: string | null;
  'odata.editLink'?: string | null;
  Title?: string | null;
  GUID?: string | null;
  Modified?: string | null;
  Created?: string | null;
  Attachments?: boolean | null;
}
