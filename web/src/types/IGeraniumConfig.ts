/**
 * 類義語辞書
 */
import PropTypes from 'prop-types';

export type SynonymDictionary = string[][];

/**
 * SPOリンク集のトップリンク
 */
export interface ICustomLinksTop {
  name?: string | null;
  url?: string | null;
}
export const ICustomLinksTopPropTypesShape = {
  name: PropTypes.string,
  url: PropTypes.string,
};

/**
 * SPOリンク集のキーワードデータ
 */
export interface ICustomLinksKeyword {
  name?: string | null;
  url?: string | null;
}
export const ICustomLinksKeywordPropTypesShape = {
  name: PropTypes.string,
  url: PropTypes.string,
};

/**
 * SPOリンク集のカテゴリ表示タイプ
 */
export type ICustomLinksCategoryDisplayType = 'default' | 'simple';

/**
 * SPOリンク集の1カテゴリ分データ
 * [viewType] 存在しないときは'default'扱い。'simple'のときはデザインが変わる
 */
export interface ICustomLinksCategory {
  title?: string | null;
  url?: string | null;
  viewType?: ICustomLinksCategoryDisplayType | null;
  keywords?: ICustomLinksKeyword[] | null;
}
export const ICustomLinksCategoryPropTypesShape = {
  title: PropTypes.string,
  url: PropTypes.string,
  displayType: PropTypes.oneOf<'default' | 'simple' | undefined>(['default', 'simple']),
  keywords: PropTypes.arrayOf(
    PropTypes.shape(ICustomLinksKeywordPropTypesShape).isRequired,
  ),
};

/**
 * GeraniumのバックエンドAPIから取得する設定データ
 */
export interface IGeraniumConfig {
  synonymGroups: SynonymDictionary;
  linksTop?: ICustomLinksTop | null;
  linksCategory?: ICustomLinksCategory[] | null;
}
export const IGeraniumConfigPropTypesShape = {
  synonymGroups: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.string).isRequired),
  linksTop: PropTypes.shape(ICustomLinksTopPropTypesShape),
  linksCategory: PropTypes.shape(ICustomLinksCategoryPropTypesShape),
};
