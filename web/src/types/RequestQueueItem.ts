import type { IBatchResponseStatus, bulkResponseConverter } from '../hooks/accessors/useGraphApiAccessor';
import { IChatResponse } from './IChatResponse';
import { IMailResponse } from './IMailResponse';

export interface RequestQueueItem {
  kind: string,
  ids: string[],
  pid: string,
  resolve: (value: IBatchResponseStatus<IMailResponse | IChatResponse>) => void,
  appendItems: (items: (IMailResponse | IChatResponse)[]) => void,
  converter? : bulkResponseConverter<IMailResponse | IChatResponse>
}
