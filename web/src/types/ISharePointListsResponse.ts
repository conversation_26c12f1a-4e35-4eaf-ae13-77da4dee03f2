import { ISharePointListsSingleResponse } from './ISharePointListsSingleResponse';

/**
 * SharePointリストのレスポンスデータ
 * _api/web/Lists('list-GUID')/items
 * ?$orderBy=Modified desc
 * &$top=100
 * &$select=Title,releaseState,category1,GUID,Modified,Created,Attachments
 */
export interface ISharePointListsResponse {
  'odata.metadata'?: string | null;
  'odata.nextLink'?: string | null;
  value?: ISharePointListsSingleResponse[] | null;
}
