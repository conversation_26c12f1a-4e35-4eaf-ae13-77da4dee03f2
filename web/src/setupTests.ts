// Jest setup file for mocking import.meta and other global objects

// Mock import.meta for Jest environment
Object.defineProperty(globalThis, 'import', {
  value: {
    meta: {
      env: {
        REACT_APP_TITLE: process.env.REACT_APP_TITLE || 'Test App',
        REACT_APP_API_URL: process.env.REACT_APP_API_URL || 'https://localhost:5001',
        REACT_APP_SHAREPOINT_REQ_TZ_OFFSET: process.env.REACT_APP_SHAREPOINT_REQ_TZ_OFFSET || '540',
      },
    },
  },
  writable: true,
  configurable: true,
});

// Mock window.Geranium for tests
Object.defineProperty(window, 'Geranium', {
  value: {
    routePrefix: '/test',
    connectionString: 'test-connection',
    spoHostName: 'test-host',
    companyName: 'Test Company',
    defaultRetryCounts: 3,
    retryRegulationTime: 1000,
    appInfoMessages: null,
    appAIInfoMessages: null,
    retriableTime: 5000,
    additionalWaitingMsec: 100,
    batchRequestChunkSize: 10,
    bookmarkListWaitingTime: 500,
    companyRegulationSiteUrl: 'https://test.com',
    company: 'test-company',
    aiSearchEnabled: true,
  },
  writable: true,
  configurable: true,
});
