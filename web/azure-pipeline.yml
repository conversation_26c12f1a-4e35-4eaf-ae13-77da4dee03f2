pool:
  name: Azure Pipelines
  demands: npm
  vmImage: ubuntu-22.04

# CIの実行トリガー
# mainブランチのweb階層以下が更新されたときにだけトリガー
trigger:
  branches:
    include:
      - main
  paths:
    include:
      - web
    exclude:
      - web/.swc
      - web/.vscode
      - web/README.md
      - web/storybook-vrt

variables:
  NPM_CACHE_DIR: $(Pipeline.Workspace)/.npm

steps:
  - template: azure-pipeline.main.yml
    parameters:
      env: ci
