parameters:
- name: env
  displayName: Build Environment
  type: string
  default: ci
  values:
  - ci
  - dev
  - infra
  - both

steps:
# npmインストール
- template: azure-pipeline.install.yml
  parameters:
    npmCacheDir: $(NPM_CACHE_DIR)

- ${{ if in(parameters.env, 'ci') }}:
  # テストカバレッジを出力
  - task: Npm@1
    displayName: 'npm run test:ci'
    inputs:
      command: custom
      workingDir: web
      verbose: false
      customCommand: 'run test:ci'

  # テスト結果をpublish
  - task: PublishTestResults@2
    displayName: 'テスト結果をpublish'
    inputs:
      testResultsFiles: '**/junit.xml'
    condition: succeededOrFailed()

  # カバレッジ情報をpublish
  - task: PublishCodeCoverageResults@1
    displayName: 'カバレッジ情報をpublish'
    inputs:
      codeCoverageTool: Cobertura
      summaryFileLocation: '$(System.DefaultWorkingDirectory)/web/**/coverage/cobertura-coverage.xml'
    condition: succeededOrFailed()

  # lint
  - task: Npm@1
    displayName: 'npm run lint'
    continueOnError: true
    inputs:
      command: custom
      workingDir: web
      verbose: false
      customCommand: 'run lint'

  # 型チェック
  - task: Npm@1
    displayName: 'npm run type'
    inputs:
      command: custom
      workingDir: web
      verbose: false
      customCommand: 'run type'

# dev環境
- ${{ if in(parameters.env, 'ci', 'dev', 'both') }}:
  - template: azure-pipeline.build.yml
    parameters:
      artifactName: 'drop-dev'

# infra環境
- ${{ if in(parameters.env, 'infra', 'both') }}:
  - template: azure-pipeline.build.yml
    parameters:
      buildParameter: ':infra'
      artifactName: 'drop-infra'
