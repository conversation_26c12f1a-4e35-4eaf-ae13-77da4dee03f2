parameters:
- name: buildParameter
  default: ''
- name: artifactName
  default: ''
- name: skipDelete
  type: boolean
  default: false
steps:

# do type check
- task: Npm@1
  displayName: 'npm run type'
  inputs:
    command: custom
    workingDir: 'web'
    verbose: false
    customCommand: 'run type'

# ビルドを実行
- task: Npm@1
  displayName: 'npm run build${{ parameters.buildParameter }}'
  inputs:
    command: custom
    workingDir: 'web'
    verbose: false
    customCommand: 'run build${{ parameters.buildParameter }}'

# ビルド結果にWebサーバ設定をコピー
- task: CopyFiles@2
  displayName: 'web/buildにWebサーバ設定をコピー'
  inputs:
    SourceFolder: web
    Contents: |
      authorization.json
      web.config
    TargetFolder: web/build

# キャッシュ無効化のための後処理を追加
- task: Bash@3
  inputs:
    targetType: 'inline'
    script: |
      sed -i "s/chunk.css\" rel=\"stylesheet\"/chunk.css?buster=${RANDOM}${RANDOM}\" rel=\"stylesheet\"/g" build/index.html
      sed -i "s/chunk.js\"><\/script>/chunk.js?buster=${RANDOM}${RANDOM}\"><\/script>/g" build/index.html
    workingDirectory: '$(System.DefaultWorkingDirectory)/web'

# アプリのビルド結果をzip圧縮
- task: ArchiveFiles@2
  displayName: 'web/buildをアーカイブ'
  inputs:
    rootFolderOrFile: 'web/build'
    includeRootFolder: false
    archiveFile: '$(Build.ArtifactStagingDirectory)/build/$(Build.BuildId).zip'

# アプリのビルド結果をアーティファクトとしてpublish
- task: PublishPipelineArtifact@1
  displayName: 'アプリのビルド結果をアーティファクト名dropとしてpublish'
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)/build'
    artifact: ${{ parameters.artifactName }}
# 一旦生成したフォルダを削除
- ${{ if eq(parameters.skipDelete, false) }}:
  - task: DeleteFiles@1
    inputs:
      SourceFolder: 'web/build'
      Contents: '*'
      RemoveSourceFolder: true
