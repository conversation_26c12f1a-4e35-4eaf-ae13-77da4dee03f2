import { Scrollbars } from 'react-custom-scrollbars-2';
import { render, unmountComponentAtNode } from 'react-dom';
import React from 'react';
import simulant from 'simulant';

export default function createTests(scrollbarWidth) {
    // Not for mobile environment
    if (!scrollbarWidth) return;

    let node;
    beforeEach(() => {
        node = document.createElement('div');
        document.body.appendChild(node);
    });
    afterEach(() => {
        unmountComponentAtNode(node);
        document.body.removeChild(node);
    });

    describe('when resizing window', () => {
        it('should update scrollbars', done => {
            render((
                <Scrollbars style={{ width: 100, height: 100 }}>
                    <div style={{ width: 200, height: 200 }}/>
                </Scrollbars>
            ), node, function callback() {
                setTimeout(() => {
                    const spy = spyOn(this, 'update');
                    simulant.fire(window, 'resize');
                    expect(spy.calls.length).toEqual(1);
                    done();
                }, 100);
            });
        });
    });
}
