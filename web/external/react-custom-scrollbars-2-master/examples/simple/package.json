{"name": "react-custom-scrollbars-2-example", "version": "0.1.0", "description": "Simple example", "main": "server.js", "scripts": {"start": "cross-env NODE_ENV=development node server.js", "build": "cross-env NODE_ENV=production node_modules/.bin/webpack", "build:pages": "npm run build && cp index.html ../../ && rm -rf ../../static && mv static ../../"}, "repository": {"type": "git", "url": "https://github.com/RobPethick/react-custom-scrollbars-2.git"}, "keywords": ["scroll", "scroller", "scrollbars", "react"], "license": "MIT", "bugs": {"url": "https://github.com/RobPethick/react-custom-scrollbars-2/issues"}, "homepage": "https://github.com/RobPethick/react-custom-scrollbars-2", "devDependencies": {"autoprefixer-loader": "^3.1.0", "babel-core": "^6.2.1", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.0", "bootstrap-sass": "^3.3.6", "cross-env": "^3.1.3", "dom-css": "^2.0.0", "eslint": "^2.9.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.10.2", "eslint-plugin-jsx-a11y": "^1.2.0", "eslint-plugin-react": "^5.2.2", "extract-text-webpack-plugin": "^1.0.1", "lodash": "^3.10.1", "node-libs-browser": "^0.5.2", "node-sass": "^3.4.2", "sass-loader": "^3.1.2", "style-loader": "^0.13.0", "webpack": "^1.9.11", "webpack-dev-server": "^1.9.0"}, "dependencies": {"css-loader": "^0.23.1", "prop-types": "^15.5.8", "rebound": "0.0.13"}}