{"name": "react-custom-scrollbars-2", "version": "4.3.0", "description": "React scrollbars component", "main": "lib/index.js", "typings": "./index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> lib dist", "build": "babel src --out-dir lib", "build:umd": "cross-env NODE_ENV=development webpack src/index.js dist/react-custom-scrollbars.js", "build:umd:min": "cross-env NODE_ENV=production webpack src/index.js dist/react-custom-scrollbars.min.js", "lint": "eslint src test examples", "test": "cross-env NODE_ENV=test karma start", "test:watch": "cross-env NODE_ENV=test karma start --auto-watch --no-single-run", "test:cov": "cross-env NODE_ENV=test COVERAGE=true karma start --single-run", "prepublishOnly": "npm run lint && npm run clean && npm run build && npm run build:umd && npm run build:umd:min && node ./prepublish"}, "repository": {"type": "git", "url": "https://github.com//RobPethick/react-custom-scrollbars-2.git"}, "keywords": ["scroll", "scroller", "scrollbars", "react-component", "react", "custom"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com//RobPethick/react-custom-scrollbars-2/issues"}, "homepage": "https://github.com//RobPethick/react-custom-scrollbars-2", "devDependencies": {"babel-cli": "^6.2.0", "babel-core": "^6.2.1", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.0", "babel-preset-es2015": "^6.1.18", "babel-preset-react": "^6.3.13", "babel-preset-stage-1": "^6.1.18", "babel-register": "^6.3.13", "babel-runtime": "^6.3.19", "cross-env": "^3.1.3", "es3ify": "^0.2.1", "eslint": "^2.9.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.10.2", "eslint-plugin-jsx-a11y": "^1.2.0", "eslint-plugin-react": "^5.2.2", "expect": "^1.6.0", "glob": "^7.0.0", "isparta-loader": "^2.0.0", "karma": "^1.1.1", "karma-chrome-launcher": "^1.0.1", "karma-cli": "^1.0.1", "karma-coverage": "^1.1.0", "karma-mocha": "^0.2.0", "karma-mocha-reporter": "^2.0.4", "karma-sourcemap-loader": "^0.3.6", "karma-webpack": "^1.6.0", "mocha": "^2.2.5", "react": "^16.0.0", "react-dom": "^16.0.0", "rimraf": "^2.3.4", "simulant": "^0.2.2", "webpack": "^1.9.6", "webpack-dev-server": "^1.8.2"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "react-dom": "^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}, "dependencies": {"dom-css": "^2.0.0", "prop-types": "^15.5.10", "raf": "^3.1.0"}}