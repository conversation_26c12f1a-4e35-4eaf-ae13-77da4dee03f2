{
  "env": {
    "browser": true,
    "es6": true
  },
  "extends": [
    "plugin:react/recommended",
    "airbnb",
    "airbnb/hooks",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:import/typescript",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "globals": {
    "Atomics": "readonly",
    "SharedArrayBuffer": "readonly"
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "plugins": [
    "react",
    "@typescript-eslint"
  ],
  "ignorePatterns": [
    "/external/**"
  ],
  "rules": {
    "prefer-arrow-callback": 0,
    // "linebreak-style": ["error", process.env.ENV === 'unix' ? "unix" : "windows"]
    "class-methods-use-this": "off",
    "linebreak-style": "off",
    "no-use-before-define": "off",
    "padded-blocks": [
      "error",
      {
        "classes": "always"
      }
    ],
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ],
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": [
          "**/*.test.tsx"
        ]
      }
    ],
    "react/jsx-filename-extension": [
      "error",
      {
        "extensions": [
          ".js",
          ".jsx",
          ".ts",
          ".tsx"
        ]
      }
    ],
    "@typescript-eslint/no-use-before-define": "error",
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_"
      }
    ],
    "function-paren-newline": [
      "error",
      "consistent"
    ],
    "react/function-component-definition": [
      2,
      {
        "namedComponents": "arrow-function",
        "unnamedComponents": "arrow-function"
      }
    ]
  },
  "overrides": [
    {
      // test, storybook用のオーバーライド
      "files": [
        "**/*.stories.*",
        "**/*.test.*"
      ],
      "rules": {
        "react/jsx-props-no-spreading": "off",
        "import/no-extraneous-dependencies": "off",
        "import/no-anonymous-default-export": "off"
      }
    }
  ]
}