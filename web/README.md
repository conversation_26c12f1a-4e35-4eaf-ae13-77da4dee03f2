# Geranium/web

Webアプリケーションのフロントエンド実装です。

## Recommended environment

- Node.js v14.x
- npm v7 or later

## Mainly used packages

- React
- Microsoft Fluent UI React Northstar
- Microsoft Graph Toolkit
- TypeScript
- ESLint
- Jest
- Storybook

## npm scripts

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Runs the unit tests.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm stylelint`

Lint and fix the all SCSS files with stylelint.
https://stylelint.io/

Please read below how to use in VSCode:
[vscode-stylelint](https://github.com/stylelint/vscode-stylelint)

