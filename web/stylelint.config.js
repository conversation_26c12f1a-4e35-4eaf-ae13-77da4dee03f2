module.exports = {
  extends: [
    'stylelint-config-recommended',
    'stylelint-config-recommended-scss',
  ],
  // https://stylelint.io/user-guide/rules/list
  rules: {
    // general
    indentation: 2,
    'string-quotes': 'single',
    'no-eol-whitespace': true,
    'no-missing-end-of-source-newline': true,

    // comment
    'scss/double-slash-comment-whitespace-inside': 'always',

    // at-rule
    'at-rule-semicolon-newline-after': 'always',
    'at-rule-semicolon-space-before': 'never',

    // selector
    'selector-max-id': 0, // prevent using ID in selector
    'selector-max-universal': 1,
    'max-nesting-depth': 6,
    'selector-list-comma-newline-after': 'always-multi-line',
    'selector-class-pattern': '^([a-z][a-z0-9]*)([-_]+[a-z0-9]+)*$',
    'selector-combinator-space-before': 'always',
    'selector-combinator-space-after': 'always',
    'selector-attribute-brackets-space-inside': 'never',
    'selector-attribute-operator-space-after': 'never',
    'selector-pseudo-class-parentheses-space-inside': 'never',
    'selector-pseudo-element-case': 'lower',
    'selector-descendant-combinator-no-non-space': true,

    // block
    'block-opening-brace-space-before': 'always',
    'block-closing-brace-empty-line-before': 'never',

    // colon
    'declaration-colon-space-before': 'never',
    'declaration-colon-space-after': 'always',
    'scss/dollar-variable-colon-space-before': 'never',
    'scss/dollar-variable-colon-space-after': 'always',
    'selector-pseudo-element-colon-notation': 'double',

    // semicolon
    'declaration-block-semicolon-space-before': 'never',
    'declaration-block-trailing-semicolon': 'always',

    // !important rule
    'declaration-bang-space-before': 'always',
    'declaration-bang-space-after': 'never',

    // color
    'color-hex-case': 'upper',
    'color-named': 'never',

    // number
    'number-leading-zero': 'always',
    'number-no-trailing-zeros': true,
    'number-max-precision': 3,
    'time-min-milliseconds': 50,

    // empty line
    'max-empty-lines': 1,
    'rule-empty-line-before': [
      'always',
      {
        except: ['after-single-line-comment', 'first-nested'],
        ignore: ['after-comment', 'first-nested'],
      },
    ],
    'declaration-empty-line-before': [
      'always',
      {
        except: [
          'after-declaration',
          'first-nested',
          'after-comment',
        ],
        ignore: [
          'after-comment',
          'after-declaration',
          'first-nested',
          'inside-single-line-block',
        ],
      },
    ],

    // disabled rules
    'block-no-empty': null,
    'no-descending-specificity': null,
    'declaration-block-no-shorthand-property-overrides': null,
    'declaration-block-no-redundant-longhand-properties': null,
  },
};
