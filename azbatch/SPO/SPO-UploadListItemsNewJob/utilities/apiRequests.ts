import * as dotenv from 'dotenv';

dotenv.config();

interface ISPOListItemRequest {
  id: string;
  method: string;
  url: string;
}

const SPO_LIST_ITEMS_SEARCH_SIZE: string = process.env['SPO_LIST_ITEMS_SEARCH_SIZE'] ?? '50';
const SPO_LIST_ITEMS_START_DATE: string = process.env['SPO_LIST_ITEMS_START_DATE'] ?? '2025-01-01';
const SPO_LIST_ITEMS_END_DATE: string = process.env['SPO_LIST_ITEMS_END_DATE'] ?? '2025-12-31';

export function createListItemRequests(id: string, siteUrl: string, listId: string): ISPOListItemRequest[] {
  if (!siteUrl || !listId) return [];

  const dateFilter = `Modified ge '${SPO_LIST_ITEMS_START_DATE}T00:00:00Z' and Modified le '${SPO_LIST_ITEMS_END_DATE}T23:59:59Z'`;
    
  return [{
      id: `${id}`,
      method: 'GET',
      url: `${siteUrl}/_api/web/lists('${listId}')/items?$top=${SPO_LIST_ITEMS_SEARCH_SIZE}&$filter=${dateFilter}&$select=*,EncodedAbsUrl`
  }];
}