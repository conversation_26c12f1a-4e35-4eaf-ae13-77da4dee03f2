import * as dotenv from 'dotenv';

dotenv.config();

const SPO_LIST_ITEMS_SEARCH_SIZE: string = process.env['SPO_LIST_ITEMS_SEARCH_SIZE'] ?? '50';

interface SharePointItemRequest {
  id: string;
  method: string;
  url: string;
  headers: {
    'Prefer': string;
  };
}

function getRollingYearDateRange(): { start: string; end: string } {
  const today = new Date();
  
  // Calculate 1 year ago from today, then subtract 1 day
  const startDate = new Date(today);
  startDate.setFullYear(today.getFullYear() - 1);
  startDate.setDate(today.getDate() - 1); // Changed from +1 to -1
  
  // Format dates as YYYY-MM-DD
  const startStr = startDate.getFullYear() + '-' + 
                   String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(startDate.getDate()).padStart(2, '0');
  
  const endStr = today.getFullYear() + '-' + 
                 String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                 String(today.getDate()).padStart(2, '0');
  return {
    start: startStr,
    end: endStr
  };
}

export function createListItemRequestsDaily(id: string, siteId: string, listId: string): SharePointItemRequest[] {
  if (!siteId || !listId) return [];

  const requests: SharePointItemRequest[] = [];
  const dateRange = getRollingYearDateRange();
  
  // Create single request for the entire 1-year range
  const dateFilter = `fields/Modified ge '${dateRange.start}T00:00:00Z' and fields/Modified le '${dateRange.end}T23:59:59Z'`;
  
  requests.push({
    id: `${id}`,
    method: 'GET',
    url: `/sites/${siteId}/lists/${listId}/items?$top=${SPO_LIST_ITEMS_SEARCH_SIZE}&$filter=${dateFilter}&$expand=fields&$select=webUrl,sharepointIds`,
    headers: {
      'Prefer': 'HonorNonIndexedQueriesWarningMayFailRandomly'
    }
  });
  
  return requests;
}