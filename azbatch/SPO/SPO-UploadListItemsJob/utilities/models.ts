import { Entity, NullableOption, PublicError } from '@microsoft/microsoft-graph-types/microsoft-graph';

export type ValueOf<T> = T[keyof T];

/**
 * Graph API Batchリクエストのレスポンス1件分
 */
export interface IBatchResponseData {
  id?: string,
  status?: number,
  headers?: NullableOption<{ 'Retry-After'?: string, 'Content-Type'?: string }>
  body?: {
    value?: Entity[],
    error?: NullableOption<PublicError>,
  },
}

/**
 * Graph API Batchリクエストのレスポンス
 * Ref: BatchResponseBody
 */
export interface IBatchResponses {
  "@odata.nextLink"?: string;
  responses?: IBatchResponseData[],
}
/**
 * Azure Functionsが渡すタイマーオブジェクト
 * https://docs.microsoft.com/ja-jp/azure/azure-functions/functions-bindings-timer?tabs=javascript
 */
export interface TimerObject {
  schedule: unknown;
  scheduleStatus: {
    last?: string;
    lastUpdated?: string;
    next?: string;
  },
  isPastDate: boolean;
}

export interface ISiteListData {
  id: string;
  siteName: string;
  siteId: string;
  listName: string;
  listId: string;
  groupIds: string[];
}

export interface ISiteLookupData {
  siteName: string;
  listName: string;
  groupIds: string[];
}