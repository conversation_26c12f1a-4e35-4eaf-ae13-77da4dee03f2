import { execSync } from "child_process";
import path from "path";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

// Directories based on structure
const ROOT_DIR = path.resolve(__dirname, "../../"); // azbatch root folder
const JOB_NAME = "UploadMailJob";
const DIST_DIR = path.join(ROOT_DIR, "dist");
const ZIP_FILE = path.join(ROOT_DIR, `${JOB_NAME}.zip`);

function runCommand(command: string): void {
  console.log(`Running: ${command}\n`);
  execSync(command, { stdio: "inherit" });
}

function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function copyDir(src: string, dest: string, isWindows: boolean): void {
  ensureDir(dest);
  
  if (fs.existsSync(src)) {
    if (isWindows) {
      runCommand(`xcopy "${src}" "${dest}" /E /I /Y`);
    } else {
      runCommand(`cp -r "${src}/"* "${dest}"`);
    }
  }
}

async function main(): Promise<void> {
  try {
    console.log("Starting build process...");
    const isWindows = process.platform === "win32";

    // Step 1: Build project using TypeScript compiler
    console.log("Building TypeScript project...");
    runCommand("npx tsc");
    console.log("TypeScript build completed");

    // Step 2: Create ZIP file
    console.log(`Creating ZIP file: ${JOB_NAME}.zip`);

    // Remove existing zip file if it exists
    if (fs.existsSync(ZIP_FILE)) {
      fs.unlinkSync(ZIP_FILE);
    }

    // Create a temporary directory
    const tempDir = path.join(ROOT_DIR, "temp_zip");
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    
    // Create the directory structure
    const tempDistDir = path.join(tempDir, "dist");
    const tempJobDir = path.join(tempDistDir, JOB_NAME);
    const tempSrcDir = path.join(tempJobDir, "src");
    const tempUtilDir = path.join(tempDistDir, "utilities");
    
    ensureDir(tempSrcDir);
    
    // Copy only needed files/directories
    console.log("Copying src files...");
    copyDir(path.join(DIST_DIR, JOB_NAME, "src"), tempSrcDir, isWindows);
    
    console.log("Copying utilities folder...");
    copyDir(path.join(DIST_DIR, "utilities"), tempUtilDir, isWindows);
    
    // Copy package files
    console.log("Copying package files...");
    ["package.json", "package-lock.json"].forEach((file: string) => {
      const srcFile = path.join(ROOT_DIR, file);
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, path.join(tempDir, file));
      }
    });

    // Create zip file
    console.log("Creating final zip file...");
    if (isWindows) {
      runCommand(`powershell -command "Compress-Archive -Path '${tempDir}/*' -DestinationPath '${ZIP_FILE}' -Force"`);
    } else {
      runCommand(`cd ${tempDir} && zip -r "${ZIP_FILE}" .`);
    }

    // Clean up
    fs.rmSync(tempDir, { recursive: true, force: true });

    console.log(`Successfully created ZIP file: ${JOB_NAME}.zip`);
    console.log("Build process completed successfully!");

  } catch (error) {
    console.error("Error:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

main().catch((error: unknown) => {
  console.error("Error:", error);
  process.exit(1);
});