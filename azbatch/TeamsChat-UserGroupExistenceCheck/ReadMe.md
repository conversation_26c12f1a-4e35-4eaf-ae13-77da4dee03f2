# UserGroupExistenceCheck

The `UserGroupExistenceCheck` is a batch process for managing user existence in group conversations.

### Chat Filtering

- Chats are filtered as group conversations using the condition:  
  `chatType eq 'group'` in the `userTeamsChat.ts`.
- Only event system messages of type `#microsoft.graph.membersDeletedEventMessageDetail` are processed in the `impl.ts`.

### Deleting Logic

The deletion logic, implemented in `cosmos.ts`, processes each message in the input array as follows:

1. **Extract Deleted Members**:  
   - Messages with deleted members are extracted from the input array.  
   - Each message is checked for the presence of an `id` and `eventDetail.members`. Messages without these fields are skipped.

2. **Find All Related Messages**:  
   - Query Cosmos DB to find all messages with the same `chatId`
   - This ensures updates apply to the entire conversation history

3. **Remove Deleted User IDs**:  
   - For each matching message:
     - Check if any deleted user IDs exist in the message's `security_user_id` array
     - Deleted user IDs are identified by comparing them with the `eventDetail.members` of the incoming message.
     - If matching user IDs are found, they are removed from the `security_user_id` field.
     - The updated `security_user_id` array is saved back to the database.


---

# UserGroupExistenceCheck

`UserGroupExistenceCheck`は、グループ会話内のユーザーの存在を管理するためのバッチプロセスです。

### チャットのフィルタリング

- チャットは、`userTeamsChat.ts`内で条件 `chatType eq 'group'` を使用してグループ会話としてフィルタリングされます。
- `impl.ts`内では、`#microsoft.graph.membersDeletedEventMessageDetail`タイプのイベントシステムメッセージのみが処理されます。

### 削除ロジック

`cosmos.ts`に実装された削除ロジックは、入力配列内の各メッセージを以下の手順で処理します：

1. **削除されたメンバーの抽出**:  
   - 削除されたメンバーを含むメッセージが入力配列から抽出されます。  
   - 各メッセージは、`id`と`eventDetail.members`の存在を確認します。これらのフィールドがないメッセージはスキップされます。

2. **関連するすべてのメッセージの検索**:  
   - Cosmos DBにクエリを実行し、同じ`chatId`を持つすべてのメッセージを検索します。
   - これにより、更新が会話履歴全体に適用されることが保証されます。

3. **削除されたユーザーIDの削除**:  
   - 各一致するメッセージについて：
     - メッセージの`security_user_id`配列内に削除されたユーザーIDが存在するか確認します。
     - 削除されたユーザーIDは、受信メッセージの`eventDetail.members`と比較することで特定されます。
     - 一致するユーザーIDが見つかった場合、それらは`security_user_id`フィールドから削除されます。
     - 更新された`security_user_id`配列がデータベースに保存されます。