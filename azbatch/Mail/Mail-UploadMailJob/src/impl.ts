import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';

import { createUserMessageRequests } from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForMails
} from '../utilities/graph';
import { CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  IMailData
} from '../utilities/models';
import { encrypt } from '../utilities/encryption';
import {
  insertMailsToCosmosDB
} from '../utilities/cosmos';

const MAIL_MAX_GRAPH_API_MAIL_BATCH_COUNTS = parseInt(process.env.MAIL_MAX_GRAPH_API_MAIL_BATCH_COUNTS ?? '5');
const MAIL_MAX_MAIL_CHUNK_SIZE = parseInt(process.env.MAIL_MAX_MAIL_CHUNK_SIZE ?? '20');

type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 2 - START ========== *******************************************************/
/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}
/******************************************************* ========== PHASE 2 - END ========== *********************************************************/
/******************************************************* ========== PHASE 4 - START ========== *******************************************************/
export async function processUserBatch(
  logger: Logger,
  client: Client,
  targetUsersData: IBatchResponseData[],
): Promise<void> {

  logger.log(`[Impl:processUserBatch] Total targetUsersData to Process: ${targetUsersData.length}`);
  
  const MAIL_MAX_USER_BATCH_COUNTS = parseInt(process.env.MAIL_MAX_USER_BATCH_COUNTS ?? '20');
  const splitUserBatch = splitArrayIntoChunks(targetUsersData, MAIL_MAX_USER_BATCH_COUNTS);
  
  logger.log(`[Impl:processUserBatch] Total User Batches: ${splitUserBatch.length} | MAIL_MAX_USER_BATCH_COUNTS: ${MAIL_MAX_USER_BATCH_COUNTS}`);

  for (let batchIndex = 0; batchIndex < splitUserBatch.length; batchIndex++) {
    const currentUserBatch = splitUserBatch[batchIndex];
    const currentUserBatchNumber = batchIndex + 1;
    
    logger.log(`\n==== START BATCH | USER: ${currentUserBatchNumber} of ${splitUserBatch.length} (${currentUserBatch.length} Users Inside) ====`);
    
    try {
      await processUserMailsBatch(logger, client, currentUserBatch, currentUserBatchNumber, splitUserBatch.length);
      splitUserBatch[batchIndex] = [];
      
      if (global.gc) {
        global.gc();
      }
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
    } catch (error) {
      logger.error(`[Impl:processUserBatch] Error Processing User Batch ${currentUserBatchNumber}: ${error}`);
      continue;
    }
    
    logger.log(`==== END BATCH | USER: ${currentUserBatchNumber} of ${splitUserBatch.length} ====\n`);
  }
  
  splitUserBatch.length = 0;
  logger.log(`[Impl:processUserBatch] Completed Processing All User Batches.`);
}

async function processUserMailsBatch(
  logger: Logger,
  client: Client,
  userBatch: IBatchResponseData[],
  currentUserBatchNumber: number,
  totalUserBatch: number
): Promise<void> {
  
  const mailsBatchRequestsCreated: BatchRequestData[] = userBatch
    .filter(data => data.id)
    .flatMap((data) =>
      createUserMessageRequests(data?.id ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processUserMailsBatch] Created ${mailsBatchRequestsCreated.length} Requests for ${userBatch.length} Users`);

  const splitMailsBatchRequests = splitArrayIntoChunks(mailsBatchRequestsCreated, MAIL_MAX_GRAPH_API_MAIL_BATCH_COUNTS);
  logger.log(`[Impl:processUserMailsBatch] Total Split Batch: ${splitMailsBatchRequests.length} | MAIL_MAX_GRAPH_API_MAIL_BATCH_COUNTS: ${MAIL_MAX_GRAPH_API_MAIL_BATCH_COUNTS}`);
  // logger.log(`[Impl:processUserMailsBatch] splitMailsBatchRequests: ${JSON.stringify(splitMailsBatchRequests)}`);  // !!!

  const totalMailsBatchRequests = splitMailsBatchRequests.length;
  for (let i = 0; i < totalMailsBatchRequests; i++) {
    try {
      const currentMailsBatchRequest = i + 1;
      logger.log(`\n---- START BATCH | API REQUEST: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} | User Batch: ${currentUserBatchNumber} of ${totalUserBatch} ----`);
      
      const currentSplitMailsBatchRequests = splitMailsBatchRequests[i];
      // logger.log(`[Impl:processListItems] splitMailsBatchRequest_inside == ${JSON.stringify(splitMailsBatchRequests)}`); // !!!
      // logger.log(`[Impl:processListItems] currentSplitMailsBatchRequests == ${JSON.stringify(currentSplitMailsBatchRequests)}`); // !!!

      const batchResultMails = await fetchJsonBatchForMails(logger, client, currentSplitMailsBatchRequests);
      // // *** View Raw Response
      // logger.log(`*** [Impl:processUserMailsBatch] batchResultMails == ${JSON.stringify(batchResultMails)}); // !!!

      if (!batchResultMails.responses || batchResultMails.responses.length === 0) {
        logger.log(`[Impl:processUserMailsBatch] No Responses in API REQUEST: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} | User Batch: ${currentUserBatchNumber} of ${totalUserBatch}`);
        continue;
      }

      await processBatchResultMails(logger, batchResultMails, currentMailsBatchRequest, totalMailsBatchRequests, currentUserBatchNumber, totalUserBatch);

      batchResultMails.responses = [];
      splitMailsBatchRequests[i] = [];
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      logger.log(`---- END BATCH | API REQUEST: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} | User Batch: ${currentUserBatchNumber} of ${totalUserBatch} ----\n`);
      
    } catch (error) {
      logger.error(`[Impl:processUserMailsBatch] Error Processing API Batch ${i + 1}: ${error}`);
      continue;
    }
  }
  mailsBatchRequestsCreated.length = 0;
  splitMailsBatchRequests.length = 0;
}

async function processBatchResultMails(
  logger: Logger,
  batchResultMails: IBatchResponses,
  currentMailsBatchRequest: number,
  totalMailsBatchRequests: number,
  currentUserBatchNumber: number,
  totalUserBatch: number
): Promise<void> {

  const totalBatchResultMails = batchResultMails.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultMails; j++) {
    const currentBatchResultMails = j + 1;
    const batchResultMailsResponses = (batchResultMails.responses ?? [])[j];
    const mailsId = batchResultMailsResponses.id;

    logger.log(`\n\n**** START BATCH | PROCESSSING MAIL: (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch}) ****`);

    if (!batchResultMailsResponses || batchResultMailsResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultMails] Skipping mailsId: ${mailsId} with Error Data: ${batchResultMailsResponses?.status} === (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch})`);
      continue;
    }

    const singleResultMails = { responses: [batchResultMailsResponses] };
    // logger.info(`[Impl:processBatchResultMails] singleResultMails == ${JSON.stringify(singleResultMails)} === (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch})`);  // !!!
                                                                      
    const modifiedMailsChunk = processSingleResultMails([singleResultMails], logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultMails] *** modifiedMailsChunk == ${JSON.stringify(modifiedMailsChunk)} === (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch})`); // !!!

    // Inserting Start...
    const totalmodifiedMailsChunk = modifiedMailsChunk.length;
    for (let k = 0; k < totalmodifiedMailsChunk; k++) {
      const mailsChunk = modifiedMailsChunk[k];
      const currentmodifiedMailsChunk = k + 1;
      if (!mailsChunk) {
        logger.info(`[Impl:processBatchResultMails] Skipping Undefined Mails at Index: ${k} === (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch})`);
        continue;
      }
      logger.log(`++++ START BATCH | INSERTING MAIL IN COSMOS_DB: (Chunk: ${currentmodifiedMailsChunk} of ${totalmodifiedMailsChunk} with ${mailsChunk.body?.value?.length} Mails Inside) ++++`);
      logger.log(`++++ (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch}) ++++`);
      try {
        logger.info(`[Impl:processBatchResultMails] Inserting Mails...`);
        await insertMailsToCosmosDB(logger, [mailsChunk]);
        logger.info(`[Impl:processBatchResultMails] Successfully Inserted Mails...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultMails] Failed Inserting: ${error} (Chunk: ${currentmodifiedMailsChunk} of ${totalmodifiedMailsChunk} with ${mailsChunk.body?.value?.length} Mails Inside) === (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch})`);
        continue;
      }
      modifiedMailsChunk[k] = {} as IBatchResponseData;
      logger.log(`++++ (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch}) ++++`);
      logger.log(`++++ END BATCH | INSERTING MAIL IN COSMOS_DB: (Chunk: ${currentmodifiedMailsChunk} of ${totalmodifiedMailsChunk} with ${mailsChunk.body?.value?.length} Mails Inside) ++++`);
    }
    // Inserting End...

    modifiedMailsChunk.length = 0;
    if (global.gc) {
      global.gc();
    }
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`**** END BATCH | PROCESSSING MAIL: (mailsId: ${mailsId} / Batch Result: ${currentBatchResultMails} of ${totalBatchResultMails} / API Batch Request: ${currentMailsBatchRequest} of ${totalMailsBatchRequests} / User Batch: ${currentUserBatchNumber} of ${totalUserBatch}) ****`);
  }
}

function processSingleResultMails(
  singleResultMails: IBatchResponses[], 
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResultMails) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultMails] Skipping Invalid Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
        const totalMails = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultMails] Total response.body.value: ${totalMails}`);

      const userId = response.id ? response.id.split('-week')[0] : ''; // Extract everything before "-week"
      const allMails = response.body?.value as IMailData[];
      // logger.log(`[Impl:processSingleResultMails] allMails: ${JSON.stringify(allMails)}`);  // !!!
      
      // Process in chunks
      const chunkSize = MAIL_MAX_MAIL_CHUNK_SIZE;
      for (let i = 0; i < allMails.length; i += chunkSize) {
        const mailsAfterChunk = allMails.slice(i, i + chunkSize);
        // logger.info(`[Impl:processSingleResultMails] mailsAfterChunk: ${JSON.stringify(mailsAfterChunk)}`); // !!!

        processMailsChunk(userId, mailsAfterChunk, allProcessedData, logger);
        mailsAfterChunk.length = 0;
      }
      allMails.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultMails] allProcessedData: ${allProcessedData}`); // !!!
  return allProcessedData;
}

function processMailsChunk(
  userId: string,
  mailsAfterChunk: IMailData[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: any[] = [];
  let skippedCount = 0;
  let targetEmailSkippedCount = 0;

  const targetEmails = JSON.parse(process.env.MAIL_DELETE_TARGET_MAILS || '[]');
  for (const item of mailsAfterChunk) {

    const emailAddress = item.from?.emailAddress?.address;
    if (emailAddress && targetEmails.includes(emailAddress)) {
      targetEmailSkippedCount++;
      logger.log(`Skipped target email: ${emailAddress} | Subject: ${item.subject}`);
      continue;
    }

    if (hasEmptyRequiredFields(item)) {
      skippedCount++;
      continue;
    }
    
    processedValues.push(createEncryptedMails(userId, item));
  }

  // Log the skipped count if any items were skipped
  if (skippedCount > 0) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipped ${skippedCount} Mails with Null Values in this chunk`);
  }

  if (processedValues.length > 0) {
    allProcessedData.push({
      id: userId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processMailsChunk] Successfully Modified: ${processedValues.length} Mails Inside Chunk | MAIL_MAX_MAIL_CHUNK_SIZE: ${MAIL_MAX_MAIL_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFields(item: IMailData): boolean {
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  return isEmpty(item.subject) ||
         isEmpty(item.bodyPreview) ||
         isEmpty(item.from?.emailAddress?.name) ||
         isEmpty(item.from?.emailAddress?.address);
}

function createEncryptedMails(userId: string, item: IMailData) {
  return {
    security_user_id: [userId],
    id: item.id,
    kind: "Mail",
    subject: encrypt(item.subject),
    from: {
      emailAddress: {
        name: encrypt(item.from.emailAddress.name),
        address: encrypt(item.from.emailAddress.address)
      }
    },
    receivedDateTime: item.receivedDateTime,
    bodyPreview: encrypt(item.bodyPreview),
    hasAttachments: item.hasAttachments,
  };
}