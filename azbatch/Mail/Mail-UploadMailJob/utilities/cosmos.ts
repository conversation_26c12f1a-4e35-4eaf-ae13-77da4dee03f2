import { Container, CosmosClient } from "@azure/cosmos";
import {
  IBatchResponseData,
  IMailData
} from './models';
import { CustomLogger } from './log';
import * as dotenv from 'dotenv';

dotenv.config();

const cosmosDBEndpoint = process.env['COSMOS_DB_ENDPOINT'];
const cosmosDBKey = process.env['COSMOS_DB_KEY'];
const databaseName = process.env['COSMOS_DB_DATABASE'];
const containerName = process.env['COSMOS_DB_CONTAINER'];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:Client] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log('[CosmosDB:Client] Initializing Client Connection...');
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey
      });
      logger.log('[CosmosDB:Client] Client Initialized Successfully');
    } else {
      logger.log('[CosmosDB:Client] Reusing Existing Connection');
    }

    return cosmosClient;

  } catch (error) {
    logger.log(`[CosmosDB:Client] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger,
): Promise<void> {

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Validate] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:Validate] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases.readAll().fetchAll();
    logger.log(`[CosmosDB:Validate] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:Validate] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:Validate] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:Validate] Container resource is undefined");
    }

    // Count Container Details
    const { resources: [count] } = await container.items
      .query("SELECT VALUE COUNT(1) FROM c")
      .fetchAll();
    logger.log(`[CosmosDB:Validate] Container has: ${count} Items`);

  } catch (error) {
    logger.log(`[CosmosDB:Validate] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertMailsToCosmosDB(
  logger: CustomLogger,
  dataMail: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:Insert] dataMail`, dataMail); // !!!
  
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:Insert] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    // Extract the body.value
    const modifiedMail: IMailData[][] = dataMail
      .map(mail => mail.body?.value || [])
      .filter(Array.isArray);

    const insertedCount = await processMail(container, modifiedMail, logger);

    logger.log(`[CosmosDB:Insert] Inserted ${insertedCount} New Mail to Cosmos DB`);
    const totalMessageCount = modifiedMail.reduce((count, array) => count + (array ? array.length : 0), 0);
    logger.log(`[CosmosDB:Insert] Skipped ${totalMessageCount - insertedCount} Existing Mail`);

  } catch (error) {
    logger.log(`[CosmosDB:Insert] Error Processing Mail: ${error}`);
    throw error;
  }
}

async function processMail(
  container: Container,
  modifiedMail: IMailData[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  for (const messageArray of modifiedMail) {
    for (const mail of messageArray) {
      if (!mail.id) {
        logger.log(`[CosmosDB:Process] Error: mail.id is undefined for mail: ${JSON.stringify(mail.id)}`);
        continue;
      }

      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: mail.id }]
      };

      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();
        if (existingMessages.length === 0) {
          await container.items.create(mail);
          insertedCount++;
        }
      } catch (error) {
        logger.log(`[CosmosDB:Process] Error Processing Mail: ${error}`);
      }
    }
  }
  return insertedCount;
}