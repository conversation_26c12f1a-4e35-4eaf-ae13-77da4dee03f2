import { execSync } from "child_process";
import path from "path";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

// Directories based on structure
const ROOT_DIR = path.resolve(__dirname, "../");
const JOB_NAME = process.env.MAIL_DELETE_BATCH_JOB_NAME ?? 'default';
const DIST_DIR = path.join(ROOT_DIR, "dist");
const ZIP_FILE = path.join(ROOT_DIR, `${JOB_NAME}.zip`);

function runCommand(command: string): void {
  console.log(`Running: ${command}`);
  execSync(command, { stdio: "inherit" });
}

function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function copyFile(src: string, dest: string): void {
  if (fs.existsSync(src)) {
    ensureDir(path.dirname(dest));
    fs.copyFileSync(src, dest);
  }
}

function copyDir(src: string, dest: string, isWindows: boolean): void {
  if (!fs.existsSync(src)) return;
  
  ensureDir(dest);
  
  if (isWindows) {
    runCommand(`xcopy "${src}" "${dest}" /E /I /Y`);
  } else {
    runCommand(`cp -r "${src}/"* "${dest}"`);
  }
}

async function main(): Promise<void> {
  try {
    console.log("Starting build process...");
    const isWindows = process.platform === "win32";

    // Build TypeScript project
    console.log("Building TypeScript project...");
    runCommand("npx tsc");

    // Create temporary directory for zip
    const tempDir = path.join(ROOT_DIR, "temp_zip");
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    
    // Create the target directory structure
    const tempDistDir = path.join(tempDir, "dist");
    const tempSrcDir = path.join(tempDistDir, "src");
    const tempUtilDir = path.join(tempDistDir, "utilities");
    
    ensureDir(tempSrcDir);
    ensureDir(tempUtilDir);
    
    // Copy compiled src files
    console.log("Copying src files...");
    copyDir(path.join(DIST_DIR, "src"), tempSrcDir, isWindows);
    
    // Copy compiled utilities
    console.log("Copying utilities...");
    copyDir(path.join(DIST_DIR, "utilities"), tempUtilDir, isWindows);
    
    // Copy package files to root of temp dir
    console.log("Copying package files...");
    copyFile(path.join(ROOT_DIR, "package.json"), path.join(tempDir, "dist", "package.json"));
    copyFile(path.join(ROOT_DIR, "package-lock.json"), path.join(tempDir, "dist", "package-lock.json"));
    copyFile(path.join(ROOT_DIR, "tsconfig.json"), path.join(tempDir, "dist", "tsconfig.json"));

    // Create zip file
    console.log(`Creating ZIP file: ${JOB_NAME}.zip...`);
    if (fs.existsSync(ZIP_FILE)) {
      fs.unlinkSync(ZIP_FILE);
    }
    
    if (isWindows) {
      runCommand(`powershell -command "Compress-Archive -Path '${tempDir}/*' -DestinationPath '${ZIP_FILE}' -Force"`);
    } else {
      runCommand(`cd "${tempDir}" && zip -r "${ZIP_FILE}" .`);
    }

    // Clean up temp directory
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log("Build process completed successfully!");
  } catch (error) {
    console.error("Error:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

main().catch(error => {
  console.error("Error:", error);
  process.exit(1);
});