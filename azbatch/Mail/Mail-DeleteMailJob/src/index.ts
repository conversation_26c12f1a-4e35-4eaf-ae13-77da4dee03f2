import { logger } from "../utilities/log";
import * as dotenv from "dotenv";
import { validateCosmosDBConnection, deleteTargetDataFromCosmos } from "../utilities/cosmos";

dotenv.config();

async function main() {

  logger.log("\n========== PHASE 1 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:validateCosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:validateCosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }

  logger.log("\n========== PHASE 2 ==========");
  logger.log("--- Deleting Mail ---");
  try {
    await deleteTargetDataFromCosmos(logger);
    logger.log("[Index:ProcessUserMessages] Successfully Get All Data from ");
  } catch (error) {
    logger.error(error);
  }
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});