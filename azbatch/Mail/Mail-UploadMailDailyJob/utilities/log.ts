import { splitArrayIntoChunks } from './array';

export interface CustomLogger {
  log: (message: string) => void;
  info: (message: string) => void;
  warn: (message: string) => void;
  error: (message: string) => void;
  verbose: (message: string) => void;
}

export const logger = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  verbose: console.debug
};

/**
 * output long array with multiple logs
 * @param log
 * @param prefix
 * @param length
 * @param ary
 */
export function logLongArray<T>(
  log: CustomLogger, prefix: string, length: number, ary: T[],
): void {
  const lengthOrig = ary.length;

  if (lengthOrig === 0) {
    log.log(`${prefix}(1/1): ${lengthOrig}, ${JSON.stringify(ary)}`);
    return;
  }

  // use concat() to safe splitting without side effects
  splitArrayIntoChunks(ary.concat(), length).forEach((ids, i, groups) => {
    log.log(`${prefix}(${i + 1}/${groups.length}): ${lengthOrig}, ${JSON.stringify(ids)}`);
  });
}