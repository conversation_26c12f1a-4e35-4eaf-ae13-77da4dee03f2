import { createGraphClient } from '../utilities/graph';
import { logger, logLongArray } from '../utilities/log';
import {
  createCredential,
  fetchTargetUsers,
  processUserBatch,
} from './impl';
import * as dotenv from 'dotenv';
import { validateCosmosDBConnection } from "../utilities/cosmos";

dotenv.config();

async function main() {
  logger.log('========== PHASE 1 ==========');
    logger.log("--- Create Client Credentials ---");
  const credential = createCredential(
    process.env['AzureTenantId'] ?? '',
    process.env['AzureClientId'] ?? '',
    process.env['AzureClientSecret'] ?? ''
  );
  if (!credential) {
    logger.error('[Index:createCredential] NO_CREDENTIALS');
    return Promise.reject(new Error('[Index:createCredential] NO_CREDENTIALS'));
  }
  const graphClient = createGraphClient(credential);

  logger.log('========== PHASE 2 ==========');
  logger.log("--- Fetch Target Users ---");
  const teamsAppId = process.env['TeamsAppId'] ?? '';
  if (!teamsAppId) {
    logger.error('[Index:teamsAppId] NO_TEAMS_APP_ID');
    return Promise.reject(new Error('[Index:teamsAppId] NO_TEAMS_APP_ID'));
  }

  const groupId = process.env['GraphGroupId'] ?? '';
  if (!groupId) {
    logger.error('NO_GRAPH_GROUP_ID');
    return Promise.reject(new Error('[Index:groupId] NO_GRAPH_GROUP_ID'));
  }
  const targetUsers = await fetchTargetUsers(graphClient, groupId, teamsAppId)
    .catch((reason) => {
      logger.error(reason);
      return Promise.reject(new Error('[Index:fetchTargetUsers] Error:', reason));
    });
  if (targetUsers.length === 0) {
    logger.log('[Index:fetchTargetUsers] PROCESS_END_BECAUSE_OF_NO_USERS_TO_NOTIFY');
    return Promise.resolve();
  }
  logLongArray(logger, '[Index:fetchTargetUsers] Target Users ID:', 800, targetUsers.map((u) => u.id));

  logger.log("\n========== PHASE 3 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:validateCosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:validateCosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }

  logger.log('========== PHASE 4 ==========');
  logger.log("--- Process User Messages ---");
  try {
    await processUserBatch(logger, graphClient, targetUsers);
    logger.log("[Index:ProcessUserBatch] Successfully Inserted User Mails.");
  } catch (error) {
    logger.error(error);
  }

  logger.log("\n========== PHASE 5 ==========");
  logger.log("--- Finish ---");
  logger.log("[Index] All Phase Successfully Completed.");
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});