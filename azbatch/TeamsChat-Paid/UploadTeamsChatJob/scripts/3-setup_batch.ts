import { BatchServiceClient, BatchSharedKeyCredentials } from "@azure/batch";
import { BlobServiceClient } from "@azure/storage-blob";
import dotenv from "dotenv";
import path from "path";

dotenv.config();

// Environment variables
const batchAccountName = process.env.BATCH_ACCOUNT_NAME ?? "";
const batchAccountKey = process.env.BATCH_ACCOUNT_KEY ?? "";
const batchAccountUrl = process.env.BATCH_ACCOUNT_URL ?? "";
const storageConnectionString = process.env.STORAGE_CONNECTION_STRING ?? "";
const storageContainer = process.env.STORAGE_BATCH_CONTAINERNAME ?? "";
const storageFilename = process.env.STORAGE_BATCH_FILENAME ?? "";
const poolId = process.env.BATCH_POOL_ID ?? ""; // Existing pool ID

// Initialize Batch client
const sharedKeyCredential = new BatchSharedKeyCredentials(batchAccountName, batchAccountKey);
const batchClient = new BatchServiceClient(sharedKeyCredential, batchAccountUrl);

/**
 * Check if the blob exists in storage
 */
async function checkBlobExistence(): Promise<string> {
  console.log(`Checking if ${storageFilename} exists in container ${storageContainer}...`);

  // Validate environment variables
  if (!storageConnectionString) {
    throw new Error("STORAGE_CONNECTION_STRING environment variable is not set");
  }
  
  if (!storageContainer) {
    throw new Error("STORAGE_BATCH_CONTAINERNAME environment variable is not set");
  }
  
  if (!storageFilename) {
    throw new Error("STORAGE_BATCH_FILENAME environment variable is not set");
  }

  try {
    // Initialize blob service client
    const blobServiceClient = BlobServiceClient.fromConnectionString(storageConnectionString);
    const containerClient = blobServiceClient.getContainerClient(storageContainer);
    const blobClient = containerClient.getBlobClient(storageFilename);
    
    // Check if blob exists
    const exists = await blobClient.exists();
    
    if (!exists) {
      throw new Error(`File ${storageFilename} does not exist in container ${storageContainer}`);
    }
    
    console.log(`File ${storageFilename} exists in container ${storageContainer}`);
    console.log(`Blob URL: ${blobClient.url}`);
    
    return blobClient.url;
  } catch (error) {
    console.error("Error checking blob existence:", error);
    throw error;
  }
}

/**
 * Create Job in the existing pool
 */
async function createJob(poolId: string): Promise<string> {
  // Validate pool ID
  if (!poolId) {
    throw new Error("BATCH_POOL_ID environment variable is not set");
  }

  // Generate a unique job ID using timestamp
  const timestamp = new Date().toISOString().replace(/[:\-]|\.\d+/g, "");
  const jobId = `job-${timestamp}`;
  
  console.log(`Creating Job: ${jobId} for Pool: ${poolId}`);

  try {
    await batchClient.job.add({
      id: jobId,
      poolInfo: { poolId: poolId },
    });
    console.log(`Job Created: ${jobId}`);
    return jobId;
  } catch (error) {
    console.error(
      `Error creating job: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
    throw error;
  }
}

/**
 * Create Task for the job
 */
async function createTask(jobId: string, blobUrl: string): Promise<string> {
  // Generate a unique task ID using timestamp
  const timestamp = new Date().toISOString().replace(/[:\-]|\.\d+/g, "");
  const taskId = `task-${timestamp}`;
  
  console.log(`Creating task: ${taskId} for job ID: ${jobId}`);

  // Extract the filename without extension for the folder name
  const filenameWithoutExt = path.parse(storageFilename).name;

  try {
    await batchClient.task.add(jobId, {
      id: taskId,
      commandLine:
        `cmd /c powershell -command \"Write-Host 'Downloading npm...'; Invoke-WebRequest -Uri 'https://nodejs.org/dist/v18.18.0/node-v18.18.0-win-x64.zip' -OutFile '%AZ_BATCH_TASK_WORKING_DIR%\\node-full.zip'; Expand-Archive -Path '%AZ_BATCH_TASK_WORKING_DIR%\\node-full.zip' -DestinationPath '%AZ_BATCH_TASK_WORKING_DIR%\\node-full' -Force; Write-Host 'Extracting application ZIP file...'; Expand-Archive -Path '%AZ_BATCH_TASK_WORKING_DIR%\\UploadMailJob.zip' -DestinationPath '%AZ_BATCH_TASK_WORKING_DIR%' -Force; Write-Host 'Installing npm globally...'; & '%AZ_BATCH_TASK_WORKING_DIR%\\node-full\\node-v18.18.0-win-x64\\node.exe' '%AZ_BATCH_TASK_WORKING_DIR%\\node-full\\node-v18.18.0-win-x64\\node_modules\\npm\\bin\\npm-cli.js' install -g npm@9.5.1; Write-Host 'Installing npm dependencies...'; Set-Location -Path '%AZ_BATCH_TASK_WORKING_DIR%\\dist'; & '%AZ_BATCH_TASK_WORKING_DIR%\\node-full\\node-v18.18.0-win-x64\\node.exe' '%AZ_BATCH_TASK_WORKING_DIR%\\node-full\\node-v18.18.0-win-x64\\node_modules\\npm\\bin\\npm-cli.js' ci; Write-Host 'Running Node.js application...'; Set-Location -Path '%AZ_BATCH_TASK_WORKING_DIR%\\dist\\UploadMailJob\\src'; & '%AZ_BATCH_TASK_WORKING_DIR%\\node-full\\node-v18.18.0-win-x64\\node.exe' index.js\"`,
      resourceFiles: [
        {
          httpUrl: blobUrl,
          filePath: storageFilename,
        },
      ],
    });
    console.log(`Task Created: ${taskId}`);
    return taskId;
  } catch (error) {
    console.error(
      `Error creating task: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
    throw error;
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  try {
    console.log("Starting Azure Batch job and task creation...");
    
    // Validate batch credentials
    if (!batchAccountName || !batchAccountKey || !batchAccountUrl) {
      throw new Error("Batch account credentials are not properly set in environment variables");
    }
    
    // Check if pool ID is provided
    if (!poolId) {
      throw new Error("BATCH_POOL_ID environment variable is not set");
    }
    
    // Check if the blob exists and get its URL
    const blobUrl = await checkBlobExistence();
    
    // Create job and task
    const jobId = await createJob(poolId);
    const taskId = await createTask(jobId, blobUrl);

    console.log("\nSummary:");
    console.log(`Pool ID: ${poolId} (existing)`);
    console.log(`Job ID: ${jobId}`);
    console.log(`Task ID: ${taskId}`);
    console.log(`Zip File: ${storageFilename}`);

    console.log("\nAzure Batch job and task created successfully!");
  } catch (error) {
    console.error("Error:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Execute the main function
main().catch((error: unknown) => {
  console.error("Unhandled error:", error);
  process.exit(1);
});