import { createGraphClient } from '../../utilities/graph';
import { logger, logLongArray } from '../../utilities/log';

import {
  createCredential,
  fetchTargetUsers,
  processUserTeamsChat,
} from './impl';
import * as dotenv from 'dotenv';

// import {
//   deleteAllDocumentsFromCosmosDB
// } from '../utilities/cosmos';

dotenv.config();

async function main() {

  logger.log('========== PHASE 1 ==========');
  const credential = createCredential(
    process.env['AzureTenantId'] ?? '',
    process.env['AzureClientId'] ?? '',
    process.env['AzureClientSecret'] ?? ''
  );
  if (!credential) {
    logger.error('[Index:createCredential] NO_CREDENTIALS');
    return Promise.reject(new Error('[Index:createCredential] NO_CREDENTIALS'));
  }
  const graphClient = createGraphClient(credential);

  logger.log('========== PHASE 2 ==========');

  const teamsAppId = process.env['TeamsAppId'] ?? '';
  if (!teamsAppId) {
    logger.error('[Index:teamsAppId] NO_TEAMS_APP_ID');
    return Promise.reject(new Error('[Index:teamsAppId] NO_TEAMS_APP_ID'));
  }

  const groupId = process.env['GraphGroupId'] ?? '';
  if (!groupId) {
    logger.error('NO_GRAPH_GROUP_ID');
    return Promise.reject(new Error('[Index:groupId] NO_GRAPH_GROUP_ID'));
  }
  const targetUsers = await fetchTargetUsers(logger, graphClient, groupId, teamsAppId)
    .catch((reason) => {
      logger.error(reason);
      return Promise.reject(new Error('[Index:fetchTargetUsers] Error:', reason));
    });
  if (targetUsers.length === 0) {
    logger.log('[Index:fetchTargetUsers] PROCESS_END_BECAUSE_OF_NO_USERS_TO_NOTIFY');
    return Promise.resolve();
  }

  // output log of the user IDs for each 800 entries
  logLongArray(logger, '[Index:fetchTargetUsers]:', 800, targetUsers);

  logger.log('========== PHASE 3 ==========');

  try {
    await processUserTeamsChat(logger, graphClient, targetUsers);
    logger.log("[Index:ProcessUserMessages] Successfully Inserted User Messages.");
  } catch (error) {
    logger.error(error);
  }

  // try {
  //   await deleteAllDocumentsFromCosmosDB(logger);
  //   logger.log("[Index:ProcessUserMessages] Successfully Delete All Date.");
  // } catch (error) {
  //   logger.error(error);
  // }
  
  logger.log('========== PHASE 4 ==========');
  logger.log("[Index] Successfully Completed.");

}

// Execute the main function
main().catch(error => {
  logger.error("Error Running Task:", error);
});