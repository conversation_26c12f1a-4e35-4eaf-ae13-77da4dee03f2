import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';

import { createUserTeamsChatRequests } from '../../utilities/userTeamsChat';
import { splitArrayIntoChunks } from '../../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForTeamsChat
} from '../../utilities/graph';
import { logLongArray, CustomLogger } from '../../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  ITeamsChatData
} from '../../utilities/models';
import { encrypt } from '../../utilities/encryption';
import {
  insertTeamsChatToCosmosDB,
  validateCosmosDBConnection,
} from '../../utilities/cosmos';

// GraphAPIのバッチリクエスト最大数
const MAX_GRAPH_API_USER_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_USER_BATCH_COUNTS ?? '5');
const MAX_MESSAGE_CHUNK_SIZE = parseInt(process.env.MAX_MESSAGE_CHUNK_SIZE ?? '20');

type Logger = CustomLogger;

/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}

/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  logger: Logger,
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);
  logLongArray(logger, '[Impl:fetchTargetUsers] AllGroupUsers.id', 800, users.map((u) => u.id));

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}

/**
 * Process all messages of the user by chunks
 */
function processUserTeamsChatByChunk(
  userId: string,
  allTeamsChat: ITeamsChatData[],
  allProcessedData: IBatchResponseData[],
  chunkSize: number,
  logger: Logger
): void {

  for (let i = 0; i < allTeamsChat.length; i += chunkSize) {
    const teamsChatAfterChunk = allTeamsChat.slice(i, i + chunkSize);

    // Process a chunk of messages
    processTeamsChatChunk(userId, teamsChatAfterChunk, allProcessedData, logger);

    teamsChatAfterChunk.length = 0;
  }
}

/**
 * Process a chunk of messages
 */
function processTeamsChatChunk(
  userId: string,
  teamsChatAfterChunk: ITeamsChatData[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues = [];

  // Process each message in the chunk
  for (const item of teamsChatAfterChunk) {

    // console.log(`item123: ${JSON.stringify(item)}`)

    // // Check if a message has empty required fields
    // if (hasEmptyRequiredFields(item, logger)) {
    //   continue;
    // }

    // Create a encrypted message object
    processedValues.push(createEncryptedMessage(userId, item));
    // processedValues.push(userId, item);
  }

  // Only create chunk if there are processed values
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: userId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processTeamsChatChunk] Successfully Processed TeamsChatChunk with ${processedValues.length} TeamsChat Inside | MaxTeamsChatInsideChunk:${MAX_MESSAGE_CHUNK_SIZE}`);
  }
}

// /**
//  * Check if a message has empty required fields
//  */
// function hasEmptyRequiredFields(item: ITeamsChatData, logger: Logger): boolean {
//   // Check for empty values
//   const isEmpty = (value: any) => value === null || value === undefined || value === "";
//   const hasEmptyValues =
//     isEmpty(item.subject) ||
//     isEmpty(item.bodyPreview) ||
//     isEmpty(item.from?.emailAddress?.name) ||
//     isEmpty(item.from?.emailAddress?.address);

//   if (hasEmptyValues) {
//     logger.log(`[Impl:hasEmptyRequiredFields] Skipping message with null values: ${JSON.stringify(item.id)}`);
//     return true;
//   }

//   return false;
// }

/**
 * Create a encrypted message object
 */
function createEncryptedMessage(userId: string, item: ITeamsChatData) {
  return {
    security_user_id: [userId],
    id: item.id,
    kind: "TeamsChat",
    replyToId: encrypt(item.replyToId) ?? null,
    createdDateTime: item.createdDateTime ?? null,
    lastModifiedDateTime: item.lastModifiedDateTime ?? null,
    chatId: encrypt(item.chatId) ?? null,
    from: item.from ? {
      user: item.from.user ? {
        displayName: encrypt(item.from.user.displayName)
      } : null,
      application: item.from.application ? {
        displayName: encrypt(item.from.application.displayName)
      } : null
    } : null,
    body: {
      content: encrypt(item.body.content) ?? null
    },
    channelIdentity: item.channelIdentity ? {
      teamId: encrypt(item.channelIdentity.teamId),
      channelId: encrypt(item.channelIdentity.channelId)
    } : null
  };
}

function processSingleUserResult(results: IBatchResponses[], logger: Logger): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  // Process batch responses one at a time
  for (const result of results) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleUserResult] Skipping Invalid Result: ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      // Skip invalid responses
      if (response.status !== 200 || !response.body?.value || !Array.isArray(response.body.value)) {
        if (response?.body?.error?.innerError) {
          logger.log(`[Impl:processSingleUserResult] Error Response: ${JSON.stringify(response)}`);
        }
        continue;
      }

      const userId = response.id ?? ''; // Extract everything before "-week"
      const allTeamsChat = response.body.value as ITeamsChatData[];
      const chunkSize = MAX_MESSAGE_CHUNK_SIZE;
      // logger.log(`[Impl:processSingleUserResult] Original Response: ${JSON.stringify(response)}`);

      // Process all messages of the user by chunks
      processUserTeamsChatByChunk(userId, allTeamsChat, allProcessedData, chunkSize, logger);

      allTeamsChat.length = 0;
    }
  }
  logger.info(`[Impl:processSingleUserResult] allProcessedData: ${JSON.stringify(allProcessedData)}`);
  return allProcessedData;
}

/**
 * Validates the COSMOS_DB connection
 */
async function validateDatabase(logger: Logger): Promise<void> {
  logger.log('--- VALIDATE COSMOS_DB CONNECTION ---');
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Impl:Validate] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Impl:Validate] CosmosDB Connection Failed: ${error}`);
    throw error;
  }
}

/**
 * Process all batch requests sequentially
 */
async function processBatchRequests(
  logger: Logger,
  client: Client,
  userSplitBatchRequests: BatchRequestData[][]
): Promise<void> {
  const totalBatchRequests = userSplitBatchRequests.length;

  // Process one batch at a time (each containing max of 20 user requests)
  for (let i = 0; i < totalBatchRequests; i++) {
    try {
      const currentBatch = userSplitBatchRequests[i];
      const currentBatchNumber = i + 1;

      logger.log(`\n====== BATCH START ${currentBatchNumber} of ${totalBatchRequests} ======`);

      // Process 1 batch request at a time
      const batchResult = await fetchJsonBatchForTeamsChat(logger, client, currentBatch);
      // const batchResult = {
      //   "responses": [
      //     {
      //       "id": "7025908f-f06e-4dee-85e9-91c42ae5663b",
      //       "status": 200,
      //       "headers": {
      //         "OData-Version": "4.0",
      //         "Content-Type": "application/json;odata.metadata=minimal;odata.streaming=true;IEEE754Compatible=false;charset=utf-8"
      //       },
      //       "body": {
      //         "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.chatMessage)",
      //         "@odata.count": 1,
      //         "@odata.nextLink": "https://graph.microsoft.com/v1.0/users/7025908f-f06e-4dee-85e9-91c42ae5663b/chats/getAllMessages?model=B&top=1&$skiptoken=U2tpcFZhbHVlPTEjTWFpbGJveEZvbGRlcj1NYWlsRm9sZGVycy9UZWFtc01lc3NhZ2VzRGF0YQ%3d%3d",
      //         "value": [
      //           {
      //             "id": "1736322063695",
      //             "replyToId": null,
      //             "etag": "1736322063695",
      //             "messageType": "message",
      //             "createdDateTime": "2025-01-08T07:41:03.695Z",
      //             "lastModifiedDateTime": "2025-01-08T07:41:03.695Z",
      //             "lastEditedDateTime": null,
      //             "deletedDateTime": null,
      //             "subject": "",
      //             "summary": null,
      //             "chatId": "19:<EMAIL>",
      //             "importance": "normal",
      //             "locale": "en-us",
      //             "webUrl": null,
      //             "channelIdentity": null,
      //             "policyViolation": null,
      //             "eventDetail": null,
      //             "from": {
      //               "application": null,
      //               "device": null,
      //               "user": {
      //                 "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                 "id": "0701e013-cf92-4c0e-9310-9704c6a23c89",
      //                 "displayName": "田中 一彰",
      //                 "userIdentityType": "aadUser",
      //                 "tenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f"
      //               }
      //             },
      //             "body": {
      //               "contentType": "html",
      //               "content": "<p>****************************************</p>"
      //             },
      //             "attachments": [],
      //             "mentions": [],
      //             "reactions": []
      //           }
      //         ]
      //       }
      //     },
      //     {
      //       "id": "0701e013-cf92-4c0e-9310-9704c6a23c89",
      //       "status": 200,
      //       "headers": {
      //         "OData-Version": "4.0",
      //         "Content-Type": "application/json;odata.metadata=minimal;odata.streaming=true;IEEE754Compatible=false;charset=utf-8"
      //       },
      //       "body": {
      //         "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.chatMessage)",
      //         "@odata.count": 1,
      //         "@odata.nextLink": "https://graph.microsoft.com/v1.0/users/0701e013-cf92-4c0e-9310-9704c6a23c89/chats/getAllMessages?model=B&top=1&$skiptoken=U2tpcFZhbHVlPTEjTWFpbGJveEZvbGRlcj1NYWlsRm9sZGVycy9UZWFtc01lc3NhZ2VzRGF0YQ%3d%3d",
      //         "value": [
      //           {
      //             "id": "1705285923200",
      //             "replyToId": null,
      //             "etag": "1705285923200",
      //             "messageType": "message",
      //             "createdDateTime": "2024-01-15T02:32:03.2Z",
      //             "lastModifiedDateTime": "2024-01-15T02:32:10Z",
      //             "lastEditedDateTime": null,
      //             "deletedDateTime": null,
      //             "subject": "",
      //             "summary": null,
      //             "chatId": "19:<EMAIL>",
      //             "importance": "normal",
      //             "locale": "en-us",
      //             "webUrl": null,
      //             "channelIdentity": null,
      //             "policyViolation": null,
      //             "eventDetail": null,
      //             "from": {
      //               "application": null,
      //               "device": null,
      //               "user": {
      //                 "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                 "id": "0701e013-cf92-4c0e-9310-9704c6a23c89",
      //                 "displayName": "田中 一彰",
      //                 "userIdentityType": "aadUser",
      //                 "tenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f"
      //               }
      //             },
      //             "body": {
      //               "contentType": "html",
      //               "content": "<p>少々お待ちください</p>"
      //             },
      //             "attachments": [],
      //             "mentions": [],
      //             "reactions": [
      //               {
      //                 "reactionType": "like",
      //                 "displayName": "like",
      //                 "reactionContentUrl": null,
      //                 "createdDateTime": "2024-01-15T02:32:10Z",
      //                 "user": {
      //                   "application": null,
      //                   "device": null,
      //                   "user": {
      //                     "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                     "id": "651e1949-020d-43e8-9d45-e01d3fde6f55",
      //                     "displayName": "真野 つずみ",
      //                     "userIdentityType": "aadUser",
      //                     "tenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f"
      //                   }
      //                 }
      //               }
      //             ],
      //             "messageHistory": [
      //               {
      //                 "modifiedDateTime": "2024-01-15T02:32:10Z",
      //                 "actions": "reactionAdded",
      //                 "reaction": {
      //                   "reactionType": "like",
      //                   "displayName": "like",
      //                   "reactionContentUrl": null,
      //                   "createdDateTime": "2024-01-15T02:32:10Z",
      //                   "user": {
      //                     "application": null,
      //                     "device": null,
      //                     "user": {
      //                       "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                       "id": "651e1949-020d-43e8-9d45-e01d3fde6f55",
      //                       "displayName": "真野 つずみ",
      //                       "userIdentityType": "aadUser"
      //                     }
      //                   }
      //                 }
      //               }
      //             ]
      //           }
      //         ]
      //       }
      //     },
      //     {
      //       "id": "2106b287-1053-49a0-a6c1-491f2fdb2c6e",
      //       "status": 200,
      //       "headers": {
      //         "OData-Version": "4.0",
      //         "Content-Type": "application/json;odata.metadata=minimal;odata.streaming=true;IEEE754Compatible=false;charset=utf-8"
      //       },
      //       "body": {
      //         "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.chatMessage)",
      //         "@odata.count": 1,
      //         "@odata.nextLink": "https://graph.microsoft.com/v1.0/users/2106b287-1053-49a0-a6c1-491f2fdb2c6e/chats/getAllMessages?model=B&top=1&$skiptoken=U2tpcFZhbHVlPTEjTWFpbGJveEZvbGRlcj1NYWlsRm9sZGVycy9UZWFtc01lc3NhZ2VzRGF0YQ%3d%3d",
      //         "value": [
      //           {
      //             "id": "1709268112723",
      //             "replyToId": null,
      //             "etag": "1709268112723",
      //             "messageType": "message",
      //             "createdDateTime": "2024-03-01T04:41:52.723Z",
      //             "lastModifiedDateTime": "2024-03-12T03:03:59Z",
      //             "lastEditedDateTime": null,
      //             "deletedDateTime": null,
      //             "subject": "",
      //             "summary": null,
      //             "chatId": "19:02eab279f44144018493456c5514e80d@thread.v2",
      //             "importance": "urgent",
      //             "locale": "en-us",
      //             "webUrl": null,
      //             "channelIdentity": null,
      //             "policyViolation": null,
      //             "eventDetail": null,
      //             "from": {
      //               "application": null,
      //               "device": null,
      //               "user": {
      //                 "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                 "id": "0701e013-cf92-4c0e-9310-9704c6a23c89",
      //                 "displayName": "田中 一彰",
      //                 "userIdentityType": "aadUser",
      //                 "tenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f"
      //               }
      //             },
      //             "body": { "contentType": "html", "content": "<p>重要テスト用</p>" },
      //             "attachments": [],
      //             "mentions": [],
      //             "reactions": []
      //           }
      //         ]
      //       }
      //     },
      //     {
      //       "id": "651e1949-020d-43e8-9d45-e01d3fde6f55",
      //       "status": 200,
      //       "headers": {
      //         "OData-Version": "4.0",
      //         "Content-Type": "application/json;odata.metadata=minimal;odata.streaming=true;IEEE754Compatible=false;charset=utf-8"
      //       },
      //       "body": {
      //         "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.chatMessage)",
      //         "@odata.count": 1,
      //         "@odata.nextLink": "https://graph.microsoft.com/v1.0/users/651e1949-020d-43e8-9d45-e01d3fde6f55/chats/getAllMessages?model=B&top=1&$skiptoken=U2tpcFZhbHVlPTEjTWFpbGJveEZvbGRlcj1NYWlsRm9sZGVycy9UZWFtc01lc3NhZ2VzRGF0YQ%3d%3d",
      //         "value": [
      //           {
      //             "id": "1705285923200",
      //             "replyToId": null,
      //             "etag": "1705285923200",
      //             "messageType": "message",
      //             "createdDateTime": "2024-01-15T02:32:03.2Z",
      //             "lastModifiedDateTime": "2024-01-15T02:32:10Z",
      //             "lastEditedDateTime": null,
      //             "deletedDateTime": null,
      //             "subject": "",
      //             "summary": null,
      //             "chatId": "19:<EMAIL>",
      //             "importance": "normal",
      //             "locale": "en-us",
      //             "webUrl": null,
      //             "channelIdentity": null,
      //             "policyViolation": null,
      //             "eventDetail": null,
      //             "from": {
      //               "application": null,
      //               "device": null,
      //               "user": {
      //                 "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                 "id": "0701e013-cf92-4c0e-9310-9704c6a23c89",
      //                 "displayName": "田中 一彰",
      //                 "userIdentityType": "aadUser",
      //                 "tenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f"
      //               }
      //             },
      //             "body": {
      //               "contentType": "html",
      //               "content": "<p>少々お待ちください</p>"
      //             },
      //             "attachments": [],
      //             "mentions": [],
      //             "reactions": [
      //               {
      //                 "reactionType": "like",
      //                 "displayName": "like",
      //                 "reactionContentUrl": null,
      //                 "createdDateTime": "2024-01-15T02:32:10Z",
      //                 "user": {
      //                   "application": null,
      //                   "device": null,
      //                   "user": {
      //                     "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                     "id": "651e1949-020d-43e8-9d45-e01d3fde6f55",
      //                     "displayName": "真野 つずみ",
      //                     "userIdentityType": "aadUser",
      //                     "tenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f"
      //                   }
      //                 }
      //               }
      //             ],
      //             "messageHistory": [
      //               {
      //                 "modifiedDateTime": "2024-01-15T02:32:10Z",
      //                 "actions": "reactionAdded",
      //                 "reaction": {
      //                   "reactionType": "like",
      //                   "displayName": "like",
      //                   "reactionContentUrl": null,
      //                   "createdDateTime": "2024-01-15T02:32:10Z",
      //                   "user": {
      //                     "application": null,
      //                     "device": null,
      //                     "user": {
      //                       "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                       "id": "651e1949-020d-43e8-9d45-e01d3fde6f55",
      //                       "displayName": "真野 つずみ",
      //                       "userIdentityType": "aadUser"
      //                     }
      //                   }
      //                 }
      //               }
      //             ]
      //           }
      //         ]
      //       }
      //     },
      //     {
      //       "id": "7b0b3f83-a9bf-41c1-8ffc-f9d4b91fb574",
      //       "status": 200,
      //       "headers": {
      //         "OData-Version": "4.0",
      //         "Content-Type": "application/json;odata.metadata=minimal;odata.streaming=true;IEEE754Compatible=false;charset=utf-8"
      //       },
      //       "body": {
      //         "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.chatMessage)",
      //         "@odata.count": 1,
      //         "@odata.nextLink": "https://graph.microsoft.com/v1.0/users/7b0b3f83-a9bf-41c1-8ffc-f9d4b91fb574/chats/getAllMessages?model=B&top=1&$skiptoken=U2tpcFZhbHVlPTEjTWFpbGJveEZvbGRlcj1NYWlsRm9sZGVycy9UZWFtc01lc3NhZ2VzRGF0YQ%3d%3d",
      //         "value": [
      //           {
      //             "id": "1724662169665",
      //             "replyToId": null,
      //             "etag": "1724662169665",
      //             "messageType": "message",
      //             "createdDateTime": "2024-08-26T08:49:29.665Z",
      //             "lastModifiedDateTime": "2024-08-26T08:49:29.665Z",
      //             "lastEditedDateTime": null,
      //             "deletedDateTime": null,
      //             "subject": null,
      //             "summary": null,
      //             "chatId": "19:<EMAIL>",
      //             "importance": "normal",
      //             "locale": "en-us",
      //             "webUrl": null,
      //             "channelIdentity": null,
      //             "policyViolation": null,
      //             "eventDetail": null,
      //             "from": {
      //               "device": null,
      //               "user": null,
      //               "application": {
      //                 "@odata.type": "#microsoft.graph.teamworkApplicationIdentity",
      //                 "id": "358f0194-6b0e-4dd3-af35-c24fe8a9ec87",
      //                 "displayName": "Workflows",
      //                 "applicationIdentityType": "bot"
      //               }
      //             },
      //             "body": {
      //               "contentType": "html",
      //               "content": "<attachment id=\"8013fd5b78bb414396076b2b277d76ae\"></attachment>"
      //             },
      //             "attachments": [
      //               {
      //                 "id": "8013fd5b78bb414396076b2b277d76ae",
      //                 "contentType": "application/vnd.microsoft.card.adaptive",
      //                 "contentUrl": null,
      //                 "content": "{\r\n  \"type\": \"AdaptiveCard\",\r\n  \"body\": [\r\n    {\r\n      \"size\": \"medium\",\r\n      \"text\": \"Your flow is ready to be run!\",\r\n      \"weight\": \"bolder\",\r\n      \"wrap\": true,\r\n      \"type\": \"TextBlock\"\r\n    },\r\n    {\r\n      \"horizontalAlignment\": \"center\",\r\n      \"url\": \"https://content.powerapps.com/resource/makerx/static/pauto/images/teamsstore/Flow_Created_Teams_Notification.7acd6603.png\",\r\n      \"height\": \"auto\",\r\n      \"type\": \"Image\"\r\n    },\r\n    {\r\n      \"size\": \"medium\",\r\n      \"text\": \"The instant flow, 'Follow up on a message', that you created is now ready to be triggered from Teams. Run it from the overflow menu of any message.\",\r\n      \"wrap\": true,\r\n      \"type\": \"TextBlock\"\r\n    }\r\n  ],\r\n  \"version\": \"1.0\"\r\n}",
      //                 "name": null,
      //                 "thumbnailUrl": null,
      //                 "teamsAppId": null
      //               }
      //             ],
      //             "mentions": [],
      //             "reactions": []
      //           }
      //         ]
      //       }
      //     },
      //     {
      //       "id": "c03c73e6-2b1a-4e55-a35c-b09299a6413d",
      //       "status": 200,
      //       "headers": {
      //         "OData-Version": "4.0",
      //         "Content-Type": "application/json;odata.metadata=minimal;odata.streaming=true;IEEE754Compatible=false;charset=utf-8"
      //       },
      //       "body": {
      //         "@odata.context": "https://graph.microsoft.com/v1.0/$metadata#Collection(microsoft.graph.chatMessage)",
      //         "@odata.count": 1,
      //         "@odata.nextLink": "https://graph.microsoft.com/v1.0/users/c03c73e6-2b1a-4e55-a35c-b09299a6413d/chats/getAllMessages?model=B&top=1&$skiptoken=U2tpcFZhbHVlPTEjTWFpbGJveEZvbGRlcj1NYWlsRm9sZGVycy9UZWFtc01lc3NhZ2VzRGF0YQ%3d%3d",
      //         "value": [
      //           {
      //             "id": "1723783295850",
      //             "replyToId": null,
      //             "etag": "1723783295850",
      //             "messageType": "message",
      //             "createdDateTime": "2024-08-16T04:41:35.85Z",
      //             "lastModifiedDateTime": "2024-08-16T04:41:35.85Z",
      //             "lastEditedDateTime": null,
      //             "deletedDateTime": null,
      //             "subject": null,
      //             "summary": null,
      //             "chatId": "19:<EMAIL>",
      //             "importance": "normal",
      //             "locale": "en-us",
      //             "webUrl": null,
      //             "channelIdentity": null,
      //             "policyViolation": null,
      //             "eventDetail": null,
      //             "from": {
      //               "application": null,
      //               "device": null,
      //               "user": {
      //                 "@odata.type": "#microsoft.graph.teamworkUserIdentity",
      //                 "id": "651e1949-020d-43e8-9d45-e01d3fde6f55",
      //                 "displayName": "真野 つずみ",
      //                 "userIdentityType": "aadUser",
      //                 "tenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f"
      //               }
      //             },
      //             "body": { "contentType": "text", "content": "良い天気ですね！" },
      //             "attachments": [],
      //             "mentions": [],
      //             "reactions": []
      //           }
      //         ]
      //       }
      //     }
      //   ]
      // };

      logger.log(`[Impl:processBatchRequests] batchResult ${JSON.stringify(batchResult)}`);

      if (!batchResult.responses || batchResult.responses.length === 0) {
        logger.log(`[Impl:processBatchRequests] No Responses in Batch ${currentBatchNumber}`);
        continue;
      }

      // Process all user responses in the batch
      await processBatchResult(logger, batchResult, currentBatchNumber);

      // Clear batch responses AFTER processing ALL users
      batchResult.responses = [];

      // Free memory for this batch
      userSplitBatchRequests[i] = [];

      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 2000));

      logger.log(`\n====== BATCH END ${currentBatchNumber} of ${totalBatchRequests} - COMPLETE ======\n`);

    } catch (error) {
      logger.error(`[Impl:processBatchRequests] Error processing batch starting at index ${i}: ${error}`);
      continue;
    }

    // Explicit GC if available
    global.gc && global.gc();
  }
}

/**
 * Process all user responses in a batch
 */
async function processBatchResult(
  logger: Logger,
  batchResult: IBatchResponses,
  currentBatchNumber: number
): Promise<void> {
  // Ensure responses array exists
  if (!batchResult.responses || batchResult.responses.length === 0) {
    return;
  }

  // Process one user at a time from the batch result
  for (let j = 0; j < batchResult.responses.length; j++) {
    const userResponse = batchResult.responses[j];

    // //uncomment to view raw response 
    // logger.info(`[Impl:processBatchResult] userResponse...`, userResponse);

    // Skip if response is undefined or status is not 200
    if (!userResponse || userResponse.status !== 200) {
      const responseId = userResponse?.id ?? 'User-ID-undefined';
      logger.log(`[Impl:processBatchResult] Skipping User-ID: ${responseId} with Error Data: ${userResponse?.status}`);
      continue;
    }

    const userId = userResponse.id;

    logger.log(`+=+= PROCESSING BATCH START: (Request: ${j + 1} of ${batchResult.responses.length} in Batch ${currentBatchNumber}) +=+=`);
    logger.log(`+=+= PROCESSING BATCH START: (User-ID: ${userId}) +=+=`);

    // Process each user's messages in chunks
    const singleUserResult = { responses: [userResponse] };

    const userTeamsChatChunks = processSingleUserResult([singleUserResult], logger);

    // Insert each chunk for each user
    for (let k = 0; k < userTeamsChatChunks.length; k++) {
      const chunk = userTeamsChatChunks[k];
      const currentChunk = k + 1;

      // //uncomment to view modified response 
      // logger.info(`[Impl:processBatchResult] chunk...`, chunk);

      if (!chunk) {
        logger.info(`[Impl:processBatchResult] Skipping Undefined TeamsChatChunk at Index: ${k} for User-ID: ${userId}`);
        continue;
      }

      logger.log(`--- INSERT COSMOS_DB START: (Request: ${j + 1} of ${batchResult.responses.length} in Batch ${currentBatchNumber}) ---`);
      logger.log(`--- INSERT COSMOS_DB START: (TeamsChatChunk: ${currentChunk} of ${userTeamsChatChunks.length} with ${chunk.body?.value?.length} TeamsChat Inside) ---`);

      try {
        logger.info(`[Impl:processBatchResult] Inserting TeamsChat...`);
        await insertTeamsChatToCosmosDB(logger, [chunk]);
        logger.info(`[Impl:processBatchResult] Successfully Inserted: (TeamsChatChunk: ${currentChunk})`);

      } catch (error) {
        logger.error(`[Impl:processBatchResult] Failed Inserting: (TeamsChatChunk: ${currentChunk}): ${error}`);
        continue;
      }

      userTeamsChatChunks[k] = {} as IBatchResponseData;

      logger.log(`--- INSERT COSMOS_DB END: (Request: ${j + 1} of ${batchResult.responses.length} in Batch ${currentBatchNumber}) ---`);
      logger.log(`--- INSERT COSMOS_DB END: (TeamsChatChunk: ${currentChunk} of ${userTeamsChatChunks.length}) ---`);
    }

    // Clear array
    userTeamsChatChunks.length = 0;

    // Give GC a chance to run
    global.gc && global.gc();

    // Small pause between users
    await new Promise(resolve => setTimeout(resolve, 1000));

    logger.log(`+=+= PROCESSING BATCH END: (User-ID: ${userId}) +=+=`);
    logger.log(`+=+= PROCESSING BATCH END: (Request: ${j + 1} of ${batchResult.responses.length} in Batch ${currentBatchNumber}) +=+=\n\n`);
  }
}

/**
 * ユーザーごとのメッセージを取得し、CosmosDBに挿入する
 * @param logger
 * @param client
 * @param users
 */
export async function processUserTeamsChat(
  logger: Logger,
  client: Client,
  users: IBatchResponseData[],
): Promise<void> {

  // TODO: 暫定的にログレベルをlogにしている。将来的にinfoに変更する
  logger.log(`[Impl:processUserTeamsChat] Total Users to Process: ${users.length}`);

  // ユーザーごとのバッチリクエストを作成
  const userBatchRequests: BatchRequestData[] = users
    .filter(user => user.id)
    .flatMap((user) =>
      createUserTeamsChatRequests(user?.id ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  // バッチリクエストを指定した数（MAX_GRAPH_API_USER_BATCH_COUNTS）で分割
  logger.log(`[Impl:processUserTeamsChat] Total Graph API Request: ${userBatchRequests.length} | MaxGraphAPIRequest: ${MAX_GRAPH_API_USER_BATCH_COUNTS}`);

  const userSplitBatchRequests = splitArrayIntoChunks(userBatchRequests, MAX_GRAPH_API_USER_BATCH_COUNTS);
  logger.log(`[Impl:processUserTeamsChat] Total Split Batch: ${userSplitBatchRequests.length}`);
  logger.log(`[Impl:processUserTeamsChat] userSplitBatchRequests: ${JSON.stringify(userSplitBatchRequests)}`);

  // Validates the COSMOS_DB connection
  await validateDatabase(logger);
  // Process all batch requests sequentially
  await processBatchRequests(logger, client, userSplitBatchRequests);

  // Final Cleanup
  userSplitBatchRequests.length = 0;
}