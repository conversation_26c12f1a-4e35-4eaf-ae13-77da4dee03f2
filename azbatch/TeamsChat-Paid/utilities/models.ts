import { Entity, NullableOption, PublicError } from '@microsoft/microsoft-graph-types/microsoft-graph';

export type ValueOf<T> = T[keyof T];

/**
 * Graph API Batchリクエストのレスポンス1件分
 */
export interface IBatchResponseData {
  id?: string,
  status?: number,
  headers?: NullableOption<{ 'Retry-After'?: string, 'Content-Type'?: string }>
  body?: {
    value?: Entity[],
    error?: NullableOption<PublicError>,
  },
}

/**
 * Graph API Batchリクエストのレスポンス
 * Ref: BatchResponseBody
 */
export interface IBatchResponses {
  "@odata.nextLink"?: string;
  responses?: IBatchResponseData[],
}

/**
 * Teamsのアクティビティフィード設定
 */
export interface ITeamsActivityNotificationConfig {
  chainId?: number,
  topic?: {
    source?: string;
    value?: string,
  },
  activityType?: string;
  previewText?: {
    content?: string;
    contentType?: string;
  },
  templateParameters?: { [key: string]: string }[],
}

/**
 * Azure Functionsが渡すタイマーオブジェクト
 * https://docs.microsoft.com/ja-jp/azure/azure-functions/functions-bindings-timer?tabs=javascript
 */
export interface TimerObject {
  schedule: unknown;
  scheduleStatus: {
    last?: string;
    lastUpdated?: string;
    next?: string;
  },
  isPastDate: boolean;
}

/**
 * User Messagesの取得条件
 */

export interface IEmailAddress {
  name: string;
  address: string;
}

export interface IMessageData {
  security_user_id : string[];
  id: string;
  kind: string;
  subject: string;
  from: {
    emailAddress: IEmailAddress;
  };
  receivedDateTime: string;
  bodyPreview: string;
  hasAttachments: boolean;
}

export interface IChannelIdentity {
  teamId: string;
  channelId: string;
}

export interface ITeamsChatData {
  security_user_id : string[];
  id: string;
  kind: string;
  replyToId: string;
  createdDateTime: string;
  lastModifiedDateTime: string;
  chatId: string;
  from: {
    user: {
      displayName: string;
    },
    application: {
      displayName: string;
    }
  };
  body: {
    content: string;
  };
  channelIdentity: IChannelIdentity
}