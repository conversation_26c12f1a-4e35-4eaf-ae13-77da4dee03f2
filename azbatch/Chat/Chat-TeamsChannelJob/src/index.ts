import { createGraphClient } from "../utilities/graph";
import { logger, logLongArray } from "../utilities/log";
import {
  createCredential,
  fetchTargetUsers,
  processJoinedTeams,
  processTeamsChannels,
  processTeamsChannelMessages,
  processTeamsChannelMessagesReplies,
  processTeamsMembers
} from "./impl";
import * as dotenv from "dotenv";
import { validateCosmosDBConnection } from "../utilities/cosmos";

dotenv.config();

async function main() {
  logger.log("========== PHASE 1 ==========");
  const credential = createCredential(
    process.env["AzureTenantId"] ?? "",
    process.env["AzureClientId"] ?? "",
    process.env["AzureClientSecret"] ?? ""
  );
  if (!credential) {
    logger.error("[Index:createCredential] NO_CREDENTIALS");
    return Promise.reject(new Error("[Index:createCredential] NO_CREDENTIALS"));
  }
  const graphClient = createGraphClient(credential);

  logger.log("\n========== PHASE 2 ==========");

  const teamsAppId = process.env["TeamsAppId"] ?? "";
  if (!teamsAppId) {
    logger.error("[Index:teamsAppId] NO_TEAMS_APP_ID");
    return Promise.reject(new Error("[Index:teamsAppId] NO_TEAMS_APP_ID"));
  }

  const groupId = process.env["GraphGroupId"] ?? "";
  if (!groupId) {
    logger.error("NO_GRAPH_GROUP_ID");
    return Promise.reject(new Error("[Index:groupId] NO_GRAPH_GROUP_ID"));
  }
  const targetUsers = await fetchTargetUsers(logger, graphClient, groupId, teamsAppId)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:fetchTargetUsers] Error:", reason));
  });
  if (targetUsers.length === 0) {
    logger.log(
      "[Index:fetchTargetUsers] PROCESS_END_BECAUSE_OF_NO_USERS_TO_NOTIFY"
    );
    return Promise.resolve();
  }
  logLongArray(logger, "[Index:fetchTargetUsers] Target Users:", 800, targetUsers);

  logger.log("\n========== PHASE 3 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:validateCosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:validateCosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }

  logger.log("\n========== PHASE 4 ==========");
  logger.log("--- Processing JoinedTeams ---");
  const uniqueTeams = await processJoinedTeams(logger, graphClient, targetUsers)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processJoinedTeams] Error:", reason));
  });
  logLongArray(logger, "[Index:processJoinedTeams] Unique Teams: ", 100, uniqueTeams);
  logger.log(`[Index:processJoinedTeams] Unique Teams Count: ${uniqueTeams.length}`);

  logger.log("\n========== PHASE 5 ==========");
  logger.log("--- Processing TeamsChannels ---");
  const uniqueTeamsChannels = await processTeamsChannels(logger, graphClient, uniqueTeams)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processTeamsChannels] Error:", reason));
  });
  logLongArray(logger, "[Index:processTeamsChannels] Unique TeamsChannels: ", 100, uniqueTeamsChannels);
  logger.log(`[Index:processTeamsChannels] Unique TeamsChannels Count: ${uniqueTeamsChannels.length}`);

  logger.log("\n========== PHASE 6 ==========");
  logger.log("--- Processing TeamsChannelMessages---");
  const uniqueTeamsChannelMessages = await processTeamsChannelMessages(logger, graphClient, uniqueTeamsChannels)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processTeamsChannelMessages] Error:", reason));
  });
  logLongArray(logger, "[Index:processTeamsChannelMessages] Unique TeamsChannelMessages: ", 800, uniqueTeamsChannelMessages);
  logger.log(`[Index:processTeamsChannelMessages] Unique TeamsChannelMessages Count: ${uniqueTeamsChannelMessages.length}`);
  logger.log(`[Index:processTeamsChannelMessages] Inserting TeamsChannelMessages - Completed`);

  logger.log("\n========== PHASE 7 ==========");
  logger.log("--- Processing TeamsChannelMessagesReplies---");
  await processTeamsChannelMessagesReplies(logger, graphClient, uniqueTeamsChannelMessages)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processTeamsChannelMessagesReplies] Error:", reason));
  });
  logger.log(`[Index:processTeamsChannelMessagesReplies] Inserting TeamsChannelMessagesReplies - Completed`);

  logger.log("\n========== PHASE 7 ==========");
  logger.log("--- Processing Teams Members ---");
  await processTeamsMembers(logger, graphClient, uniqueTeams, targetUsers)
  .catch((reason) => {
    logger.error(reason);
    return Promise.reject(new Error("[Index:processTeamsMembers] Error:", reason));
  });
  logger.log(`[Index:processTeamsMembers] Updating TeamsMembers - Completed`);

  logger.log("\n========== PHASE 8 ==========");
  logger.log("[Index] All Phase Successfully Completed.");
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});