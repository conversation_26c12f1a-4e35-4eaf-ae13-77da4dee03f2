import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeams1on1GroupMessages } from "./models";
import { CustomLogger } from "./log";
import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDBKey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertTeams1on1GroupChatsMessages(
  logger: CustomLogger,
  dataTeams1on1GroupChatsMessages: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:insertTeams1on1GroupChatsMessages] dataTeams1on1GroupChatsMessages: ${JSON.stringify(dataTeams1on1GroupChatsMessages)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertTeams1on1GroupChatsMessages] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataTeams1on1GroupChatsMessages: ITeams1on1GroupMessages[][] = dataTeams1on1GroupChatsMessages
      .map((message) => message.body?.value || [])
      .filter(Array.isArray);

    const insertedCount = await processTeams1on1GroupChatsMessages(container, modifiedDataTeams1on1GroupChatsMessages, logger);

    logger.log(`[CosmosDB:insertTeams1on1GroupChatsMessages] Inserted: ${insertedCount} New TeamsChatMessages to Cosmos DB`);
    const totalMessageCount = modifiedDataTeams1on1GroupChatsMessages.reduce((count, array) => count + (array ? array.length : 0),0 );
    logger.log(`[CosmosDB:insertTeams1on1GroupChatsMessages] Skipped: ${totalMessageCount - insertedCount} Existing TeamsChatMessages`);
  } catch (error) {
    logger.log(`[CosmosDB:insertTeams1on1GroupChatsMessages] Error Processing Messages: ${error}`);
    throw error;
  }
}

async function processTeams1on1GroupChatsMessages(
  container: Container,
  modifiedDataTeams1on1GroupChatsMessages: ITeams1on1GroupMessages[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  logger.log(`[CosmosDB:processTeams1on1GroupChatsMessages] Total modifiedDataTeams1on1GroupChatsMessages: ${modifiedDataTeams1on1GroupChatsMessages.length}`);
  // logger.log(`[CosmosDB:processTeams1on1GroupChatsMessages] modifiedDataTeams1on1GroupChatsMessages: ${JSON.stringify(modifiedDataTeams1on1GroupChatsMessages)}`); // !!!

  for (const messageArray of modifiedDataTeams1on1GroupChatsMessages) {
    for (const message of messageArray) {
      if (!message.id) {
        logger.log(`[CosmosDB:Process] Error: message.id is undefined for message: ${JSON.stringify(message.id)}`);
        continue;
      }
      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: message.id }]
      };
      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();
        if (existingMessages.length === 0) {
          await container.items.create(message);
          insertedCount++;
        }
      } catch (error) {
        logger.log(`[CosmosDB:processTeams1on1GroupChatsMessages] Error processing message: ${error}`);
      }
    }
  }
  return insertedCount;
}

export async function updateChatsMembers(
  logger: CustomLogger,
  dataChatMembers: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:updateChatsMembers] dataChatMembers: ${JSON.stringify(dataChatMembers)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:updateChatsMembers] databaseName and containerName must be defined");
    }
    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataChatsMembers = dataChatMembers
    .map((chat) => ({
      chatId: chat.id,
      members: chat.body?.value || []
    }))
  
    const updatedMessagesCount = await processChatMembers(container, modifiedDataChatsMembers, logger);
    logger.log(`[CosmosDB:updateChatsMembers] Updated ${updatedMessagesCount} Messages to Cosmos DB`);
  } catch (error) {
    logger.log(`[CosmosDB:updateChatsMembers] Error processing system messages: ${error}`);
    throw error;
  }
}

async function processChatMembers(
  container: Container,
  modifiedDataChatsMembers: any[],
  logger: CustomLogger
): Promise<number> {
  let updatedCount = 0;
  
  logger.log(`[CosmosDB:processChatMembers] Total modifiedDataChatsMembers: ${modifiedDataChatsMembers.length}`);
  // logger.log(`[CosmosDB:processChatMembers] modifiedDataChatsMembers: ${JSON.stringify(modifiedDataChatsMembers)}`); // !!!

  // Create a map of chat IDs to user IDs
  const chatToUserIdsMap: {[chatId: string]: string[]} = {};
  
  // Process each chat entry
  for (const chatEntry of modifiedDataChatsMembers) {
    const chatId = chatEntry.chatId;
    const members = chatEntry.members;
    
    if (!chatToUserIdsMap[chatId]) {
      chatToUserIdsMap[chatId] = [];
    }
    // Extract user IDs from each member
    if (!members || !Array.isArray(members)) {
      logger.log(`[CosmosDB:processChatMembers] Skipping Invalid Members: ${members?.id}`);
      continue;
    }
    for (const member of members) {
      if (member.userId && !chatToUserIdsMap[chatId].includes(member.userId)) {
        chatToUserIdsMap[chatId].push(member.userId);
      }
    }
  }
  
  logger.log(`[CosmosDB:processChatMembers] Chat to User ID's Map: ${JSON.stringify(chatToUserIdsMap)}`);
 
  // Process Messages on Chats
  for (const chatId in chatToUserIdsMap) {
    const userIds = chatToUserIdsMap[chatId];
    const querySpec = {
      query: "SELECT * FROM c WHERE c.chatId = @chatId",
      parameters: [
        {
          name: "@chatId",
          value: chatId
        }
      ]
    };
    const { resources: messages } = await container.items.query(querySpec).fetchAll();
    logger.log(`[CosmosDB:processChatMembers] Found ${messages.length} Messages for Chat: ${chatId}`);
    for (const message of messages) {
      message.security_user_id = userIds;
      try {
        await container.items.upsert(message);
        updatedCount++;
      } catch (error) {
        logger.log(`[CosmosDB:processChatMembers] Error Updating Message: ${message.id}: ${error}`);
      }
    }
  }
  return updatedCount;
}