import "isomorphic-fetch";
import { TokenCredential } from '@azure/core-auth';
import { ClientSecretCredential } from '@azure/identity';
import { BatchRequestData, Client } from '@microsoft/microsoft-graph-client';
import { 
  createTeams1on1GroupChatsRequests,
  createTeams1on1GroupChatsMessagesRequests
} from '../utilities/apiRequests';
import { splitArrayIntoChunks } from '../utilities/array';
import {
  fetchGroupUsers,
  fetchJsonBatchForTeamsChats
} from '../utilities/graph';
import { logLongArray, CustomLogger } from '../utilities/log';
import {
  IBatchResponseData,
  IBatchResponses,
  ITeamsChats,
  ITeamsUniqueChats,
  ITeamsChatMessageEvent
} from '../utilities/models';
import {
  checkTeams1on1GroupChatsMessages
} from '../utilities/cosmos';

// GraphAPIのバッチリクエスト最大数
const MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS ?? '20');
const MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS = parseInt(process.env.MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS ?? '20');
const MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE = parseInt(process.env.MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE ?? '50');

type Logger = CustomLogger;

/******************************************************* ========== PHASE 1 - START ========== *******************************************************/
/**
 * credentialを作成
 * 必須パラメータが存在しない場合はundefinedを返却
 */
export function createCredential(
  tenantId: string,
  clientId: string,
  clientSecret: string,
): TokenCredential | undefined {

  // 必須パラメータが存在しない場合はundefined
  if (!tenantId || !clientId || !clientSecret) {
    return undefined;
  }
  return new ClientSecretCredential(tenantId, clientId, clientSecret);
}
/******************************************************* ========== PHASE 1 - END ========== *********************************************************/
/******************************************************* ========== PHASE 2 - START ========== *******************************************************/
/**
 * グループ内に所属する全ユーザーのうち、カスタムアプリを所持するユーザーだけを抽出する
 * @param logger
 * @param client
 * @param groupId
 * @param teamsAppId
 */
export async function fetchTargetUsers(
  logger: Logger,
  client: Client,
  groupId: string,
  teamsAppId: string,
): Promise<IBatchResponseData[]> {

  // 対象とするユーザーを取得するためのグループID
  if (!groupId || !teamsAppId) {
    // 存在しない場合は異常終了
    return Promise.reject('NO_REQUIRED_IDS');
  }

  // グループ内のユーザー一覧を取得
  const users = await fetchGroupUsers(client, groupId);
  logLongArray(logger, '[Impl:fetchTargetUsers] AllGroupUsers.id', 800, users.map((u) => u.id));

  if (users.length === 0) {
    // 0件の場合はここで終了
    return Promise.resolve([]);
  }
  return users;
}
/******************************************************* ========== PHASE 2 - END ========== *********************************************************/
/******************************************************* ========== PHASE 4 - START ========== *******************************************************/
export async function processTeams1on1GroupChats(
  logger: Logger,
  client: Client,
  targetUsersData: IBatchResponseData[],
): Promise<ITeamsUniqueChats[]> {

  logger.log(`[Impl:processTeams1on1GroupChats] Total targetUsersData to Process: ${targetUsersData.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChats] targetUsersData: ${JSON.stringify(targetUsersData)}`); // !!!

  const userTeams1on1GroupBatchRequestsCreated: BatchRequestData[] = targetUsersData
    .filter(data => data.id)
    .flatMap((data) =>
      createTeams1on1GroupChatsRequests(data?.id ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeams1on1GroupChats] Total GraphAPI - userTeams1on1GroupBatchRequestsCreated: ${userTeams1on1GroupBatchRequestsCreated.length} | MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS: ${MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS}`);
  const userSplitTeams1on1GroupChatsBatchRequests = splitArrayIntoChunks(userTeams1on1GroupBatchRequestsCreated, MAX_GRAPH_API_1on1GROUP_CHATS_BATCH_COUNTS);
  logger.log(`[Impl:processTeams1on1GroupChats] Total Teams1on1GroupChats Split Batch: ${userSplitTeams1on1GroupChatsBatchRequests.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChats] userSplitTeams1on1GroupChatsBatchRequests: ${JSON.stringify(userSplitTeams1on1GroupChatsBatchRequests)}`); // !!!

  const uniqueChatsMap = new Map<string, ITeamsUniqueChats>();

  // Process all batch requests
  const totalTeams1on1GroupChatsRequests = userSplitTeams1on1GroupChatsBatchRequests.length;
  for (let i = 0; i < totalTeams1on1GroupChatsRequests; i++) {
    try {
      const currentTeams1on1GroupChatsBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_1on1GROUP_CHATS: (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests}) ====`);
      const currentUserSplitTeams1on1GroupChatsBatchRequests = userSplitTeams1on1GroupChatsBatchRequests[i];
      logger.log(`[Impl:processTeams1on1GroupChats] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeams1on1GroupChats = await fetchJsonBatchForTeamsChats(logger, client, currentUserSplitTeams1on1GroupChatsBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeams1on1GroupChats] *** batchResultTeams1on1GroupChats == ${JSON.stringify(batchResultTeams1on1GroupChats)} === (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests})`); // !!!
      
      if (!batchResultTeams1on1GroupChats.responses || batchResultTeams1on1GroupChats.responses.length === 0) {
        logger.log(`[Impl:processTeams1on1GroupChats] No Responses in Batch === (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests})`);
        continue;
      }

      // Extract the Unique Chats
      for (const response of batchResultTeams1on1GroupChats.responses) {
        if (response.status !== 200 || !response.body?.value) {
          logger.log(`[Impl:processTeams1on1GroupChats] Skipping ${response.id} due to invalid status or missing body value.`);
          continue;
        }
        for (const chatDetail of response.body.value) {
          const chatIdVal =(chatDetail as ITeamsChats).id;
          const topicVal = (chatDetail as ITeamsChats).topic;
          const chatTypeVal = (chatDetail as ITeamsChats).chatType;
          // Get chatId, topic, chatType
          if (chatIdVal && !uniqueChatsMap.has(chatIdVal)) {
            uniqueChatsMap.set(chatIdVal, {
              chatId: chatIdVal,
              topic: topicVal,
              chatType: chatTypeVal
            });
          }
        }
      }

      // Clear batch responses AFTER processing ALL users
      batchResultTeams1on1GroupChats.responses = [];
      // Free memory for this batch
      userSplitTeams1on1GroupChatsBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_1on1GROUP_CHATS: (Batch Request: ${currentTeams1on1GroupChatsBatchRequests} of ${totalTeams1on1GroupChatsRequests}) ====\n`);
    } catch (error) {
      logger.error(`[Impl:processTeams1on1GroupChats] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
  
  const uniqueChats = Array.from(uniqueChatsMap.values());
  userSplitTeams1on1GroupChatsBatchRequests.length = 0;

  return uniqueChats;
}
/******************************************************* ========== PHASE 4 - END ========== *********************************************************/
/******************************************************* ========== PHASE 5 - START ========== *******************************************************/
export async function processTeams1on1GroupChatsMessages(
  logger: Logger,
  client: Client,
  uniqueChatsData: ITeamsUniqueChats[],
  targetUsersData: IBatchResponseData[],
): Promise<void> {

  logger.log(`[Impl:processTeams1on1GroupChatsMessages] Total uniqueChatsData to Process: ${uniqueChatsData.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChatsMessages] uniqueChatsData: ${JSON.stringify(uniqueChatsData)}`);  // !!!
  logger.log(`[Impl:processTeams1on1GroupChats] Total targetUsersData to Process: ${targetUsersData.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChats] targetUsersData: ${JSON.stringify(targetUsersData)}`); // !!!

  const usersTeams1on1GroupChatsMessagesBatchRequestsCreated: BatchRequestData[] = uniqueChatsData
    .filter(data => data.chatId)
    .flatMap((data) =>
      createTeams1on1GroupChatsMessagesRequests(data?.chatId ?? '').map((request) => ({
        id: request.id,
        method: request.method,
        url: request.url
      }))
    );

  logger.log(`[Impl:processTeams1on1GroupChatsMessages] Total GraphAPI - usersTeams1on1GroupChatsMessagesBatchRequestsCreated: ${usersTeams1on1GroupChatsMessagesBatchRequestsCreated.length} | MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS: ${MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS}`);
  const userSplitTeams1on1GroupChatsMessagesBatchRequests = splitArrayIntoChunks(usersTeams1on1GroupChatsMessagesBatchRequestsCreated, MAX_GRAPH_API_1on1GROUP_CHATS_MESSAGES_BATCH_COUNTS);
  logger.log(`[Impl:processTeams1on1GroupChatsMessages] Total Teams1on1GroupChatsMessages Split Batch: ${userSplitTeams1on1GroupChatsMessagesBatchRequests.length}`);
  // logger.log(`[Impl:processTeams1on1GroupChatsMessages] userSplitTeams1on1GroupChatsMessagesBatchRequests: ${JSON.stringify(userSplitTeams1on1GroupChatsMessagesBatchRequests)}`);  // !!!

  // Process all batch requests
  const totalTeams1on1GroupChatsMessagesBatchRequests = userSplitTeams1on1GroupChatsMessagesBatchRequests.length;
  for (let i = 0; i < totalTeams1on1GroupChatsMessagesBatchRequests; i++) {
    try {
      const currentTeams1on1GroupChatsMessagesBatchRequests = i + 1;

      logger.log(`\n==== START - BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ====`);
      const currentUserSplitTeams1on1GroupChatsMessagesBatchRequests = userSplitTeams1on1GroupChatsMessagesBatchRequests[i];
      logger.log(`[Impl:processTeams1on1GroupChatsMessages] Processing Batch...`);

      // Process 1 batch request at a time
      const batchResultTeams1on1GroupChatsMessages = await fetchJsonBatchForTeamsChats(logger, client, currentUserSplitTeams1on1GroupChatsMessagesBatchRequests);
      // // *** View Raw Response
      // logger.log(`[Impl:processTeams1on1GroupChatsMessages] *** batchResultTeams1on1GroupChatsMessages == ${JSON.stringify(batchResultTeams1on1GroupChatsMessages)} === (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`); // !!!

      if (!batchResultTeams1on1GroupChatsMessages.responses || batchResultTeams1on1GroupChatsMessages.responses.length === 0) {
        logger.log(`[Impl:processTeams1on1GroupChatsMessages] No Responses in Batch === (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
        continue;
      }

      await processBatchResultTeams1on1GroupChatsMessages(logger, batchResultTeams1on1GroupChatsMessages, targetUsersData, currentTeams1on1GroupChatsMessagesBatchRequests, totalTeams1on1GroupChatsMessagesBatchRequests);

      // Clear batch responses AFTER processing ALL users
      batchResultTeams1on1GroupChatsMessages.responses = [];
      // Free memory for this batch
      userSplitTeams1on1GroupChatsMessagesBatchRequests[i] = [];
      // Add a delay between batches
      await new Promise(resolve => setTimeout(resolve, 3000));

      logger.log(`==== END - BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ====\n`);

    } catch (error) {
      logger.error(`[Impl:processTeams1on1GroupChatsMessages] Error Processing Batch Starting at Index ${i}: ${error}`);
      continue;
    }
    global.gc && global.gc();
  }
}

async function processBatchResultTeams1on1GroupChatsMessages(
  logger: Logger,
  batchResultTeams1on1GroupChatsMessages: IBatchResponses,
  targetUsersData: IBatchResponseData[],
  currentTeams1on1GroupChatsMessagesBatchRequests: number,
  totalTeams1on1GroupChatsMessagesBatchRequests: number
): Promise<void> {

  const totalBatchResultTeams1on1GroupChatsMessages = batchResultTeams1on1GroupChatsMessages.responses?.length ?? 0;
  for (let j = 0; j < totalBatchResultTeams1on1GroupChatsMessages; j++) {
    const currentBatchResultTeams1on1GroupChatsMessages = j + 1;
    const batchResultTeams1on1GroupChatsMessagesResponses = (batchResultTeams1on1GroupChatsMessages.responses ?? [])[j];
    const chatId = batchResultTeams1on1GroupChatsMessagesResponses.id;
    
    logger.log(`\n+=+= START - PROCESSING BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) +=+=`);

    if (!batchResultTeams1on1GroupChatsMessagesResponses || batchResultTeams1on1GroupChatsMessagesResponses.status !== 200) {
      logger.log(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Skipping chatId: ${chatId} with Error Data: ${batchResultTeams1on1GroupChatsMessagesResponses?.status} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
      continue;
    }

    const singleResultTeams1on1GroupChatsMessages = { responses: [batchResultTeams1on1GroupChatsMessagesResponses] };
    // logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] singleResultTeams1on1GroupChatsMessages == ${JSON.stringify(singleResultTeams1on1GroupChatsMessages)} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);  // !!!
    
    const modifiedTeams1on1GroupChatsMessagesChunk = processSingleResultTeams1on1GroupChatsMessages([singleResultTeams1on1GroupChatsMessages], targetUsersData, logger);
    // // *** View Raw Response
    // logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] *** modifiedTeams1on1GroupChatsMessagesChunk == ${JSON.stringify(modifiedTeams1on1GroupChatsMessagesChunk)} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`); // !!!

    const totalModifiedTeams1on1GroupChatsMessageChunk = modifiedTeams1on1GroupChatsMessagesChunk.length;
    // Insert each messageChunk
    for (let k = 0; k < totalModifiedTeams1on1GroupChatsMessageChunk; k++) {
      const messageChunk = modifiedTeams1on1GroupChatsMessagesChunk[k];
      const currentModifiedTeams1on1GroupChatsMessages = k + 1;
      if (!messageChunk) {
        logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Skipping Undefined Teams1on1GroupChatsMessages at Index: ${k} === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
        continue;
      }
      logger.log(`---- START - UPDATE COSMOS_DB | TEAMS_1on1GROUP_CHATS_MESSAGES: (Chunk: ${currentModifiedTeams1on1GroupChatsMessages} of ${totalModifiedTeams1on1GroupChatsMessageChunk} with ${messageChunk.body?.value?.length} ChatsMessages Inside) === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ----`);
      try {
        logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Updating Teams1on1GroupChatsMessages...`);
        await checkTeams1on1GroupChatsMessages(logger, [messageChunk]);
        logger.info(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Successfully Updated Teams1on1GroupChatsMessages...`);

      } catch (error) {
        logger.error(`[Impl:processBatchResultTeams1on1GroupChatsMessages] Failed Updating: ${error} (Chunk: ${currentModifiedTeams1on1GroupChatsMessages} of ${totalModifiedTeams1on1GroupChatsMessageChunk} with ${messageChunk.body?.value?.length} ChatsMessages Inside) === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests})`);
        continue;
      }
      modifiedTeams1on1GroupChatsMessagesChunk[k] = {} as IBatchResponseData;
      logger.log(`---- END - UPDATE COSMOS_DB | TEAMS_1on1GROUP_CHATS_MESSAGES: (Chunk: ${currentModifiedTeams1on1GroupChatsMessages} of ${totalModifiedTeams1on1GroupChatsMessageChunk} with ${messageChunk.body?.value?.length} ChatsMessages Inside) === (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) ----`);
    }

    modifiedTeams1on1GroupChatsMessagesChunk.length = 0;
    global.gc && global.gc();
    await new Promise(resolve => setTimeout(resolve, 3000));

    logger.log(`+=+= END - PROCESSING BATCH | TEAMS_1on1GROUP_CHATS_MESSAGES: (chatId: ${chatId} / Batch Result: ${currentBatchResultTeams1on1GroupChatsMessages} of ${totalBatchResultTeams1on1GroupChatsMessages} / Batch Request: ${currentTeams1on1GroupChatsMessagesBatchRequests} of ${totalTeams1on1GroupChatsMessagesBatchRequests}) +=+=`);
  }
}

function processSingleResultTeams1on1GroupChatsMessages(
  singleResTeams1on1GroupChatsMessages: IBatchResponses[],
  targetUsersData: IBatchResponseData[],
  logger: Logger
): IBatchResponseData[] {
  const allProcessedData: IBatchResponseData[] = [];

  for (const result of singleResTeams1on1GroupChatsMessages) {
    if (!result?.responses || !Array.isArray(result.responses)) {
      logger.log(`[Impl:processSingleResultTeams1on1GroupChatsMessages] Skipping Invalid Teams1on1GroupChatsMessages Result == ${JSON.stringify(result)}`);
      continue;
    }

    // Process each response in the batch
    for (const response of result.responses) {
      const totalTeams1on1GroupChatsMessages = response.body?.value?.length || 0;
      logger.log(`[Impl:processSingleResultTeams1on1GroupChatsMessages] Total Teams1on1GroupChatsMessages in response.body.value: ${totalTeams1on1GroupChatsMessages}`);

      const chatId = response.id ?? '';
      const allTeamsChatMessages = response.body?.value as ITeamsChatMessageEvent[];
      // logger.log(`[Impl:processSingleResultChatsMembers] allChatsMembers: ${JSON.stringify(allChatsMembers)}`); // !!!
      const filteredMessages = allTeamsChatMessages
      .filter((item: ITeamsChatMessageEvent) => {
        return !!(item.eventDetail && 
          (item.eventDetail["@odata.type"] === "#microsoft.graph.membersDeletedEventMessageDetail" ||
           item.eventDetail["@odata.type"] === "#microsoft.graph.membersAddedEventMessageDetail"));
      })
      .map((item: ITeamsChatMessageEvent) => {
        // Create a copy of the item to avoid modifying the original
        const modifiedItem = { ...item };
        // Check if the event detail has members
        if (modifiedItem.eventDetail?.members && Array.isArray(modifiedItem.eventDetail.members)) {
          // Filter members to only include those that match targetUsersData
          const filteredMembers = modifiedItem.eventDetail.members.filter(member => 
            targetUsersData.some(user => user.id === member.id)
          );
          // Remove duplicates
          const uniqueMemberIds = new Set();
          const uniqueMembers = filteredMembers.filter(member => {
            if (uniqueMemberIds.has(member.id)) {
              return false;
            } else {
              uniqueMemberIds.add(member.id);
              return true;
            }
          });
          // Replace the members array in the item
          modifiedItem.eventDetail.members = uniqueMembers;
        }
        return modifiedItem;
      })
      .sort((a, b) => {
        const dateA = new Date(a.lastModifiedDateTime || 0);
        const dateB = new Date(b.lastModifiedDateTime || 0);
        return dateA.getTime() - dateB.getTime(); // Ascending order (oldest first)
      });
      
      // logger.log(`[Impl:processSingleResultTeams1on1GroupChatsMessages] filteredMessages: ${JSON.stringify(filteredMessages)}`); // !!!
      const chunkSize = MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE;
      
      // Process messages in chunks
      for (let i = 0; i < filteredMessages.length; i += chunkSize) {
        const teams1on1GroupChatsMessagesAfterChunk = filteredMessages.slice(i, i + chunkSize);
        // logger.info(`[Impl:processSingleResultTeams1on1GroupChatsMessages] teams1on1GroupChatsMessagesAfterChunk: ${JSON.stringify(teams1on1GroupChatsMessagesAfterChunk)}`); // !!!
        processTeams1on1GroupChatsMessagesChunk(chatId, teams1on1GroupChatsMessagesAfterChunk, allProcessedData, logger);
        teams1on1GroupChatsMessagesAfterChunk.length = 0;
      }
      allTeamsChatMessages.length = 0;
    }
  }
  // logger.info(`[Impl:processSingleResultTeams1on1GroupChatsMessages] allProcessedData == ${JSON.stringify(allProcessedData)}`); // !!!
  return allProcessedData;
}

function processTeams1on1GroupChatsMessagesChunk(
  chatId: string,
  teams1on1GroupChatsMessagesAfterChunk: ITeamsChatMessageEvent[],
  allProcessedData: IBatchResponseData[],
  logger: Logger
): void {
  const processedValues: ITeamsChatMessageEvent[] = [];

  for (const item of teams1on1GroupChatsMessagesAfterChunk) {
    if (hasEmptyRequiredFields(item, logger)) {
      continue;
    }
    processedValues.push(createNewMessage(item));
  }
  if (processedValues.length > 0) {
    allProcessedData.push({
      id: chatId,
      body: {
        value: processedValues
      }
    } as IBatchResponseData);

    logger.info(`[Impl:processTeams1on1GroupChatsMessagesChunk] Successfully Modified Teams1on1GroupChatsMessages ${processedValues.length} Teams1on1GroupChatsMessages Inside Chunk | MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE: ${MAX_TEAMS_1on1GROUP_CHATS_MESSAGES_CHUNK_SIZE}`);
  }
}

function hasEmptyRequiredFields(item: ITeamsChatMessageEvent, logger: Logger): boolean {
  // Check for empty values
  const isEmpty = (value: any) => value === null || value === undefined || value === "";
  const hasEmptyId = !item.id || isEmpty(item.id);
  const hasEmptyBody = !item.body || isEmpty(item.body.content);
  const hasEmptyValues = hasEmptyId || hasEmptyBody;
  if (hasEmptyValues) {
    logger.log(`[Impl:hasEmptyRequiredFields] Skipping Message: ${item.id} with Empty Required Fields`);
    return true;
  }
  return false;
}

function createNewMessage(item: ITeamsChatMessageEvent): ITeamsChatMessageEvent {
  return {
    id: item.id,
    chatId: item.chatId,
    lastModifiedDateTime: item.lastModifiedDateTime,
    body: {
      content: item.body.content
    },
    eventDetail: item.eventDetail ? {
      "@odata.type": item.eventDetail["@odata.type"],
      visibleHistoryStartDateTime: item.eventDetail.visibleHistoryStartDateTime,
      members: item.eventDetail.members
    } : null
  };
}
/******************************************************* ========== PHASE 5 - END ========== *********************************************************/