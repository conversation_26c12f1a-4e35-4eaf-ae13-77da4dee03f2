import CryptoJS from 'crypto-js';
import * as dotenv from 'dotenv';

dotenv.config();

const SERVER_SECRET = process.env['AES_SECRET_KEY']!;
const IV = CryptoJS.enc.Hex.parse(process.env['AES_IV_KEY']!);

export function encrypt(message: string): string {
  try {
    const key = CryptoJS.enc.Utf8.parse(SERVER_SECRET);
    const encrypted = CryptoJS.AES.encrypt(message, key, {
      iv: IV,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
  } catch (error) {
    throw new Error('Encryption Failed: ' + (error instanceof Error ? error.message : 'Unknown Error'));
  }
}

export function decrypt(encryptedMessage: string): string {
  try {
    const key = CryptoJS.enc.Utf8.parse(SERVER_SECRET);
    const decrypted = CryptoJS.AES.decrypt(
      CryptoJS.lib.CipherParams.create({
        ciphertext: CryptoJS.enc.Base64.parse(encryptedMessage)
      }),
      key,
      {
        iv: IV,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    );
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    throw new Error('Decryption Failed: ' + (error instanceof Error ? error.message : 'Unknown Error'));
  }
}