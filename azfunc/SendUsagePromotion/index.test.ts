import { mockLogger, createMockContext as mockContext } from '../mocks/context';
import { TimerObject } from '../utilities/models';
import main from './index';
import * as impl from './impl';

const logger = mockLogger;

const timerObjMock = {
  schedule: null,
  scheduleStatus: {},
  isPastDate: false,
} as TimerObject;

function createMockContext() {
  return mockContext({
    storageIn: '2021-08-01',
    storageOut: '',
  });
}

describe('main', () => {

  let sendActivitiesToUsersMock: jest.SpyInstance | undefined = undefined;

  beforeEach(() => {
    jest
      .spyOn(impl, 'fetchTargetUsersByGroupIds')
      .mockResolvedValue([{ status: 200, id: 'abc' }]);

    sendActivitiesToUsersMock = jest.spyOn(impl, 'sendActivitiesToUsers')
      .mockResolvedValue([{ status: 204 }]);

    logger.mockClear();

    process.env['AzureTenantId'] = 'a';
    process.env['AzureClientId'] = 'b';
    process.env['AzureClientSecret'] ='c';
    process.env['TeamsAppId'] = 'd';
    process.env['GraphGroupId'] = 'e';
    process.env['FeedTitleMessage'] = 'f';
  });

  it('should log some values on the way', async () => {
    await expect(main(
      createMockContext(),
      timerObjMock,
    )).resolves.toBeUndefined();

    expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
    expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));
    expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 3'));
    expect(logger).toHaveBeenCalledWith(
      `Selected users(1/1): 1, ${JSON.stringify(['abc'])}`
    );

  });

  it('should call sendActivitiesToUsers with "FEEDの内容"', async () => {
    await expect(main(
    createMockContext(),
    timerObjMock,
  )).resolves.toBeUndefined();

  expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
  expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));
  expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 3'));

  expect(sendActivitiesToUsersMock).not.toBeUndefined();
  expect(sendActivitiesToUsersMock).toBeCalledWith(
    expect.anything(),
    expect.anything(),
    [{ status: 200, id: 'abc' }],
    'f',
  );
});

// 【credential env paramsが取得できなかったとき】
  describe('when the credential env params are not available', () => {
    it('should reject with NO_CREDENTIALS', async () => {
      process.env['AzureTenantId'] = '';

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).rejects.toMatch('NO_CREDENTIALS');
      expect(logger).toHaveBeenLastCalledWith('NO_CREDENTIALS');

      process.env['AzureTenantId'] = 'a';
      process.env['AzureClientId'] = '';

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).rejects.toMatch('NO_CREDENTIALS');
      expect(logger).toHaveBeenLastCalledWith('NO_CREDENTIALS');

      process.env['AzureClientId'] = 'b';
      process.env['AzureClientSecret'] ='';

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).rejects.toMatch('NO_CREDENTIALS');
      expect(logger).toHaveBeenLastCalledWith('NO_CREDENTIALS');

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
      expect(logger).not.toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));
      expect(logger).not.toHaveBeenCalledWith(expect.stringContaining('PHASE 3'));
    });
  });

  // 【TeamsAppIdが取得できなかったとき】
  describe('when TeamsAppId is not available', () => {
    it('should rejects with NO_TEAMS_APP_ID', async () => {
      process.env['TeamsAppId'] = '';

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).rejects.toMatch('NO_TEAMS_APP_ID');

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));

      expect(logger).toHaveBeenLastCalledWith('NO_TEAMS_APP_ID');
    });
  });

  // 【fetchTargetUsersByGroupIdsが拒否された時】
  describe('when fetchTargetUsersByGroupIds rejects', () => {
    it('should rejects with the reason', async () => {
      jest
        .spyOn(impl, 'fetchTargetUsersByGroupIds')
        .mockRejectedValueOnce('reason');

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).rejects.toMatch('reason');

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));

      expect(logger).toHaveBeenLastCalledWith('reason');
    });
  });

  // 【fetchTargetUsersByGroupIdsの結果が０だった時】
  describe('when fetchTargetUsersByGroupIds resolves a zero length array', () => {
    it('should resolves undefined and log PROCESS_END_BECAUSE_OF_NO_USERS_TO_NOTIFY', async () => {
      jest
        .spyOn(impl, 'fetchTargetUsersByGroupIds')
        .mockResolvedValueOnce([]);

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));

      expect(logger).toHaveBeenLastCalledWith('PROCESS_END_BECAUSE_OF_NO_USERS_TO_NOTIFY');
    });
  });

  // 【sendActivitiesToUsersの結果は問題ないけど何かしらのエラーが出た時】
  describe('when sendActivitiesToUsers resolves but the results includes some errors', () => {
    it('should log error results', async () => {
      jest.spyOn(impl, 'sendActivitiesToUsers')
        .mockResolvedValueOnce([{ status: 500 }, { status: 204 }, { status: 400 }]);

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 3'));

      expect(logger).toHaveBeenCalledWith({ status: 500 });
      expect(logger).not.toHaveBeenCalledWith({ status: 204 });
      expect(logger).toHaveBeenCalledWith({ status: 400 });
    });
  });

  // 【sendActivitiesToUsersの取得に失敗した時】
  describe('when sendActivitiesToUsers rejects', () => {
    it('should resolves undefined but log the reason', async () => {
      jest.spyOn(impl, 'sendActivitiesToUsers')
        .mockRejectedValueOnce('reason');

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).resolves.toBeUndefined();

      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 3'));

      expect(logger).toHaveBeenLastCalledWith('reason');
    });
  });

  // 【feedTitleMessageが取得できなかったとき】
  describe('when feedTitleMessage is not available', () => {
    it('Should be notified with defaultFeedTitleMessage', async () => {
      process.env['FeedTitleMessage'] = "";

      await expect(main(
        createMockContext(),
        timerObjMock,
      )).resolves.toBeUndefined();
    
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 1'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 2'));
      expect(logger).toHaveBeenCalledWith(expect.stringContaining('PHASE 3'));
    
      expect(sendActivitiesToUsersMock).not.toBeUndefined();
      expect(sendActivitiesToUsersMock).toBeCalledWith(
        expect.anything(),
        expect.anything(),
        [{ status: 200, id: 'abc' }],
        '横断検索してみませんか？AI検索機能が追加されました!',
      );
    });
  });


});
